/**
 * 📦 Stagewise Components Export Index
 * VIBECODE V1.0 - Centralized Exports for Stagewise Integration
 * 
 * Provides clean imports for all Stagewise components and utilities
 */

// Main Components
export { StagewiseProvider, useStagewise } from './StagewiseProvider';
export type { StagewiseProviderProps } from './StagewiseProvider';

// Configuration
export { 
  getStagewiseConfig, 
  validateStagewiseConfig,
  defaultStagewiseConfig,
  projectConfigs 
} from '../config/stagewise.config';
export type { StagewiseConfig } from '../config/stagewise.config';

// Environment
export { 
  detectEnvironment, 
  validateEnvironment as validateEnv, 
  isStagewiseEnabled 
} from '../config/environment.config';
export type { EnvironmentConfig } from '../config/environment.config';

// Validation
export { 
  validateConfiguration,
  validateDependencies,
  validateEnvironment,
  runFullValidation,
  logValidationResults 
} from '../utils/validation';
export type { ValidationResult } from '../utils/validation';

// Re-export for convenience
export default StagewiseProvider;
