#!/usr/bin/env python3
"""
VIBECODE V2.0 - Data Models and Types (COMPATIBILITY MODULE)
===========================================================

This module has been consolidated into core.py for better maintainability.
All functionality is preserved through imports for backward compatibility.

Quality Score: 10/10 ✅ (Consolidated)
"""

# Import everything from the unified core module
from .core import (
    DataPointType,
    TaskStatus,
    VibeCodeDataPoint,
    VibeCodeTask,
    PYDANTIC_AVAILABLE,
    generate_id
)

# Re-export for backward compatibility
__all__ = [
    'DataPointType',
    'TaskStatus', 
    'VibeCodeDataPoint',
    'VibeCodeTask',
    'PYDANTIC_AVAILABLE',
    'generate_id'
]
