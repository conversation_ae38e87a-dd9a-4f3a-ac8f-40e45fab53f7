import { createClient } from "@/app/utils/supabase/client";
import { useCallback, useState } from "react";

// =============================================
// NeonPro Conflict Override Hook
// Story 1.2: Manager conflict override system
// =============================================

interface ConflictItem {
  type: string;
  message: string;
  severity: "error" | "warning" | "info";
}

interface ConflictOverrideData {
  appointment_id?: string;
  professional_id: string;
  clinic_id: string;
  patient_id: string;
  service_type_id: string;
  start_time: string;
  end_time: string;
  override_reason: string;
  conflicts: ConflictItem[];
}

interface ConflictOverrideResponse {
  success: boolean;
  appointment_id?: string;
  override_id?: string;
  warnings?: string[];
  errors?: string[];
}

interface OverrideHistory {
  id: string;
  override_reason: string;
  conflicts: ConflictItem[];
  override_timestamp: string;
  manager_email: string;
  is_active: boolean;
  profiles?: {
    full_name: string;
  };
}

export const useConflictOverride = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  const submitOverride = useCallback(
    async (
      overrideData: ConflictOverrideData
    ): Promise<ConflictOverrideResponse> => {
      try {
        setIsSubmitting(true);
        setError(null);

        const response = await fetch("/api/appointments/conflict-override", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(overrideData),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to submit override");
        }

        return data;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        setError(errorMessage);
        throw err;
      } finally {
        setIsSubmitting(false);
      }
    },
    []
  );

  const getOverrideHistory = useCallback(
    async (
      appointmentId: string,
      clinicId: string
    ): Promise<OverrideHistory[]> => {
      try {
        setError(null);

        const response = await fetch(
          `/api/appointments/conflict-override?appointment_id=${appointmentId}&clinic_id=${clinicId}`
        );

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch override history");
        }

        return data.overrides || [];
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Failed to fetch override history";
        setError(errorMessage);
        throw err;
      }
    },
    []
  );

  const checkManagerPermissions = useCallback(
    async (clinicId: string): Promise<boolean> => {
      try {
        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) return false;

        // Check user role in clinic
        const { data, error } = await supabase
          .from("clinic_users")
          .select("role")
          .eq("user_id", user.id)
          .eq("clinic_id", clinicId)
          .eq("is_active", true)
          .single();

        if (error || !data) return false;

        return ["admin", "manager"].includes(data.role);
      } catch (err) {
        console.error("Error checking manager permissions:", err);
        return false;
      }
    },
    [supabase]
  );

  const formatConflictOverrideRequest = useCallback(
    (
      appointmentData: {
        appointment_id?: string;
        professional_id: string;
        clinic_id: string;
        patient_id: string;
        service_type_id: string;
        start_time: string;
        end_time: string;
      },
      conflicts: ConflictItem[],
      overrideReason: string
    ): ConflictOverrideData => {
      return {
        appointment_id: appointmentData.appointment_id,
        professional_id: appointmentData.professional_id,
        clinic_id: appointmentData.clinic_id,
        patient_id: appointmentData.patient_id,
        service_type_id: appointmentData.service_type_id,
        start_time: appointmentData.start_time,
        end_time: appointmentData.end_time,
        override_reason: overrideReason,
        conflicts,
      };
    },
    []
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    submitOverride,
    getOverrideHistory,
    checkManagerPermissions,
    formatConflictOverrideRequest,
    isSubmitting,
    error,
    clearError,
  };
};

export type {
  ConflictItem,
  ConflictOverrideData,
  ConflictOverrideResponse,
  OverrideHistory,
};
