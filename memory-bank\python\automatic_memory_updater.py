"""
🔄 VIBECODE V1.0 - AUTOMATIC MEMORY UPDATER
Sistema de atualização automática de memória para Memory Bank

Funcionalidades:
- Automatic memory updates após mudanças significativas
- Plan Mode & Act Mode workflow integration
- Real-time context preservation
- Cross-session memory persistence
"""

import json
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MemoryUpdate:
    """Estrutura para updates de memória"""
    timestamp: str
    update_type: str  # 'context', 'progress', 'learning', 'task'
    content: Dict[str, Any]
    source: str  # 'plan_mode', 'act_mode', 'auto_update'
    priority: int  # 1-10

class AutomaticMemoryUpdater:
    """Sistema de atualização automática de memória"""
    
    def __init__(self, memory_bank_path: str = "memory-bank"):
        self.memory_bank_path = Path(memory_bank_path)
        self.active_context_path = self.memory_bank_path / "activeContext.md"
        self.progress_path = self.memory_bank_path / "progress.md"
        self.system_learning_path = self.memory_bank_path / "systemLearning.md"
        
    def update_active_context(self, 
                            task_name: str,
                            phase: str,
                            status: str,
                            complexity: int,
                            agent: str = "OPERATIONS_COORDINATOR") -> bool:
        """Atualiza o contexto ativo automaticamente"""
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M UTC")
            
            # Lê o contexto atual
            if self.active_context_path.exists():
                with open(self.active_context_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Atualiza as informações principais
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if line.startswith('**Task**:'):
                        lines[i] = f"**Task**: {task_name}"
                    elif line.startswith('**Phase**:'):
                        lines[i] = f"**Phase**: {phase}"
                    elif line.startswith('**Status**:'):
                        lines[i] = f"**Status**: {status}"
                    elif line.startswith('**Complexity**:'):
                        lines[i] = f"**Complexity**: {complexity}/10"
                    elif line.startswith('**Agent**:'):
                        lines[i] = f"**Agent**: {agent}"
                
                # Adiciona timestamp de última atualização
                if "**Last Auto-Update**:" not in content:
                    lines.append(f"\n**Last Auto-Update**: {timestamp}")
                else:
                    for i, line in enumerate(lines):
                        if line.startswith('**Last Auto-Update**:'):
                            lines[i] = f"**Last Auto-Update**: {timestamp}"
                
                # Salva o arquivo atualizado
                with open(self.active_context_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                
                logger.info(f"✅ Active context updated: {task_name} - {status}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error updating active context: {e}")
            return False
    
    def log_progress_update(self, 
                          milestone: str,
                          description: str,
                          completion_percentage: int) -> bool:
        """Registra progresso automaticamente"""
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M UTC")
            
            progress_entry = f"""
## 📈 {milestone} - {timestamp}

**Completion**: {completion_percentage}%
**Description**: {description}
**Auto-logged**: ✅ Automatic Memory Update

---
"""
            
            # Adiciona ao arquivo de progresso
            with open(self.progress_path, 'a', encoding='utf-8') as f:
                f.write(progress_entry)
            
            logger.info(f"✅ Progress logged: {milestone} ({completion_percentage}%)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error logging progress: {e}")
            return False
    
    def activate_plan_mode(self, objective: str, complexity: int) -> Dict[str, Any]:
        """Ativa Plan Mode workflow"""
        plan_context = {
            "mode": "PLAN_MODE",
            "objective": objective,
            "complexity": complexity,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "PLANNING_ACTIVE",
            "next_steps": []
        }
        
        # Atualiza contexto ativo
        self.update_active_context(
            task_name=f"Planning: {objective}",
            phase="Plan Mode Active",
            status="Structured Planning in Progress",
            complexity=complexity
        )
        
        logger.info(f"🎯 Plan Mode activated for: {objective}")
        return plan_context
    
    def activate_act_mode(self, plan_context: Dict[str, Any]) -> Dict[str, Any]:
        """Ativa Act Mode workflow"""
        act_context = {
            "mode": "ACT_MODE",
            "plan_reference": plan_context.get("objective", "Unknown"),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "EXECUTION_ACTIVE",
            "completed_steps": [],
            "current_step": None
        }
        
        # Atualiza contexto ativo
        self.update_active_context(
            task_name=f"Executing: {plan_context.get('objective', 'Plan')}",
            phase="Act Mode Active",
            status="Execution Tracking in Progress",
            complexity=plan_context.get("complexity", 5)
        )
        
        logger.info(f"⚡ Act Mode activated for: {plan_context.get('objective', 'Plan')}")
        return act_context
    
    def enable_continuous_learning(self) -> bool:
        """Ativa aprendizado contínuo via Knowledge Graph"""
        try:
            # Importa o Knowledge Graph Manager
            import sys
            sys.path.append(str(self.memory_bank_path / "python"))
            from knowledge_graph_manager import KnowledgeGraphManager
            
            # Inicializa o sistema de aprendizado
            kg_manager = KnowledgeGraphManager()
            
            # Registra ativação do aprendizado contínuo
            learning_entry = f"""
## 🧠 Continuous Learning Activated - {datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M UTC")}

**Status**: ✅ Knowledge Graph Manager Active
**Features**: Pattern recognition, task optimization, context learning
**Integration**: Real-time memory updates enabled

---
"""
            
            with open(self.system_learning_path, 'a', encoding='utf-8') as f:
                f.write(learning_entry)
            
            logger.info("🧠 Continuous learning activated via Knowledge Graph Manager")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error activating continuous learning: {e}")
            return False

# Instância global para uso fácil
memory_updater = AutomaticMemoryUpdater()

def quick_update(task: str, status: str, complexity: int = 5):
    """Função de conveniência para updates rápidos"""
    return memory_updater.update_active_context(task, "Operational", status, complexity)

def quick_progress(milestone: str, percentage: int, description: str = ""):
    """Função de conveniência para logging de progresso"""
    return memory_updater.log_progress_update(milestone, description, percentage)

if __name__ == "__main__":
    # Teste básico
    print("🔄 Testing Automatic Memory Updater...")
    
    # Teste de atualização de contexto
    result = quick_update(
        task="Memory Bank Operational Test",
        status="Testing Automatic Updates",
        complexity=6
    )
    
    if result:
        print("✅ Automatic Memory Updater is working!")
    else:
        print("❌ Error in Automatic Memory Updater")
