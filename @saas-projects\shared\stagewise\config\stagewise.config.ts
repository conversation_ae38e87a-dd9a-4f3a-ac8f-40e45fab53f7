/**
 * 🎯 Stagewise Centralized Configuration
 * VIBECODE V1.0 - Unified Stagewise Setup for All Next.js Projects
 * 
 * Provides consistent Stagewise toolbar configuration across
 * aegiswallet, agendatrintae3, neonpro, and future projects.
 */

import { ReactPlugin } from '@stagewise-plugins/react';
import { detectEnvironment, isStagewiseEnabled } from './environment.config';

export interface StagewiseConfig {
  plugins: any[];
  enabled: boolean;
  settings: {
    position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    theme: 'light' | 'dark' | 'auto';
    hotkey: string;
    zIndex: number;
    showInProduction: boolean;
  };
  features: {
    elementSelection: boolean;
    commentSystem: boolean;
    aiIntegration: boolean;
    visualFeedback: boolean;
  };
  performance: {
    lazyLoad: boolean;
    debounceMs: number;
    maxElements: number;
  };
}

/**
 * Default Stagewise configuration following VIBECODE V1.0 standards
 */
export const defaultStagewiseConfig: StagewiseConfig = {
  plugins: [ReactPlugin],
  enabled: isStagewiseEnabled(),
  settings: {
    position: 'bottom-right',
    theme: 'auto',
    hotkey: 'cmd+shift+s',
    zIndex: 9999,
    showInProduction: false
  },
  features: {
    elementSelection: true,
    commentSystem: true,
    aiIntegration: true,
    visualFeedback: true
  },
  performance: {
    lazyLoad: true,
    debounceMs: 300,
    maxElements: 1000
  }
};

/**
 * Project-specific configuration overrides
 */
export const projectConfigs = {
  aegiswallet: {
    ...defaultStagewiseConfig,
    settings: {
      ...defaultStagewiseConfig.settings,
      theme: 'dark' as const, // Crypto theme preference
    }
  },
  agendatrintae3: {
    ...defaultStagewiseConfig,
    settings: {
      ...defaultStagewiseConfig.settings,
      theme: 'light' as const, // Medical theme preference
    }
  },
  neonpro: {
    ...defaultStagewiseConfig,
    settings: {
      ...defaultStagewiseConfig.settings,
      theme: 'auto' as const, // Aesthetic theme preference
    }
  }
};

/**
 * Get configuration for specific project
 */
export function getStagewiseConfig(projectName?: string): StagewiseConfig {
  const env = detectEnvironment();
  
  // Base configuration
  let config = defaultStagewiseConfig;
  
  // Apply project-specific overrides
  if (projectName && projectName in projectConfigs) {
    config = projectConfigs[projectName as keyof typeof projectConfigs];
  }
  
  // Environment-based overrides
  return {
    ...config,
    enabled: env.enableStagewise,
    settings: {
      ...config.settings,
      showInProduction: false // Always false for security
    }
  };
}

/**
 * Validate configuration for debugging
 */
export function validateStagewiseConfig(config: StagewiseConfig): boolean {
  const requiredPlugins = config.plugins.length > 0;
  const validPosition = ['top-left', 'top-right', 'bottom-left', 'bottom-right'].includes(config.settings.position);
  const validTheme = ['light', 'dark', 'auto'].includes(config.settings.theme);
  
  const isValid = requiredPlugins && validPosition && validTheme;
  
  if (!isValid && typeof window !== 'undefined' && config.enabled) {
    console.warn('⚠️ Invalid Stagewise configuration detected');
  }
  
  return isValid;
}
