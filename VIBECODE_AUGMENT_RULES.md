# VIBECODE RULES ADAPTADAS PARA AUGMENT VSCODE

**Versão**: 1.0.0 - <PERSON><PERSON><PERSON> das regras VIBECODE para Augment Code no VSCode
**Fonte**: E:\VIBECODE\.cursor\rules\master_rule.mdc
**Princípio Central**: "Aprimore, Não Prolifere" (≥85% reuso)

---

## 🛡️ PRINCÍPIOS FUNDAMENTAIS

### **Regra de Ouro**
```json
{
  "principle": "Aprimore, Não Prolifere (≥85% reuse)",
  "quality_threshold": 8,
  "confidence_minimum": 90,
  "always_verify": "Ler arquivo após escrever",
  "backup_strategy": "Preservar soluções existentes"
}
```

### **Padrões de Qualidade Obrigatórios**
- **Qualidade mínima**: ≥8/10 (NUNCA comprometer)
- **Completude**: 100% dos requisitos atendidos
- **Verificação**: Sempre validar operações de arquivo
- **Documentação**: Inline + externa completa
- **Reutilização**: ≥85% de código/configurações existentes

---

## 🔄 WORKFLOW OBRIGATÓRIO (7 PASSOS)

### **Processo Mandatório para Todas as Tarefas**

1. **ANALYZE** (Avaliação de Complexidade)
   - Avaliar complexidade: 1-10
   - Identificar tipo de tarefa e requisitos
   - Verificar indicadores multi-step
   - Avaliar necessidades de recursos

2. **STRATEGY** (Seleção de Ferramentas e Planejamento)
   - Selecionar ferramentas baseado na complexidade
   - Planejar abordagem de execução
   - Identificar necessidades de task management
   - Avaliar oportunidades de batch operations

3. **EXECUTE** (Implementação com Ferramentas Selecionadas)
   - Usar ferramentas apropriadas conforme complexidade
   - Aplicar protocolo de pesquisa se necessário
   - Executar com monitoramento de qualidade
   - Implementar batch operations quando possível

4. **REFLECT** (Avaliação de Qualidade)
   - Avaliar qualidade do output internamente
   - Verificar conformidade com requisitos
   - Medir nível de confiança

5. **REFINE** (Melhoria)
   - Se qualidade <8/10: melhorar imediatamente
   - Aplicar best practices
   - Otimizar abordagem da solução

6. **VALIDATE** (Verificação Final)
   - Confirmar qualidade ≥8/10 (OBRIGATÓRIO)
   - Verificar todos os requisitos atendidos
   - Checar conformidade com padrões

7. **LEARN** (Atualização de Conhecimento)
   - Atualizar base de conhecimento
   - Documentar insights e padrões
   - Armazenar abordagens bem-sucedidas

---

## 🔧 MATRIZ DE SELEÇÃO DE FERRAMENTAS

### **Roteamento por Complexidade**
```json
{
  "tool_selection": {
    "complexity_1_3": {
      "tools": ["codebase-retrieval", "str-replace-editor", "view"],
      "approach": "Execução direta",
      "task_management": "Opcional"
    },
    "complexity_4_6": {
      "tools": ["codebase-retrieval", "task_management", "web-search"],
      "approach": "Abordagem estruturada",
      "task_management": "Recomendado"
    },
    "complexity_7_10": {
      "tools": ["sequential-thinking", "task_management", "all_research_tools"],
      "approach": "Planejamento estratégico",
      "task_management": "Obrigatório"
    }
  }
}
```

### **Operações de Arquivo**
```json
{
  "file_operations": {
    "small_files": {
      "size": "≤200 linhas",
      "tool": "str-replace-editor",
      "verification": "Sempre ler após escrever"
    },
    "large_files": {
      "size": ">200 linhas",
      "tool": "str-replace-editor com chunks",
      "strategy": "Edições focadas e incrementais"
    }
  }
}
```

---

## 🔍 PROTOCOLO DE PESQUISA OBRIGATÓRIO

### **Ativação Automática**
Qualquer uma dessas keywords **AUTOMATICAMENTE** ativa o protocolo:
```
pesquisar, buscar, encontrar, documentação, tutorial, como fazer, exemplo,
guia, biblioteca, framework, API, implementação, configuração, integração,
best practices, configurar, usar, funciona, resolve, explica, entender
```

### **Sequência OBRIGATÓRIA**
```json
{
  "research_protocol": {
    "step_1": {
      "tool": "codebase-retrieval",
      "purpose": "Buscar informações na base de código atual",
      "requirement": "SEMPRE primeiro - OBRIGATÓRIO"
    },
    "step_2": {
      "tool": "web-search",
      "purpose": "Pesquisa web geral, informações atualizadas",
      "requirement": "SEMPRE segundo - OBRIGATÓRIO"
    },
    "step_3": {
      "tool": "tavily-search",
      "purpose": "Pesquisa avançada e fontes alternativas",
      "requirement": "SEMPRE terceiro - OBRIGATÓRIO"
    }
  }
}
```

### **Validação Obrigatória**
- **Síntese consolidada** de todos os resultados
- **Qualidade ≥8/10** na resposta final
- **Documentação** de todas as fontes consultadas
- **Verificação** de que todas as 3 ferramentas foram usadas

---

## 📋 TASK MANAGEMENT INTELIGENTE

### **Critérios de Ativação Automática**
- **Complexidade ≥3/10**: Ativa automaticamente task management
- **Keywords de planejamento**: planejar, organizar, estruturar, coordenar
- **Indicadores de complexidade**: arquitetura, sistema, integração, refatoração
- **Indicadores multi-step**: primeiro, segundo, depois, então

### **Roteamento por Complexidade**
```json
{
  "task_routing": {
    "simple_tasks": {
      "complexity": "1-4",
      "tools": ["native_augment_tasks"],
      "auto_complete": true
    },
    "medium_tasks": {
      "complexity": "5-7",
      "tools": ["task_management", "sequential_thinking"],
      "planning_required": false
    },
    "complex_tasks": {
      "complexity": "8-10",
      "tools": ["task_management", "sequential_thinking", "memory_system"],
      "planning_required": true
    }
  }
}
```

---

## 🚀 OTIMIZAÇÃO DE PERFORMANCE

### **Batch Operations (CRÍTICO)**
```json
{
  "efficiency_rules": {
    "principle": "SEMPRE consolidar operações similares múltiplas",
    "cost_reduction_target": "≥70% menos chamadas de API",
    "anti_patterns": [
      "multiple_sequential_reads - PROIBIDO",
      "repetitive_tool_calls - PROIBIDO",
      "individual_file_operations - PROIBIDO"
    ],
    "mandatory_practices": [
      "Avaliar se operações podem ser agrupadas",
      "Usar scripts abrangentes quando possível",
      "Monitorar redução de chamadas ≥70%"
    ]
  }
}
```

### **Estratégias de Otimização**
- **Consolidação**: Agrupar operações similares
- **Caching**: Reutilizar resultados de pesquisas
- **Batch Processing**: Processar múltiplos itens juntos
- **Early Exit**: Parar quando objetivo for atingido

---

## ✅ CRITÉRIOS DE SUCESSO

### **Padrões Obrigatórios**
- **Qualidade**: ≥8/10 (nunca comprometer)
- **Completude**: 100% dos requisitos
- **Performance**: <30s para tarefas complexas
- **Documentação**: Completa inline + externa
- **Conformidade**: Padrões VIBECODE 100%

### **Padrões de Comunicação**
- **Idioma**: Português (BR) para respostas
- **Formato**: Linguagem natural para usuários, JSON para interno
- **Contexto**: Sempre preservar dados completos
- **Rastreamento**: Incluir scores de qualidade

---

## 🚨 TRATAMENTO DE FALHAS

### **Protocolo de Recuperação**
```json
{
  "failure_handling": {
    "tool_fails": "Continuar com ferramentas alternativas + documentar limitação",
    "partial_success": "Usar resultados disponíveis + documentar limitações",
    "complete_failure": "Reportar erro + usar métodos alternativos"
  }
}
```

### **Regra de Continuidade**
Mesmo se uma ferramenta falhar, **SEMPRE** tentar as alternativas disponíveis.

---

## 📊 MÉTRICAS DE PERFORMANCE

### **Targets de Otimização**
- **Redução de chamadas**: ≥70% através de batch operations
- **Qualidade mantida**: ≥8/10 em 100% das operações
- **Reutilização de código**: ≥85% (Aprimore, Não Prolifere)
- **Tempo de resposta**: <30s para tarefas complexas

---

**"Aprimore, Não Prolifere - Uma Regra, Zero Redundância, Máxima Eficiência"**

**VIBECODE RULES FOR AUGMENT VSCODE - Sistema Ativo e Otimizado**
