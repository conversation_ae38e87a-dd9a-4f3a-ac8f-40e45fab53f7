import type { AvailableSlotsResponse } from "@/app/lib/types/appointments";
import { createClient } from "@/app/utils/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Check if user is authenticated
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const professionalId = searchParams.get("professional_id");
    const date = searchParams.get("date");

    if (!professionalId || !date) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Get clinic_id from user's profile
    const { data: profile } = await supabase
      .from("profiles")
      .select("clinic_id")
      .eq("id", user.id)
      .single();

    if (!profile?.clinic_id) {
      return NextResponse.json({ error: "Clinic not found" }, { status: 400 });
    }

    // Generate time slots for the day (8 AM to 6 PM, 15-minute intervals)
    const generateTimeSlots = (date: string) => {
      const slots = [];
      const baseDate = new Date(date + "T00:00:00.000Z");

      for (let hour = 8; hour < 18; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const slotStart = new Date(baseDate);
          slotStart.setUTCHours(hour, minute, 0, 0);

          const slotEnd = new Date(slotStart);
          slotEnd.setUTCMinutes(slotEnd.getUTCMinutes() + 15);

          slots.push({
            slot_start: slotStart.toISOString(),
            slot_end: slotEnd.toISOString(),
            is_available: true, // Will be updated based on existing appointments
            duration_minutes: 15,
          });
        }
      }
      return slots;
    };

    const timeSlots = generateTimeSlots(date);

    // Get existing appointments for the professional on the specified date
    const startOfDay = new Date(date + "T00:00:00.000Z");
    const endOfDay = new Date(date + "T23:59:59.999Z");

    const { data: existingAppointments, error: appointmentsError } =
      await supabase
        .from("appointments")
        .select("start_time, end_time")
        .eq("clinic_id", profile.clinic_id)
        .eq("professional_id", professionalId)
        .not("status", "eq", "cancelled")
        .gte("start_time", startOfDay.toISOString())
        .lte("start_time", endOfDay.toISOString());

    if (appointmentsError) {
      console.error("Error fetching appointments:", appointmentsError);
      return NextResponse.json(
        {
          success: false,
          data: [],
          error_message: appointmentsError.message,
        } as AvailableSlotsResponse,
        { status: 500 }
      );
    }

    // Mark slots as unavailable if they overlap with existing appointments
    const availableSlots = timeSlots.map((slot) => {
      const slotStart = new Date(slot.slot_start);
      const slotEnd = new Date(slot.slot_end);

      const hasConflict = existingAppointments?.some((appointment) => {
        const appointmentStart = new Date(appointment.start_time);
        const appointmentEnd = new Date(appointment.end_time);

        // Check if the slot overlaps with any existing appointment
        return (
          (slotStart >= appointmentStart && slotStart < appointmentEnd) ||
          (slotEnd > appointmentStart && slotEnd <= appointmentEnd) ||
          (slotStart <= appointmentStart && slotEnd >= appointmentEnd)
        );
      });

      return {
        ...slot,
        is_available: !hasConflict,
      };
    });

    const response: AvailableSlotsResponse = {
      success: true,
      data: availableSlots,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("API Error:", error);
    return NextResponse.json(
      {
        success: false,
        data: [],
        error_message: "Internal Server Error",
      } as AvailableSlotsResponse,
      { status: 500 }
    );
  }
}
