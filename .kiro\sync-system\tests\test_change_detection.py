"""
Tests for the change detection engine.
"""

import os
import tempfile
import unittest
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch, mock_open

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.change_detection import ChangeDetectionEngine
from models.sync_models import (
    FileChange, SyncAction, Conflict, ChangeType, ActionType, 
    Priority, ConflictType
)


class TestChangeDetectionEngine(unittest.TestCase):
    """Test cases for ChangeDetectionEngine."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'source_path': 'E:\\VIBECODE\\.cursor',
            'target_path': '.kiro',
            'file_types': ['.mdc', '.json', '.md'],
            'excluded_paths': ['temp', 'cache', 'logs']
        }
        self.engine = ChangeDetectionEngine(self.config)
        
        # Create sample file changes
        self.sample_changes = [
            FileChange(
                file_path='E:\\VIBECODE\\.cursor\\rules\\master-rule.mdc',
                change_type=ChangeType.MODIFIED,
                timestamp=datetime.now(),
                file_size=1024
            ),
            FileChange(
                file_path='E:\\VIBECODE\\.cursor\\mcp.json',
                change_type=ChangeType.MODIFIED,
                timestamp=datetime.now(),
                file_size=512
            ),
            FileChange(
                file_path='E:\\VIBECODE\\.cursor\\config\\settings.json',
                change_type=ChangeType.CREATED,
                timestamp=datetime.now(),
                file_size=256
            )
        ]
    
    def test_initialization(self):
        """Test engine initialization."""
        self.assertEqual(self.engine.source_path, 'E:\\VIBECODE\\.cursor')
        self.assertEqual(self.engine.target_path, '.kiro')
        self.assertEqual(self.engine.monitored_file_types, ['.mdc', '.json', '.md'])
        self.assertEqual(self.engine.excluded_paths, ['temp', 'cache', 'logs'])
    
    def test_analyze_changes_basic(self):
        """Test basic change analysis."""
        actions = self.engine.analyze_changes(self.sample_changes)
        
        # Should generate actions for all valid changes
        self.assertEqual(len(actions), 3)
        
        # Check action types
        action_types = [action.action_type for action in actions]
        self.assertIn(ActionType.UPDATE, action_types)
        self.assertIn(ActionType.CREATE, action_types)
    
    def test_analyze_changes_excluded_files(self):
        """Test that excluded files are filtered out."""
        excluded_changes = [
            FileChange(
                file_path='E:\\VIBECODE\\.cursor\\temp\\temp_file.mdc',
                change_type=ChangeType.MODIFIED,
                timestamp=datetime.now()
            ),
            FileChange(
                file_path='E:\\VIBECODE\\.cursor\\cache\\cache_file.json',
                change_type=ChangeType.CREATED,
                timestamp=datetime.now()
            )
        ]
        
        actions = self.engine.analyze_changes(excluded_changes)
        
        # Should not generate actions for excluded files
        self.assertEqual(len(actions), 0)
    
    def test_analyze_changes_unsupported_file_types(self):
        """Test that unsupported file types are filtered out."""
        unsupported_changes = [
            FileChange(
                file_path='E:\\VIBECODE\\.cursor\\rules\\image.png',
                change_type=ChangeType.CREATED,
                timestamp=datetime.now()
            ),
            FileChange(
                file_path='E:\\VIBECODE\\.cursor\\rules\\binary.exe',
                change_type=ChangeType.MODIFIED,
                timestamp=datetime.now()
            )
        ]
        
        actions = self.engine.analyze_changes(unsupported_changes)
        
        # Should not generate actions for unsupported file types
        self.assertEqual(len(actions), 0)
    
    def test_get_target_file_path_rules(self):
        """Test target path mapping for rule files."""
        source_path = 'E:\\VIBECODE\\.cursor\\rules\\master-rule.mdc'
        target_path = self.engine._get_target_file_path(source_path)
        
        # Should map to steering directory and convert extension
        expected = str(Path('.kiro') / 'steering' / 'master-rule.md')
        self.assertEqual(target_path, expected)
    
    def test_get_target_file_path_config(self):
        """Test target path mapping for config files."""
        source_path = 'E:\\VIBECODE\\.cursor\\config\\settings.json'
        target_path = self.engine._get_target_file_path(source_path)
        
        # Should map to settings directory
        expected = str(Path('.kiro') / 'settings' / 'settings.json')
        self.assertEqual(target_path, expected)
    
    def test_get_target_file_path_mcp(self):
        """Test target path mapping for MCP config."""
        source_path = 'E:\\VIBECODE\\.cursor\\mcp.json'
        target_path = self.engine._get_target_file_path(source_path)
        
        # Should map to settings/mcp.json
        expected = str(Path('.kiro') / 'settings' / 'mcp.json')
        self.assertEqual(target_path, expected)
    
    def test_get_action_type_created(self):
        """Test action type determination for created files."""
        action_type = self.engine._get_action_type(ChangeType.CREATED, 'nonexistent.md')
        self.assertEqual(action_type, ActionType.CREATE)
    
    def test_get_action_type_modified_existing(self):
        """Test action type determination for modified existing files."""
        with patch('os.path.exists', return_value=True):
            action_type = self.engine._get_action_type(ChangeType.MODIFIED, 'existing.md')
            self.assertEqual(action_type, ActionType.UPDATE)
    
    def test_get_action_type_modified_nonexistent(self):
        """Test action type determination for modified non-existent files."""
        with patch('os.path.exists', return_value=False):
            action_type = self.engine._get_action_type(ChangeType.MODIFIED, 'nonexistent.md')
            self.assertEqual(action_type, ActionType.CREATE)
    
    def test_get_action_type_deleted(self):
        """Test action type determination for deleted files."""
        action_type = self.engine._get_action_type(ChangeType.DELETED, 'any.md')
        self.assertEqual(action_type, ActionType.DELETE)
    
    def test_get_file_priority_critical(self):
        """Test priority assignment for critical files."""
        priority = self.engine._get_file_priority('E:\\VIBECODE\\.cursor\\mcp.json')
        self.assertEqual(priority, Priority.CRITICAL)
        
        priority = self.engine._get_file_priority('E:\\VIBECODE\\.cursor\\master-config.json')
        self.assertEqual(priority, Priority.CRITICAL)
    
    def test_get_file_priority_high(self):
        """Test priority assignment for high priority files."""
        priority = self.engine._get_file_priority('E:\\VIBECODE\\.cursor\\rules\\rule.mdc')
        self.assertEqual(priority, Priority.HIGH)
        
        priority = self.engine._get_file_priority('E:\\VIBECODE\\.cursor\\unified-config.json')
        self.assertEqual(priority, Priority.HIGH)
    
    def test_get_file_priority_medium(self):
        """Test priority assignment for medium priority files."""
        priority = self.engine._get_file_priority('E:\\VIBECODE\\.cursor\\rules\\rule.md')
        self.assertEqual(priority, Priority.MEDIUM)
    
    def test_get_file_priority_default(self):
        """Test priority assignment for default files."""
        priority = self.engine._get_file_priority('E:\\VIBECODE\\.cursor\\other\\file.txt')
        self.assertEqual(priority, Priority.LOW)
    
    def test_requires_adaptation_mdc_files(self):
        """Test adaptation requirement for .mdc files."""
        requires = self.engine._requires_adaptation('E:\\VIBECODE\\.cursor\\rules\\rule.mdc')
        self.assertTrue(requires)
    
    def test_requires_adaptation_mcp_config(self):
        """Test adaptation requirement for MCP config."""
        requires = self.engine._requires_adaptation('E:\\VIBECODE\\.cursor\\mcp.json')
        self.assertTrue(requires)
    
    def test_requires_adaptation_config_files(self):
        """Test adaptation requirement for config files."""
        requires = self.engine._requires_adaptation('E:\\VIBECODE\\.cursor\\config\\settings.json')
        self.assertTrue(requires)
    
    def test_requires_adaptation_regular_files(self):
        """Test adaptation requirement for regular files."""
        requires = self.engine._requires_adaptation('E:\\VIBECODE\\.cursor\\other\\file.txt')
        self.assertFalse(requires)
    
    def test_requires_backup_update_action(self):
        """Test backup requirement for update actions."""
        requires = self.engine._requires_backup('any_file.md', ActionType.UPDATE)
        self.assertTrue(requires)
    
    def test_requires_backup_delete_action(self):
        """Test backup requirement for delete actions."""
        requires = self.engine._requires_backup('any_file.md', ActionType.DELETE)
        self.assertTrue(requires)
    
    def test_requires_backup_critical_create(self):
        """Test backup requirement for critical file creation."""
        requires = self.engine._requires_backup('mcp.json', ActionType.CREATE)
        self.assertTrue(requires)
    
    def test_requires_backup_regular_create(self):
        """Test backup requirement for regular file creation."""
        requires = self.engine._requires_backup('regular.txt', ActionType.CREATE)
        self.assertFalse(requires)
    
    def test_prioritize_actions(self):
        """Test action prioritization."""
        actions = [
            SyncAction(
                action_type=ActionType.UPDATE,
                source_file='regular.md',
                target_file='.kiro/regular.md',
                priority=Priority.LOW
            ),
            SyncAction(
                action_type=ActionType.UPDATE,
                source_file='mcp.json',
                target_file='.kiro/settings/mcp.json',
                priority=Priority.CRITICAL
            ),
            SyncAction(
                action_type=ActionType.UPDATE,
                source_file='rule.mdc',
                target_file='.kiro/steering/rule.md',
                priority=Priority.HIGH
            )
        ]
        
        prioritized = self.engine.prioritize_actions(actions)
        
        # Should be ordered by priority (highest first)
        self.assertEqual(prioritized[0].priority, Priority.CRITICAL)
        self.assertEqual(prioritized[1].priority, Priority.HIGH)
        self.assertEqual(prioritized[2].priority, Priority.LOW)
    
    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open)
    def test_detect_conflicts_no_conflict(self, mock_file, mock_exists):
        """Test conflict detection when no conflicts exist."""
        mock_exists.return_value = True
        mock_file.return_value.read.return_value = "same content"
        
        actions = [
            SyncAction(
                action_type=ActionType.UPDATE,
                source_file='source.md',
                target_file='target.md',
                priority=Priority.MEDIUM
            )
        ]
        
        conflicts = self.engine.detect_conflicts(actions)
        self.assertEqual(len(conflicts), 0)
    
    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open)
    def test_detect_conflicts_content_conflict(self, mock_file, mock_exists):
        """Test conflict detection for content conflicts."""
        mock_exists.return_value = True
        
        # Mock different content for source and target
        def mock_read_side_effect():
            calls = mock_file.return_value.read.call_count
            if calls == 1:
                return "target content"
            else:
                return "source content"
        
        mock_file.return_value.read.side_effect = mock_read_side_effect
        
        actions = [
            SyncAction(
                action_type=ActionType.UPDATE,
                source_file='source.md',
                target_file='target.md',
                priority=Priority.MEDIUM
            )
        ]
        
        conflicts = self.engine.detect_conflicts(actions)
        self.assertEqual(len(conflicts), 1)
        self.assertEqual(conflicts[0].conflict_type, ConflictType.CONTENT_CONFLICT)
    
    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open)
    def test_detect_conflicts_customization_conflict(self, mock_file, mock_exists):
        """Test conflict detection for customization conflicts."""
        mock_exists.return_value = True
        
        # Mock target content with Kiro customizations
        def mock_read_side_effect():
            calls = mock_file.return_value.read.call_count
            if calls == 1:
                return "# KIRO-SPECIFIC customization\nsome content"
            else:
                return "different source content"
        
        mock_file.return_value.read.side_effect = mock_read_side_effect
        
        actions = [
            SyncAction(
                action_type=ActionType.UPDATE,
                source_file='source.md',
                target_file='target.md',
                priority=Priority.MEDIUM
            )
        ]
        
        conflicts = self.engine.detect_conflicts(actions)
        self.assertEqual(len(conflicts), 1)
        self.assertEqual(conflicts[0].conflict_type, ConflictType.CUSTOMIZATION_CONFLICT)
    
    def test_has_kiro_customizations_positive(self):
        """Test detection of Kiro customizations."""
        content_with_kiro = """
        # Some content
        # KIRO-SPECIFIC optimization
        more content
        """
        
        has_customizations = self.engine._has_kiro_customizations(content_with_kiro)
        self.assertTrue(has_customizations)
    
    def test_has_kiro_customizations_negative(self):
        """Test detection when no Kiro customizations exist."""
        content_without_kiro = """
        # Some regular content
        normal configuration
        """
        
        has_customizations = self.engine._has_kiro_customizations(content_without_kiro)
        self.assertFalse(has_customizations)
    
    def test_has_path_differences_positive(self):
        """Test detection of path differences."""
        source_content = 'path: "E:\\VIBECODE\\.cursor\\rules"'
        target_content = 'path: ".kiro/steering"'
        
        has_differences = self.engine._has_path_differences(source_content, target_content)
        self.assertTrue(has_differences)
    
    def test_has_path_differences_negative(self):
        """Test detection when no path differences exist."""
        source_content = 'path: ".kiro/steering"'
        target_content = 'path: ".kiro/steering"'
        
        has_differences = self.engine._has_path_differences(source_content, target_content)
        self.assertFalse(has_differences)
    
    def test_requires_manual_review(self):
        """Test manual review requirement determination."""
        # Customization conflicts require manual review
        requires = self.engine._requires_manual_review(ConflictType.CUSTOMIZATION_CONFLICT)
        self.assertTrue(requires)
        
        # Permission conflicts require manual review
        requires = self.engine._requires_manual_review(ConflictType.PERMISSION_CONFLICT)
        self.assertTrue(requires)
        
        # Content conflicts don't require manual review
        requires = self.engine._requires_manual_review(ConflictType.CONTENT_CONFLICT)
        self.assertFalse(requires)
    
    def test_get_change_statistics(self):
        """Test change statistics generation."""
        stats = self.engine.get_change_statistics(self.sample_changes)
        
        # Check basic statistics
        self.assertEqual(stats['total_changes'], 3)
        self.assertIn('by_type', stats)
        self.assertIn('by_file_type', stats)
        self.assertIn('by_priority', stats)
        self.assertIn('requires_adaptation', stats)
        self.assertIn('requires_backup', stats)
        
        # Check specific counts
        self.assertEqual(stats['by_type']['modified'], 2)
        self.assertEqual(stats['by_type']['created'], 1)
    
    def test_convert_change_to_action_complete(self):
        """Test complete change to action conversion."""
        change = FileChange(
            file_path='E:\\VIBECODE\\.cursor\\rules\\test.mdc',
            change_type=ChangeType.MODIFIED,
            timestamp=datetime.now(),
            file_size=1024
        )
        
        action = self.engine._convert_change_to_action(change)
        
        self.assertIsNotNone(action)
        self.assertEqual(action.action_type, ActionType.UPDATE)
        self.assertEqual(action.source_file, change.file_path)
        self.assertTrue(action.target_file.endswith('test.md'))
        self.assertEqual(action.priority, Priority.HIGH)
        self.assertTrue(action.requires_adaptation)
        self.assertTrue(action.backup_required)
        self.assertIn('timestamp', action.metadata)
        self.assertIn('file_size', action.metadata)


class TestChangeDetectionEngineIntegration(unittest.TestCase):
    """Integration tests for ChangeDetectionEngine."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'source_path': os.path.join(self.temp_dir, 'vibecode'),
            'target_path': os.path.join(self.temp_dir, 'kiro'),
            'file_types': ['.mdc', '.json', '.md'],
            'excluded_paths': ['temp']
        }
        self.engine = ChangeDetectionEngine(self.config)
        
        # Create test directory structure
        os.makedirs(os.path.join(self.config['source_path'], 'rules'), exist_ok=True)
        os.makedirs(os.path.join(self.config['source_path'], 'config'), exist_ok=True)
        os.makedirs(os.path.join(self.config['target_path'], 'steering'), exist_ok=True)
        os.makedirs(os.path.join(self.config['target_path'], 'settings'), exist_ok=True)
    
    def tearDown(self):
        """Clean up integration test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow."""
        # Create test files
        rule_file = os.path.join(self.config['source_path'], 'rules', 'test.mdc')
        config_file = os.path.join(self.config['source_path'], 'mcp.json')
        
        with open(rule_file, 'w') as f:
            f.write("# Test rule content")
        
        with open(config_file, 'w') as f:
            f.write('{"test": "config"}')
        
        # Create file changes
        changes = [
            FileChange(
                file_path=rule_file,
                change_type=ChangeType.CREATED,
                timestamp=datetime.now(),
                file_size=os.path.getsize(rule_file)
            ),
            FileChange(
                file_path=config_file,
                change_type=ChangeType.MODIFIED,
                timestamp=datetime.now(),
                file_size=os.path.getsize(config_file)
            )
        ]
        
        # Analyze changes
        actions = self.engine.analyze_changes(changes)
        self.assertEqual(len(actions), 2)
        
        # Detect conflicts (should be none for new files)
        conflicts = self.engine.detect_conflicts(actions)
        self.assertEqual(len(conflicts), 0)
        
        # Prioritize actions
        prioritized_actions = self.engine.prioritize_actions(actions)
        self.assertEqual(len(prioritized_actions), 2)
        
        # Check that MCP config has higher priority
        mcp_action = next(a for a in prioritized_actions if 'mcp.json' in a.source_file)
        rule_action = next(a for a in prioritized_actions if 'test.mdc' in a.source_file)
        
        self.assertEqual(mcp_action.priority, Priority.CRITICAL)
        self.assertEqual(rule_action.priority, Priority.HIGH)
        
        # MCP action should come first due to higher priority
        self.assertEqual(prioritized_actions[0], mcp_action)


if __name__ == '__main__':
    unittest.main()