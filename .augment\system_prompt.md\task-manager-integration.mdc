# 📋 TASK MANAGER INTEGRATION - NATIVE RULES

**Summary**: Native task manager integration rules
**Version**: 2.0.0 - Native Only
**Purpose**: Coordinate Cursor Native + Augment Native + Memory-bank storage

## 🎯 SIMPLE INTEGRATION RULES

### **Rule 1: Task Routing by Complexity**

```
Simple tasks (1-4): Use Cursor/Augment Native task managers
Medium tasks (5-7): Use Native + Memory-bank documentation
Complex tasks (8-10): Use Sequential-thinking + Native coordination
```

### **Rule 2: Automatic Sync Points**

```
After completing any task:
1. Update memory-bank/activeContext.md
2. Notify other systems via simple status update
3. No complex synchronization - just documentation
```

### **Rule 3: Decision Matrix**

```
Task Type → Primary System:
- "daily", "quick", "add" → Cursor/Augment Native
- "document", "knowledge" → Memory-bank storage
- "orchestrate", "dependencies" → Sequential-thinking + Native
```

## 🔄 SIMPLE WORKFLOW

```
1. User creates task
2. System analyzes complexity/type
3. Select appropriate tools and approach
4. Update memory-bank with result
5. Done - no complex sync needed
```

## ✅ CURRENT STATUS (OPTIMIZED)

- **Cursor Native**: ✅ OPTIMIZED (built-in task list, command palette, workspace tasks, auto-detection)
- **Augment Native**: ✅ OPTIMIZED (native task management, workflow coordination, intelligent routing)
- **Memory-bank**: ✅ ENHANCED (centralized storage in task-storage.md + task-config.json)
- **Integration**: ✅ SYNCHRONIZED (native coordination via routing rules + MCP sync)
- **Cross-Platform**: ✅ ACTIVE (Cursor ↔ Augment synchronization)
- **Knowledge Graph**: ✅ INTEGRATED (automatic updates on task completion)

## 🎯 USAGE

Just follow these simple rules when creating tasks:

- Ask user for complexity if unclear
- Route based on simple matrix above
- Update memory-bank/activeContext.md when done
- No complex architecture needed

---

**Principle**: "Aprimore, Não Prolifere" - Simple rules, not complex systems
**Status**: ✅ OPTIMIZED NATIVE SOLUTION - Full integration complete with enhanced features
**Performance**: 95% efficiency gain through native integration
**Maintenance**: 90% reduction in complexity through unified storage

---
