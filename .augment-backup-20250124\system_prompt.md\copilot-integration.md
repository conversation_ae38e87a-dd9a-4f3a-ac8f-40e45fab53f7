# GitHub Copilot Agent Rules - VIBECODE Integration

## Core Principles
- Quality Threshold: 8/10
- Confidence Minimum: 90%
- Code Reuse Target: ≥85%
- System Status: CONSOLIDATED

## Architectural Requirements

### Knowledge Integration
- Consult existing patterns before modifications
- Maintain centralized rule sources
- Track all changes with proper documentation
- Follow established cognitive patterns

### Code Standards
1. Context-First Approach
   - Full system understanding before modifications
   - Pattern-based decision making
   - Edge case identification

2. Quality Gates
   - Modular implementation
   - Testable components
   - Clear documentation
   - Architecture-focused solutions

3. File Operations
   - Use appropriate tools based on file size
   - Maintain sync between .cursor and .augment
   - Validate changes across configurations

### Operational Guidelines
1. Before Any Change:
   - Validate against existing patterns
   - Check for reusable components
   - Verify architectural alignment

2. During Implementation:
   - Follow modular design principles
   - Document decisions and rationale
   - Maintain sync between configurations

3. After Changes:
   - Verify quality metrics
   - Ensure cross-reference integrity
   - Update related documentation

## Sync Requirements
- Environment configurations
- MCP settings
- System prompts
- Configuration files

## Quality Metrics
- Code Coverage: ≥90%
- Documentation: Required for all changes
- Pattern Compliance: Mandatory
- Sync Validation: Required post-change
