"use client";

// =============================================
// NeonPro Conflict Prevention Hook
// Story 1.2: Real-time slot validation
// =============================================

import type {
  AlternativeSlot,
  AppointmentConflict,
  SlotValidationRequest,
  SlotValidationResponse,
} from "@/app/lib/types/conflict-prevention";
import { useCallback, useRef, useState } from "react";

interface UseConflictPreventionProps {
  debounceMs?: number; // Debounce validation calls
  enableRealTime?: boolean; // Enable real-time validation
}

interface UseConflictPreventionReturn {
  // State
  isValidating: boolean;
  lastValidation: SlotValidationResponse | null;
  conflicts: AppointmentConflict[];
  warnings: AppointmentConflict[];
  alternativeSlots: AlternativeSlot[];
  isAvailable: boolean;

  // Actions
  validateSlot: (
    request: SlotValidationRequest
  ) => Promise<SlotValidationResponse>;
  clearValidation: () => void;

  // Utilities
  hasErrors: boolean;
  hasWarnings: boolean;
  getConflictsByType: (type: string) => AppointmentConflict[];
  getSuggestedSlots: (limit?: number) => AlternativeSlot[];
}

export function useConflictPrevention({
  debounceMs = 300,
  enableRealTime = true,
}: UseConflictPreventionProps = {}): UseConflictPreventionReturn {
  // State management
  const [isValidating, setIsValidating] = useState(false);
  const [lastValidation, setLastValidation] =
    useState<SlotValidationResponse | null>(null);
  const [conflicts, setConflicts] = useState<AppointmentConflict[]>([]);
  const [warnings, setWarnings] = useState<AppointmentConflict[]>([]);
  const [alternativeSlots, setAlternativeSlots] = useState<AlternativeSlot[]>(
    []
  );
  const [isAvailable, setIsAvailable] = useState(false);

  // Refs for debouncing and request management
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentRequestRef = useRef<string>("");

  // Generate request key for deduplication
  const generateRequestKey = (request: SlotValidationRequest): string => {
    return `${request.professional_id}-${request.service_type_id}-${request.start_time}-${request.end_time}`;
  };

  // Main validation function
  const validateSlot = useCallback(
    async (request: SlotValidationRequest): Promise<SlotValidationResponse> => {
      const requestKey = generateRequestKey(request);

      // Prevent duplicate requests
      if (currentRequestRef.current === requestKey && isValidating) {
        return (
          lastValidation || {
            success: false,
            available: false,
            conflicts: [],
            warnings: [],
            alternative_slots: [],
            validation_details: {
              appointment_date: "",
              appointment_time: "",
              day_of_week: 0,
              duration_minutes: 0,
            },
          }
        );
      }

      // Clear previous debounce
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Set up debounced validation
      return new Promise((resolve, reject) => {
        debounceTimeoutRef.current = setTimeout(
          async () => {
            try {
              setIsValidating(true);
              currentRequestRef.current = requestKey;

              // Make API call
              const response = await fetch("/api/appointments/validate-slot", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify(request),
              });

              if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.details || `HTTP ${response.status}`);
              }

              const validationResult: SlotValidationResponse =
                await response.json();

              // Update state
              setLastValidation(validationResult);
              setConflicts(validationResult.conflicts || []);
              setWarnings(validationResult.warnings || []);
              setAlternativeSlots(validationResult.alternative_slots || []);
              setIsAvailable(validationResult.available || false);

              resolve(validationResult);
            } catch (error) {
              console.error("Slot validation error:", error);

              // Reset state on error
              const errorResponse: SlotValidationResponse = {
                success: false,
                available: false,
                conflicts: [
                  {
                    type: "APPOINTMENT_OVERLAP", // Generic type for errors
                    message: `Validation failed: ${
                      error instanceof Error ? error.message : "Unknown error"
                    }`,
                    severity: "error",
                  },
                ],
                warnings: [],
                alternative_slots: [],
                validation_details: {
                  appointment_date: new Date(request.start_time)
                    .toISOString()
                    .split("T")[0],
                  appointment_time: new Date(request.start_time)
                    .toTimeString()
                    .split(" ")[0],
                  day_of_week: new Date(request.start_time).getDay(),
                  duration_minutes: Math.round(
                    (new Date(request.end_time).getTime() -
                      new Date(request.start_time).getTime()) /
                      60000
                  ),
                },
              };

              setLastValidation(errorResponse);
              setConflicts(errorResponse.conflicts);
              setWarnings([]);
              setAlternativeSlots([]);
              setIsAvailable(false);

              reject(error);
            } finally {
              setIsValidating(false);
              currentRequestRef.current = "";
            }
          },
          enableRealTime ? debounceMs : 0
        );
      });
    },
    [debounceMs, enableRealTime, isValidating, lastValidation]
  );

  // Clear validation state
  const clearValidation = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    setIsValidating(false);
    setLastValidation(null);
    setConflicts([]);
    setWarnings([]);
    setAlternativeSlots([]);
    setIsAvailable(false);
    currentRequestRef.current = "";
  }, []);

  // Utility functions
  const hasErrors = conflicts.some((c) => c.severity === "error");
  const hasWarnings =
    warnings.length > 0 || conflicts.some((c) => c.severity === "warning");

  const getConflictsByType = useCallback(
    (type: string): AppointmentConflict[] => {
      return conflicts.filter((conflict) => conflict.type === type);
    },
    [conflicts]
  );

  const getSuggestedSlots = useCallback(
    (limit: number = 3): AlternativeSlot[] => {
      return alternativeSlots
        .filter((slot) => slot.available)
        .sort((a, b) => (b.score || 0) - (a.score || 0))
        .slice(0, limit);
    },
    [alternativeSlots]
  );

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, []);

  // Auto-cleanup effect would go here if this was a full component

  return {
    // State
    isValidating,
    lastValidation,
    conflicts,
    warnings,
    alternativeSlots,
    isAvailable,

    // Actions
    validateSlot,
    clearValidation,

    // Utilities
    hasErrors,
    hasWarnings,
    getConflictsByType,
    getSuggestedSlots,
  };
}

// Utility hook for checking availability without full conflict details
export function useQuickAvailabilityCheck() {
  const [isChecking, setIsChecking] = useState(false);

  const checkAvailability = useCallback(
    async (
      professionalId: string,
      serviceTypeId: string,
      startTime: string,
      endTime: string,
      excludeAppointmentId?: string
    ): Promise<boolean> => {
      try {
        setIsChecking(true);

        const params = new URLSearchParams({
          professional_id: professionalId,
          service_type_id: serviceTypeId,
          start_time: startTime,
          end_time: endTime,
          ...(excludeAppointmentId && {
            exclude_appointment_id: excludeAppointmentId,
          }),
        });

        const response = await fetch(
          `/api/appointments/validate-slot?${params}`
        );

        if (!response.ok) {
          return false;
        }

        const result = await response.json();
        return result.available || false;
      } catch (error) {
        console.error("Quick availability check error:", error);
        return false;
      } finally {
        setIsChecking(false);
      }
    },
    []
  );

  return {
    isChecking,
    checkAvailability,
  };
}
