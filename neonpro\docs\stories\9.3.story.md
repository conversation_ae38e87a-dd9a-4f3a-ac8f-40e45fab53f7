# Story 9.3: Gestão de Documentos e Anexos

## Story Overview

**Como** profissional de saúde e administrativo da clínica  
**Eu quero** sistema completo de gestão de documentos médicos com upload seguro e versionamento  
**Para que** eu possa armazenar, organizar e compartilhar documentos médicos com total segurança e compliance

### Story Details

- **Epic**: Epic 9 - Cadastro Pacientes & Prontuário
- **Story Points**: 10
- **Priority**: P0 (Critical)
- **Theme**: Document Management & File Security
- **Dependencies**: Story 9.1, 9.2 (Patient & Medical Records)

### Acceptance Criteria

#### AC1: Upload Seguro de Documentos Médicos

- [ ] **GIVEN** usuário com permissão de upload
- [ ] **WHEN** faz upload de documento médico
- [ ] **THEN** valida tipo de arquivo e tamanho máximo (10MB)
- [ ] **AND** criptografa arquivo automaticamente antes do armazenamento
- [ ] **AND** gera thumbnail para imagens e preview para PDFs
- [ ] **AND** extrai metadados EXIF e indexa conteúdo OCR

#### AC2: Versionamento Automático e Controle de Alterações

- [ ] **GIVEN** documento existente sendo atualizado
- [ ] **WHEN** nova versão é enviada
- [ ] **THEN** mantém versões anteriores com timestamp
- [ ] **AND** mostra diff visual entre versões
- [ ] **AND** permite restore de versões anteriores
- [ ] **AND** registra quem fez cada alteração

#### AC3: Compartilhamento Controlado e Seguro

- [ ] **GIVEN** documento médico armazenado
- [ ] **WHEN** profissional compartilha documento
- [ ] **THEN** gera link seguro com expiração configurável
- [ ] **AND** controla acesso por perfil (médico, paciente, administrativo)
- [ ] **AND** permite compartilhamento via email com criptografia
- [ ] **AND** registra log de todos os acessos ao documento

#### AC4: OCR e Digitalização Automática

- [ ] **GIVEN** upload de documento digitalizado ou foto
- [ ] **WHEN** sistema detecta conteúdo de texto
- [ ] **THEN** executa OCR automaticamente
- [ ] **AND** indexa texto extraído para busca
- [ ] **AND** identifica automaticamente tipo de documento (RG, CPF, exame)
- [ ] **AND** extrai dados estruturados relevantes

#### AC5: Archive Management e Retenção

- [ ] **GIVEN** documentos médicos com diferentes tipos
- [ ] **WHEN** aplica políticas de retenção
- [ ] **THEN** arquiva automaticamente documentos antigos
- [ ] **AND** mantém documentos médicos por 20 anos (regulamentação)
- [ ] **AND** permite busca em arquivos mortos
- [ ] **AND** notifica antes da exclusão definitiva

#### AC6: Integração com Prontuário e Assinatura Digital

- [ ] **GIVEN** documento médico relevante
- [ ] **WHEN** associa ao prontuário do paciente
- [ ] **THEN** vincula automaticamente ao histórico médico
- [ ] **AND** permite assinatura digital de documentos
- [ ] **AND** valida integridade do documento assinado
- [ ] **AND** gera certificado de autenticidade

### Technical Requirements

#### Document Management System

```typescript
// Document Management Interface
interface DocumentoMedico {
  id: string
  prontuarioId: string
  pacienteId: string
  
  // Metadados do Arquivo
  nomeOriginal: string
  nomeArmazenamento: string
  tipoArquivo: string
  tamanhoBytes: number
  hashSHA256: string
  
  // Classificação
  categoria: DocumentoCategoria
  subcategoria?: string
  tags: string[]
  descricao: string
  
  // Segurança e Acesso
  nivelAcesso: NivelAcesso
  criptografado: boolean
  chaveEncryptacao: string
  
  // Versionamento
  versao: number
  versaoAnterior?: string
  motivoAlteracao?: string
  
  // OCR e Conteúdo
  textoExtraido?: string
  metadados: DocumentoMetadados
  dadosEstruturados?: any
  
  // Armazenamento
  storagePath: string
  thumbnailPath?: string
  previewPath?: string
  
  // Assinatura Digital
  assinaturaDigital?: AssinaturaDigital
  certificadoAutenticidade?: string
  
  // Auditoria
  uploadedBy: string
  createdAt: Date
  updatedAt: Date
  acessos: DocumentoAcesso[]
}

// Categorias de Documentos
type DocumentoCategoria = 
  | 'identidade' 
  | 'exame_laboratorial'
  | 'exame_imagem'
  | 'receita_medica'
  | 'atestado'
  | 'foto_procedimento'
  | 'consentimento'
  | 'contrato'
  | 'comprovante_pagamento'
  | 'outros'

// Níveis de Acesso
type NivelAcesso = 
  | 'publico'          // Visível para todos
  | 'paciente'         // Paciente pode acessar
  | 'equipe_medica'    // Apenas equipe médica
  | 'profissional'     // Apenas profissional responsável
  | 'administracao'    // Apenas administração
  | 'restrito'         // Acesso especial requerido

// Metadados do Documento
interface DocumentoMetadados {
  // Metadados de Arquivo
  dimensoes?: { width: number; height: number }
  resolucao?: number
  dadosExif?: any
  
  // Metadados Médicos
  dataExame?: Date
  profissionalSolicitante?: string
  laboratório?: string
  resultadosRelevantes?: string[]
  
  // Metadados de Processamento
  ocrProcessado: boolean
  thumbnailGerado: boolean
  previewGerado: boolean
  validadoMedicamente: boolean
  
  // Conformidade
  lgpdCompliant: boolean
  retencaoAte?: Date
  motivoRetencao: string
}

// Compartilhamento Seguro
interface CompartilhamentoSeguro {
  id: string
  documentoId: string
  
  // Configurações de Acesso
  tokenAcesso: string
  linkSeguro: string
  expiracaoEm: Date
  maxAcessos?: number
  acessosRealizados: number
  
  // Destinatários
  destinatarios: {
    email?: string
    userId?: string
    tipoUsuario: 'paciente' | 'profissional' | 'externo'
  }[]
  
  // Configurações de Segurança
  requerSenha: boolean
  senha?: string
  permitirDownload: boolean
  permitirCompartilhar: boolean
  marca_dagua: boolean
  
  // Auditoria
  criadoPor: string
  criadoEm: Date
  acessos: CompartilhamentoAcesso[]
}

// Sistema de Versionamento
interface VersaoDocumento {
  id: string
  documentoId: string
  versaoNumero: number
  
  // Dados da Versão
  hashArquivo: string
  tamanhoBytes: number
  storagePath: string
  
  // Contexto da Mudança
  motivoAlteracao: string
  alteradoPor: string
  alteradoEm: Date
  
  // Comparação
  diferencas?: DocumentoDiff
  tipoAlteracao: 'upload' | 'edicao' | 'correcao' | 'atualizacao'
  
  // Status
  ativa: boolean
  arquivada: boolean
}
```

#### Database Schema for Document Management

```sql
-- Documentos Médicos Principal
CREATE TABLE documentos_medicos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  prontuario_id UUID NOT NULL REFERENCES prontuarios_eletronicos(id) ON DELETE CASCADE,
  paciente_id UUID NOT NULL REFERENCES pacientes(id) ON DELETE CASCADE,
  historico_medico_id UUID REFERENCES historico_medico(id),
  
  -- Metadados do Arquivo
  nome_original VARCHAR(255) NOT NULL,
  nome_armazenamento VARCHAR(255) NOT NULL,
  tipo_arquivo VARCHAR(50) NOT NULL,
  tamanho_bytes BIGINT NOT NULL,
  hash_sha256 VARCHAR(64) NOT NULL,
  
  -- Classificação e Organização
  categoria documento_categoria_type NOT NULL,
  subcategoria VARCHAR(100),
  tags TEXT[],
  descricao TEXT,
  
  -- Segurança e Acesso
  nivel_acesso nivel_acesso_type NOT NULL DEFAULT 'equipe_medica',
  criptografado BOOLEAN DEFAULT TRUE,
  chave_encryptacao VARCHAR(255),
  
  -- Versionamento
  versao INTEGER DEFAULT 1,
  versao_anterior UUID REFERENCES documentos_medicos(id),
  motivo_alteracao TEXT,
  
  -- OCR e Processamento
  texto_extraido TEXT,
  ocr_processado BOOLEAN DEFAULT FALSE,
  ocr_confidence NUMERIC(5,2),
  dados_estruturados JSONB,
  
  -- Armazenamento
  storage_path TEXT NOT NULL,
  storage_bucket VARCHAR(100) NOT NULL,
  thumbnail_path TEXT,
  preview_path TEXT,
  
  -- Metadados Médicos
  data_exame DATE,
  profissional_solicitante UUID REFERENCES profissionais(id),
  laboratorio VARCHAR(255),
  resultados_relevantes TEXT[],
  
  -- Assinatura Digital
  assinatura_digital JSONB,
  certificado_autenticidade TEXT,
  
  -- Conformidade e Retenção
  lgpd_compliant BOOLEAN DEFAULT TRUE,
  retencao_ate DATE,
  motivo_retencao VARCHAR(255),
  
  -- Auditoria
  uploaded_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id),
  
  -- Constraints
  CONSTRAINT chk_versao_positiva CHECK (versao > 0),
  CONSTRAINT chk_tamanho_valido CHECK (tamanho_bytes > 0),
  CONSTRAINT chk_retencao_futura CHECK (retencao_ate > CURRENT_DATE)
);

-- Metadados Estendidos dos Documentos
CREATE TABLE documento_metadados (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  documento_id UUID NOT NULL REFERENCES documentos_medicos(id) ON DELETE CASCADE,
  
  -- Metadados de Arquivo (imagens)
  dimensoes JSONB, -- {width: number, height: number}
  resolucao INTEGER,
  dados_exif JSONB,
  
  -- Metadados de Processamento
  thumbnail_gerado BOOLEAN DEFAULT FALSE,
  preview_gerado BOOLEAN DEFAULT FALSE,
  validado_medicamente BOOLEAN DEFAULT FALSE,
  
  -- Indexação e Busca
  palavras_chave TEXT[],
  entidades_identificadas JSONB,
  
  -- Conformidade
  nivel_sensibilidade VARCHAR(50),
  requer_consentimento BOOLEAN DEFAULT FALSE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Log de Acesso aos Documentos
CREATE TABLE documento_acessos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  documento_id UUID NOT NULL REFERENCES documentos_medicos(id) ON DELETE CASCADE,
  
  -- Dados do Acesso
  usuario_id UUID REFERENCES auth.users(id),
  ip_address INET NOT NULL,
  user_agent TEXT,
  
  -- Tipo de Acesso
  tipo_acesso acesso_tipo_type NOT NULL,
  sucesso BOOLEAN NOT NULL,
  motivo_falha TEXT,
  
  -- Contexto
  compartilhamento_id UUID REFERENCES compartilhamentos_seguros(id),
  referrer TEXT,
  
  -- Timestamp
  accessed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Compartilhamentos Seguros
CREATE TABLE compartilhamentos_seguros (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  documento_id UUID NOT NULL REFERENCES documentos_medicos(id) ON DELETE CASCADE,
  
  -- Configurações de Acesso
  token_acesso VARCHAR(255) UNIQUE NOT NULL,
  link_seguro TEXT NOT NULL,
  expiracao_em TIMESTAMPTZ NOT NULL,
  max_acessos INTEGER,
  acessos_realizados INTEGER DEFAULT 0,
  
  -- Destinatários
  destinatarios JSONB NOT NULL, -- Array de {email, userId, tipoUsuario}
  
  -- Configurações de Segurança
  requer_senha BOOLEAN DEFAULT FALSE,
  senha_hash VARCHAR(255),
  permitir_download BOOLEAN DEFAULT TRUE,
  permitir_compartilhar BOOLEAN DEFAULT FALSE,
  marca_dagua BOOLEAN DEFAULT TRUE,
  
  -- Notificações
  notificar_acessos BOOLEAN DEFAULT TRUE,
  
  -- Auditoria
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  revogado_em TIMESTAMPTZ,
  revogado_por UUID REFERENCES auth.users(id),
  motivo_revogacao TEXT,
  
  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  
  CONSTRAINT chk_expiracao_futura CHECK (expiracao_em > criado_em),
  CONSTRAINT chk_max_acessos_positivo CHECK (max_acessos IS NULL OR max_acessos > 0)
);

-- Versões dos Documentos
CREATE TABLE documento_versoes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  documento_id UUID NOT NULL REFERENCES documentos_medicos(id) ON DELETE CASCADE,
  versao_numero INTEGER NOT NULL,
  
  -- Dados da Versão
  hash_arquivo VARCHAR(64) NOT NULL,
  tamanho_bytes BIGINT NOT NULL,
  storage_path TEXT NOT NULL,
  
  -- Contexto da Mudança
  motivo_alteracao TEXT NOT NULL,
  alterado_por UUID NOT NULL REFERENCES auth.users(id),
  alterado_em TIMESTAMPTZ DEFAULT NOW(),
  
  -- Diferenças (para revisão)
  diferencas JSONB,
  tipo_alteracao alteracao_tipo_type NOT NULL,
  
  -- Status
  ativa BOOLEAN DEFAULT FALSE,
  arquivada BOOLEAN DEFAULT FALSE,
  
  CONSTRAINT uk_documento_versao UNIQUE (documento_id, versao_numero),
  CONSTRAINT chk_versao_numero_positivo CHECK (versao_numero > 0)
);

-- Processamento OCR
CREATE TABLE documento_ocr (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  documento_id UUID NOT NULL REFERENCES documentos_medicos(id) ON DELETE CASCADE,
  
  -- Dados do OCR
  texto_extraido TEXT NOT NULL,
  confidence_media NUMERIC(5,2),
  idioma_detectado VARCHAR(10),
  
  -- Processamento
  engine_ocr VARCHAR(50) NOT NULL,
  processado_em TIMESTAMPTZ DEFAULT NOW(),
  tempo_processamento INTEGER, -- segundos
  
  -- Análise de Conteúdo
  entidades_identificadas JSONB,
  dados_estruturados JSONB,
  categoria_sugerida VARCHAR(100),
  
  -- Qualidade
  qualidade_imagem VARCHAR(20),
  recomendacoes TEXT[],
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tipos Enum
CREATE TYPE documento_categoria_type AS ENUM (
  'identidade', 'exame_laboratorial', 'exame_imagem', 'receita_medica',
  'atestado', 'foto_procedimento', 'consentimento', 'contrato',
  'comprovante_pagamento', 'outros'
);

CREATE TYPE nivel_acesso_type AS ENUM (
  'publico', 'paciente', 'equipe_medica', 'profissional', 'administracao', 'restrito'
);

CREATE TYPE acesso_tipo_type AS ENUM (
  'visualizacao', 'download', 'compartilhamento', 'edicao', 'exclusao'
);

CREATE TYPE alteracao_tipo_type AS ENUM (
  'upload', 'edicao', 'correcao', 'atualizacao'
);

-- Índices para Performance
CREATE INDEX idx_documentos_paciente ON documentos_medicos(paciente_id);
CREATE INDEX idx_documentos_prontuario ON documentos_medicos(prontuario_id);
CREATE INDEX idx_documentos_categoria ON documentos_medicos(categoria);
CREATE INDEX idx_documentos_data_exame ON documentos_medicos(data_exame);
CREATE INDEX idx_documentos_hash ON documentos_medicos(hash_sha256);
CREATE INDEX idx_documentos_tags ON documentos_medicos USING gin(tags);

-- Full-text search no conteúdo OCR
CREATE INDEX idx_documentos_texto_search ON documentos_medicos USING gin(
  to_tsvector('portuguese', coalesce(texto_extraido, ''))
);

-- Índices para auditoria e acesso
CREATE INDEX idx_acessos_documento_data ON documento_acessos(documento_id, accessed_at);
CREATE INDEX idx_compartilhamentos_token ON compartilhamentos_seguros(token_acesso);
CREATE INDEX idx_compartilhamentos_expiracao ON compartilhamentos_seguros(expiracao_em) WHERE ativo = TRUE;

-- Índices para versionamento
CREATE INDEX idx_versoes_documento ON documento_versoes(documento_id, versao_numero DESC);
CREATE INDEX idx_versoes_ativas ON documento_versoes(documento_id) WHERE ativa = TRUE;
```

#### Document Management API

```typescript
// Secure Document Upload API
export async function POST(request: NextRequest) {
  const formData = await request.formData()
  const file = formData.get('file') as File
  const metadata = JSON.parse(formData.get('metadata') as string)
  
  const supabase = createServerClient()
  
  try {
    // Validações de segurança
    const validation = await validateDocumentUpload(file, metadata)
    if (!validation.valid) {
      return NextResponse.json({
        error: 'Arquivo inválido',
        details: validation.errors
      }, { status: 400 })
    }
    
    // Verificar permissões
    const { data: uploadPermission } = await supabase
      .rpc('check_document_upload_permission', {
        prontuario_id: metadata.prontuarioId,
        user_id: (await supabase.auth.getUser()).data.user?.id
      })
    
    if (!uploadPermission) {
      return NextResponse.json({
        error: 'Permissão insuficiente para upload'
      }, { status: 403 })
    }
    
    // Processar arquivo
    const fileBuffer = Buffer.from(await file.arrayBuffer())
    const fileHash = createHash('sha256').update(fileBuffer).digest('hex')
    
    // Verificar duplicatas
    const { data: existingDoc } = await supabase
      .from('documentos_medicos')
      .select('id, nome_original')
      .eq('hash_sha256', fileHash)
      .eq('prontuario_id', metadata.prontuarioId)
      .limit(1)
    
    if (existingDoc.length > 0) {
      return NextResponse.json({
        error: 'Documento já existe',
        existingDocument: existingDoc[0]
      }, { status: 409 })
    }
    
    // Criptografar arquivo
    const encryptionKey = await generateEncryptionKey()
    const encryptedBuffer = await encryptFile(fileBuffer, encryptionKey)
    
    // Upload para storage seguro
    const storageFileName = `${metadata.prontuarioId}/${Date.now()}-${fileHash}`
    const { data: uploadResult, error: uploadError } = await supabase.storage
      .from('documentos-medicos')
      .upload(storageFileName, encryptedBuffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      })
    
    if (uploadError) {
      throw new Error(`Erro no upload: ${uploadError.message}`)
    }
    
    // Processar OCR se aplicável
    let ocrData = null
    if (isImageFile(file.type) || isPDFFile(file.type)) {
      ocrData = await processOCR(fileBuffer, file.type)
    }
    
    // Gerar thumbnail e preview
    const thumbnailPath = await generateThumbnail(fileBuffer, file.type)
    const previewPath = await generatePreview(fileBuffer, file.type)
    
    // Extrair metadados
    const extractedMetadata = await extractFileMetadata(fileBuffer, file.type)
    
    // Salvar documento no banco
    const { data: documento, error } = await supabase
      .from('documentos_medicos')
      .insert({
        prontuario_id: metadata.prontuarioId,
        paciente_id: metadata.pacienteId,
        historico_medico_id: metadata.historicoMedicoId,
        nome_original: file.name,
        nome_armazenamento: storageFileName,
        tipo_arquivo: file.type,
        tamanho_bytes: file.size,
        hash_sha256: fileHash,
        categoria: metadata.categoria,
        subcategoria: metadata.subcategoria,
        tags: metadata.tags || [],
        descricao: metadata.descricao,
        nivel_acesso: metadata.nivelAcesso || 'equipe_medica',
        criptografado: true,
        chave_encryptacao: encryptionKey,
        texto_extraido: ocrData?.texto,
        ocr_processado: !!ocrData,
        ocr_confidence: ocrData?.confidence,
        dados_estruturados: ocrData?.dadosEstruturados,
        storage_path: uploadResult.path,
        storage_bucket: 'documentos-medicos',
        thumbnail_path: thumbnailPath,
        preview_path: previewPath,
        data_exame: metadata.dataExame,
        profissional_solicitante: metadata.profissionalSolicitante,
        laboratorio: metadata.laboratorio,
        uploaded_by: (await supabase.auth.getUser()).data.user?.id
      })
      .select()
      .single()
    
    if (error) {
      // Limpar uploads em caso de erro
      await supabase.storage.from('documentos-medicos').remove([storageFileName])
      throw error
    }
    
    // Salvar metadados estendidos
    await supabase
      .from('documento_metadados')
      .insert({
        documento_id: documento.id,
        dimensoes: extractedMetadata.dimensoes,
        resolucao: extractedMetadata.resolucao,
        dados_exif: extractedMetadata.exif,
        thumbnail_gerado: !!thumbnailPath,
        preview_gerado: !!previewPath,
        palavras_chave: ocrData?.palavrasChave || [],
        entidades_identificadas: ocrData?.entidades || {}
      })
    
    // Log de auditoria
    await logDocumentAccess({
      documentoId: documento.id,
      tipoAcesso: 'upload',
      usuarioId: (await supabase.auth.getUser()).data.user?.id,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get('user-agent'),
      sucesso: true
    })
    
    // Notificar integrações
    await Promise.all([
      // Epic 5: Notificar paciente sobre novo documento
      notifyPatientNewDocument(metadata.pacienteId, documento),
      // Epic 6: Atualizar contexto na agenda
      updateSchedulingDocumentContext(metadata.pacienteId),
      // Processar análise automática de documento
      scheduleDocumentAnalysis(documento.id)
    ])
    
    return NextResponse.json({
      documento: {
        id: documento.id,
        nomeOriginal: documento.nome_original,
        categoria: documento.categoria,
        tamanhoBytes: documento.tamanho_bytes,
        ocrProcessado: documento.ocr_processado,
        thumbnailUrl: thumbnailPath ? await getSecureDocumentUrl(thumbnailPath) : null
      },
      ocr: ocrData ? {
        textoExtraido: ocrData.texto,
        confidence: ocrData.confidence,
        entidadesIdentificadas: ocrData.entidades
      } : null,
      message: 'Documento enviado e processado com sucesso'
    })
    
  } catch (error) {
    console.error('Document upload error:', error)
    return NextResponse.json({
      error: 'Erro no upload do documento'
    }, { status: 500 })
  }
}

// Secure Document Sharing API
export async function createSecureShare(shareData: {
  documentoId: string
  destinatarios: Array<{ email?: string; userId?: string; tipoUsuario: string }>
  expiracaoHoras: number
  maxAcessos?: number
  requerSenha: boolean
  senha?: string
  permitirDownload: boolean
}) {
  const supabase = createServerClient()
  
  try {
    // Verificar permissões de compartilhamento
    const { data: sharePermission } = await supabase
      .rpc('check_document_share_permission', {
        documento_id: shareData.documentoId,
        user_id: (await supabase.auth.getUser()).data.user?.id
      })
    
    if (!sharePermission) {
      throw new Error('Permissão insuficiente para compartilhamento')
    }
    
    // Gerar token seguro
    const tokenAcesso = generateSecureToken(64)
    const linkSeguro = `${process.env.NEXT_PUBLIC_APP_URL}/documents/shared/${tokenAcesso}`
    
    // Hash da senha se fornecida
    let senhaHash = null
    if (shareData.requerSenha && shareData.senha) {
      senhaHash = await hashPassword(shareData.senha)
    }
    
    // Criar compartilhamento
    const { data: compartilhamento, error } = await supabase
      .from('compartilhamentos_seguros')
      .insert({
        documento_id: shareData.documentoId,
        token_acesso: tokenAcesso,
        link_seguro: linkSeguro,
        expiracao_em: new Date(Date.now() + shareData.expiracaoHoras * 60 * 60 * 1000),
        max_acessos: shareData.maxAcessos,
        destinatarios: shareData.destinatarios,
        requer_senha: shareData.requerSenha,
        senha_hash: senhaHash,
        permitir_download: shareData.permitirDownload,
        permitir_compartilhar: false,
        marca_dagua: true,
        criado_por: (await supabase.auth.getUser()).data.user?.id
      })
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    // Enviar notificações por email
    for (const destinatario of shareData.destinatarios) {
      if (destinatario.email) {
        await sendDocumentShareEmail({
          email: destinatario.email,
          linkSeguro,
          expiracaoEm: compartilhamento.expiracao_em,
          requerSenha: shareData.requerSenha,
          remetente: (await supabase.auth.getUser()).data.user?.email
        })
      }
    }
    
    return {
      compartilhamento,
      linkSeguro,
      tokenAcesso,
      expiracaoEm: compartilhamento.expiracao_em
    }
    
  } catch (error) {
    console.error('Document sharing error:', error)
    throw new Error('Erro ao criar compartilhamento seguro')
  }
}

// OCR Processing Function
export async function processOCR(fileBuffer: Buffer, fileType: string) {
  try {
    let texto = ''
    let confidence = 0
    
    // Usar Tesseract.js para OCR
    if (isImageFile(fileType)) {
      const { data: { text, confidence: conf } } = await recognize(fileBuffer, 'por', {
        logger: m => console.log(m)
      })
      texto = text
      confidence = conf
    } else if (isPDFFile(fileType)) {
      // Para PDFs, primeiro converter para imagem depois OCR
      const images = await convertPDFToImages(fileBuffer)
      const ocrResults = await Promise.all(
        images.map(img => recognize(img, 'por'))
      )
      
      texto = ocrResults.map(r => r.data.text).join('\n')
      confidence = ocrResults.reduce((sum, r) => sum + r.data.confidence, 0) / ocrResults.length
    }
    
    // Processar texto extraído
    const palavrasChave = extractKeywords(texto)
    const entidades = extractEntities(texto)
    const dadosEstruturados = extractStructuredData(texto, fileType)
    
    return {
      texto: texto.trim(),
      confidence: Math.round(confidence),
      palavrasChave,
      entidades,
      dadosEstruturados
    }
    
  } catch (error) {
    console.error('OCR processing error:', error)
    return null
  }
}

// Document Version Management
export async function createDocumentVersion(
  documentoId: string,
  newFileBuffer: Buffer,
  motivoAlteracao: string
) {
  const supabase = createServerClient()
  
  try {
    // Buscar documento atual
    const { data: documento } = await supabase
      .from('documentos_medicos')
      .select('*')
      .eq('id', documentoId)
      .single()
    
    if (!documento) {
      throw new Error('Documento não encontrado')
    }
    
    // Criar nova versão
    const novaVersao = documento.versao + 1
    const novoHash = createHash('sha256').update(newFileBuffer).digest('hex')
    
    // Upload da nova versão
    const novoStoragePath = `${documento.prontuario_id}/${Date.now()}-v${novaVersao}-${novoHash}`
    const encryptedBuffer = await encryptFile(newFileBuffer, documento.chave_encryptacao)
    
    await supabase.storage
      .from('documentos-medicos')
      .upload(novoStoragePath, encryptedBuffer)
    
    // Arquivar versão atual
    await supabase
      .from('documento_versoes')
      .insert({
        documento_id: documentoId,
        versao_numero: documento.versao,
        hash_arquivo: documento.hash_sha256,
        tamanho_bytes: documento.tamanho_bytes,
        storage_path: documento.storage_path,
        motivo_alteracao: 'arquivamento_automatico',
        alterado_por: (await supabase.auth.getUser()).data.user?.id,
        tipo_alteracao: 'atualizacao',
        ativa: false,
        arquivada: true
      })
    
    // Atualizar documento principal
    await supabase
      .from('documentos_medicos')
      .update({
        versao: novaVersao,
        versao_anterior: documentoId,
        motivo_alteracao: motivoAlteracao,
        hash_sha256: novoHash,
        tamanho_bytes: newFileBuffer.length,
        storage_path: novoStoragePath,
        updated_at: new Date()
      })
      .eq('id', documentoId)
    
    // Criar registro da nova versão ativa
    await supabase
      .from('documento_versoes')
      .insert({
        documento_id: documentoId,
        versao_numero: novaVersao,
        hash_arquivo: novoHash,
        tamanho_bytes: newFileBuffer.length,
        storage_path: novoStoragePath,
        motivo_alteracao: motivoAlteracao,
        alterado_por: (await supabase.auth.getUser()).data.user?.id,
        tipo_alteracao: 'atualizacao',
        ativa: true,
        arquivada: false
      })
    
    return {
      versaoAnterior: documento.versao,
      novaVersao: novaVersao,
      motivoAlteracao,
      success: true
    }
    
  } catch (error) {
    console.error('Version creation error:', error)
    throw new Error('Erro ao criar nova versão do documento')
  }
}
```

### Integration Points

#### Epic 5 Integration (Portal Paciente)

- **Document Access**: Pacientes acessam documentos pelo portal
- **Shared Results**: Resultados de exames compartilhados automaticamente
- **Consent Documents**: Termos e consentimentos digitais
- **Upload Self-Service**: Pacientes fazem upload de documentos próprios

#### Epic 6 Integration (Agenda Inteligente)

- **Document Context**: Documentos disponíveis durante agendamento
- **Pre-Consultation**: Upload de documentos antes da consulta
- **Result Scheduling**: Agendamento automático para entrega de resultados
- **Medical Alerts**: Alertas baseados em documentos críticos

#### Epic 7 Integration (Financeiro Essencial)

- **Insurance Documents**: Documentos de planos de saúde para faturamento
- **Payment Receipts**: Comprovantes de pagamento anexados
- **Audit Trail**: Documentos para auditoria financeira
- **Cost Analysis**: Análise de custos por tipo de documento

### Testing Strategy

#### Document Management Tests

```typescript
describe('Document Management System', () => {
  test('uploads and encrypts medical document', async () => {
    const testFile = createTestImageFile()
    const metadata = createTestDocumentMetadata()
    
    const response = await uploadDocument(testFile, metadata)
    
    expect(response.documento.id).toBeDefined()
    expect(response.documento.ocrProcessado).toBe(true)
    
    // Verify encryption
    const storedFile = await getStoredDocument(response.documento.id)
    expect(storedFile.criptografado).toBe(true)
  })
  
  test('processes OCR correctly for medical documents', async () => {
    const testPrescription = createTestPrescriptionImage()
    
    const ocrResult = await processOCR(testPrescription, 'image/jpeg')
    
    expect(ocrResult.texto).toContain('prescrição')
    expect(ocrResult.confidence).toBeGreaterThan(70)
    expect(ocrResult.entidades).toHaveProperty('medicamentos')
  })
  
  test('creates secure sharing link with expiration', async () => {
    const documento = await createTestDocument()
    
    const shareData = {
      documentoId: documento.id,
      destinatarios: [{ email: '<EMAIL>', tipoUsuario: 'paciente' }],
      expiracaoHoras: 24,
      requerSenha: true,
      senha: 'test123',
      permitirDownload: true
    }
    
    const share = await createSecureShare(shareData)
    
    expect(share.linkSeguro).toContain('/documents/shared/')
    expect(share.tokenAcesso).toHaveLength(64)
    expect(new Date(share.expiracaoEm)).toBeAfter(new Date())
  })
  
  test('maintains document version history', async () => {
    const originalDoc = await createTestDocument()
    const newFileBuffer = createTestUpdatedFile()
    
    const version = await createDocumentVersion(
      originalDoc.id,
      newFileBuffer,
      'Atualização de resultados'
    )
    
    expect(version.novaVersao).toBe(2)
    expect(version.versaoAnterior).toBe(1)
    
    const versions = await getDocumentVersions(originalDoc.id)
    expect(versions).toHaveLength(2)
  })
})
```

#### Security Tests

```typescript
describe('Document Security', () => {
  test('prevents unauthorized document access', async () => {
    const documento = await createTestDocument()
    const unauthorizedUser = await createTestUser({ role: 'recepcao' })
    
    const response = await getDocument(documento.id, unauthorizedUser.id)
    
    expect(response.status).toBe(403)
  })
  
  test('logs all document access attempts', async () => {
    const documento = await createTestDocument()
    
    await getDocument(documento.id)
    
    const accessLogs = await getDocumentAccessLogs(documento.id)
    expect(accessLogs.length).toBeGreaterThan(0)
    expect(accessLogs[0].tipo_acesso).toBe('visualizacao')
  })
})
```

### Dev Notes

#### Security Implementation

- **End-to-End Encryption**: AES-256 encryption for all medical documents
- **Secure Sharing**: Tokenized links with expiration and access control
- **Access Logging**: Complete audit trail for compliance
- **Digital Signatures**: Document integrity verification

#### OCR and Processing

- **Tesseract.js**: Portuguese OCR for medical documents
- **Entity Extraction**: Medical entities (medications, diagnoses)
- **Document Classification**: Automatic categorization
- **Quality Assessment**: Image quality validation for OCR

#### Performance Optimization

- **CDN Integration**: Fast document delivery via CDN
- **Lazy Loading**: Progressive loading of document lists
- **Thumbnail Generation**: Fast preview generation
- **Compression**: Intelligent file compression

---

## Story 9.3 Status

**Status**: ✅ READY - Sistema completo de gestão de documentos com OCR, versionamento, compartilhamento seguro e compliance total.

**Próxima**: Story 9.4 - Anamnese Digital e Integração
