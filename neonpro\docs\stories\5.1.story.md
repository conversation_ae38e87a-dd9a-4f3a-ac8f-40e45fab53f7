# Story 5.1: Portal Público de Agendamento

## Status

Approved

## Story

**As a** potential or existing patient,  
**I want** to view available appointment slots and book appointments online without calling the clinic,  
**so that** I can conveniently schedule appointments at any time that works for me.

## Acceptance Criteria

1. **Public Appointment Calendar:**
   - Display available time slots by professional and service type
   - Real-time availability updates with conflict prevention
   - Calendar view with multiple date navigation (month/week views)
   - Service duration and pricing display for each appointment type
   - Professional profiles with specializations and photos

2. **Self-Service Booking Flow:**
   - Guest booking with minimal required information (name, phone, email)
   - Service selection with detailed descriptions and estimated duration
   - Professional preference selection or auto-assignment
   - Date and time selection from available slots
   - Booking confirmation with appointment details and instructions

3. **Patient Information Collection:**
   - Basic patient data collection during first booking
   - Medical history questionnaire for new patients
   - Insurance information and payment preferences
   - Emergency contact information
   - Consent forms and privacy policy acceptance

4. **Booking Validation and Confirmation:**
   - Real-time slot availability validation before confirmation
   - Automatic appointment confirmation emails/SMS
   - Calendar integration options (Google, Outlook, Apple)
   - Booking reference number generation
   - Integration with clinic's internal appointment system

## Tasks / Subtasks

- [ ] Task 1: Create Public Portal Landing Page (AC: 1)
  - [ ] Implement responsive landing page with clinic information
  - [ ] Add service overview and professional profiles display
  - [ ] Integrate with existing authentication system for clinic branding
  - [ ] Implement SEO optimization for public discovery

- [ ] Task 2: Build Calendar Component with Real-time Availability (AC: 1, 2)
  - [ ] Create calendar widget using shadcn/ui components
  - [ ] Implement month/week view navigation
  - [ ] Connect to Supabase appointments table with RLS for public read
  - [ ] Add real-time subscription for availability updates
  - [ ] Implement conflict prevention logic

- [ ] Task 3: Implement Service and Professional Selection (AC: 1, 2)
  - [ ] Create service catalog component with descriptions and pricing
  - [ ] Build professional selection with photos and specializations
  - [ ] Implement duration and availability calculation per service type
  - [ ] Add auto-assignment algorithm when no preference specified

- [ ] Task 4: Build Patient Information Collection Forms (AC: 3)
  - [ ] Create multi-step form with validation using react-hook-form + zod
  - [ ] Implement conditional fields for new vs returning patients
  - [ ] Add medical history questionnaire with LGPD compliance
  - [ ] Integrate consent forms and privacy policy acceptance

- [ ] Task 5: Develop Booking Validation and Confirmation System (AC: 4)
  - [ ] Implement real-time slot validation before booking
  - [ ] Create booking confirmation with reference number generation
  - [ ] Build email/SMS notification system integration
  - [ ] Add calendar export functionality (iCal format)
  - [ ] Integrate with existing appointment system via Edge Functions

- [ ] Task 6: Implement Security and Rate Limiting (All ACs)
  - [ ] Add CAPTCHA protection for public booking forms
  - [ ] Implement rate limiting for booking requests
  - [ ] Add CSRF protection for all forms
  - [ ] Ensure LGPD compliance for data collection

- [ ] Task 7: Create Mobile-Responsive UI Components (All ACs)
  - [ ] Optimize calendar component for mobile devices
  - [ ] Implement touch-friendly form interactions
  - [ ] Add progressive enhancement for offline capabilities
  - [ ] Test accessibility compliance (WCAG 2.1 AA)

## Dev Notes

### Architecture Context

**Source:** docs/architecture/01-system-overview-context.md, 02-logical-components-data-flow.md

The public portal follows the established Next.js 15 "islands" architecture:
- Server Components for initial page load and SEO optimization
- Client Components for interactive calendar and booking forms
- Edge Functions for booking validation and confirmation processing
- Supabase RLS policies for secure public data access

### Database Integration

**Source:** docs/architecture/03-data-model-rls-policies.md

Public portal requires new RLS policies for read-only access to:
- `services` table for service information and pricing
- `professionals` table for professional profiles and availability
- `appointments` table for availability checking (anonymous read)
- `patients` table creation for new patient registration

### API Endpoints

**Source:** docs/architecture/05-api-surface-edge-functions.md

Required Edge Functions following existing patterns:
- `/api/public/availability` - Get available slots with conflict checking
- `/api/public/book` - Process booking with validation (similar to existing booking flow)
- `/api/public/services` - Get service catalog with pricing
- `/api/public/professionals` - Get professional profiles

### Component Architecture

**Source:** Existing codebase patterns in components/

Location: `components/public/` (new directory)
- `PublicCalendar` - Calendar component with availability display
- `ServiceSelector` - Service selection with descriptions
- `ProfessionalSelector` - Professional selection component
- `BookingForm` - Multi-step booking form with validation
- `BookingConfirmation` - Confirmation page with reference number

Pages: `app/portal/` (new directory)
- `page.tsx` - Main public portal landing page
- `book/page.tsx` - Booking flow page
- `confirmation/page.tsx` - Booking confirmation page

### Technical Constraints

**Source:** docs/architecture/06-security-compliance.md

- LGPD compliance required for patient data collection
- Rate limiting: 10 booking attempts per IP per hour
- CAPTCHA required for booking submission
- SSL/TLS encryption for all patient data transmission
- Session-based booking cart to prevent double-booking

### Integration Points

**Source:** Epic 1 (Authentication), Epic 2 (Financial), Epic 4 (AI)

- Epic 1: Use existing clinic authentication for staff portal integration
- Epic 2: Integration with financial system for payment processing
- Epic 4: AI-powered appointment suggestions (future enhancement)
- Existing messaging system for booking confirmations

### Testing

**Testing Standards from Architecture:**
- Test file location: `__tests__/public/` and `components/public/__tests__/`
- Unit tests for all form validation logic
- Integration tests for booking flow end-to-end
- API endpoint testing for Edge Functions
- Accessibility testing for public-facing components
- Performance testing for calendar rendering with large datasets

**Required Test Coverage:**
- Form validation scenarios (valid/invalid inputs)
- Calendar availability logic with edge cases
- Booking confirmation flow with error handling
- Rate limiting and security measures
- Mobile responsiveness across devices

### Performance Requirements

**Source:** docs/prd/06-requirements.md

- Page load times under 3 seconds on 3G networks (RNF-01)
- Calendar rendering optimization for mobile devices
- Image optimization for professional photos
- Lazy loading for service descriptions
- Caching strategy for availability data

### File Structure

```
app/portal/
├── page.tsx                    # Public portal landing
├── book/
│   ├── page.tsx               # Booking flow
│   └── confirmation/page.tsx   # Confirmation page
└── layout.tsx                 # Public portal layout

components/public/
├── PublicCalendar.tsx
├── ServiceSelector.tsx
├── ProfessionalSelector.tsx
├── BookingForm.tsx
├── BookingConfirmation.tsx
└── __tests__/
    ├── PublicCalendar.test.tsx
    ├── ServiceSelector.test.tsx
    └── BookingForm.test.tsx

app/api/public/
├── availability/route.ts
├── book/route.ts
├── services/route.ts
└── professionals/route.ts
```

### Dependencies

**External Dependencies:**
- react-hook-form + @hookform/resolvers for form handling
- date-fns for calendar date manipulation
- react-calendar or custom calendar component
- @radix-ui/react-calendar from shadcn/ui
- zod for validation schemas

**Internal Dependencies:**
- Epic 1: Supabase authentication and RLS policies
- Existing appointment system and database schema
- shadcn/ui component library
- Existing messaging/notification system

## Testing

### Testing Requirements

**Unit Testing:**
- All form validation logic with edge cases
- Calendar component date manipulation
- Service and professional selection logic
- Booking validation algorithms

**Integration Testing:**
- Complete booking flow from calendar to confirmation
- API endpoint testing with mocked Supabase responses
- Real-time availability updates testing
- Email/SMS notification integration testing

**End-to-End Testing:**
- Full patient booking journey across devices
- Error handling scenarios (network failures, validation errors)
- Accessibility testing with screen readers
- Performance testing under load

**Security Testing:**
- Rate limiting enforcement
- CAPTCHA bypass attempts
- SQL injection prevention
- XSS protection validation

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-18 | 1.0 | Initial story creation from Epic 5 | Scrum Master Bob |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

### Agent Model Used

*To be filled by dev agent*

### Implementation Status

*To be filled by dev agent*

### Task Completion Tracking

*To be filled by dev agent*

### Debug Log References

*To be filled by dev agent*

### Completion Notes

*To be filled by dev agent*

### File List

*To be filled by dev agent*

### Change Log

*To be filled by dev agent*
