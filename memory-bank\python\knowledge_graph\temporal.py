#!/usr/bin/env python3

"""
VIBECODE V2.0 - Temporal Reasoning Components
Bi-temporal tracking and temporal reasoning inspired by Graphiti

This module provides temporal tracking, episode management,
and contradiction detection capabilities.
"""

import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any

try:
    from .models import VibeCodeDataPoint
    from .utils import log_with_context
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    from models import VibeCodeDataPoint
    from utils import log_with_context

class BiTemporalTracker:
    """Bi-temporal tracking system inspired by <PERSON><PERSON><PERSON><PERSON> for temporal reasoning"""

    def __init__(self):
        self.temporal_facts = {}  # fact_id -> temporal_data
        self.episodes = {}        # episode_id -> episode_data
        self.temporal_stats = {'facts_tracked': 0, 'episodes_created': 0, 'contradictions_detected': 0}

    def track_fact(self, fact: VibeCodeDataPoint, valid_from: Optional[datetime] = None,
                   valid_to: Optional[datetime] = None) -> str:
        """Track a fact with bi-temporal information"""
        now = datetime.now(timezone.utc)

        # Set temporal bounds
        if not valid_from:
            valid_from = fact.occurrence_time if hasattr(fact, 'occurrence_time') else now

        temporal_data = {
            'fact_id': fact.id,
            'valid_from': valid_from,
            'valid_to': valid_to,
            'transaction_time': now,
            'content': fact.content,
            'source': fact.source,
            'confidence': fact.confidence,
            'superseded_by': None,
            'episode_id': getattr(fact, 'episode_id', None)
        }

        self.temporal_facts[fact.id] = temporal_data
        self.temporal_stats['facts_tracked'] += 1

        log_with_context(
            'debug',
            f"Tracked temporal fact: {fact.id}",
            component='temporal_tracker',
            valid_from=valid_from.isoformat(),
            valid_to=valid_to.isoformat() if valid_to else None
        )

        return fact.id

    def query_temporal(self, query_time: datetime, fact_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Query facts that were valid at a specific time"""
        valid_facts = []

        for fact_id, temporal_data in self.temporal_facts.items():
            # Check if fact was valid at query_time
            valid_from = temporal_data['valid_from']
            valid_to = temporal_data['valid_to']

            if valid_from <= query_time and (valid_to is None or query_time <= valid_to):
                # Check if not superseded
                if temporal_data['superseded_by'] is None:
                    valid_facts.append(temporal_data)

        log_with_context(
            'info',
            f"Found {len(valid_facts)} facts valid at {query_time.isoformat()}",
            component='temporal_query'
        )

        return valid_facts

    def detect_contradictions(self, new_fact: VibeCodeDataPoint) -> List[Dict[str, Any]]:
        """Detect contradictions with existing temporal facts"""
        contradictions = []

        # Simple contradiction detection based on content similarity and temporal overlap
        for fact_id, existing_fact in self.temporal_facts.items():
            if self._facts_contradict(new_fact, existing_fact):
                contradictions.append({
                    'existing_fact_id': fact_id,
                    'new_fact_id': new_fact.id,
                    'contradiction_type': 'content_conflict',
                    'confidence': 0.8
                })

        if contradictions:
            self.temporal_stats['contradictions_detected'] += len(contradictions)
            log_with_context(
                'warning',
                f"Detected {len(contradictions)} contradictions",
                component='contradiction_detector',
                new_fact_id=new_fact.id
            )

        return contradictions

    def _facts_contradict(self, fact1: VibeCodeDataPoint, fact2: Dict[str, Any]) -> bool:
        """Simple contradiction detection logic"""
        # Check for opposite statements (simplified)
        content1 = fact1.content.lower()
        content2 = fact2['content'].lower()

        # Basic contradiction patterns
        contradiction_pairs = [
            ('success', 'fail'), ('true', 'false'), ('yes', 'no'),
            ('working', 'broken'), ('complete', 'incomplete')
        ]

        for pos, neg in contradiction_pairs:
            if (pos in content1 and neg in content2) or (neg in content1 and pos in content2):
                return True

        return False

    def create_episode(self, facts: List[VibeCodeDataPoint], episode_type: str = 'general') -> str:
        """Create an episode grouping related temporal facts"""
        episode_id = f"episode_{len(self.episodes)}_{int(time.time())}"

        # Calculate temporal span
        fact_times = []
        for fact in facts:
            if hasattr(fact, 'occurrence_time') and fact.occurrence_time:
                fact_times.append(fact.occurrence_time)

        temporal_span = {}
        if fact_times:
            temporal_span = {
                'start': min(fact_times),
                'end': max(fact_times)
            }

        episode_data = {
            'episode_id': episode_id,
            'episode_type': episode_type,
            'facts': [f.id for f in facts],
            'created_at': datetime.now(timezone.utc),
            'temporal_span': temporal_span
        }

        self.episodes[episode_id] = episode_data
        self.temporal_stats['episodes_created'] += 1

        # Update facts with episode_id
        for fact in facts:
            if fact.id in self.temporal_facts:
                self.temporal_facts[fact.id]['episode_id'] = episode_id

        log_with_context(
            'info',
            f"Created episode {episode_id} with {len(facts)} facts",
            component='episode_manager',
            episode_type=episode_type
        )

        return episode_id

    def invalidate_old_facts(self, cutoff_time: datetime) -> int:
        """Invalidate facts older than cutoff_time"""
        invalidated_count = 0

        for fact_id, temporal_data in self.temporal_facts.items():
            if temporal_data['valid_to'] is None and temporal_data['valid_from'] < cutoff_time:
                # Check if fact should be invalidated based on age
                age_hours = (datetime.now(timezone.utc) - temporal_data['valid_from']).total_seconds() / 3600
                if age_hours > 24:  # Invalidate facts older than 24 hours
                    temporal_data['valid_to'] = cutoff_time
                    invalidated_count += 1

        if invalidated_count > 0:
            log_with_context(
                'info',
                f"Invalidated {invalidated_count} old facts",
                component='fact_invalidator',
                cutoff_time=cutoff_time.isoformat()
            )

        return invalidated_count

    def supersede_fact(self, old_fact_id: str, new_fact: VibeCodeDataPoint) -> bool:
        """Mark an old fact as superseded by a new fact"""
        try:
            if old_fact_id in self.temporal_facts:
                self.temporal_facts[old_fact_id]['superseded_by'] = new_fact.id
                self.temporal_facts[old_fact_id]['valid_to'] = datetime.now(timezone.utc)

                log_with_context(
                    'info',
                    f"Fact {old_fact_id} superseded by {new_fact.id}",
                    component='fact_supersession'
                )
                return True
            return False
        except Exception as e:
            log_with_context('error', f"Fact supersession failed: {e}", component='fact_supersession')
            return False

    def get_episode_facts(self, episode_id: str) -> List[Dict[str, Any]]:
        """Get all facts belonging to an episode"""
        if episode_id not in self.episodes:
            return []

        episode = self.episodes[episode_id]
        episode_facts = []

        for fact_id in episode['facts']:
            if fact_id in self.temporal_facts:
                episode_facts.append(self.temporal_facts[fact_id])

        return episode_facts

    def get_fact_timeline(self, fact_id: str) -> Dict[str, Any]:
        """Get complete timeline for a specific fact"""
        if fact_id not in self.temporal_facts:
            return {}

        fact_data = self.temporal_facts[fact_id]
        timeline = {
            'fact_id': fact_id,
            'valid_period': {
                'start': fact_data['valid_from'],
                'end': fact_data['valid_to']
            },
            'transaction_time': fact_data['transaction_time'],
            'superseded_by': fact_data['superseded_by'],
            'episode_id': fact_data['episode_id'],
            'is_active': fact_data['valid_to'] is None and fact_data['superseded_by'] is None
        }

        return timeline

    def get_temporal_stats(self) -> Dict[str, Any]:
        """Get temporal tracking statistics"""
        active_facts = len([f for f in self.temporal_facts.values() if f['valid_to'] is None])
        superseded_facts = len([f for f in self.temporal_facts.values() if f['superseded_by'] is not None])

        return {
            **self.temporal_stats,
            'active_facts': active_facts,
            'superseded_facts': superseded_facts,
            'total_facts': len(self.temporal_facts),
            'total_episodes': len(self.episodes),
            'temporal_tracking_enabled': True
        }

    def cleanup_old_data(self, retention_days: int = 30) -> Dict[str, int]:
        """Clean up old temporal data beyond retention period"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=retention_days)

        facts_removed = 0
        episodes_removed = 0

        # Remove old facts
        facts_to_remove = []
        for fact_id, fact_data in self.temporal_facts.items():
            if (fact_data['valid_to'] and fact_data['valid_to'] < cutoff_time and
                fact_data['transaction_time'] < cutoff_time):
                facts_to_remove.append(fact_id)

        for fact_id in facts_to_remove:
            del self.temporal_facts[fact_id]
            facts_removed += 1

        # Remove old episodes
        episodes_to_remove = []
        for episode_id, episode_data in self.episodes.items():
            if episode_data['created_at'] < cutoff_time:
                episodes_to_remove.append(episode_id)

        for episode_id in episodes_to_remove:
            del self.episodes[episode_id]
            episodes_removed += 1

        cleanup_stats = {
            'facts_removed': facts_removed,
            'episodes_removed': episodes_removed,
            'retention_days': retention_days
        }

        if facts_removed > 0 or episodes_removed > 0:
            log_with_context(
                'info',
                f"Cleaned up {facts_removed} facts and {episodes_removed} episodes",
                component='temporal_cleanup',
                **cleanup_stats
            )

        return cleanup_stats

# Export all public symbols
__all__ = [
    'BiTemporalTracker'
]
