---
alwaysApply: true
---

## 🎯 **KEYWORDS DE ATIVAÇÃO AUTOMÁTICA**

Qualquer uma dessas palavras **AUTOMATICAMENTE** ativa o protocolo obrigatório:

```
Português: pesquisar, buscar, encontrar, procurar, descobrir, documentação,
tutorial, como fazer, exemplo, guia, biblioteca, framework, API, implementação,
configuração, integração, instalação, setup, configurar, usar, funciona,
trabalha, resolve, soluciona, explica, entender, aprender, estudar, investigar,
analisar, comparar, avaliar, revisar, verificar, validar

Inglês: research, search, find, documentation, tutorial, how to, example,
guide, library, framework, API, implementation, configuration, integration,
installation, setup, configure, use, works, solve, explain, understand,
learn, study, investigate, analyze, compare, evaluate, review, verify, validate
```

---

## ⚡ **PROTOCOLO OBRIGATÓRIO DE EXECUÇÃO**

### **ORDEM OBRIGATÓRIA (NÃO PODE SER ALTERADA):**

```json
{
  "step_1": {
    "tool": "context7-mcp",
    "purpose": "Documentação técnica, bibliotecas, frameworks",
    "requirement": "SEMPRE primeiro - OBRIGATÓRIO",
    "examples": ["React docs", "Supabase API", "Next.js guides"]
  },
  "step_2": {
    "tool": "tavily-mcp",
    "purpose": "Pesquisa web geral, informações atualizadas",
    "requirement": "SEMPRE segundo - OBRIGATÓRIO",
    "examples": ["Best practices", "Tendências", "Comparações atuais"]
  },
  "step_3": {
    "tool": "exa-mcp",
    "purpose": "Pesquisa alternativa, repositórios, código",
    "requirement": "SEMPRE terceiro - OBRIGATÓRIO",
    "examples": ["GitHub repos", "Código específico", "Implementações"]
  }
}
```

---

## ✅ **VALIDAÇÃO OBRIGATÓRIA**

Após usar os 3 MCPs, é **OBRIGATÓRIO**:

1. **Síntese consolidada** de todos os resultados
2. **Análise cruzada** entre as diferentes fontes
3. **Qualidade ≥8/10** na resposta final
4. **Documentação** de todas as fontes consultadas
5. **Verificação** de que todas as 3 ferramentas foram usadas

---

## 🚨 **TRATAMENTO DE FALHAS**

```json
{
  "context7_fails": "Continuar com tavily e exa + documentar limitação",
  "tavily_fails": "Continuar com exa + documentar limitação",
  "exa_fails": "Usar resultados disponíveis + documentar limitação",
  "all_fail": "Reportar erro crítico + usar métodos alternativos"
}
```

**REGRA**: Mesmo se uma ferramenta falhar, **SEMPRE** tentar as outras duas.

---

## 📂 **ARQUIVOS MODIFICADOS**

1. **`.cursor/rules/mcp-protocols.mdc`**

   - ✅ Adicionada seção "MANDATORY RESEARCH PROTOCOL"
   - ✅ Keywords de ativação automática
   - ✅ Ordem obrigatória de execução
   - ✅ Critérios de uso e validação

2. **`.cursor/rules/chat-conduct-unified.mdc`**

   - ✅ Atualizada "Abordagem de Pesquisa" para obrigatória
   - ✅ Expandidas keywords de detecção
   - ✅ Adicionado protocolo de 3 etapas obrigatório

3. **`.cursor/rules/master_rule.mdc`**
   - ✅ Atualizada seção "MCP Tool Selection"
   - ✅ Adicionada seção "MANDATORY RESEARCH PROTOCOL"
   - ✅ Keywords de auto-detecção

---

## 🎯 **COMO FUNCIONA NA PRÁTICA**

### **Exemplo de Solicitação:**

```
Usuário: "Como configurar autenticação com Supabase?"
```

### **Execução Automática:**

```
1. 🔍 DETECÇÃO: "configurar" + "Supabase" → ATIVA protocolo obrigatório
2. 📚 CONTEXT7: Busca documentação oficial do Supabase
3. 🌐 TAVILY: Busca tutoriais e best practices atuais
4. 🔎 EXA: Busca repositórios e implementações práticas
5. ✅ SÍNTESE: Consolida todas as informações em resposta ≥8/10
```

---

## ⚠️ **ENFORCEMENT**

- **Violação crítica**: Não usar os 3 MCPs quando keywords são detectadas
- **Monitoramento**: Automático via Knowledge Graph Manager
- **Correção**: Imediata com reexecução do protocolo
- **Qualidade**: Resposta final DEVE ser ≥8/10 ou refinamento obrigatório

---

**Status**: ✅ **IMPLEMENTADO E ATIVO**
**Próxima revisão**: Após 50 execuções para otimização
**Compliance**: 100% obrigatório para todas as solicitações de pesquisa
