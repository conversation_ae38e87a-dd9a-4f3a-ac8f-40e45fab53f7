# 🎨 VIBECODE Shared Components Library

A comprehensive shared component library for all VIBECODE SaaS projects, providing reusable UI components, utilities, and configurations.

## 📁 Directory Structure

```
shared/
├── src/                    # Main source code
│   ├── ui/                # Reusable UI components (shadcn/ui based)
│   ├── lib/               # Utility functions and helpers
│   ├── providers/         # React context providers
│   ├── config/            # Configuration files
│   └── index.ts           # Main export file
├── stagewise/             # Stagewise integration components
│   ├── components/        # StagewiseProvider and related
│   ├── config/           # Stagewise configuration
│   └── utils/            # Stagewise utilities
├── package.json          # Dependencies and build configuration
├── tsconfig.json         # TypeScript configuration
└── tsup.config.ts        # Build configuration
```

## 🚀 Installation & Usage

### In Your Project

```bash
# Install the shared library
npm install @vibecode/shared

# Or if developing locally
npm install file:../shared
```

### Import Components

```typescript
// UI Components
import { Button, Card, Input, Label } from '@vibecode/shared/ui';

// Utilities
import { cn, formatDate } from '@vibecode/shared/lib/utils';

// Providers
import { ThemeProvider } from '@vibecode/shared/providers';

// Stagewise Integration
import { StagewiseProvider, useStagewise } from '@vibecode/shared/stagewise';
```

## 📋 Available Components

### UI Components
- **Button**: Customizable button with variants
- **Card**: Container component with header/content/footer
- **Input**: Form input with validation support
- **Label**: Accessible form labels
- **[More components as needed]**

### Providers
- **ThemeProvider**: Dark/light theme management
- **StagewiseProvider**: Stagewise integration for development

### Utilities
- **cn()**: Class name utility (clsx + tailwind-merge)
- **formatDate()**: Date formatting utilities
- **Supabase helpers**: Database and auth utilities

## 🔧 Development

### Building the Library

```bash
# Build for production
npm run build

# Build and watch for changes
npm run dev

# Type checking
npm run type-check
```

### Adding New Components

1. Create component in `src/ui/`
2. Export from `src/ui/index.ts`
3. Update main `src/index.ts`
4. Build and test

## 🎯 Design Principles

- **Consistency**: All components follow the same design patterns
- **Accessibility**: WCAG 2.1 compliant components
- **Type Safety**: Full TypeScript support
- **Performance**: Tree-shakeable exports
- **Flexibility**: Customizable through props and CSS variables

## 🔗 Integration with Projects

This library is designed to work seamlessly with:
- **NeonPro**: Dashboard and analytics components
- **AegisWallet**: Security-focused UI patterns
- **AgendaTrintaE3**: Calendar and scheduling components

## 📚 Documentation

For detailed component documentation and examples, see the individual component files or visit the project documentation.

## 🤝 Contributing

When adding new shared components:
1. Ensure they're truly reusable across projects
2. Follow existing naming conventions
3. Include proper TypeScript types
4. Add documentation and examples
5. Test with all three main projects