# Epic 1: Authentication & Core Agenda System

## Epic Overview
**Sprint:** Sprint 1 - Autenticação & Agenda Core  
**Duration:** 2 weeks  
**Priority:** P0  
**Success Criteria:** Login/OAuth, Agenda CRUD, Portal Paciente α - Agendar ≤ 2 min  

## Epic Description
Establish the foundational authentication system and core appointment scheduling functionality that serves as the backbone for all clinic operations. This epic focuses on creating a secure, user-friendly system for managing appointments with proper authentication and authorization.

## User Stories

### Story 1.1: Enhanced Appointment CRUD Operations
**As a** clinic administrator  
**I want** to create, read, update, and delete appointments with full validation  
**So that** I can efficiently manage the clinic's schedule with data integrity  

**Acceptance Criteria:**
1. **Create Appointment:**
   - Form includes patient selection, professional selection, service type, date/time, duration, notes
   - Validates appointment conflicts (same professional, overlapping times)
   - Validates business hours and professional availability
   - Creates appointment record with proper relationships
   - Shows success/error feedback

2. **Read Appointments:**
   - Display appointments in calendar view (day/week/month)
   - Show appointment details in sidebar/modal when clicked
   - Filter by professional, service type, date range
   - Search by patient name or appointment details
   - Real-time updates when appointments change

3. **Update Appointment:**
   - Edit all appointment fields with same validations as create
   - Handle conflict detection when changing time/professional
   - Update related records (notifications, patient history)
   - Track changes for audit purposes

4. **Delete Appointment:**
   - Soft delete with reason tracking
   - Confirmation dialog with impact warning
   - Cascade to related notifications/reminders
   - Maintain historical data for reporting

5. **Performance & UX:**
   - CRUD operations complete ≤ 3 clicks as specified
   - Page load times ≤ 2 seconds
   - Responsive design for mobile/tablet access
   - Keyboard shortcuts for power users

### Story 1.2: Appointment Conflict Prevention System
**As a** clinic staff member  
**I want** the system to prevent scheduling conflicts automatically  
**So that** double-bookings and overlapping appointments are impossible  

**Acceptance Criteria:**
1. **Conflict Detection:**
   - Real-time validation during appointment creation/editing
   - Check professional availability across all appointment types
   - Validate against blocked times and breaks
   - Consider appointment duration and buffer times

2. **Business Rules Enforcement:**
   - Respect professional working hours
   - Honor clinic holidays and closure periods
   - Implement minimum/maximum booking notice periods
   - Enforce service-specific duration requirements

3. **User Feedback:**
   - Clear error messages explaining conflicts
   - Suggest alternative time slots when conflicts occur
   - Visual indicators in calendar showing availability
   - Warning dialogs before overriding (if permitted)

### Story 1.3: Patient Portal Appointment Self-Service
**As a** patient  
**I want** to view, book, and manage my appointments online  
**So that** I can schedule appointments conveniently without calling the clinic  

**Acceptance Criteria:**
1. **Patient Authentication:**
   - Secure login with email/phone verification
   - Password reset functionality
   - Session management with appropriate timeouts
   - LGPD-compliant data handling

2. **Appointment Booking:**
   - View available time slots for desired services
   - Select preferred professional (if applicable)
   - Provide appointment notes/special requests
   - Receive booking confirmation via email/SMS
   - Complete booking process ≤ 2 minutes (success criteria)

3. **Appointment Management:**
   - View upcoming and past appointments
   - Cancel appointments (within policy limits)
   - Request rescheduling with available alternatives
   - Receive appointment reminders
   - Complete actions ≤ 3 clicks (success criteria)

4. **Portal Features:**
   - Mobile-responsive design
   - Accessible interface (WCAG 2.1 AA)
   - Multi-language support (PT-BR primary)
   - Integration with clinic's communication channels

### Story 1.4: OAuth Google Integration Enhancement
**As a** clinic professional  
**I want** to sign in securely using my Google account  
**So that** I can access the system quickly without managing additional passwords  

**Acceptance Criteria:**
1. **OAuth Implementation:**
   - Google OAuth 2.0 integration with Supabase
   - Secure token handling and refresh
   - Profile information synchronization
   - Fallback to email/password authentication

2. **User Experience:**
   - Login process completes ≤ 3 seconds (success criteria)
   - Clear login interface with Google option
   - Proper error handling for OAuth failures
   - Seamless redirect flow without information loss

3. **Security & Compliance:**
   - Secure token storage and transmission
   - LGPD-compliant data handling
   - Session management with appropriate expiry
   - Audit logging for authentication events

4. **Role-Based Access:**
   - Automatic role assignment based on email domain/list
   - Proper RLS policy enforcement
   - Professional profile linking
   - Permission validation on all protected routes

## Technical Requirements

### Database Schema Updates
- Enhance appointments table with conflict prevention constraints
- Add patient portal access controls
- Implement audit logging for appointment changes
- Optimize queries for calendar views and conflict detection

### API Endpoints
- RESTful appointment CRUD operations
- Patient portal authentication endpoints
- Real-time conflict validation APIs
- Calendar data aggregation endpoints

### Security Considerations
- RLS policies for appointment data access
- Patient data privacy protection (LGPD)
- Secure OAuth implementation
- Session management and timeout handling

### Performance Targets
- Login ≤ 3 seconds (p95)
- CRUD operations ≤ 3 clicks
- Appointment booking ≤ 2 minutes
- Real-time conflict detection < 500ms
- Calendar views load ≤ 2 seconds

## Dependencies
- Supabase authentication system (OAuth provider setup)
- Professional and service management (RF-09, RF-10 - already implemented)
- Patient management system
- Communication channel integration (for reminders)

## Success Metrics
- Appointment booking time: Target ≤ 2 minutes
- User satisfaction: ≥ 80% positive feedback
- Conflict prevention: 100% effective (zero double-bookings)
- System performance: Meet all specified response times
- Portal adoption: ≥ 60% of patients use self-service features

## Definition of Done
- All user stories completed with acceptance criteria met
- Code reviewed and meets quality standards
- Unit and integration tests passing (≥ 80% coverage)
- Security review completed
- Performance testing validates success criteria
- Documentation updated (API docs, user guides)
- Deployed to staging environment and tested
- Stakeholder approval received
