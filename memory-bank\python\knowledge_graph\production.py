#!/usr/bin/env python3

"""
VIBECODE V2.0 - Production Components
Production-grade optimization, monitoring, error handling, and configuration management

This module contains all production-ready components including performance optimization,
error handling with circuit breakers, observability, and configuration management.
"""

import asyncio
import logging
import os
import statistics
import threading
import time
from collections import defaultdict
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from .utils import log_with_context

# Check for optional dependencies
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

class ProductionOptimizer:
    """Production-grade performance optimization and monitoring"""

    def __init__(self):
        self.performance_metrics = {
            'method_timings': defaultdict(list),
            'memory_usage': [],
            'cpu_usage': [],
            'cache_performance': {},
            'optimization_applied': []
        }
        self.optimization_thresholds = {
            'slow_method_ms': 100,
            'high_memory_mb': 500,
            'high_cpu_percent': 80,
            'cache_miss_rate': 0.3
        }
        self.monitoring_active = False
        self.optimization_stats = {'optimizations_applied': 0, 'performance_improvements': 0}

    def start_monitoring(self):
        """Start performance monitoring"""
        self.monitoring_active = True
        if PSUTIL_AVAILABLE:
            self._start_system_monitoring()

    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False

    def profile_method(self, method_name: str, execution_time_ms: float, memory_delta_mb: float = 0):
        """Profile method execution"""
        if not self.monitoring_active:
            return

        self.performance_metrics['method_timings'][method_name].append(execution_time_ms)

        # Check for slow methods
        if execution_time_ms > self.optimization_thresholds['slow_method_ms']:
            self._suggest_optimization(method_name, 'slow_execution', execution_time_ms)

        # Keep only recent measurements (last 100)
        if len(self.performance_metrics['method_timings'][method_name]) > 100:
            self.performance_metrics['method_timings'][method_name] = \
                self.performance_metrics['method_timings'][method_name][-100:]

    def _start_system_monitoring(self):
        """Start system resource monitoring"""
        def monitor_resources():
            while self.monitoring_active:
                try:
                    # Memory monitoring
                    process = psutil.Process()
                    memory_mb = process.memory_info().rss / 1024 / 1024
                    self.performance_metrics['memory_usage'].append(memory_mb)

                    # CPU monitoring
                    cpu_percent = process.cpu_percent()
                    self.performance_metrics['cpu_usage'].append(cpu_percent)

                    # Check thresholds
                    if memory_mb > self.optimization_thresholds['high_memory_mb']:
                        self._suggest_optimization('system', 'high_memory', memory_mb)

                    if cpu_percent > self.optimization_thresholds['high_cpu_percent']:
                        self._suggest_optimization('system', 'high_cpu', cpu_percent)

                    # Keep only recent measurements
                    if len(self.performance_metrics['memory_usage']) > 100:
                        self.performance_metrics['memory_usage'] = \
                            self.performance_metrics['memory_usage'][-100:]
                    if len(self.performance_metrics['cpu_usage']) > 100:
                        self.performance_metrics['cpu_usage'] = \
                            self.performance_metrics['cpu_usage'][-100:]

                    time.sleep(5)  # Monitor every 5 seconds

                except Exception as e:
                    log_with_context('warning', f"Resource monitoring error: {e}", component='performance_monitor')
                    break

        # Start monitoring in background thread
        monitor_thread = threading.Thread(target=monitor_resources, daemon=True)
        monitor_thread.start()

    def _suggest_optimization(self, component: str, issue_type: str, value: float):
        """Suggest optimization based on performance issues"""
        suggestion = {
            'component': component,
            'issue_type': issue_type,
            'value': value,
            'timestamp': datetime.now(timezone.utc),
            'suggestion': self._get_optimization_suggestion(issue_type)
        }

        self.performance_metrics['optimization_applied'].append(suggestion)
        log_with_context('warning', f"Performance issue detected: {component} - {issue_type} ({value})", component='optimizer')

    def _get_optimization_suggestion(self, issue_type: str) -> str:
        """Get optimization suggestion for issue type"""
        suggestions = {
            'slow_execution': 'Consider caching results or optimizing algorithm',
            'high_memory': 'Consider implementing memory cleanup or reducing cache size',
            'high_cpu': 'Consider optimizing CPU-intensive operations or adding rate limiting',
            'cache_miss': 'Consider warming cache or adjusting cache strategy'
        }
        return suggestions.get(issue_type, 'Review performance characteristics')

    def optimize_cache_performance(self, cache_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize cache performance based on statistics"""
        optimizations = []

        hit_rate = cache_stats.get('hit_rate', 0.0)
        if hit_rate < (1.0 - self.optimization_thresholds['cache_miss_rate']):
            optimizations.append({
                'type': 'cache_tuning',
                'action': 'increase_cache_size',
                'current_hit_rate': hit_rate,
                'target_hit_rate': 0.8
            })

        cache_size = cache_stats.get('cache_size', 0)
        max_size = cache_stats.get('max_size', 1000)

        if cache_size / max_size > 0.9:  # Cache nearly full
            optimizations.append({
                'type': 'cache_sizing',
                'action': 'optimize_eviction_policy',
                'utilization': cache_size / max_size
            })

        if optimizations:
            self.optimization_stats['optimizations_applied'] += len(optimizations)

        return {
            'optimizations_suggested': optimizations,
            'current_performance': cache_stats,
            'optimization_applied': len(optimizations) > 0
        }

    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        report = {
            'monitoring_active': self.monitoring_active,
            'optimization_stats': self.optimization_stats,
            'performance_summary': {}
        }

        # Method timing analysis
        if self.performance_metrics['method_timings']:
            method_stats = {}
            for method, timings in self.performance_metrics['method_timings'].items():
                if timings:
                    method_stats[method] = {
                        'avg_time_ms': statistics.mean(timings),
                        'max_time_ms': max(timings),
                        'call_count': len(timings)
                    }
            report['performance_summary']['method_timings'] = method_stats

        # System resource analysis
        if PSUTIL_AVAILABLE and self.performance_metrics['memory_usage']:
            report['performance_summary']['system_resources'] = {
                'avg_memory_mb': statistics.mean(self.performance_metrics['memory_usage']),
                'max_memory_mb': max(self.performance_metrics['memory_usage']),
                'avg_cpu_percent': statistics.mean(self.performance_metrics['cpu_usage']) if self.performance_metrics['cpu_usage'] else 0
            }

        # Recent optimizations
        recent_optimizations = [
            opt for opt in self.performance_metrics['optimization_applied']
            if (datetime.now(timezone.utc) - opt['timestamp']).total_seconds() < 3600  # Last hour
        ]
        report['recent_optimizations'] = recent_optimizations

        return report

class ErrorHandler:
    """Production-grade error handling and resilience patterns"""

    def __init__(self):
        self.circuit_breakers = {}
        self.retry_configs = {
            'default': {'max_retries': 3, 'base_delay': 1.0, 'max_delay': 30.0, 'backoff_factor': 2.0}
        }
        self.error_stats = {
            'total_errors': 0,
            'circuit_breaker_trips': 0,
            'successful_retries': 0,
            'failed_retries': 0
        }

    def circuit_breaker(self, service_name: str, failure_threshold: int = 5,
                       recovery_timeout: int = 60, timeout: int = 30):
        """Circuit breaker decorator for external service calls"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                breaker = self._get_circuit_breaker(service_name, failure_threshold, recovery_timeout, timeout)

                if breaker['state'] == 'open':
                    if time.time() - breaker['last_failure'] > recovery_timeout:
                        breaker['state'] = 'half_open'
                        log_with_context('info', f"Circuit breaker {service_name} moved to half-open state", component='circuit_breaker')
                    else:
                        raise Exception(f"Circuit breaker {service_name} is open")

                try:
                    result = await asyncio.wait_for(func(*args, **kwargs), timeout=timeout)

                    if breaker['state'] == 'half_open':
                        breaker['state'] = 'closed'
                        breaker['failure_count'] = 0
                        log_with_context('info', f"Circuit breaker {service_name} recovered", component='circuit_breaker')

                    return result

                except Exception as e:
                    breaker['failure_count'] += 1
                    breaker['last_failure'] = time.time()

                    if breaker['failure_count'] >= failure_threshold:
                        breaker['state'] = 'open'
                        self.error_stats['circuit_breaker_trips'] += 1
                        log_with_context('error', f"Circuit breaker {service_name} tripped", component='circuit_breaker')

                    self.error_stats['total_errors'] += 1
                    raise

            return wrapper
        return decorator

    def _get_circuit_breaker(self, service_name: str, failure_threshold: int,
                           recovery_timeout: int, timeout: int) -> Dict[str, Any]:
        """Get or create circuit breaker for service"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = {
                'state': 'closed',  # closed, open, half_open
                'failure_count': 0,
                'last_failure': 0,
                'failure_threshold': failure_threshold,
                'recovery_timeout': recovery_timeout,
                'timeout': timeout
            }
        return self.circuit_breakers[service_name]

    async def retry_with_backoff(self, func, *args, config_name: str = 'default', **kwargs):
        """Retry function with exponential backoff"""
        config = self.retry_configs.get(config_name, self.retry_configs['default'])

        last_exception = None
        delay = config['base_delay']

        for attempt in range(config['max_retries'] + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)

                if attempt > 0:
                    self.error_stats['successful_retries'] += 1
                    log_with_context('info', f"Retry succeeded on attempt {attempt + 1}", component='retry_handler')

                return result

            except Exception as e:
                last_exception = e
                self.error_stats['total_errors'] += 1

                if attempt < config['max_retries']:
                    log_with_context('warning', f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}", component='retry_handler')
                    await asyncio.sleep(delay)
                    delay = min(delay * config['backoff_factor'], config['max_delay'])
                else:
                    self.error_stats['failed_retries'] += 1
                    log_with_context('error', f"All {config['max_retries']} retry attempts failed", component='retry_handler')

        raise last_exception

    def graceful_degradation(self, primary_func, fallback_func, fallback_threshold: float = 0.5):
        """Graceful degradation with fallback function"""
        async def wrapper(*args, **kwargs):
            try:
                # Check if primary service is healthy enough
                service_name = getattr(primary_func, '__name__', 'unknown')
                breaker = self.circuit_breakers.get(service_name)

                if breaker and breaker['state'] == 'open':
                    log_with_context('info', f"Using fallback for {service_name} due to circuit breaker", component='degradation')
                    return await fallback_func(*args, **kwargs)

                # Try primary function
                return await primary_func(*args, **kwargs)

            except Exception as e:
                log_with_context('warning', f"Primary function failed, using fallback: {e}", component='degradation')
                try:
                    return await fallback_func(*args, **kwargs)
                except Exception as fallback_error:
                    log_with_context('error', f"Fallback also failed: {fallback_error}", component='degradation')
                    raise e  # Raise original error

        return wrapper

    def get_error_stats(self) -> Dict[str, Any]:
        """Get error handling statistics"""
        return {
            **self.error_stats,
            'circuit_breakers': {
                name: {
                    'state': breaker['state'],
                    'failure_count': breaker['failure_count']
                }
                for name, breaker in self.circuit_breakers.items()
            }
        }

class ObservabilityManager:
    """Production observability with structured logging and metrics"""

    def __init__(self):
        self.metrics = defaultdict(int)
        self.custom_metrics = defaultdict(list)
        self.health_checks = {}
        self.structured_logger = self._setup_structured_logging()

    def _setup_structured_logging(self):
        """Setup structured logging for production"""
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s - '
            'correlation_id=%(correlation_id)s - component=%(component)s'
        )

        handler = logging.StreamHandler()
        handler.setFormatter(formatter)

        structured_logger = logging.getLogger('vibecode.structured')
        structured_logger.addHandler(handler)
        structured_logger.setLevel(logging.INFO)

        return structured_logger

    def log_structured(self, level: str, message: str, component: str = 'unknown',
                      correlation_id: Optional[str] = None, **kwargs):
        """Log with structured format"""
        extra = {
            'component': component,
            'correlation_id': correlation_id or self._generate_correlation_id(),
            **kwargs
        }

        log_method = getattr(self.structured_logger, level.lower(), self.structured_logger.info)
        log_method(message, extra=extra)

    def _generate_correlation_id(self) -> str:
        """Generate correlation ID for request tracing"""
        return f"req_{int(time.time() * 1000)}_{threading.current_thread().ident}"

    def increment_metric(self, metric_name: str, value: int = 1, tags: Optional[Dict[str, str]] = None):
        """Increment counter metric"""
        metric_key = f"{metric_name}:{':'.join(f'{k}={v}' for k, v in (tags or {}).items())}"
        self.metrics[metric_key] += value

    def record_metric(self, metric_name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Record gauge/histogram metric"""
        metric_key = f"{metric_name}:{':'.join(f'{k}={v}' for k, v in (tags or {}).items())}"
        self.custom_metrics[metric_key].append({
            'value': value,
            'timestamp': datetime.now(timezone.utc)
        })

        # Keep only recent values (last 1000)
        if len(self.custom_metrics[metric_key]) > 1000:
            self.custom_metrics[metric_key] = self.custom_metrics[metric_key][-1000:]

    def register_health_check(self, name: str, check_func):
        """Register health check function"""
        self.health_checks[name] = check_func

    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'checks': {}
        }

        overall_healthy = True

        for name, check_func in self.health_checks.items():
            try:
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()

                health_status['checks'][name] = {
                    'status': 'healthy' if result else 'unhealthy',
                    'details': result if isinstance(result, dict) else {}
                }

                if not result:
                    overall_healthy = False

            except Exception as e:
                health_status['checks'][name] = {
                    'status': 'error',
                    'error': str(e)
                }
                overall_healthy = False

        health_status['status'] = 'healthy' if overall_healthy else 'unhealthy'
        return health_status

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get metrics summary for monitoring"""
        summary = {
            'counters': dict(self.metrics),
            'gauges': {},
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        # Calculate gauge summaries
        for metric_name, values in self.custom_metrics.items():
            if values:
                recent_values = [v['value'] for v in values[-100:]]  # Last 100 values
                summary['gauges'][metric_name] = {
                    'current': recent_values[-1],
                    'avg': statistics.mean(recent_values),
                    'min': min(recent_values),
                    'max': max(recent_values),
                    'count': len(values)
                }

        return summary

class ConfigurationManager:
    """Production configuration management with environment support"""

    def __init__(self):
        self.config = {}
        self.feature_flags = {}
        self.environment = os.getenv('VIBECODE_ENV', 'development')
        self.config_sources = ['environment', 'file', 'defaults']
        self._load_configuration()

    def _load_configuration(self):
        """Load configuration from multiple sources"""
        # Default configuration
        self.config = {
            'performance': {
                'cache_size': 1000,
                'timeout_seconds': 30,
                'max_retries': 3,
                'circuit_breaker_threshold': 5
            },
            'logging': {
                'level': 'INFO',
                'structured': True,
                'correlation_tracking': True
            },
            'security': {
                'rate_limit_requests_per_minute': 100,
                'input_validation': True,
                'audit_logging': True
            },
            'features': {
                'ml_enhanced_recommendations': True,
                'temporal_tracking': True,
                'adaptive_learning': True,
                'predictive_analytics': True
            }
        }

        # Environment-specific overrides
        env_config = self._load_environment_config()
        self._deep_merge(self.config, env_config)

        # Feature flags
        self.feature_flags = self.config.get('features', {})

    def _load_environment_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        env_config = {}

        # Map environment variables to config structure
        env_mappings = {
            'VIBECODE_CACHE_SIZE': ('performance', 'cache_size', int),
            'VIBECODE_TIMEOUT': ('performance', 'timeout_seconds', int),
            'VIBECODE_LOG_LEVEL': ('logging', 'level', str),
            'VIBECODE_RATE_LIMIT': ('security', 'rate_limit_requests_per_minute', int),
            'VIBECODE_ML_ENABLED': ('features', 'ml_enhanced_recommendations', bool)
        }

        for env_var, (section, key, type_func) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    if section not in env_config:
                        env_config[section] = {}

                    # Convert boolean strings
                    if type_func == bool:
                        env_config[section][key] = value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        env_config[section][key] = type_func(value)

                except (ValueError, TypeError) as e:
                    log_with_context('warning', f"Invalid environment variable {env_var}: {e}", component='config_manager')

        return env_config

    def _deep_merge(self, base_dict: Dict[str, Any], override_dict: Dict[str, Any]):
        """Deep merge configuration dictionaries"""
        for key, value in override_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value

    def get_config(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key_path.split('.')
        value = self.config

        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def is_feature_enabled(self, feature_name: str) -> bool:
        """Check if feature flag is enabled"""
        return self.feature_flags.get(feature_name, False)

    def get_environment(self) -> str:
        """Get current environment"""
        return self.environment

    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment.lower() == 'production'

    def get_full_config(self) -> Dict[str, Any]:
        """Get complete configuration"""
        return {
            'config': self.config,
            'environment': self.environment,
            'feature_flags': self.feature_flags
        }

# Export all public symbols
__all__ = [
    'ProductionOptimizer',
    'ErrorHandler',
    'ObservabilityManager',
    'ConfigurationManager'
]
