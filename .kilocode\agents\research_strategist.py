import json
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from .base_agent import BaseAgent

class ResearchStrategist(BaseAgent):
    """
    Research Strategist agent specialized in information research, data analysis,
    and strategic information gathering using Gemini Flash.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Research Strategist agent.

        Args:
            config (Dict[str, Any]): Agent configuration
        """
        super().__init__(config)
        self.model = "google/gemini-flash"
        self.complexity_range = {"min": 3, "max": 8}
        self.optimal_complexity = [4, 5, 6, 7]
        self.quality_threshold = 7.0

        # Specialized capabilities
        self.domain_expertise = {
            "primary": [
                "information_research",
                "web_search",
                "data_analysis",
                "competitive_analysis",
                "market_research"
            ],
            "secondary": [
                "documentation_review",
                "trend_analysis",
                "technology_research",
                "best_practices_research"
            ]
        }

        # Research-specific keywords
        self.research_keywords = {
            "high_priority": [
                "research", "search", "find", "investigate", "analyze",
                "study", "explore", "discover", "gather", "collect"
            ],
            "medium_priority": [
                "information", "data", "documentation", "resources", "sources",
                "references", "examples", "samples", "tutorials", "guides"
            ],
            "low_priority": [
                "web", "internet", "online", "website", "url", "link"
            ],
            "analysis_types": [
                "competitive", "market", "trend", "technology", "comparative",
                "statistical", "qualitative", "quantitative", "strategic"
            ]
        }

        # Research methodologies
        self.research_methodologies = {
            "systematic_search": "Structured search with multiple sources",
            "comparative_analysis": "Side-by-side comparison methodology",
            "trend_analysis": "Historical and predictive trend analysis",
            "depth_research": "Deep-dive research with detailed analysis",
            "breadth_survey": "Wide-scope survey of available information"
        }

    def can_handle(self, request: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Determines if this agent can handle the given request.

        Args:
            request (Dict[str, Any]): The request to evaluate

        Returns:
            Tuple[bool, float]: (can_handle, confidence_score)
        """
        complexity = request.get("complexity", 0)
        message = request.get("message", "").lower()
        request_type = request.get("type", "general")

        # Base confidence from complexity
        if complexity < self.complexity_range["min"]:
            base_confidence = 0.4  # Can handle but simple
        elif complexity > self.complexity_range["max"]:
            base_confidence = 0.5  # Can handle but challenging
        elif complexity in self.optimal_complexity:
            base_confidence = 0.9  # Optimal range
        else:
            base_confidence = 0.7  # Good range

        # Keyword matching boost
        keyword_boost = 0.0

        # High priority keywords (research focus)
        high_priority_matches = sum(1 for keyword in self.research_keywords["high_priority"] if keyword in message)
        keyword_boost += high_priority_matches * 0.25

        # Medium priority keywords (information gathering)
        medium_priority_matches = sum(1 for keyword in self.research_keywords["medium_priority"] if keyword in message)
        keyword_boost += medium_priority_matches * 0.15

        # Analysis types keywords
        analysis_matches = sum(1 for keyword in self.research_keywords["analysis_types"] if keyword in message)
        keyword_boost += analysis_matches * 0.20

        # Domain expertise boost
        domain_boost = 0.0
        for domain in self.domain_expertise["primary"]:
            if domain.replace("_", " ") in message:
                domain_boost += 0.30
        for domain in self.domain_expertise["secondary"]:
            if domain.replace("_", " ") in message:
                domain_boost += 0.20

        # Request type boost
        type_boost = 0.0
        if request_type in ["research", "analysis", "information_gathering"]:
            type_boost = 0.25
        elif request_type in ["competitive_analysis", "market_research"]:
            type_boost = 0.30

        # Calculate final confidence
        confidence = min(base_confidence + keyword_boost + domain_boost + type_boost, 1.0)

        # Decision threshold
        can_handle = confidence >= 0.6

        return can_handle, confidence

    def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes a request using research and analysis expertise.

        Args:
            request (Dict[str, Any]): The request to process

        Returns:
            Dict[str, Any]: Processed response
        """
        start_time = time.time()

        # Extract request details
        message = request.get("message", "")
        complexity = request.get("complexity", 0)
        context = request.get("context", {})

        # Determine research strategy
        research_strategy = self._determine_research_strategy(message, complexity)

        # Process based on strategy
        if research_strategy == "systematic_search":
            response = self._process_systematic_search(message, context)
        elif research_strategy == "competitive_analysis":
            response = self._process_competitive_analysis(message, context)
        elif research_strategy == "trend_analysis":
            response = self._process_trend_analysis(message, context)
        elif research_strategy == "technology_research":
            response = self._process_technology_research(message, context)
        elif research_strategy == "market_research":
            response = self._process_market_research(message, context)
        else:
            response = self._process_general_research(message, context)

        # Calculate processing time
        processing_time = (time.time() - start_time) * 1000

        # Enhance response with research-specific metadata
        response.update({
            "agent": "ResearchStrategist",
            "model": self.model,
            "processing_time_ms": processing_time,
            "complexity_handled": complexity,
            "research_strategy": research_strategy,
            "quality_score": self._calculate_quality_score(response, complexity),
            "research_methodology": self._get_research_methodology(research_strategy),
            "information_sources": self._identify_information_sources(message),
            "research_depth": self._assess_research_depth(complexity),
            "confidence_level": self._assess_confidence_level(response),
            "timestamp": datetime.now().isoformat()
        })

        return response

    def _determine_research_strategy(self, message: str, complexity: int) -> str:
        """
        Determines the best research strategy based on message content and complexity.
        """
        message_lower = message.lower()

        # Competitive analysis keywords
        if any(keyword in message_lower for keyword in ["competitive", "competitor", "comparison", "vs", "versus"]):
            return "competitive_analysis"

        # Trend analysis keywords
        elif any(keyword in message_lower for keyword in ["trend", "trending", "future", "prediction", "forecast"]):
            return "trend_analysis"

        # Technology research keywords
        elif any(keyword in message_lower for keyword in ["technology", "tool", "framework", "library", "solution"]):
            return "technology_research"

        # Market research keywords
        elif any(keyword in message_lower for keyword in ["market", "industry", "business", "commercial", "enterprise"]):
            return "market_research"

        # Systematic search for complex queries
        elif complexity >= 6 or any(keyword in message_lower for keyword in ["comprehensive", "detailed", "thorough"]):
            return "systematic_search"

        # Default to general research
        else:
            return "general_research"

    def _process_systematic_search(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes systematic search requests.
        """
        return {
            "type": "systematic_search",
            "content": f"Systematic search strategy for: {message}",
            "search_methodology": [
                "Define search objectives and scope",
                "Identify primary and secondary sources",
                "Execute multi-source search strategy",
                "Validate and cross-reference findings",
                "Synthesize and analyze results"
            ],
            "search_sources": self._identify_search_sources(message),
            "search_terms": self._generate_search_terms(message),
            "information_hierarchy": self._establish_information_hierarchy(message),
            "validation_criteria": self._define_validation_criteria(message),
            "synthesis_framework": self._create_synthesis_framework(message)
        }

    def _process_competitive_analysis(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes competitive analysis requests.
        """
        return {
            "type": "competitive_analysis",
            "content": f"Competitive analysis for: {message}",
            "analysis_framework": [
                "Identify key competitors and market players",
                "Analyze competitive positioning and strategies",
                "Compare features, capabilities, and offerings",
                "Assess market share and performance metrics",
                "Identify competitive advantages and gaps"
            ],
            "competitor_identification": self._identify_competitors(message),
            "comparison_dimensions": self._define_comparison_dimensions(message),
            "competitive_intelligence": self._gather_competitive_intelligence(message),
            "market_positioning": self._analyze_market_positioning(message),
            "strategic_insights": self._generate_strategic_insights(message)
        }

    def _process_trend_analysis(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes trend analysis requests.
        """
        return {
            "type": "trend_analysis",
            "content": f"Trend analysis for: {message}",
            "analysis_methodology": [
                "Collect historical data and patterns",
                "Identify emerging trends and signals",
                "Analyze trend drivers and influences",
                "Project future trend trajectories",
                "Assess trend implications and impacts"
            ],
            "trend_identification": self._identify_trends(message),
            "historical_analysis": self._conduct_historical_analysis(message),
            "future_projections": self._create_future_projections(message),
            "impact_assessment": self._assess_trend_impacts(message),
            "strategic_recommendations": self._generate_trend_recommendations(message)
        }

    def _process_technology_research(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes technology research requests.
        """
        return {
            "type": "technology_research",
            "content": f"Technology research for: {message}",
            "research_approach": [
                "Define technology requirements and criteria",
                "Survey available technology landscape",
                "Evaluate technology maturity and adoption",
                "Analyze implementation considerations",
                "Provide technology recommendations"
            ],
            "technology_landscape": self._map_technology_landscape(message),
            "evaluation_criteria": self._establish_evaluation_criteria(message),
            "maturity_assessment": self._assess_technology_maturity(message),
            "implementation_analysis": self._analyze_implementation_factors(message),
            "recommendation_framework": self._create_recommendation_framework(message)
        }

    def _process_market_research(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes market research requests.
        """
        return {
            "type": "market_research",
            "content": f"Market research for: {message}",
            "research_methodology": [
                "Define market scope and segments",
                "Analyze market size and growth potential",
                "Identify key market drivers and barriers",
                "Study customer needs and preferences",
                "Assess market opportunities and threats"
            ],
            "market_segmentation": self._analyze_market_segmentation(message),
            "market_sizing": self._estimate_market_size(message),
            "customer_analysis": self._conduct_customer_analysis(message),
            "opportunity_assessment": self._assess_market_opportunities(message),
            "market_insights": self._generate_market_insights(message)
        }

    def _process_general_research(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes general research requests.
        """
        return {
            "type": "general_research",
            "content": f"General research approach for: {message}",
            "research_plan": [
                "Define research objectives and questions",
                "Identify relevant information sources",
                "Execute information gathering strategy",
                "Analyze and synthesize findings",
                "Present insights and recommendations"
            ],
            "information_gathering": self._plan_information_gathering(message),
            "analysis_approach": self._define_analysis_approach(message),
            "synthesis_method": self._establish_synthesis_method(message),
            "quality_assurance": self._implement_quality_assurance(message)
        }

    def _calculate_quality_score(self, response: Dict[str, Any], complexity: int) -> float:
        """
        Calculates quality score for the response.
        """
        base_score = 7.0  # ResearchStrategist baseline

        # Complexity handling bonus
        if complexity in self.optimal_complexity:
            base_score += 0.5
        elif complexity >= 6:
            base_score += 0.3

        # Response completeness bonus
        if len(response.get("content", "")) > 70:
            base_score += 0.3

        # Research-specific content bonus
        if any(key in response for key in ["search_methodology", "analysis_framework", "research_methodology"]):
            base_score += 0.4

        # Information sources bonus
        if any(key in response for key in ["search_sources", "information_sources", "validation_criteria"]):
            base_score += 0.3

        return min(base_score, 10.0)

    def _get_research_methodology(self, strategy: str) -> str:
        """
        Returns the research methodology for the given strategy.
        """
        methodologies = {
            "systematic_search": "Comprehensive multi-source systematic search",
            "competitive_analysis": "Structured competitive intelligence gathering",
            "trend_analysis": "Historical and predictive trend analysis",
            "technology_research": "Technology landscape mapping and evaluation",
            "market_research": "Market analysis and opportunity assessment",
            "general_research": "Flexible research approach based on objectives"
        }
        return methodologies.get(strategy, "Standard research methodology")

    def _identify_information_sources(self, message: str) -> List[str]:
        """
        Identifies relevant information sources for the research.
        """
        sources = [
            "Academic and research publications",
            "Industry reports and market research",
            "Company websites and documentation",
            "Professional networks and communities",
            "News and media sources",
            "Government and regulatory sources"
        ]

        # Add specific sources based on message content
        message_lower = message.lower()
        if "technology" in message_lower:
            sources.append("Technical documentation and API references")
        if "market" in message_lower:
            sources.append("Market intelligence platforms")
        if "competitive" in message_lower:
            sources.append("Competitive intelligence databases")

        return sources

    def _assess_research_depth(self, complexity: int) -> str:
        """
        Assesses the required research depth based on complexity.
        """
        if complexity <= 4:
            return "Surface-level research with key insights"
        elif complexity <= 6:
            return "Moderate-depth research with detailed analysis"
        else:
            return "Deep research with comprehensive analysis"

    def _assess_confidence_level(self, response: Dict[str, Any]) -> str:
        """
        Assesses confidence level in the research findings.
        """
        # Simple heuristic based on response completeness
        content_length = len(response.get("content", ""))

        if content_length > 100:
            return "High confidence - comprehensive research conducted"
        elif content_length > 50:
            return "Medium confidence - solid research foundation"
        else:
            return "Moderate confidence - initial research completed"

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Returns the agent's capabilities.
        """
        return {
            "agent_type": "ResearchStrategist",
            "model": self.model,
            "complexity_range": self.complexity_range,
            "optimal_complexity": self.optimal_complexity,
            "domain_expertise": self.domain_expertise,
            "specializations": [
                "Information Research and Gathering",
                "Competitive Analysis",
                "Market Research and Analysis",
                "Technology Research and Evaluation",
                "Trend Analysis and Forecasting",
                "Data Analysis and Synthesis",
                "Strategic Information Intelligence"
            ],
            "quality_threshold": self.quality_threshold,
            "research_strategies": [
                "systematic_search",
                "competitive_analysis",
                "trend_analysis",
                "technology_research",
                "market_research",
                "general_research"
            ],
            "research_methodologies": self.research_methodologies
        }

    # Helper methods for specific research types
    def _identify_search_sources(self, message: str) -> List[str]:
        """Identifies appropriate search sources."""
        return [
            "Academic databases and journals",
            "Professional industry publications",
            "Government and regulatory databases",
            "Company and organization websites",
            "News and media sources",
            "Social media and community forums"
        ]

    def _generate_search_terms(self, message: str) -> List[str]:
        """Generates effective search terms."""
        # Extract key terms from message
        base_terms = message.split()[:5]  # Simple extraction

        return [
            "Primary keywords from query",
            "Related and synonym terms",
            "Industry-specific terminology",
            "Technical and professional terms",
            "Contextual and associative terms"
        ]

    def _establish_information_hierarchy(self, message: str) -> Dict[str, str]:
        """Establishes information hierarchy."""
        return {
            "primary_sources": "Original research, primary data, authoritative sources",
            "secondary_sources": "Analysis, reviews, synthesized information",
            "supporting_sources": "Background information, context, supplementary data",
            "validation_sources": "Cross-reference and verification sources"
        }

    def _define_validation_criteria(self, message: str) -> List[str]:
        """Defines validation criteria for information."""
        return [
            "Source credibility and authority",
            "Information recency and relevance",
            "Data accuracy and consistency",
            "Cross-source verification",
            "Bias assessment and neutrality"
        ]

    def _create_synthesis_framework(self, message: str) -> Dict[str, str]:
        """Creates framework for synthesizing information."""
        return {
            "organization": "Thematic organization of findings",
            "analysis": "Critical analysis of information",
            "synthesis": "Integration and synthesis of insights",
            "conclusions": "Evidence-based conclusions and recommendations"
        }

    def _identify_competitors(self, message: str) -> List[str]:
        """Identifies key competitors for analysis."""
        return [
            "Direct competitors with similar offerings",
            "Indirect competitors with alternative solutions",
            "Emerging competitors and disruptors",
            "Market leaders and dominant players",
            "Niche players and specialists"
        ]

    def _define_comparison_dimensions(self, message: str) -> List[str]:
        """Defines dimensions for competitive comparison."""
        return [
            "Product features and capabilities",
            "Pricing and value proposition",
            "Market positioning and strategy",
            "Customer base and segments",
            "Performance and market share"
        ]

    def _gather_competitive_intelligence(self, message: str) -> Dict[str, str]:
        """Gathers competitive intelligence framework."""
        return {
            "public_information": "Websites, press releases, public filings",
            "market_research": "Industry reports and analyst insights",
            "customer_feedback": "Reviews, testimonials, case studies",
            "social_intelligence": "Social media presence and engagement"
        }

    def _analyze_market_positioning(self, message: str) -> Dict[str, str]:
        """Analyzes market positioning."""
        return {
            "value_proposition": "Core value proposition and differentiation",
            "target_market": "Primary and secondary target markets",
            "competitive_advantage": "Key competitive advantages and strengths",
            "market_strategy": "Go-to-market and competitive strategy"
        }

    def _generate_strategic_insights(self, message: str) -> List[str]:
        """Generates strategic insights from competitive analysis."""
        return [
            "Market gaps and opportunities identified",
            "Competitive threats and challenges",
            "Strategic positioning recommendations",
            "Differentiation and value creation opportunities"
        ]

    def _identify_trends(self, message: str) -> List[str]:
        """Identifies relevant trends."""
        return [
            "Current dominant trends in the space",
            "Emerging trends and early signals",
            "Technology and innovation trends",
            "Market and consumer behavior trends",
            "Regulatory and policy trends"
        ]

    def _conduct_historical_analysis(self, message: str) -> Dict[str, str]:
        """Conducts historical trend analysis."""
        return {
            "historical_patterns": "Analysis of historical patterns and cycles",
            "trend_evolution": "Evolution of trends over time",
            "driving_factors": "Key factors that drove historical changes",
            "impact_assessment": "Impact of historical trends on market and industry"
        }

    def _create_future_projections(self, message: str) -> Dict[str, str]:
        """Creates future trend projections."""
        return {
            "short_term": "6-12 month trend projections",
            "medium_term": "1-3 year trend outlook",
            "long_term": "3-5 year strategic trend forecast",
            "scenario_planning": "Multiple scenario planning and projections"
        }

    def _assess_trend_impacts(self, message: str) -> Dict[str, str]:
        """Assesses trend impacts."""
        return {
            "market_impact": "Impact on market dynamics and structure",
            "business_impact": "Impact on business models and operations",
            "technology_impact": "Impact on technology and innovation",
            "customer_impact": "Impact on customer needs and behaviors"
        }

    def _generate_trend_recommendations(self, message: str) -> List[str]:
        """Generates trend-based recommendations."""
        return [
            "Strategic positioning for emerging trends",
            "Investment and resource allocation guidance",
            "Risk mitigation for trend-related challenges",
            "Opportunity capture strategies"
        ]

    def _map_technology_landscape(self, message: str) -> Dict[str, str]:
        """Maps the technology landscape."""
        return {
            "established_technologies": "Mature and widely adopted technologies",
            "emerging_technologies": "New and emerging technology solutions",
            "disruptive_technologies": "Potentially disruptive technology innovations",
            "technology_ecosystem": "Technology ecosystem and interdependencies"
        }

    def _establish_evaluation_criteria(self, message: str) -> List[str]:
        """Establishes technology evaluation criteria."""
        return [
            "Technical capabilities and performance",
            "Maturity and stability of technology",
            "Adoption rate and market acceptance",
            "Implementation complexity and cost",
            "Long-term viability and roadmap"
        ]

    def _assess_technology_maturity(self, message: str) -> Dict[str, str]:
        """Assesses technology maturity."""
        return {
            "innovation_stage": "Research and development stage",
            "early_adoption": "Early adoption and pilot implementations",
            "market_adoption": "Market adoption and mainstream use",
            "maturity_stage": "Mature technology with widespread adoption"
        }

    def _analyze_implementation_factors(self, message: str) -> List[str]:
        """Analyzes technology implementation factors."""
        return [
            "Technical requirements and dependencies",
            "Integration complexity and challenges",
            "Resource requirements and costs",
            "Timeline and implementation roadmap",
            "Risk factors and mitigation strategies"
        ]

    def _create_recommendation_framework(self, message: str) -> Dict[str, str]:
        """Creates technology recommendation framework."""
        return {
            "selection_criteria": "Criteria for technology selection",
            "implementation_approach": "Recommended implementation approach",
            "risk_management": "Risk management and mitigation strategies",
            "success_metrics": "Metrics for measuring implementation success"
        }

    def _analyze_market_segmentation(self, message: str) -> Dict[str, str]:
        """Analyzes market segmentation."""
        return {
            "demographic_segments": "Age, gender, income-based segments",
            "geographic_segments": "Regional and location-based segments",
            "behavioral_segments": "Usage patterns and behavior-based segments",
            "psychographic_segments": "Lifestyle and value-based segments"
        }

    def _estimate_market_size(self, message: str) -> Dict[str, str]:
        """Estimates market size and potential."""
        return {
            "total_addressable_market": "Total market size and potential",
            "serviceable_addressable_market": "Serviceable market opportunity",
            "serviceable_obtainable_market": "Realistic market capture potential",
            "market_growth_rate": "Historical and projected market growth"
        }

    def _conduct_customer_analysis(self, message: str) -> Dict[str, str]:
        """Conducts customer analysis."""
        return {
            "customer_needs": "Primary and secondary customer needs",
            "customer_pain_points": "Key challenges and pain points",
            "customer_journey": "Customer journey and decision process",
            "customer_preferences": "Preferences and selection criteria"
        }

    def _assess_market_opportunities(self, message: str) -> List[str]:
        """Assesses market opportunities."""
        return [
            "Unmet needs and market gaps",
            "Emerging market segments and niches",
            "Technology-enabled opportunities",
            "Competitive displacement opportunities",
            "Market expansion possibilities"
        ]

    def _generate_market_insights(self, message: str) -> List[str]:
        """Generates market insights."""
        return [
            "Key market trends and dynamics",
            "Competitive landscape insights",
            "Customer behavior patterns",
            "Market opportunity prioritization",
            "Strategic market entry recommendations"
        ]

    def _plan_information_gathering(self, message: str) -> Dict[str, str]:
        """Plans information gathering approach."""
        return {
            "primary_research": "Direct information gathering and surveys",
            "secondary_research": "Existing research and published sources",
            "observational_research": "Market observation and trend monitoring",
            "expert_consultation": "Expert interviews and consultations"
        }

    def _define_analysis_approach(self, message: str) -> List[str]:
        """Defines analysis approach."""
        return [
            "Quantitative analysis of numerical data",
            "Qualitative analysis of textual information",
            "Comparative analysis across sources",
            "Trend analysis and pattern recognition",
            "SWOT and framework-based analysis"
        ]

    def _establish_synthesis_method(self, message: str) -> Dict[str, str]:
        """Establishes information synthesis method."""
        return {
            "data_integration": "Integration of multiple data sources",
            "insight_extraction": "Extraction of key insights and patterns",
            "conclusion_formation": "Formation of evidence-based conclusions",
            "recommendation_development": "Development of actionable recommendations"
        }

    def _implement_quality_assurance(self, message: str) -> List[str]:
        """Implements research quality assurance."""
        return [
            "Source verification and validation",
            "Cross-reference checking and confirmation",
            "Bias identification and mitigation",
            "Accuracy and completeness review",
            "Peer review and quality validation"
        ]
