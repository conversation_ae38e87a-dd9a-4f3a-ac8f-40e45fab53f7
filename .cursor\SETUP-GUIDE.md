# 🚀 VIBECODE V1.0 - SETUP & TROUBLESHOOTING GUIDE

**Version**: 1.0.0 | **Status**: PRODUCTION_READY | **Language**: Português (BR)

## 📋 CONFIGURAÇÃO RÁPIDA

### **1. Configuração de Ambiente**

Todas as API keys estão em: `.cursor/config/environment-complete.env`

```bash
# API Keys obrigatórias
TAVILY_API_KEY=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
EXA_API_KEY=fae6582d-4562-45be-8ce9-f6c0c3518c66
UPSTASH_CONTEXT7_API_KEY=your_context7_api_key_here  # obter em upstash.com
```

### **2. Instalação de MCPs**

**Windows (Método Recomendado):**
```bash
# Instalar via NPX (mais confiável)
npx -y @mcp/desktop-commander
npx -y @mcp/sequential-thinking
# Native task management - no external MCP needed
npx -y @mcp/context7-mcp
npx -y @mcp/tavily-mcp
npx -y @mcp/exa-mcp
```

### **3. Verificação**

```bash
# Verificar instalação
Get-Command npx
npm ls -g | findstr mcp

# Restart do Cursor após instalação
```

## 🔧 CONFIGURAÇÃO DO SISTEMA

### **Arquivo Principal**: `.cursor/mcp.json`

Configuração correta para Windows:
```json
{
  "mcpServers": {
    "desktop-commander": {
      "command": "npx",
      "args": ["@mcp/desktop-commander"]
    },
    "sequential-thinking": {
      "command": "npx", 
      "args": ["@mcp/sequential-thinking"]
    },

  }
}
```

### **Estrutura Consolidada**
```
.cursor/
├── master-config.json      # Configuração master
├── mcp.json               # MCPs principais
├── environment.json       # Variáveis de ambiente
├── config.json           # Configurações básicas
├── rules/                # Regras do sistema
└── SETUP-GUIDE.md        # Este guia
```

## 🚨 TROUBLESHOOTING

### **MCPs Não Carregam**

**Problema**: MCPs não aparecem na interface
**Soluções**:
1. Verificar instalação: `npx -y @mcp/nome-do-mcp`
2. Restart completo do Cursor
3. Verificar formato do `.cursor/mcp.json`
4. Verificar permissões: executar como Administrator

### **Permission Error no Windows**

**Erro**:
```
PermissionError: [Errno 13] Permission denied
```

**Soluções**:
1. Executar PowerShell como Administrator
2. Verificar antivírus (pode estar bloqueando)
3. Verificar permissões da pasta VIBECODE
4. Usar: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### **NPM/PNPM Conflicts**

**Problema**: Erro "npm ci command can only install with existing package-lock.json"
**Soluções**:
1. Verificar arquivo `.gitignore` tem `package-lock.json`
2. Usar apenas PNPM: `pnpm install`
3. Configurar Vercel com comandos PNPM corretos

### **Context7 API Issues**

**Problema**: Context7 não responde
**Soluções**:
1. Verificar API key em `environment-complete.env`
2. Obter nova key em upstash.com
3. Verificar limite da API
4. Usar Tavily como fallback

### **Arquivos Não Salvam**

**Problema**: Edições não são aplicadas
**Soluções**:
1. **≤200 linhas**: Usar Desktop Commander
2. **>200 linhas**: Usar Cursor Editor
3. Sempre verificar após operação
4. Verificar permissões de escrita

### **Performance Lenta**

**Problema**: Sistema lento ou trava
**Soluções**:
1. Verificar uso de memória: Task Manager
2. Limpar cache: `.cursor/cache/`
3. Reduzir MCPs simultâneos
4. Verificar antivírus interferindo

## ⚙️ CONFIGURAÇÕES AVANÇADAS

### **Otimização de Performance**

```json
{
  "cache": {
    "ttl": 300,
    "max_entries": 1000,
    "eviction_policy": "lru"
  },
  "timeout": {
    "mcp_activation": 200,
    "response_timeout": 30000
  }
}
```

### **Logs e Debugging**

```bash
# Verificar logs do sistema
Get-Content .cursor/logs/system.log -Tail 50

# Debug MCP específico
npx @mcp/desktop-commander --debug

# Verificar status completo
uv run python .cursor/scripts/finaltest.py
```

### **Backup e Restauração**

```bash
# Backup configurações essenciais
Copy-Item .cursor/mcp.json .cursor/mcp-backup.json
Copy-Item .cursor/master-config.json .cursor/master-config-backup.json

# Restaurar configurações
Copy-Item .cursor/mcp-backup.json .cursor/mcp.json
```

## 📊 COMPLIANCE

### **Estrutura Obrigatória**
- ✅ Qualidade ≥8/10 
- ✅ Workflow de 7 passos
- ✅ "Aprimore, Não Prolifere"
- ✅ Verificação pós-operação

### **Comandos de Verificação**
```bash
# Sync de regras
npm run sync:ai

# Validar Knowledge Graph
uv run python memory-bank/python/knowledge_graph_manager.py --validate_connections

# System validation
uv run python .cursor/scripts/vibecode_core_validator.py
```

## 🎯 REFERÊNCIA RÁPIDA

### **Roteamento de Agentes**
```
Complexidade 1-4: Manager (planejamento)
Complexidade 2-5: Advisor (revisão)
Complexidade 3-6: Strategist (pesquisa)
Complexidade 4-7: Executor (automação)
Complexidade 6-9: Coder (implementação)
Complexidade 8-10: Architect (arquitetura)
```

### **Prioridade de Pesquisa**
1. **Context7** (documentação de bibliotecas)
2. **Tavily** (pesquisa web geral)
3. **Exa** (pesquisa alternativa)

### **Ferramentas por Tamanho**
- **≤200 linhas**: Desktop Commander
- **>200 linhas**: Cursor Editor
- **Sempre**: Verificar resultado

---

## 📞 SUPORTE

Em caso de problemas persistentes:
1. Verificar este guia primeiro
2. Consultar `.cursor/rules/master_rule.mdc`
3. Executar diagnósticos automáticos
4. Reportar no GitHub Issues

**"Um Sistema, Zero Problemas"**