# Story 2.3: Daily Cash Flow Management

## Status

Approved

## Story

**As a** clinic financial manager,  
**I want** to track daily cash inflows and outflows in real-time,  
**so that** I can maintain optimal cash position and make informed financial decisions.

## Acceptance Criteria

1. **Cash Flow Tracking:**
   - Record all daily cash receipts and payments
   - Track cash by source (appointments, retail sales, other income)
   - Monitor cash outflows by category and vendor
   - Support multiple payment methods and cash registers
   - Real-time cash position updates

2. **Cash Register Management:**
   - Open and close cash registers with shift tracking
   - Record opening and closing balances
   - Track cash float and change fund management
   - Handle cash reconciliation and variance reporting
   - Support multiple registers and operators

3. **Daily Reconciliation:**
   - Automated cash flow summarization
   - Bank deposit preparation and tracking
   - Cash variance analysis and reporting
   - Daily cash flow statement generation
   - Integration with bank reconciliation system

4. **Cash Flow Analytics:**
   - Daily, weekly, and monthly cash flow trends
   - Cash flow forecasting based on historical data
   - Liquidity analysis and cash position monitoring
   - Operational cash flow vs. investment activities
   - Cash conversion cycle analysis

## Tasks / Subtasks

- [ ] Design cash flow database schema (AC: 1, 2)
  - [ ] Create cash_transactions table for all cash movements
  - [ ] Design cash_registers table for register management
  - [ ] Add daily_cash_summary table for reconciliation
  - [ ] Create cash_floats table for change fund tracking
  - [ ] Implement cash flow categorization system

- [ ] Build cash transaction recording system (AC: 1)
  - [ ] Create cash receipt entry interface
  - [ ] Implement cash payment recording
  - [ ] Add bulk transaction import functionality
  - [ ] Build transaction categorization system
  - [ ] Create real-time cash position display

- [ ] Implement cash register management (AC: 2)
  - [ ] Create register opening/closing procedures
  - [ ] Build shift management and operator tracking
  - [ ] Implement cash counting and verification
  - [ ] Add variance reporting and investigation
  - [ ] Create register performance analytics

- [ ] Develop daily reconciliation workflows (AC: 3)
  - [ ] Build automated cash flow summarization
  - [ ] Create bank deposit preparation interface
  - [ ] Implement variance analysis algorithms
  - [ ] Add daily cash flow statement generation
  - [ ] Build reconciliation approval workflows

- [ ] Build cash flow analytics dashboard (AC: 4)
  - [ ] Create real-time cash position monitoring
  - [ ] Implement cash flow trend analysis
  - [ ] Add cash flow forecasting algorithms
  - [ ] Build liquidity ratio calculations
  - [ ] Create cash flow performance metrics

- [ ] Implement cash forecasting system (AC: 4)
  - [ ] Build historical data analysis engine
  - [ ] Create seasonal pattern recognition
  - [ ] Implement appointment-based cash projections
  - [ ] Add scenario planning capabilities
  - [ ] Build cash flow alert system

- [ ] Develop cash security and controls (AC: 2, 3)
  - [ ] Implement cash handling authorization levels
  - [ ] Add cash limit monitoring and alerts
  - [ ] Create cash audit trail logging
  - [ ] Build segregation of duties controls
  - [ ] Implement cash variance investigation workflows

- [ ] Add integration with payment systems (AC: 1)
  - [ ] Integrate with POS systems and payment terminals
  - [ ] Connect with online payment gateways
  - [ ] Add PIX and instant payment tracking
  - [ ] Build payment method reconciliation
  - [ ] Create unified cash flow reporting

## Dev Notes

### System Architecture Context

[Source: architecture/01-system-overview-context.md]

- Cash flow operations use Edge Functions for real-time processing
- Server Actions handle cash transaction recording and reconciliation
- Real-time updates via Supabase channels for cash position monitoring
- PWA offline capability for cash entry during network outages

### Financial Data Model Requirements

[Source: architecture/03-data-model-rls-policies.md]

- All cash flow tables follow UUID + clinic_id + audit pattern
- Strict RLS policies for cash data access based on roles
- Comprehensive audit logging for all cash transactions
- Real-time data consistency for cash position accuracy
- Encryption for sensitive cash handling information

### API Surface & Cash Flow Endpoints

[Source: architecture/05-api-surface-edge-functions.md]

- POST /v1/finance/cash/transactions - Record cash transactions
- POST /v1/finance/cash/registers - Cash register management
- GET /v1/finance/cash/position - Real-time cash position
- POST /v1/finance/cash/reconcile - Daily reconciliation
- GET /v1/finance/cash/forecast - Cash flow forecasting

### Integration with Epic 1 Components

- Appointment system (Story 1.1) for service revenue tracking
- Patient Portal (Story 1.3) for payment confirmation integration
- Authentication system for role-based cash handling access
- Real-time notifications for cash alerts and reconciliation

### Integration with Epic 2 Components

- Story 2.1 (Accounts Payable) for cash outflow tracking
- Story 2.2 (Accounts Receivable) for cash inflow from payments
- Story 2.4 (Bank Reconciliation) for bank deposit verification

### Business Rules Context

[Source: PRD Core Functionality]

- Financeiro Essencial module: Caixa fecha < 2 h target
- P0 priority for daily cash management efficiency
- Success metric: Cash reconciliation accuracy ≥ 99%
- Real-time cash position visibility requirement

### Cash Handling Requirements

- Support for multiple Brazilian payment methods
- Integration with local POS systems and terminals
- Compliance with cash handling regulations
- Real-time fraud detection and prevention
- Automated cash variance investigation

### Performance Requirements

[Source: PRD requirements]

- Cash transaction recording ≤ 1 second
- Real-time cash position updates ≤ 2 seconds
- Daily reconciliation processing ≤ 30 seconds
- Cash flow reporting ≤ 3 seconds
- Bulk transaction import ≤ 60 seconds for 1000+ transactions

### File Structure Context

- Cash flow routes: app/dashboard/finance/cash-flow/
- Cash register components: components/finance/cash-registers/
- Transaction recording: components/finance/transactions/
- Cash analytics: components/finance/cash-analytics/
- Reconciliation: components/finance/reconciliation/

### Database Schema Design

**cash_transactions table:**

- id (UUID, PK)
- clinic_id (UUID, FK)
- register_id (UUID, FK, nullable)
- transaction_type (ENUM: receipt, payment, transfer, adjustment)
- amount (DECIMAL)
- payment_method (ENUM: cash, card, pix, transfer, check)
- category (VARCHAR)
- description (TEXT)
- reference_id (UUID, nullable) // Link to invoice, payment, etc.
- recorded_by (UUID, FK)
- transaction_date (TIMESTAMP)

**cash_registers table:**

- id (UUID, PK)
- clinic_id (UUID, FK)
- name (VARCHAR)
- location (VARCHAR)
- opening_balance (DECIMAL)
- current_balance (DECIMAL)
- status (ENUM: open, closed, reconciling)
- operator_id (UUID, FK)
- opened_at (TIMESTAMP)
- closed_at (TIMESTAMP, nullable)

**daily_cash_summary table:**

- id (UUID, PK)
- clinic_id (UUID, FK)
- summary_date (DATE)
- opening_balance (DECIMAL)
- total_receipts (DECIMAL)
- total_payments (DECIMAL)
- closing_balance (DECIMAL)
- bank_deposits (DECIMAL)
- variance_amount (DECIMAL)
- reconciled_by (UUID, FK)
- reconciled_at (TIMESTAMP)

**cash_floats table:**

- id (UUID, PK)
- clinic_id (UUID, FK)
- register_id (UUID, FK)
- denomination (DECIMAL) // 0.05, 0.10, 0.25, 0.50, 1.00, etc.
- quantity (INTEGER)
- total_value (DECIMAL)
- float_date (DATE)

### Security & Compliance

[Source: architecture/06-security-compliance.md]

- Role-based access for cash handling operations
- Segregation of duties for cash reconciliation
- Comprehensive audit trails for all cash movements
- Cash limit controls and authorization levels
- Real-time monitoring for unusual cash activities

### Testing

**Testing Standards:**

- Jest unit tests for cash calculation algorithms
- Integration tests for payment method recording
- E2E tests for complete cash flow workflows
- Security tests for cash handling authorization
- Performance tests for real-time cash position updates

**Testing Requirements for this Story:**

- Unit tests for cash transaction calculations
- Integration tests for register management workflows
- E2E tests for daily reconciliation processes
- Security tests for cash handling access controls
- Performance tests for real-time cash monitoring
- Cash variance calculation accuracy tests

**Key Test Scenarios:**

- Cash register opening and closing procedures
- Multi-payment method transaction recording
- Daily cash reconciliation with bank deposits
- Cash variance detection and investigation
- Real-time cash position monitoring
- Cash flow forecasting accuracy
- Payment method integration testing

### Brazilian Cash Management Compliance

- Central Bank regulations for cash handling
- Tax authority requirements for cash transactions
- Anti-money laundering (AML) compliance
- Cash transaction reporting thresholds
- Integration with SPED fiscal systems

### Cash Flow Intelligence Features

- Machine learning for cash flow pattern recognition
- Predictive analytics for cash requirements
- Seasonal trend analysis and forecasting
- Anomaly detection for unusual cash movements
- Automated cash optimization recommendations

### Integration Points with External Systems

- POS system integration for transaction import
- Payment gateway integration for digital payments
- Banking API integration for real-time balances
- Accounting system integration for financial reporting
- Tax system integration for compliance reporting

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-18 | 1.0 | Initial story creation from Epic 2 | Scrum Master |

## Dev Agent Record

### Agent Model Used

*To be populated by development agent*

### Debug Log References

*To be populated by development agent*

### Completion Notes List

*To be populated by development agent*

### File List

*To be populated by development agent*

## QA Results

*To be populated by QA agent*
