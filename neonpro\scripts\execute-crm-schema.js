// Script para executar o schema CRM no Supabase usando MCP
// Configurações do Supabase
const supabaseUrl = "https://gfkskrkbnawkuppazkpt.supabase.co";
const supabaseAccessToken = "********************************************";
const projectRef = "gfkskrkbnawkuppazkpt";

console.log("🚀 Executando Schema CRM no Supabase via MCP...");
console.log("📊 URL:", supabaseUrl);
console.log("🔑 Project Ref:", projectRef);

// Para executar: node execute-crm-schema.js
