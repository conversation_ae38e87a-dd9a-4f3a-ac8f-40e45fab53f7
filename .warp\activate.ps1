# WARP CLI - <PERSON><PERSON><PERSON><PERSON> ACTIVATION SCRIPT
Write-Host "Activating Warp CLI with VIBECODE Configuration..." -ForegroundColor Cyan

# Check .warp directory
if (Test-Path ".warp") {
    Write-Host "Found .warp directory" -ForegroundColor Green
} else {
    Write-Host "Error: .warp directory not found" -ForegroundColor Red
    exit 1
}

# Set environment variables
$env:WARP_CONFIG_PATH = "$PWD\.warp"
$env:WARP_QUALITY_THRESHOLD = "8"
$env:WARP_WORKFLOW_ENABLED = "true"

Write-Host "Environment variables set" -ForegroundColor Green

# Check key files
$files = @(
    ".warp\config.json",
    ".warp\master-rules.md",
    ".warp\coding-standards.md",
    ".warp\mcps.json"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Found: $file" -ForegroundColor Green
    } else {
        Write-Host "Missing: $file" -ForegroundColor Red
    }
}

# Create backup directory
if (!(Test-Path "E:\CODE-BACKUP")) {
    New-Item -ItemType Directory -Path "E:\CODE-BACKUP" -Force | Out-Null
    Write-Host "Created backup directory" -ForegroundColor Green
}

Write-Host ""
Write-Host "WARP CLI CONFIGURED WITH VIBECODE V1.0!" -ForegroundColor Green
Write-Host "Quality Threshold: 8/10 minimum" -ForegroundColor White
Write-Host "7-Step Workflow: Active" -ForegroundColor White
Write-Host "Task Management: Auto-activation at complexity 3+" -ForegroundColor White
Write-Host "Research Protocol: Mandatory 3-source synthesis" -ForegroundColor White
Write-Host ""
Write-Host "Configuration Status: ACTIVE" -ForegroundColor Green
