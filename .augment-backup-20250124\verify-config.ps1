# 🔧 Augment Code Configuration Verification Script
# Verifica se todas as configurações MCP estão corretas

Write-Host "🚀 Verificando configuração do Augment Code..." -ForegroundColor Green

# Verificar arquivos de configuração
$projectRoot = "E:\VIBECODE"
$envFile = Join-Path $projectRoot ".cursor\config\environment-complete.env"
$mcpConfig = Join-Path $projectRoot ".augment\mcp.json"
$augmentEnv = Join-Path $projectRoot ".augment\environment.json"

Write-Host "`n📁 Verificando arquivos de configuração:" -ForegroundColor Yellow

# Environment file
if (Test-Path $envFile) {
  Write-Host "  ✅ environment-complete.env: ENCONTRADO" -ForegroundColor Green
}
else {
  Write-Host "  ❌ environment-complete.env: NÃO ENCONTRADO" -ForegroundColor Red
}

# MCP config
if (Test-Path $mcpConfig) {
  Write-Host "  ✅ .augment/mcp.json: ENCONTRADO" -ForegroundColor Green
}
else {
  Write-Host "  ❌ .augment/mcp.json: NÃO ENCONTRADO" -ForegroundColor Red
}

# Augment environment
if (Test-Path $augmentEnv) {
  Write-Host "  ✅ .augment/environment.json: ENCONTRADO" -ForegroundColor Green
}
else {
  Write-Host "  ❌ .augment/environment.json: NÃO ENCONTRADO" -ForegroundColor Red
}

# Verificar API keys no environment file
if (Test-Path $envFile) {
  Write-Host "`n🔑 Verificando API Keys:" -ForegroundColor Yellow

  $envContent = Get-Content $envFile -Raw

  $apiKeys = @(
    "TAVILY_API_KEY",
    "EXA_API_KEY",
    "UPSTASH_CONTEXT7_API_KEY",
    "SENTRY_ACCESS_TOKEN"
  )

  foreach ($key in $apiKeys) {
    if ($envContent -match "$key=(.+)") {
      $value = $matches[1].Trim()
      if ($value -like "*your_*" -or $value -like "*_here*" -or $value -eq "") {
        Write-Host "  ⚠️ $key`: PRECISA CONFIGURAR" -ForegroundColor Yellow
      }
      else {
        Write-Host "  ✅ $key`: CONFIGURADA" -ForegroundColor Green
      }
    }
    else {
      Write-Host "  ❌ $key`: NÃO ENCONTRADA" -ForegroundColor Red
    }
  }
}

# Verificar servidores MCP configurados
if (Test-Path $mcpConfig) {
  Write-Host "`n🔧 Servidores MCP configurados:" -ForegroundColor Yellow

  try {
    $mcpData = Get-Content $mcpConfig -Raw | ConvertFrom-Json
    $servers = $mcpData.mcpServers.PSObject.Properties.Name

    foreach ($server in $servers) {
      Write-Host "  • $server" -ForegroundColor Cyan
    }

    Write-Host "`n📊 Total de servidores MCP: $($servers.Count)" -ForegroundColor Green
  }
  catch {
    Write-Host "  ❌ Erro ao ler configuração MCP" -ForegroundColor Red
  }
}

Write-Host "`n🎯 RESUMO DA CONFIGURAÇÃO:" -ForegroundColor Magenta
Write-Host "  • Arquivos de configuração: $(if (Test-Path $mcpConfig) { 'OK' } else { 'FALTANDO' })" -ForegroundColor $(if (Test-Path $mcpConfig) { 'Green' } else { 'Red' })
Write-Host "  • Variáveis de ambiente: $(if (Test-Path $envFile) { 'OK' } else { 'FALTANDO' })" -ForegroundColor $(if (Test-Path $envFile) { 'Green' } else { 'Red' })

Write-Host "`n📋 PRÓXIMOS PASSOS:" -ForegroundColor Yellow
Write-Host "  1. Reinicie o Augment Code para carregar as configurações"
Write-Host "  2. Teste os MCPs usando as ferramentas disponíveis"
Write-Host "  3. Configure API keys pendentes se necessário"

Write-Host "`n✅ Verificação concluída!" -ForegroundColor Green
