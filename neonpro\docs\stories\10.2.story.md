# Story 10.2: Automação de Campanhas e Marketing

## Story Overview

**Como** gerente de marketing da clínica  
**Eu quero** sistema de automação de campanhas multicanais com workflows personalizáveis  
**Para que** eu possa executar campanhas eficazes com mínimo esforço manual e máximo engajamento

### Story Details

- **Epic**: Epic 10 - CRM & Campanhas
- **Story Points**: 10
- **Priority**: P1 (High)
- **Theme**: Marketing Automation & Campaign Management
- **Dependencies**: Story 10.1 (Patient Segmentation)

### Acceptance Criteria

#### AC1: Criação e Configuração de Campanhas

- [ ] **GIVEN** acesso ao sistema de campanhas
- [ ] **WHEN** crio nova campanha
- [ ] **THEN** posso definir nome, objetivo e público-alvo (Story 10.1)
- [ ] **AND** configurar multicanais (email, SMS, WhatsApp, push)
- [ ] **AND** definir cronograma de envio (imediato, agendado, recorrente)
- [ ] **AND** criar templates personalizados para cada canal
- [ ] **AND** configurar A/B testing com variações de conteúdo
- [ ] **AND** definir critérios de sucesso e KPIs

#### AC2: Templates e Editor de Conteúdo

- [ ] **GIVEN** configuração de campanha
- [ ] **WHEN** crio conteúdo da campanha
- [ ] **THEN** tenho editor visual drag-and-drop para emails
- [ ] **AND** templates responsivos pré-configurados
- [ ] **AND** personalização dinâmica com dados do paciente
- [ ] **AND** biblioteca de imagens e assets compartilhados
- [ ] **AND** preview em diferentes dispositivos e clientes de email
- [ ] **AND** validação automática de links e imagens
- [ ] **AND** compliance automático com LGPD/GDPR

#### AC3: Workflows Automatizados e Triggers

- [ ] **GIVEN** campanha configurada
- [ ] **WHEN** configuro automação
- [ ] **THEN** posso criar workflows baseados em triggers específicos
- [ ] **AND** triggers por data (aniversário, última consulta)
- [ ] **AND** triggers por comportamento (agenda consulta, cancela)
- [ ] **AND** triggers por status financeiro (pagamento, inadimplência)
- [ ] **AND** sequências automáticas com delays personalizáveis
- [ ] **AND** condições e bifurcações no workflow
- [ ] **AND** re-engagement automático para não respondentes

#### AC4: Execução e Monitoramento em Tempo Real

- [ ] **GIVEN** campanha ativa
- [ ] **WHEN** monitoro execução
- [ ] **THEN** visualizo status de envio em tempo real
- [ ] **AND** métricas de entrega por canal (entregues, falhas, bounces)
- [ ] **AND** taxas de abertura e cliques atualizadas dinamicamente
- [ ] **AND** conversões e agendamentos gerados pela campanha
- [ ] **AND** alertas automáticos para problemas de entrega
- [ ] **AND** pausar/retomar campanhas manualmente

#### AC5: Personalização Avançada e Segmentação Dinâmica

- [ ] **GIVEN** base de dados de pacientes (Epic 9)
- [ ] **WHEN** personalizo conteúdo
- [ ] **THEN** insiro campos dinâmicos (nome, próxima consulta, profissional)
- [ ] **AND** recomendações personalizadas de procedimentos
- [ ] **AND** ofertas baseadas no histórico do paciente
- [ ] **AND** horários de envio otimizados por perfil do paciente
- [ ] **AND** frequência de comunicação respeitando preferências
- [ ] **AND** exclusão automática de pacientes descadastrados

#### AC6: Compliance e Gestão de Opt-out

- [ ] **GIVEN** regulamentações de privacidade
- [ ] **WHEN** envio campanhas
- [ ] **THEN** incluo automaticamente links de descadastro
- [ ] **AND** respeito preferências de canal por paciente (Epic 5)
- [ ] **AND** mantenho histórico de consentimentos
- [ ] **AND** bloqueio automático para pacientes opt-out
- [ ] **AND** relatórios de compliance para auditoria
- [ ] **AND** integração com política de privacidade da clínica

### Technical Requirements

#### Campaign Management System

```typescript
// Sistema de Gestão de Campanhas
interface CampanhaMarketing {
  id: string
  nome: string
  descricao?: string
  objetivo: ObjetivoCampanha
  
  // Configuração de Público
  segmentoId: string // Referência ao Story 10.1
  filtrosAdicionais?: CriteriosSegmentacao
  tamanhoPublico: number
  
  // Configuração de Canais
  canais: CanaisCampanha
  
  // Configuração de Conteúdo
  templates: TemplatesPorCanal
  personalizacao: ConfigPersonalizacao
  
  // Configuração de Cronograma
  cronograma: CronogramaCampanha
  
  // Automação e Workflows
  workflows: WorkflowAutomacao[]
  triggers: TriggerCampanha[]
  
  // A/B Testing
  abTesting?: ConfigABTesting
  
  // Métricas e Resultados
  metricas: MetricasCampanha
  
  // Status e Controle
  status: StatusCampanha
  criadaPor: string
  criadaEm: Date
  
  // Compliance
  configuracaoCompliance: ConfigCompliance
  
  // Metadados
  tags: string[]
  categoria: CategoriaCampanha
  orcamento?: number
  roiEsperado?: number
}

// Configuração de Canais
interface CanaisCampanha {
  email?: ConfigCanal
  sms?: ConfigCanal
  whatsapp?: ConfigCanal
  push?: ConfigCanal
  ligacao?: ConfigCanal
}

interface ConfigCanal {
  ativo: boolean
  prioridade: number
  configuracoes: ConfiguracaoEspecificaCanal
  limiteTentativas: number
  intervaloTentativas: number // em minutos
  horarioEnvio: HorarioEnvio
  diasSemana: DiaSemana[]
}

// Template de Conteúdo
interface TemplateCampanha {
  id: string
  nome: string
  canal: CanalComunicacao
  tipo: TipoTemplate
  
  // Conteúdo
  assunto?: string // Para email
  conteudoHTML?: string
  conteudoTexto: string
  
  // Assets
  imagens: ImagemTemplate[]
  anexos?: AnexoTemplate[]
  
  // Personalização
  camposDinamicos: CampoDinamico[]
  condicoesDinamicas: CondicaoDinamica[]
  
  // Design
  tema: TemaVisual
  responsivo: boolean
  
  // Validação
  validado: boolean
  errosValidacao: string[]
  
  // Metadados
  criadoPor: string
  criadoEm: Date
  ultimaEdicao: Date
  categoria: CategoriaTemplate
  publico: boolean // Disponível para outras campanhas
}

// Workflow de Automação
interface WorkflowAutomacao {
  id: string
  nome: string
  descricao?: string
  
  // Trigger Inicial
  triggerInicial: TriggerCampanha
  
  // Etapas do Workflow
  etapas: EtapaWorkflow[]
  
  // Configurações Globais
  configuracoes: ConfigWorkflow
  
  // Status
  ativo: boolean
  pausado: boolean
  
  // Métricas
  pacientesAtivos: number
  pacientesCompletaram: number
  taxaCompletude: number
  
  // Metadados
  criadoEm: Date
  ultimaExecucao?: Date
}

// Etapa do Workflow
interface EtapaWorkflow {
  id: string
  ordem: number
  nome: string
  tipo: TipoEtapaWorkflow
  
  // Configuração da Etapa
  configuracao: ConfigEtapaWorkflow
  
  // Condições
  condicoes?: CondicaoEtapa[]
  acaoSeCondicaoVerdadeira?: AcaoWorkflow
  acaoSeCondicaoFalsa?: AcaoWorkflow
  
  // Delays
  delay?: DelayEtapa
  
  // Métricas
  pacientesExecutaram: number
  pacientesCompletaram: number
  taxaSucesso: number
}

// Trigger de Campanha
interface TriggerCampanha {
  id: string
  nome: string
  tipo: TipoTrigger
  
  // Configuração do Trigger
  configuracao: ConfigTrigger
  
  // Condições
  condicoes: CondicaoTrigger[]
  
  // Frequência
  frequencia: FrequenciaTrigger
  limitePorPaciente?: number
  
  // Janela de Tempo
  janelaTempoMinutos?: number
  horariosPermitidos?: HorarioEnvio[]
  
  // Status
  ativo: boolean
  ultimaExecucao?: Date
  proximaExecucao?: Date
}

// Métricas de Campanha
interface MetricasCampanha {
  // Estatísticas de Envio
  totalEnviado: number
  totalEntregue: number
  totalFalhas: number
  totalBounces: number
  
  // Engajamento por Canal
  metricas: {
    [canal: string]: MetricasCanal
  }
  
  // Conversões
  totalAberturas: number
  totalCliques: number
  taxaAbertura: number
  taxaClique: number
  taxaConversao: number
  
  // ROI e Financeiro
  custoTotal: number
  receitaGerada: number
  roi: number
  custosPorConversao: number
  
  // Comportamento
  agendamentosGerados: number
  cancelamentosGerados: number
  pacientesEngajados: number
  
  // Temporal
  melhorDiaEnvio: DiaSemana
  melhorHorarioEnvio: string
  
  // Atualização
  ultimaAtualizacao: Date
}

// Métricas por Canal
interface MetricasCanal {
  canal: CanalComunicacao
  enviado: number
  entregue: number
  aberto: number
  clicado: number
  respondido: number
  convertido: number
  
  // Taxas
  taxaEntrega: number
  taxaAbertura: number
  taxaClique: number
  taxaResposta: number
  taxaConversao: number
  
  // Custos
  custoEnvio: number
  custoPorClique: number
  custoPorConversao: number
  
  // Problemas
  bounces: number
  reclamacoes: number
  descadastros: number
  bloqueios: number
}

// Configuração A/B Testing
interface ConfigABTesting {
  ativo: boolean
  nomeExperimento: string
  
  // Variações
  variacoes: VariacaoAB[]
  
  // Configuração do Teste
  percentualTeste: number // Porcentagem do público para teste
  metricaObjetivo: MetricaObjetivo
  significanciaEstatistica: number // 95%, 99%
  
  // Duração
  duracaoMinimaHoras: number
  tamanhoAmostraMinimo: number
  
  // Resultados
  vencedor?: string
  confiancaResultado?: number
  
  // Status
  status: StatusABTesting
  iniciadoEm?: Date
  finalizadoEm?: Date
}

// Variação A/B
interface VariacaoAB {
  id: string
  nome: string
  percentualTrafico: number
  
  // Conteúdo da Variação
  templates: TemplatesPorCanal
  configuracoes: ConfiguracaoVariacao
  
  // Resultados
  metricas: MetricasCampanha
  
  // Status
  ativa: boolean
}

// Configuração de Compliance
interface ConfigCompliance {
  // LGPD/GDPR
  incluirLinkDescadastro: boolean
  incluirRazaoContato: boolean
  respeitarPreferenciasCanal: boolean
  
  // Frequência
  limitePorDia?: number
  limitePorSemana?: number
  limitePorMes?: number
  
  // Horários
  respeitarHorarioComercial: boolean
  horarioComercialInicio: string
  horarioComercialFim: string
  
  // Exclusões
  excluirOptOut: boolean
  excluirInativos: boolean
  excluirBloqueados: boolean
  
  // Auditoria
  logCompleto: boolean
  retencaoLogs: number // dias
}

// Tipos Enum para Campanhas
type ObjetivoCampanha = 'aquisicao' | 'retencao' | 'reativacao' | 'upsell' | 'cobranca' | 'educativo'
type StatusCampanha = 'rascunho' | 'agendada' | 'executando' | 'pausada' | 'concluida' | 'cancelada'
type CanalComunicacao = 'email' | 'sms' | 'whatsapp' | 'push' | 'ligacao'
type TipoTemplate = 'promocional' | 'transacional' | 'educativo' | 'cobranca' | 'lembrete'
type TipoTrigger = 'data' | 'comportamento' | 'evento' | 'condicao' | 'manual'
type TipoEtapaWorkflow = 'envio' | 'delay' | 'condicao' | 'acao' | 'tag'
type StatusABTesting = 'configurando' | 'executando' | 'analisando' | 'concluido'
type MetricaObjetivo = 'abertura' | 'clique' | 'conversao' | 'agendamento'
type CategoriaCampanha = 'promocional' | 'educativa' | 'retencao' | 'aquisicao' | 'cobranca'
```

#### Database Schema for Campaign Management

```sql
-- Campanhas de Marketing
CREATE TABLE campanhas_marketing (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  objetivo objetivo_campanha_type NOT NULL,
  
  -- Público-Alvo
  segmento_id UUID REFERENCES segmentos_pacientes(id),
  filtros_adicionais JSONB DEFAULT '{}',
  tamanho_publico INTEGER DEFAULT 0,
  
  -- Configuração de Canais
  canais_configuracao JSONB NOT NULL,
  
  -- Cronograma
  cronograma JSONB NOT NULL,
  timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',
  
  -- Templates e Conteúdo
  templates JSONB DEFAULT '{}',
  personalizacao JSONB DEFAULT '{}',
  
  -- A/B Testing
  ab_testing JSONB,
  
  -- Compliance
  config_compliance JSONB NOT NULL,
  
  -- Métricas e Resultados
  metricas JSONB DEFAULT '{}',
  custo_total DECIMAL(15,2) DEFAULT 0,
  receita_gerada DECIMAL(15,2) DEFAULT 0,
  roi DECIMAL(10,4) DEFAULT 0,
  
  -- Status e Controle
  status status_campanha_type DEFAULT 'rascunho',
  data_inicio TIMESTAMPTZ,
  data_fim TIMESTAMPTZ,
  pausada_em TIMESTAMPTZ,
  pausada_por UUID REFERENCES auth.users(id),
  motivo_pausa TEXT,
  
  -- Metadados
  tags TEXT[],
  categoria categoria_campanha_type,
  orcamento DECIMAL(15,2),
  roi_esperado DECIMAL(10,4),
  
  -- Auditoria
  criada_por UUID NOT NULL REFERENCES auth.users(id),
  criada_em TIMESTAMPTZ DEFAULT NOW(),
  ultima_edicao TIMESTAMPTZ DEFAULT NOW(),
  editada_por UUID REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_campanha_nome_valido CHECK (LENGTH(TRIM(nome)) > 0),
  CONSTRAINT chk_tamanho_publico_positivo CHECK (tamanho_publico >= 0),
  CONSTRAINT chk_datas_campanha CHECK (data_fim IS NULL OR data_fim > data_inicio)
);

-- Templates de Campanhas
CREATE TABLE templates_campanhas (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(255) NOT NULL,
  canal canal_comunicacao_type NOT NULL,
  tipo tipo_template_type NOT NULL,
  
  -- Conteúdo
  assunto VARCHAR(500), -- Para emails
  conteudo_html TEXT,
  conteudo_texto TEXT NOT NULL,
  
  -- Assets
  imagens JSONB DEFAULT '[]',
  anexos JSONB DEFAULT '[]',
  
  -- Personalização
  campos_dinamicos JSONB DEFAULT '[]',
  condicoes_dinamicas JSONB DEFAULT '[]',
  
  -- Design e Layout
  tema JSONB DEFAULT '{}',
  responsivo BOOLEAN DEFAULT TRUE,
  css_customizado TEXT,
  
  -- Validação
  validado BOOLEAN DEFAULT FALSE,
  erros_validacao TEXT[],
  ultima_validacao TIMESTAMPTZ,
  
  -- Métricas de Uso
  vezes_usado INTEGER DEFAULT 0,
  ultima_utilizacao TIMESTAMPTZ,
  
  -- Metadados
  categoria categoria_template_type,
  publico BOOLEAN DEFAULT FALSE,
  aprovado_por UUID REFERENCES auth.users(id),
  aprovado_em TIMESTAMPTZ,
  
  -- Auditoria
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  ultima_edicao TIMESTAMPTZ DEFAULT NOW(),
  editado_por UUID REFERENCES auth.users(id),
  ativo BOOLEAN DEFAULT TRUE,
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_template_nome_valido CHECK (LENGTH(TRIM(nome)) > 0),
  CONSTRAINT chk_conteudo_texto_presente CHECK (LENGTH(TRIM(conteudo_texto)) > 0)
);

-- Workflows de Automação
CREATE TABLE workflows_automacao (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  
  -- Configuração do Workflow
  trigger_inicial JSONB NOT NULL,
  etapas JSONB NOT NULL,
  configuracoes JSONB DEFAULT '{}',
  
  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  pausado BOOLEAN DEFAULT FALSE,
  pausado_por UUID REFERENCES auth.users(id),
  pausado_em TIMESTAMPTZ,
  motivo_pausa TEXT,
  
  -- Métricas
  pacientes_ativos INTEGER DEFAULT 0,
  pacientes_completaram INTEGER DEFAULT 0,
  taxa_completude DECIMAL(5,2) DEFAULT 0,
  
  -- Execução
  ultima_execucao TIMESTAMPTZ,
  proxima_execucao TIMESTAMPTZ,
  frequencia_verificacao INTERVAL DEFAULT '1 hour',
  
  -- Auditoria
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  ultima_edicao TIMESTAMPTZ DEFAULT NOW(),
  editado_por UUID REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_workflow_nome_valido CHECK (LENGTH(TRIM(nome)) > 0)
);

-- Execução de Campanhas (Log de Envios)
CREATE TABLE execucoes_campanhas (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campanha_id UUID NOT NULL REFERENCES campanhas_marketing(id) ON DELETE CASCADE,
  paciente_id UUID NOT NULL REFERENCES pacientes(id) ON DELETE CASCADE,
  
  -- Detalhes do Envio
  canal canal_comunicacao_type NOT NULL,
  template_id UUID REFERENCES templates_campanhas(id),
  conteudo_enviado JSONB, -- Snapshot do conteúdo personalizado
  
  -- Status do Envio
  status_envio status_envio_type DEFAULT 'pendente',
  data_envio TIMESTAMPTZ,
  data_entrega TIMESTAMPTZ,
  
  -- Resposta e Engajamento
  data_abertura TIMESTAMPTZ,
  data_clique TIMESTAMPTZ,
  data_resposta TIMESTAMPTZ,
  data_conversao TIMESTAMPTZ,
  
  -- Detalhes do Canal
  detalhes_canal JSONB DEFAULT '{}', -- IDs externos, tracking, etc.
  
  -- Problemas e Erros
  erro_envio TEXT,
  codigo_erro VARCHAR(50),
  bounce_type bounce_type,
  
  -- Métricas Específicas
  tempo_ate_abertura INTEGER, -- em minutos
  tempo_ate_clique INTEGER,
  tempo_ate_conversao INTEGER,
  
  -- Workflow
  workflow_id UUID REFERENCES workflows_automacao(id),
  etapa_workflow INTEGER,
  
  -- Metadados
  executado_por UUID REFERENCES auth.users(id),
  origem execucao_origem_type DEFAULT 'campanha',
  
  CONSTRAINT uk_execucao_campanha_paciente UNIQUE (campanha_id, paciente_id, canal)
);

-- Métricas Consolidadas de Campanhas
CREATE TABLE metricas_campanhas (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campanha_id UUID NOT NULL REFERENCES campanhas_marketing(id) ON DELETE CASCADE,
  
  -- Período da Métrica
  data_inicio DATE NOT NULL,
  data_fim DATE NOT NULL,
  
  -- Estatísticas Gerais
  total_enviado INTEGER DEFAULT 0,
  total_entregue INTEGER DEFAULT 0,
  total_aberto INTEGER DEFAULT 0,
  total_clicado INTEGER DEFAULT 0,
  total_convertido INTEGER DEFAULT 0,
  
  -- Problemas
  total_falhas INTEGER DEFAULT 0,
  total_bounces INTEGER DEFAULT 0,
  total_reclamacoes INTEGER DEFAULT 0,
  total_descadastros INTEGER DEFAULT 0,
  
  -- Taxas Calculadas
  taxa_entrega DECIMAL(5,2) DEFAULT 0,
  taxa_abertura DECIMAL(5,2) DEFAULT 0,
  taxa_clique DECIMAL(5,2) DEFAULT 0,
  taxa_conversao DECIMAL(5,2) DEFAULT 0,
  
  -- Métricas por Canal
  metricas_por_canal JSONB DEFAULT '{}',
  
  -- Financeiro
  custo_periodo DECIMAL(15,2) DEFAULT 0,
  receita_periodo DECIMAL(15,2) DEFAULT 0,
  roi_periodo DECIMAL(10,4) DEFAULT 0,
  
  -- Temporal
  melhor_dia_semana dia_semana_type,
  melhor_horario TIME,
  
  -- Última Atualização
  atualizada_em TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT uk_metricas_campanha_periodo UNIQUE (campanha_id, data_inicio, data_fim),
  CONSTRAINT chk_periodo_valido CHECK (data_fim >= data_inicio)
);

-- Histórico de A/B Testing
CREATE TABLE ab_testing_resultados (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campanha_id UUID NOT NULL REFERENCES campanhas_marketing(id) ON DELETE CASCADE,
  
  -- Configuração do Teste
  nome_experimento VARCHAR(255) NOT NULL,
  variacao_id VARCHAR(100) NOT NULL,
  percentual_trafico DECIMAL(5,2) NOT NULL,
  
  -- Período do Teste
  inicio_teste TIMESTAMPTZ NOT NULL,
  fim_teste TIMESTAMPTZ,
  
  -- Métricas da Variação
  participantes INTEGER DEFAULT 0,
  conversoes INTEGER DEFAULT 0,
  taxa_conversao DECIMAL(5,2) DEFAULT 0,
  
  -- Resultado Estatístico
  significancia_estatistica DECIMAL(5,2),
  confianca_resultado DECIMAL(5,2),
  vencedor BOOLEAN DEFAULT FALSE,
  
  -- Metadados
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT chk_percentual_trafico CHECK (percentual_trafico > 0 AND percentual_trafico <= 100),
  CONSTRAINT chk_periodo_teste CHECK (fim_teste IS NULL OR fim_teste > inicio_teste)
);

-- Triggers de Automação
CREATE TABLE triggers_automacao (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  workflow_id UUID NOT NULL REFERENCES workflows_automacao(id) ON DELETE CASCADE,
  
  -- Configuração do Trigger
  nome VARCHAR(255) NOT NULL,
  tipo tipo_trigger_type NOT NULL,
  configuracao JSONB NOT NULL,
  
  -- Condições
  condicoes JSONB DEFAULT '[]',
  
  -- Frequência e Limites
  frequencia frequencia_trigger_type DEFAULT 'unico',
  limite_por_paciente INTEGER,
  janela_tempo_minutos INTEGER,
  
  -- Horários Permitidos
  horarios_permitidos JSONB DEFAULT '[]',
  dias_semana dia_semana_type[],
  
  -- Status e Execução
  ativo BOOLEAN DEFAULT TRUE,
  ultima_execucao TIMESTAMPTZ,
  proxima_execucao TIMESTAMPTZ,
  total_execucoes INTEGER DEFAULT 0,
  
  -- Métricas
  pacientes_disparados INTEGER DEFAULT 0,
  taxa_sucesso DECIMAL(5,2) DEFAULT 0,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tipos Enum para Campanhas
CREATE TYPE objetivo_campanha_type AS ENUM ('aquisicao', 'retencao', 'reativacao', 'upsell', 'cobranca', 'educativo');
CREATE TYPE status_campanha_type AS ENUM ('rascunho', 'agendada', 'executando', 'pausada', 'concluida', 'cancelada');
CREATE TYPE canal_comunicacao_type AS ENUM ('email', 'sms', 'whatsapp', 'push', 'ligacao');
CREATE TYPE tipo_template_type AS ENUM ('promocional', 'transacional', 'educativo', 'cobranca', 'lembrete');
CREATE TYPE categoria_template_type AS ENUM ('promocional', 'educativo', 'operacional', 'comercial');
CREATE TYPE categoria_campanha_type AS ENUM ('promocional', 'educativa', 'retencao', 'aquisicao', 'cobranca');
CREATE TYPE status_envio_type AS ENUM ('pendente', 'enviado', 'entregue', 'aberto', 'clicado', 'respondido', 'convertido', 'falhado', 'bounce');
CREATE TYPE bounce_type AS ENUM ('hard', 'soft', 'spam', 'bloqueado');
CREATE TYPE execucao_origem_type AS ENUM ('campanha', 'workflow', 'trigger', 'manual');
CREATE TYPE tipo_trigger_type AS ENUM ('data', 'comportamento', 'evento', 'condicao', 'manual');
CREATE TYPE frequencia_trigger_type AS ENUM ('unico', 'diario', 'semanal', 'mensal', 'continuo');
CREATE TYPE dia_semana_type AS ENUM ('segunda', 'terca', 'quarta', 'quinta', 'sexta', 'sabado', 'domingo');

-- Índices para Performance de Campanhas
CREATE INDEX idx_campanhas_status ON campanhas_marketing(status);
CREATE INDEX idx_campanhas_clinica ON campanhas_marketing(clinica_id);
CREATE INDEX idx_campanhas_data_inicio ON campanhas_marketing(data_inicio);
CREATE INDEX idx_campanhas_criador ON campanhas_marketing(criada_por);

-- Índices para Templates
CREATE INDEX idx_templates_canal ON templates_campanhas(canal);
CREATE INDEX idx_templates_tipo ON templates_campanhas(tipo);
CREATE INDEX idx_templates_publico ON templates_campanhas(publico) WHERE publico = true;
CREATE INDEX idx_templates_clinica ON templates_campanhas(clinica_id);

-- Índices para Execuções
CREATE INDEX idx_execucoes_campanha ON execucoes_campanhas(campanha_id);
CREATE INDEX idx_execucoes_paciente ON execucoes_campanhas(paciente_id);
CREATE INDEX idx_execucoes_canal ON execucoes_campanhas(canal);
CREATE INDEX idx_execucoes_status ON execucoes_campanhas(status_envio);
CREATE INDEX idx_execucoes_data_envio ON execucoes_campanhas(data_envio);

-- Índices para Workflows
CREATE INDEX idx_workflows_ativo ON workflows_automacao(ativo) WHERE ativo = true;
CREATE INDEX idx_workflows_proxima_execucao ON workflows_automacao(proxima_execucao) WHERE ativo = true;
CREATE INDEX idx_workflows_clinica ON workflows_automacao(clinica_id);

-- Índices para Métricas
CREATE INDEX idx_metricas_campanha ON metricas_campanhas(campanha_id);
CREATE INDEX idx_metricas_periodo ON metricas_campanhas(data_inicio, data_fim);

-- Índices para Triggers
CREATE INDEX idx_triggers_workflow ON triggers_automacao(workflow_id);
CREATE INDEX idx_triggers_tipo ON triggers_automacao(tipo);
CREATE INDEX idx_triggers_ativo ON triggers_automacao(ativo) WHERE ativo = true;
CREATE INDEX idx_triggers_proxima_execucao ON triggers_automacao(proxima_execucao) WHERE ativo = true;

-- Full-text search para campanhas e templates
CREATE INDEX idx_campanhas_search ON campanhas_marketing USING gin(
  to_tsvector('portuguese', coalesce(nome, '') || ' ' || coalesce(descricao, ''))
);

CREATE INDEX idx_templates_search ON templates_campanhas USING gin(
  to_tsvector('portuguese', coalesce(nome, '') || ' ' || coalesce(conteudo_texto, ''))
);
```

#### Campaign API Endpoints

```typescript
// Create Campaign API
export async function POST(request: NextRequest) {
  const {
    nome,
    descricao,
    objetivo,
    segmentoId,
    canaisConfiguracao,
    cronograma,
    templates,
    abTesting,
    configCompliance
  } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Validar segmento
    const { data: segmento } = await supabase
      .from('segmentos_pacientes')
      .select('quantidade_pacientes')
      .eq('id', segmentoId)
      .single()
    
    if (!segmento) {
      return NextResponse.json({
        error: 'Segmento não encontrado'
      }, { status: 404 })
    }
    
    // Validar templates
    const templateIds = Object.values(templates).filter(Boolean)
    if (templateIds.length === 0) {
      return NextResponse.json({
        error: 'Pelo menos um template deve ser especificado'
      }, { status: 400 })
    }
    
    // Calcular estimativas
    const estimativas = await calcularEstimativasCampanha({
      tamanhoPublico: segmento.quantidade_pacientes,
      canaisConfiguracao,
      objetivoCampanha: objetivo
    })
    
    // Criar campanha
    const { data: campanha, error } = await supabase
      .from('campanhas_marketing')
      .insert({
        nome,
        descricao,
        objetivo,
        segmento_id: segmentoId,
        tamanho_publico: segmento.quantidade_pacientes,
        canais_configuracao: canaisConfiguracao,
        cronograma,
        templates,
        ab_testing: abTesting,
        config_compliance: configCompliance,
        orcamento: estimativas.custoEstimado,
        roi_esperado: estimativas.roiEsperado,
        criada_por: user.id,
        clinica_id: await getClinicaId(user.id)
      })
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    // Agendar execução se necessário
    if (cronograma.tipo === 'agendado') {
      await agendarExecucaoCampanha(campanha.id, cronograma.dataInicio)
    } else if (cronograma.tipo === 'imediato') {
      await executarCampanhaImediata(campanha.id)
    }
    
    return NextResponse.json({
      campanha,
      estimativas,
      message: 'Campanha criada com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating campaign:', error)
    return NextResponse.json({
      error: 'Erro ao criar campanha'
    }, { status: 500 })
  }
}

// Execute Campaign API
export async function POST(
  request: NextRequest,
  { params }: { params: { campanhaId: string } }
) {
  const { forceRestart = false } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    // Buscar campanha
    const { data: campanha } = await supabase
      .from('campanhas_marketing')
      .select('*')
      .eq('id', params.campanhaId)
      .single()
    
    if (!campanha) {
      return NextResponse.json({
        error: 'Campanha não encontrada'
      }, { status: 404 })
    }
    
    // Verificar se já está executando
    if (campanha.status === 'executando' && !forceRestart) {
      return NextResponse.json({
        error: 'Campanha já está em execução'
      }, { status: 400 })
    }
    
    // Atualizar status
    await supabase
      .from('campanhas_marketing')
      .update({
        status: 'executando',
        data_inicio: new Date()
      })
      .eq('id', params.campanhaId)
    
    // Executar campanha baseada no tipo
    const resultadoExecucao = await executarCampanha(campanha)
    
    // Atualizar métricas iniciais
    await atualizarMetricasCampanha(params.campanhaId, resultadoExecucao)
    
    return NextResponse.json({
      resultado: resultadoExecucao,
      message: 'Campanha iniciada com sucesso'
    })
    
  } catch (error) {
    // Reverter status em caso de erro
    await supabase
      .from('campanhas_marketing')
      .update({ status: 'rascunho' })
      .eq('id', params.campanhaId)
    
    console.error('Error executing campaign:', error)
    return NextResponse.json({
      error: 'Erro ao executar campanha'
    }, { status: 500 })
  }
}

// Campaign Templates API
export async function POST(request: NextRequest) {
  const {
    nome,
    canal,
    tipo,
    assunto,
    conteudoHTML,
    conteudoTexto,
    camposDinamicos,
    tema,
    categoria
  } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Validar conteúdo
    const validacao = await validarTemplateConteudo({
      canal,
      assunto,
      conteudoHTML,
      conteudoTexto,
      camposDinamicos
    })
    
    // Criar template
    const { data: template, error } = await supabase
      .from('templates_campanhas')
      .insert({
        nome,
        canal,
        tipo,
        assunto,
        conteudo_html: conteudoHTML,
        conteudo_texto: conteudoTexto,
        campos_dinamicos: camposDinamicos,
        tema,
        categoria,
        validado: validacao.valido,
        erros_validacao: validacao.erros,
        criado_por: user.id,
        clinica_id: await getClinicaId(user.id)
      })
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    // Gerar preview se válido
    let preview = null
    if (validacao.valido) {
      preview = await gerarPreviewTemplate(template.id, {
        canal,
        conteudoHTML,
        conteudoTexto,
        tema
      })
    }
    
    return NextResponse.json({
      template,
      validacao,
      preview,
      message: 'Template criado com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating template:', error)
    return NextResponse.json({
      error: 'Erro ao criar template'
    }, { status: 500 })
  }
}

// Campaign Metrics API
export async function GET(
  request: NextRequest,
  { params }: { params: { campanhaId: string } }
) {
  const supabase = createServerClient()
  
  try {
    // Buscar métricas consolidadas
    const { data: metricas } = await supabase
      .from('metricas_campanhas')
      .select('*')
      .eq('campanha_id', params.campanhaId)
      .order('data_inicio', { ascending: false })
      .limit(30) // Últimos 30 períodos
    
    // Buscar execuções detalhadas
    const { data: execucoes } = await supabase
      .from('execucoes_campanhas')
      .select(`
        *,
        paciente:paciente_id(nome, email, telefone)
      `)
      .eq('campanha_id', params.campanhaId)
      .order('data_envio', { ascending: false })
    
    // Calcular métricas em tempo real
    const metricasTempoReal = await calcularMetricasTempoReal(params.campanhaId)
    
    // Análise de performance por canal
    const performancePorCanal = await analisarPerformancePorCanal(execucoes)
    
    // Insights e recomendações
    const insights = await gerarInsightsCampanha(metricas, execucoes)
    
    return NextResponse.json({
      metricas,
      metricasTempoReal,
      performancePorCanal,
      execucoes: execucoes.slice(0, 100), // Últimas 100 execuções
      insights,
      message: 'Métricas carregadas com sucesso'
    })
    
  } catch (error) {
    console.error('Error loading campaign metrics:', error)
    return NextResponse.json({
      error: 'Erro ao carregar métricas'
    }, { status: 500 })
  }
}

// Workflow Automation API
export async function POST(
  request: NextRequest,
  { params }: { params: { action: string } }
) {
  const supabase = createServerClient()
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    if (params.action === 'create') {
      const { nome, descricao, triggerInicial, etapas, configuracoes } = await request.json()
      
      // Validar configuração do workflow
      const validacao = await validarConfigWorkflow({
        triggerInicial,
        etapas,
        configuracoes
      })
      
      if (!validacao.valido) {
        return NextResponse.json({
          error: 'Configuração de workflow inválida',
          detalhes: validacao.erros
        }, { status: 400 })
      }
      
      // Criar workflow
      const { data: workflow, error } = await supabase
        .from('workflows_automacao')
        .insert({
          nome,
          descricao,
          trigger_inicial: triggerInicial,
          etapas,
          configuracoes,
          criado_por: user.id,
          clinica_id: await getClinicaId(user.id)
        })
        .select()
        .single()
      
      if (error) {
        throw error
      }
      
      // Criar triggers associados
      await criarTriggersWorkflow(workflow.id, triggerInicial)
      
      return NextResponse.json({
        workflow,
        validacao,
        message: 'Workflow criado com sucesso'
      })
    }
    
    if (params.action === 'execute') {
      const { workflowId, pacienteId, contexto } = await request.json()
      
      // Executar workflow para paciente específico
      const resultado = await executarWorkflowPaciente(workflowId, pacienteId, contexto)
      
      return NextResponse.json({
        resultado,
        message: 'Workflow executado com sucesso'
      })
    }
    
  } catch (error) {
    console.error('Error managing workflow:', error)
    return NextResponse.json({
      error: 'Erro ao gerenciar workflow'
    }, { status: 500 })
  }
}
```

### Integration Points

#### Epic 5 Integration (Portal Paciente)

- **Communication Preferences**: Respeito às preferências de canal
- **Engagement Data**: Dados de uso do portal para campanhas
- **Consent Management**: Integração com configurações de privacidade
- **Push Notifications**: Envio via PWA do portal

#### Epic 6 Integration (Agenda Inteligente)

- **Appointment Triggers**: Campanhas baseadas em agendamentos
- **Reminder Automation**: Lembretes automáticos personalizados
- **No-Show Follow-up**: Campanhas para reagendamento
- **Professional Availability**: Campanhas baseadas na agenda

#### Epic 7 Integration (Financeiro Essencial)

- **Payment Reminders**: Automação de cobrança e lembretes
- **Invoice Integration**: Templates com dados financeiros
- **ROI Tracking**: Acompanhamento de conversões financeiras
- **Credit Analysis**: Segmentação baseada em score de crédito

#### Epic 8 Integration (BI & Dashboards)

- **Campaign Analytics**: Dashboards de performance em tempo real
- **Predictive Models**: ML para otimização de campanhas
- **A/B Testing Results**: Análise estatística nos dashboards
- **ROI Optimization**: Insights automáticos para melhoria

#### Epic 9 Integration (Cadastro & Prontuário)

- **Medical Triggers**: Campanhas baseadas em tratamentos
- **Health Reminders**: Lembretes de retorno e exames
- **Consent Integration**: Respeito ao consentimento médico
- **Treatment Follow-up**: Acompanhamento pós-procedimento

### Testing Strategy

#### Campaign Automation Tests

```typescript
describe('Campaign Automation System', () => {
  test('creates campaign with email template correctly', async () => {
    const template = await createTemplate({
      nome: 'Welcome Email',
      canal: 'email',
      conteudoTexto: 'Bem-vindo {nome}!'
    })
    
    const campaign = await createCampaign({
      nome: 'Campanha Boas-vindas',
      objetivo: 'aquisicao',
      templates: { email: template.id }
    })
    
    expect(campaign.templates.email).toBe(template.id)
  })
  
  test('executes workflow automation correctly', async () => {
    const workflow = await createWorkflow({
      nome: 'Reativação Pacientes',
      triggerInicial: {
        tipo: 'comportamento',
        configuracao: { ultimaConsulta: 90 }
      }
    })
    
    const execution = await executeWorkflow(workflow.id)
    expect(execution.pacientesProcessados).toBeGreaterThan(0)
  })
  
  test('tracks campaign metrics accurately', async () => {
    const campaign = await createAndExecuteCampaign({
      segmentoId: 'test-segment',
      canal: 'email'
    })
    
    // Simular abertura
    await simulateEmailOpen(campaign.id, 'test-patient-id')
    
    const metrics = await getCampaignMetrics(campaign.id)
    expect(metrics.totalAberto).toBe(1)
    expect(metrics.taxaAbertura).toBeGreaterThan(0)
  })
  
  test('respects compliance settings', async () => {
    const campaign = await createCampaign({
      configCompliance: {
        respeitarPreferenciasCanal: true,
        incluirLinkDescadastro: true
      }
    })
    
    const executions = await executeCampaign(campaign.id)
    
    // Verificar que pacientes opt-out foram excluídos
    const optOutPatients = await getOptOutPatients()
    expect(executions.every(e => 
      !optOutPatients.includes(e.paciente_id)
    )).toBe(true)
  })
})
```

### Dev Notes

#### Advanced Campaign Features

- **Predictive Send Time**: ML para otimizar horários de envio
- **Content Optimization**: A/B testing automático de subject lines
- **Deliverability Monitoring**: Controle de reputação de sender
- **Multi-Channel Orchestration**: Coordenação inteligente entre canais

#### Performance Optimization

- **Batch Processing**: Envio em lotes para otimização
- **Rate Limiting**: Controle de velocidade por canal
- **Queue Management**: Filas inteligentes para priorização
- **Retry Logic**: Tentativas automáticas com backoff exponencial

#### Integration Architecture

- **Event-Driven**: Triggers baseados em eventos do sistema
- **Real-time Sync**: Sincronização em tempo real com outros épicos
- **Webhook Support**: Integração com ferramentas externas
- **API Flexibility**: APIs para integrações personalizadas

---

## Dev Agent Record

### Task Status

- [x] Analyzed campaign automation requirements for Epic 10
- [x] Created comprehensive story with 6 acceptance criteria
- [x] Designed advanced TypeScript interfaces for campaign management
- [x] Specified database schema with multi-channel support and metrics
- [x] Developed complete API endpoints for campaign lifecycle
- [x] Added A/B testing and workflow automation capabilities
- [x] Integrated compliance and LGPD requirements
- [x] Created comprehensive testing strategy for automation
- [x] Established integration points with Epic 5-9

### File List

- `docs/stories/10.2.story.md` - Campaign Automation and Marketing implementation story

### Change Log

- **Story 10.2 Creation**: Complete campaign automation system with multi-channel support
- **Workflow Automation**: Advanced automation with triggers, conditions, and sequences
- **Template Management**: Visual editor and responsive template system
- **A/B Testing**: Statistical testing framework for campaign optimization
- **Compliance Integration**: LGPD/GDPR compliance with opt-out management
- **Real-time Metrics**: Live campaign tracking with detailed analytics
- **Epic Integration**: Full integration with Epic 5-9 for comprehensive automation

### Completion Notes

Story 10.2 delivers a powerful campaign automation system that enables sophisticated marketing workflows with minimal manual intervention. The system supports multi-channel campaigns, advanced personalization, and comprehensive compliance management.

### Next Story

Ready to create **Story 10.3: Lembretes de Cobrança e Financeiro** - the automated billing and payment reminder system.
