{"metadata": {"name": "VIBECODE V1.0 - Unified Configuration", "version": "1.0.0", "description": "Configuração consolidada seguindo 'Aprimore, Não Prolifere'", "created": "2025-07-16", "status": "PRODUCTION_READY", "consolidation": {"original_files": ["config.json", "workflow.json", "essential.json"], "reduction": "75% menos arquivos de configuração"}}, "vibecode": {"version": "1.0.0", "quality_threshold": 8, "root_path": "E:/VIBECODE", "principle": "Aprimore, Não Prolifere"}, "file_operations": {"desktop_commander_limit": 200, "tools": {"small_files": "desktop-commander", "large_files": "cursor-editor"}, "verification": "always_read_after_write"}, "paths": {"project_root": "E:/VIBECODE", "project_core": "memory-bank", "rules": ".cursor/rules", "memory": "memory-bank", "backup": "E:/CODE-BACKUP", "logs": "E:/CODE-BACKUP/logs", "scripts": ".cursor/scripts", "saas_projects": "@saas-projects", "memory_bank": "memory-bank"}, "workflow": {"mandatory_steps": ["phase_0_5_validation", "analyze_complexity", "select_tools", "execute_task", "reflect_quality", "refine_if_needed", "validate_final"], "complexity_routing": {"1-3": "basic_operations", "4-6": "standard_workflow", "7-10": "advanced_workflow"}, "quality_gates": {"minimum_score": 8, "mandatory_verification": true, "auto_refinement": true, "max_refinement_cycles": 3}, "phase_05_commands": ["uv run python memory-bank/python/knowledge_graph_manager.py --validate_connections", "uv run python .cursor/scripts/finaltest.py", "uv run python .cursor/scripts/vibecode_core_validator.py", "uv run python .cursor/scripts/vibecode_task_system.py --status"]}, "mcp_routing": {"sequential_thinking": {"complexity_min": 7, "activation": "complex_reasoning"}, "task_management": {"complexity_min": 3, "tools": ["view_tasklist", "add_tasks", "update_tasks", "reorganize_tasklist"]}, "research_tools": {"priority_order": ["context7", "tavily", "exa"], "fallback_chain": true}, "specialized_tools": ["desktop-commander", "figma", "playwright"]}, "task_automation": {"auto_detection": true, "complexity_threshold": 3, "confidence_threshold": 0.5, "batch_updates": true, "workflow_transitions": "automatic", "kg_integration": true}, "optimization": {"minimize_temp_files": true, "backup_location": "E:/CODE-BACKUP", "auto_cleanup": true, "cache": {"default_ttl": 300, "max_entries": 1000, "eviction_policy": "lru", "max_memory_mb": 50}, "performance_targets": {"load_time_ms": 100, "response_time_ms": 30000, "memory_usage_mb": 150}}, "development": {"language": "pt-BR", "platform": "windows", "shell": "powershell", "package_manager": "uv", "node_version": ">=18.0.0", "python_version": ">=3.11.0"}, "quality_standards": {"minimum_quality_threshold": 8, "code_coverage_minimum": 80, "consolidation_targets": {"file_reduction": "30-40%", "code_reduction": "50-70%", "functionality_preservation": "100%"}}, "documentation_control": {"automatic_file_generation": false, "cleanup_temp_files": true, "auto_generate_reports": false, "auto_generate_logs": false, "memory_system": {"auto_update_memory": true, "backup_before_update": true, "memory_consolidation": true}}, "monitoring": {"sampling_rate": 0.1, "retention_hours": 24, "aggregation_interval": 60, "max_metrics": 1000, "health_checks": {"mcp_servers": "every_5_minutes", "api_keys": "daily", "performance": "real_time"}}, "security": {"api_key_management": "environment_variables", "gitignore_protection": true, "key_rotation": "monthly", "access_logging": true, "data_protection": "gdpr_compliant"}}