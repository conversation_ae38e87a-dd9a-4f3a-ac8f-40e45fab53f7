# Story 11.4 - Alertas e Relatórios de Estoque

**Epic:** 11 - Estoque Simplificado  
**Priority:** P1  
**Points:** 8  
**Status:** Draft  
**Created:** 2025-01-18  
**Updated:** 2025-01-18

## Story Description

Como administrador da clínica, quero ter um sistema completo de alertas e relatórios de estoque para monitorar automaticamente níveis críticos, vencimentos próximos e tendências de consumo, garantindo operação contínua e otimização de custos através de insights acionáveis e notificações proativas.

## Business Value

- **Prevenção de Faltas**: Alertas automáticos evitam interrupção de serviços por falta de materiais
- **Redução de Perdas**: Notificações de vencimento reduzem desperdício por expiração
- **Otimização Financeira**: Relatórios de consumo permitem compras mais inteligentes
- **Compliance**: Rastreabilidade completa para auditorias e regulamentações
- **Eficiência Operacional**: Dashboards em tempo real reduzem tempo de gestão manual

## Acceptance Criteria

### AC1: Sistema de Alertas Inteligentes

**Given** que sou um administrador da clínica  
**When** os níveis de estoque atingem thresholds configuráveis  
**Then** o sistema deve:

- Gerar alertas automáticos com severidade (crítico/atenção/informativo)
- Enviar notificações via múltiplos canais (in-app, email, WhatsApp)
- Incluir sugestões automáticas de reposição baseadas em consumo histórico
- Permitir configuração personalizada de thresholds por produto e categoria
- Considerar sazonalidade e padrões de consumo na geração de alertas

### AC2: Relatórios Analíticos de Consumo

**Given** que preciso analisar padrões de consumo  
**When** acesso os relatórios de estoque  
**Then** o sistema deve exibir:

- Relatório de consumo por período (diário/semanal/mensal/anual)
- Análise de tendências com gráficos interativos e projeções
- Comparativo de consumo entre períodos e centros de custo
- Relatório de desperdício por vencimento e perda
- Análise de ROI e custo por procedimento realizado

### AC3: Dashboard de Performance em Tempo Real

**Given** que preciso monitorar performance do estoque  
**When** acesso o dashboard principal  
**Then** o sistema deve apresentar:

- KPIs principais: giro de estoque, dias de cobertura, valor imobilizado
- Gráficos de evolução de consumo com drill-down por categoria
- Top 10 produtos mais consumidos e com maior impacto financeiro
- Alertas visuais com semáforo para status críticos
- Métricas de eficiência: acuracidade de inventário, tempo de reposição

### AC4: Relatórios de Vencimento e Validade

**Given** que preciso controlar validade dos produtos  
**When** acesso relatórios de vencimento  
**Then** o sistema deve fornecer:

- Lista de produtos próximos ao vencimento (configurável: 30/60/90 dias)
- Relatório de produtos vencidos com valor e impacto financeiro
- Sugestões de ações: consumo prioritário, promoções, descarte
- Histórico de perdas por vencimento com análise de causas
- Alertas automáticos para produtos com validade crítica

### AC5: Integração com BI e Analytics Avançados

**Given** que preciso de insights profundos sobre estoque  
**When** utilizo funcionalidades avançadas de BI  
**Then** o sistema deve oferecer:

- Integração nativa com Epic 8 (BI & Dashboards) para análises cruzadas
- Machine Learning para previsão de demanda e otimização de estoque
- Análise de correlação entre consumo e procedimentos agendados (Epic 6)
- Relatórios financeiros integrados com Epic 7 (custo real vs orçado)
- Exportação para ferramentas externas (Excel, Power BI, Tableau)

### AC6: Configuração e Personalização Avançada

**Given** que cada clínica tem necessidades específicas  
**When** configuro o sistema de alertas e relatórios  
**Then** o sistema deve permitir:

- Configuração flexível de thresholds por produto, categoria e sazonalidade
- Criação de relatórios personalizados com filtros dinâmicos
- Agendamento automático de relatórios via email
- Configuração de workflows de aprovação para ações críticas
- Personalização de dashboards por perfil de usuário (admin, gerente, operacional)

## Technical Implementation

### Database Schema Extensions

```sql
-- Tabela para configuração de alertas
CREATE TABLE stock_alert_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    clinic_id UUID REFERENCES clinics(id),
    product_id UUID REFERENCES products(id),
    category_id UUID REFERENCES product_categories(id),
    alert_type VARCHAR(50) NOT NULL, -- 'low_stock', 'expiring', 'expired', 'overstock'
    threshold_value DECIMAL(10,2),
    threshold_unit VARCHAR(20), -- 'quantity', 'days', 'percentage'
    severity_level VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
    is_active BOOLEAN DEFAULT true,
    notification_channels TEXT[], -- ['in_app', 'email', 'whatsapp', 'sms']
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- Tabela para histórico de alertas
CREATE TABLE stock_alerts_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    clinic_id UUID REFERENCES clinics(id),
    alert_config_id UUID REFERENCES stock_alert_configs(id),
    product_id UUID REFERENCES products(id),
    alert_type VARCHAR(50) NOT NULL,
    severity_level VARCHAR(20) NOT NULL,
    current_value DECIMAL(10,2),
    threshold_value DECIMAL(10,2),
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'acknowledged', 'resolved'
    acknowledged_by UUID REFERENCES users(id),
    acknowledged_at TIMESTAMP,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT now()
);

-- Tabela para relatórios personalizados
CREATE TABLE custom_stock_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    clinic_id UUID REFERENCES clinics(id),
    user_id UUID REFERENCES users(id),
    report_name VARCHAR(200) NOT NULL,
    report_type VARCHAR(50) NOT NULL, -- 'consumption', 'valuation', 'movement', 'custom'
    filters JSONB, -- Filtros dinâmicos
    schedule_config JSONB, -- Configuração de agendamento
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- Tabela para métricas de performance
CREATE TABLE stock_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    clinic_id UUID REFERENCES clinics(id),
    metric_date DATE NOT NULL,
    total_value DECIMAL(12,2),
    turnover_rate DECIMAL(8,4),
    days_coverage INTEGER,
    accuracy_percentage DECIMAL(5,2),
    waste_value DECIMAL(10,2),
    waste_percentage DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT now(),
    UNIQUE(clinic_id, metric_date)
);

-- Índices para performance
CREATE INDEX idx_stock_alerts_clinic_type ON stock_alerts_history(clinic_id, alert_type);
CREATE INDEX idx_stock_alerts_status ON stock_alerts_history(status, created_at);
CREATE INDEX idx_performance_metrics_date ON stock_performance_metrics(clinic_id, metric_date);
CREATE INDEX idx_alert_configs_product ON stock_alert_configs(product_id, is_active);
```

### TypeScript Interfaces

```typescript
// Interface para configuração de alertas
interface StockAlertConfig {
  id: string;
  clinicId: string;
  productId?: string;
  categoryId?: string;
  alertType: 'low_stock' | 'expiring' | 'expired' | 'overstock';
  thresholdValue: number;
  thresholdUnit: 'quantity' | 'days' | 'percentage';
  severityLevel: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  notificationChannels: ('in_app' | 'email' | 'whatsapp' | 'sms')[];
  createdAt: Date;
  updatedAt: Date;
}

// Interface para alertas ativos
interface StockAlert {
  id: string;
  clinicId: string;
  alertConfigId: string;
  productId: string;
  alertType: string;
  severityLevel: string;
  currentValue: number;
  thresholdValue: number;
  message: string;
  status: 'active' | 'acknowledged' | 'resolved';
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  createdAt: Date;
  product?: Product;
  acknowledgedUser?: User;
}

// Interface para relatórios personalizados
interface CustomStockReport {
  id: string;
  clinicId: string;
  userId: string;
  reportName: string;
  reportType: 'consumption' | 'valuation' | 'movement' | 'custom';
  filters: {
    dateRange?: { start: Date; end: Date };
    productIds?: string[];
    categoryIds?: string[];
    supplierId?: string;
    costCenterId?: string;
    customFilters?: Record<string, any>;
  };
  scheduleConfig?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    dayOfWeek?: number;
    dayOfMonth?: number;
    time: string;
    recipients: string[];
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Interface para métricas de performance
interface StockPerformanceMetrics {
  id: string;
  clinicId: string;
  metricDate: Date;
  totalValue: number;
  turnoverRate: number;
  daysCoverage: number;
  accuracyPercentage: number;
  wasteValue: number;
  wastePercentage: number;
  createdAt: Date;
}

// Interface para dashboard de estoque
interface StockDashboardData {
  kpis: {
    totalValue: number;
    turnoverRate: number;
    daysCoverage: number;
    accuracyPercentage: number;
    activeAlerts: number;
    criticalAlerts: number;
  };
  charts: {
    consumptionTrend: Array<{ date: string; value: number; category?: string }>;
    topProducts: Array<{ productId: string; name: string; consumption: number; value: number }>;
    alertsByType: Array<{ type: string; count: number; severity: string }>;
    wasteAnalysis: Array<{ period: string; waste: number; percentage: number }>;
  };
  alerts: StockAlert[];
  recommendations: Array<{
    type: 'reorder' | 'optimize' | 'attention';
    priority: 'high' | 'medium' | 'low';
    message: string;
    actionable: boolean;
    productId?: string;
  }>;
}
```

### API Endpoints

```typescript
// Alertas e configurações
GET    /api/stock/alerts                    // Lista alertas ativos
POST   /api/stock/alerts/acknowledge        // Reconhece alerta
PUT    /api/stock/alerts/resolve           // Resolve alerta
GET    /api/stock/alerts/configs           // Lista configurações de alertas
POST   /api/stock/alerts/configs           // Cria configuração de alerta
PUT    /api/stock/alerts/configs/:id       // Atualiza configuração
DELETE /api/stock/alerts/configs/:id       // Remove configuração

// Relatórios e analytics
GET    /api/stock/reports/consumption      // Relatório de consumo
GET    /api/stock/reports/expiration       // Relatório de vencimentos
GET    /api/stock/reports/valuation        // Relatório de valorização
GET    /api/stock/reports/performance      // Métricas de performance
POST   /api/stock/reports/custom           // Cria relatório personalizado
GET    /api/stock/reports/custom/:id       // Executa relatório personalizado

// Dashboard e analytics
GET    /api/stock/dashboard                // Dados do dashboard principal
GET    /api/stock/analytics/trends         // Análise de tendências
GET    /api/stock/analytics/predictions    // Previsões de demanda
GET    /api/stock/analytics/optimization   // Sugestões de otimização

// Exportação e integração
POST   /api/stock/reports/export           // Exporta relatórios
GET    /api/stock/bi/integration           // Dados para BI (Epic 8)
```

### Component Architecture

```text
app/dashboard/stock/
├── alerts/
│   ├── page.tsx                  // Página principal de alertas
│   ├── components/
│   │   ├── alert-list.tsx        // Lista de alertas ativos
│   │   ├── alert-config.tsx      // Configuração de alertas
│   │   └── alert-acknowledge.tsx // Ações em alertas
│   └── [id]/
│       └── page.tsx              // Detalhes do alerta
├── reports/
│   ├── page.tsx                  // Dashboard de relatórios
│   ├── consumption/
│   │   └── page.tsx              // Relatório de consumo
│   ├── expiration/
│   │   └── page.tsx              // Relatório de vencimentos
│   ├── performance/
│   │   └── page.tsx              // Métricas de performance
│   └── custom/
│       ├── page.tsx              // Relatórios personalizados
│       └── [id]/
│           └── page.tsx          // Execução de relatório
├── dashboard/
│   ├── page.tsx                  // Dashboard principal
│   └── components/
│       ├── stock-kpis.tsx        // KPIs principais
│       ├── consumption-chart.tsx // Gráfico de consumo
│       ├── alerts-summary.tsx    // Resumo de alertas
│       └── recommendations.tsx   // Recomendações
└── analytics/
    ├── page.tsx                  // Analytics avançados
    └── components/
        ├── trend-analysis.tsx    // Análise de tendências
        ├── prediction-models.tsx // Modelos preditivos
        └── optimization-insights.tsx // Insights de otimização

components/stock/
├── alert-system/
│   ├── alert-badge.tsx           // Badge de alerta
│   ├── alert-notification.tsx    // Notificação
│   └── alert-actions.tsx         // Ações rápidas
├── reporting/
│   ├── report-builder.tsx        // Construtor de relatórios
│   ├── chart-components.tsx      // Componentes de gráfico
│   └── export-controls.tsx       // Controles de exportação
└── dashboard/
    ├── metric-card.tsx           // Card de métrica
    ├── trend-indicator.tsx       // Indicador de tendência
    └── alert-summary.tsx         // Resumo de alertas
```

## Integration Points

### Epic 6 (Agenda Inteligente)

- **Previsão de Consumo**: Usa agendamentos para prever demanda de materiais
- **Alertas Proativos**: Notifica sobre falta de materiais para procedimentos agendados
- **Otimização de Estoque**: Ajusta níveis baseado na agenda

### Epic 7 (Financeiro Essencial)

- **Controle de Custos**: Integra custos reais de materiais com orçamentos
- **Análise de ROI**: Calcula retorno por procedimento incluindo custos de materiais
- **Relatórios Financeiros**: Combina dados de estoque com análises financeiras

### Epic 8 (BI & Dashboards)

- **Data Integration**: Fornece dados de estoque para análises avançadas
- **Cross-Analytics**: Permite análises cruzadas entre estoque, financeiro e operacional
- **Predictive Models**: Usa modelos de ML para previsões de demanda

### Epic 9 (Cadastro Pacientes & Prontuário)

- **Consumo por Paciente**: Rastreia materiais usados por atendimento
- **Controle de Lote**: Rastreabilidade para compliance médico
- **Alertas de Segurança**: Notifica sobre produtos vencidos ou recalls

## Testing Strategy

### Unit Tests

```typescript
// Testes de lógica de alertas
describe('StockAlertService', () => {
  test('should generate low stock alert when threshold reached', async () => {
    const product = mockProduct({ currentStock: 5, minStock: 10 });
    const alerts = await generateStockAlerts(product);
    expect(alerts).toHaveLength(1);
    expect(alerts[0].alertType).toBe('low_stock');
  });

  test('should calculate days coverage correctly', () => {
    const consumption = mockConsumptionData();
    const coverage = calculateDaysCoverage(100, consumption);
    expect(coverage).toBeGreaterThan(0);
  });
});

// Testes de relatórios
describe('StockReportsService', () => {
  test('should generate consumption report with correct data', async () => {
    const filters = { dateRange: { start: '2025-01-01', end: '2025-01-31' } };
    const report = await generateConsumptionReport(filters);
    expect(report.data).toBeDefined();
    expect(report.totals).toBeDefined();
  });
});
```

### Integration Tests

```typescript
// Testes de integração com Epic 6
describe('Stock-Schedule Integration', () => {
  test('should predict material consumption from scheduled procedures', async () => {
    const procedures = await getScheduledProcedures('2025-01-20');
    const prediction = await predictMaterialConsumption(procedures);
    expect(prediction.materials).toHaveLength(greaterThan(0));
  });
});

// Testes de integração com Epic 8
describe('Stock-BI Integration', () => {
  test('should provide correct data for BI analytics', async () => {
    const biData = await getStockDataForBI('2025-01');
    expect(biData.metrics).toBeDefined();
    expect(biData.trends).toBeDefined();
  });
});
```

### E2E Tests
```typescript
// Teste completo de fluxo de alertas
test('Alert workflow - from generation to resolution', async ({ page }) => {
  // Setup: produto com estoque baixo
  await setupLowStockProduct();
  
  // Geração automática de alerta
  await page.goto('/dashboard/stock/alerts');
  await expect(page.locator('.alert-critical')).toBeVisible();
  
  // Reconhecimento do alerta
  await page.click('[data-testid="acknowledge-alert"]');
  await expect(page.locator('.alert-acknowledged')).toBeVisible();
  
  // Resolução através de compra
  await page.goto('/dashboard/stock/purchases');
  await createPurchaseOrder();
  await page.goto('/dashboard/stock/alerts');
  await expect(page.locator('.alert-resolved')).toBeVisible();
});
```

## Performance Considerations

### Database Optimization
- **Índices Estratégicos**: Otimização para consultas frequentes de alertas e relatórios
- **Particionamento**: Tabelas de histórico particionadas por data para performance
- **Materialized Views**: Views materializadas para cálculos complexos de métricas
- **Query Optimization**: Consultas otimizadas para grandes volumes de dados

### Caching Strategy
- **Alert Cache**: Cache de alertas ativos com invalidação inteligente
- **Report Cache**: Cache de relatórios por período configurável
- **Metrics Cache**: Cache de métricas de performance com refresh automático
- **Real-time Updates**: WebSockets para atualizações em tempo real de alertas

### Scalability Planning
- **Background Jobs**: Processamento assíncrono de relatórios pesados
- **Queue Management**: Filas para processamento de alertas e notificações
- **Load Balancing**: Distribuição de carga para operações intensivas
- **Data Archiving**: Estratégia de arquivamento para dados históricos

## Security & Compliance

### Data Protection
- **Access Control**: Controle granular de acesso por perfil e clínica
- **Audit Trail**: Rastreamento completo de todas as ações críticas
- **Data Encryption**: Criptografia de dados sensíveis em trânsito e repouso
- **Backup Strategy**: Backup automático e recuperação de dados críticos

### Regulatory Compliance
- **LGPD Compliance**: Proteção de dados pessoais e corporativos
- **Medical Standards**: Compliance com regulamentações médicas (CFM, ANVISA)
- **Traceability**: Rastreabilidade completa para auditorias regulatórias
- **Retention Policies**: Políticas de retenção de dados conforme regulamentação

## Success Criteria

### Performance Metrics
- **Alert Accuracy**: 98% de precisão na geração de alertas relevantes
- **Response Time**: <2s para carregamento de dashboards e relatórios
- **System Uptime**: 99.9% de disponibilidade do sistema de alertas
- **User Adoption**: 90% dos usuários utilizando sistema de alertas regularmente

### Business Impact
- **Waste Reduction**: 30% de redução em perdas por vencimento
- **Stockout Prevention**: 95% de redução em faltas de material
- **Cost Optimization**: 15% de redução em custos de estoque
- **Operational Efficiency**: 40% de redução no tempo de gestão manual

### User Experience
- **Ease of Use**: Interface intuitiva com curva de aprendizado mínima
- **Mobile Friendly**: Acesso completo via dispositivos móveis
- **Customization**: Personalização avançada para diferentes perfis
- **Integration**: Integração transparente com outros módulos do sistema

## Dependencies
- **Story 11.1**: Sistema de cadastro de produtos (base para alertas)
- **Story 11.2**: Controle de compras (fonte de dados de reposição)
- **Story 11.3**: Controle de saídas (dados de consumo para análises)
- **Epic 6**: Integração com agenda para previsão de demanda
- **Epic 7**: Integração financeira para análise de custos
- **Epic 8**: Plataforma de BI para analytics avançados
- **Epic 9**: Integração com prontuários para rastreabilidade médica

## Definition of Done
- [ ] Todos os critérios de aceitação implementados e testados
- [ ] Sistema de alertas automáticos funcionando com múltiplos canais
- [ ] Relatórios analíticos completos com exportação
- [ ] Dashboard em tempo real com KPIs principais
- [ ] Integração completa com Epic 6, 7, 8 e 9
- [ ] Testes unitários, integração e E2E com cobertura ≥90%
- [ ] Documentação técnica e manual do usuário
- [ ] Performance validada conforme critérios estabelecidos
- [ ] Security review e compliance validation aprovados
- [ ] Deploy em ambiente de produção aprovado
- [ ] Treinamento da equipe concluído
- [ ] Métricas de sucesso definidas e em monitoramento

---

## Dev Agent Record

### Task Implementation Status
- [ ] Setup database schema with alert configurations and metrics tables
- [ ] Implement TypeScript interfaces for alert system and reporting
- [ ] Create API endpoints for alerts, reports, and dashboard data
- [ ] Build alert management components with real-time notifications
- [ ] Develop comprehensive reporting system with custom filters
- [ ] Create performance dashboard with KPIs and analytics
- [ ] Implement integration points with Epic 6, 7, 8, and 9
- [ ] Setup background jobs for alert processing and report generation
- [ ] Configure caching strategy for optimal performance
- [ ] Implement security controls and audit logging
- [ ] Create comprehensive test suite (unit, integration, E2E)
- [ ] Performance optimization and scalability enhancements
- [ ] Documentation and user training materials

### Debug Log References
- Story creation: 2025-01-18 - Initial comprehensive alerting and reporting system design
- Integration mapping: Epic 6 (schedule-based predictions), Epic 7 (financial analysis), Epic 8 (BI integration), Epic 9 (medical traceability)
- Advanced features: ML-powered demand forecasting, automated optimization suggestions, multi-channel notifications

### Completion Notes
*To be updated during implementation*

### File List
*To be updated during implementation*

### Change Log
*To be updated during implementation*

### Status: Draft → Approved → Ready for Review → Done
