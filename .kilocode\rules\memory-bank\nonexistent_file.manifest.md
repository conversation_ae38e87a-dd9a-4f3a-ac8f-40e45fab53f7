---
source_id: nonexistent_test
data_file: @project-core/memory/arquivo_que_nao_existe.md
priority: 6
enabled: true
tags_func: error_handling_tags
description: Teste de tratamento de erro para arquivo inexistente
---

# Nonexistent File Test Manifest

Este manifesto aponta propositalmente para um arquivo que não existe, para testar o tratamento robusto de erros do sistema.

## Configuração

- **Source ID**: `nonexistent_test`
- **Data File**: Arquivo inexistente para teste
- **Priority**: 6 (prioridade muito baixa)
- **Tags Function**: `error_handling_tags` para categorização de erros

## Funcionalidade

Este manifesto serve para verificar se o sistema de carregamento dinâmico consegue:

1. Detectar arquivos inexistentes
2. <PERSON>gar adequadamente os erros
3. Continuar o processamento dos outros manifestos
4. Manter a robustez do sistema
