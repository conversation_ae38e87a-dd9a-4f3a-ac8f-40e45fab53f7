"""
Unit tests for FileSystemMonitor.
"""

import os
import time
import tempfile
import unittest
from unittest.mock import Mock, patch
from pathlib import Path

from ..core.file_monitor import FileSystemMonitor, SyncFileSystemEventHandler
from ..models.sync_models import <PERSON>Change, ChangeType, MonitoringConfig


class TestFileSystemMonitor(unittest.TestCase):
    """Test cases for FileSystemMonitor."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = MonitoringConfig(
            source_path=self.temp_dir,
            file_types=['.txt', '.md'],
            excluded_paths=['temp', 'cache'],
            debounce_delay_ms=100
        )
        self.monitor = FileSystemMonitor(self.config)
        self.changes_received = []
        
        def change_callback(changes):
            self.changes_received.extend(changes)
        
        self.monitor.set_change_callback(change_callback)
    
    def tearDown(self):
        """Clean up test environment."""
        if self.monitor.is_monitoring():
            self.monitor.stop_monitoring()
        
        # Clean up temp directory
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_monitor_initialization(self):
        """Test monitor initialization."""
        self.assertFalse(self.monitor.is_monitoring())
        self.assertEqual(self.monitor.get_monitored_paths(), [])
        self.assertEqual(self.monitor.config.source_path, self.temp_dir)
    
    def test_start_stop_monitoring(self):
        """Test starting and stopping monitoring."""
        # Start monitoring
        self.monitor.start_monitoring()
        self.assertTrue(self.monitor.is_monitoring())
        self.assertIn(self.temp_dir, self.monitor.get_monitored_paths())
        
        # Stop monitoring
        self.monitor.stop_monitoring()
        self.assertFalse(self.monitor.is_monitoring())
        self.assertEqual(self.monitor.get_monitored_paths(), [])
    
    def test_file_creation_detection(self):
        """Test detection of file creation."""
        self.monitor.start_monitoring()
        
        # Create a test file
        test_file = os.path.join(self.temp_dir, 'test.txt')
        with open(test_file, 'w') as f:
            f.write('test content')
        
        # Wait for debounce and processing
        time.sleep(0.2)
        
        # Check if change was detected
        self.assertGreater(len(self.changes_received), 0)
        
        # Find the creation event
        creation_events = [c for c in self.changes_received if c.change_type == ChangeType.CREATED]
        self.assertGreater(len(creation_events), 0)
        
        creation_event = creation_events[0]
        self.assertEqual(creation_event.file_path, test_file)
    
    def test_file_modification_detection(self):
        """Test detection of file modification."""
        # Create initial file
        test_file = os.path.join(self.temp_dir, 'test.txt')
        with open(test_file, 'w') as f:
            f.write('initial content')
        
        self.monitor.start_monitoring()
        self.changes_received.clear()  # Clear initial creation events
        
        # Modify the file
        time.sleep(0.1)  # Brief pause
        with open(test_file, 'a') as f:
            f.write('\nmodified content')
        
        # Wait for debounce and processing
        time.sleep(0.2)
        
        # Check if modification was detected
        modification_events = [c for c in self.changes_received if c.change_type == ChangeType.MODIFIED]
        self.assertGreater(len(modification_events), 0)
    
    def test_file_deletion_detection(self):
        """Test detection of file deletion."""
        # Create initial file
        test_file = os.path.join(self.temp_dir, 'test.txt')
        with open(test_file, 'w') as f:
            f.write('test content')
        
        self.monitor.start_monitoring()
        self.changes_received.clear()  # Clear initial events
        
        # Delete the file
        time.sleep(0.1)  # Brief pause
        os.remove(test_file)
        
        # Wait for debounce and processing
        time.sleep(0.2)
        
        # Check if deletion was detected
        deletion_events = [c for c in self.changes_received if c.change_type == ChangeType.DELETED]
        self.assertGreater(len(deletion_events), 0)
    
    def test_file_type_filtering(self):
        """Test filtering by file types."""
        self.monitor.start_monitoring()
        
        # Create files with different extensions
        txt_file = os.path.join(self.temp_dir, 'test.txt')
        py_file = os.path.join(self.temp_dir, 'test.py')
        
        with open(txt_file, 'w') as f:
            f.write('txt content')
        
        with open(py_file, 'w') as f:
            f.write('py content')
        
        # Wait for processing
        time.sleep(0.2)
        
        # Check that only .txt file was detected (based on config)
        detected_files = [c.file_path for c in self.changes_received]
        self.assertIn(txt_file, detected_files)
        self.assertNotIn(py_file, detected_files)
    
    def test_excluded_paths(self):
        """Test exclusion of specified paths."""
        # Create excluded directory
        excluded_dir = os.path.join(self.temp_dir, 'temp')
        os.makedirs(excluded_dir)
        
        self.monitor.start_monitoring()
        
        # Create file in excluded directory
        excluded_file = os.path.join(excluded_dir, 'test.txt')
        with open(excluded_file, 'w') as f:
            f.write('excluded content')
        
        # Create file in main directory
        included_file = os.path.join(self.temp_dir, 'test.txt')
        with open(included_file, 'w') as f:
            f.write('included content')
        
        # Wait for processing
        time.sleep(0.2)
        
        # Check that excluded file was not detected
        detected_files = [c.file_path for c in self.changes_received]
        self.assertNotIn(excluded_file, detected_files)
        self.assertIn(included_file, detected_files)
    
    def test_debouncing(self):
        """Test debouncing of rapid changes."""
        self.monitor.start_monitoring()
        
        test_file = os.path.join(self.temp_dir, 'test.txt')
        
        # Make rapid changes
        for i in range(5):
            with open(test_file, 'w') as f:
                f.write(f'content {i}')
            time.sleep(0.01)  # Very short delay
        
        # Wait for debounce
        time.sleep(0.2)
        
        # Should have fewer events than changes due to debouncing
        self.assertLess(len(self.changes_received), 5)
        self.assertGreater(len(self.changes_received), 0)
    
    def test_monitoring_stats(self):
        """Test monitoring statistics."""
        stats = self.monitor.get_monitoring_stats()
        
        self.assertIn('is_running', stats)
        self.assertIn('monitored_paths', stats)
        self.assertIn('file_types', stats)
        self.assertIn('excluded_paths', stats)
        self.assertFalse(stats['is_running'])
        
        # Start monitoring and check stats again
        self.monitor.start_monitoring()
        stats = self.monitor.get_monitoring_stats()
        self.assertTrue(stats['is_running'])
        self.assertIn(self.temp_dir, stats['monitored_paths'])
    
    def test_context_manager(self):
        """Test context manager functionality."""
        with FileSystemMonitor(self.config) as monitor:
            monitor.set_change_callback(lambda changes: None)
            self.assertTrue(monitor.is_monitoring())
        
        # Should be stopped after context exit
        self.assertFalse(monitor.is_monitoring())
    
    def test_error_handling_invalid_path(self):
        """Test error handling for invalid paths."""
        invalid_config = MonitoringConfig(
            source_path='/nonexistent/path',
            file_types=['.txt'],
            excluded_paths=[]
        )
        
        invalid_monitor = FileSystemMonitor(invalid_config)
        invalid_monitor.set_change_callback(lambda changes: None)
        
        with self.assertRaises(FileNotFoundError):
            invalid_monitor.start_monitoring()
    
    def test_callback_not_set_error(self):
        """Test error when callback is not set."""
        monitor = FileSystemMonitor(self.config)
        
        with self.assertRaises(ValueError):
            monitor.start_monitoring()


class TestSyncFileSystemEventHandler(unittest.TestCase):
    """Test cases for SyncFileSystemEventHandler."""
    
    def setUp(self):
        """Set up test environment."""
        self.callback_mock = Mock()
        self.config = MonitoringConfig(
            source_path='/test',
            file_types=['.txt', '.md'],
            excluded_paths=['temp'],
            debounce_delay_ms=50
        )
        self.handler = SyncFileSystemEventHandler(self.callback_mock, self.config)
    
    def test_should_monitor_file(self):
        """Test file type filtering."""
        self.assertTrue(self.handler._should_monitor_file('test.txt'))
        self.assertTrue(self.handler._should_monitor_file('test.md'))
        self.assertFalse(self.handler._should_monitor_file('test.py'))
        self.assertFalse(self.handler._should_monitor_file('test'))
    
    def test_debounce_buffer(self):
        """Test debounce buffer functionality."""
        from watchdog.events import FileCreatedEvent
        
        # Create mock event
        event = FileCreatedEvent('/test/file.txt')
        
        # Process event multiple times rapidly
        for _ in range(3):
            self.handler.on_any_event(event)
        
        # Wait for debounce
        time.sleep(0.1)
        
        # Should have been called once due to debouncing
        self.callback_mock.assert_called_once()


if __name__ == '__main__':
    unittest.main()