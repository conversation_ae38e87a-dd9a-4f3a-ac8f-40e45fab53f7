import json
import time
import async<PERSON>
from typing import Dict, Any, <PERSON>, Optional, Tuple
from datetime import datetime
from pathlib import Path

class KiloSystemValidator:
    """
    Comprehensive system validation for the Kilo Code architecture.
    """

    def __init__(self, config_path: str = "kilo-code/config"):
        """
        Initialize the system validator.

        Args:
            config_path (str): Path to configuration files
        """
        self.config_path = Path(config_path)
        self.validation_results = {}
        self.startup_time = None

    def validate_configuration_integrity(self) -> Dict[str, Any]:
        """
        Validates the integrity of all configuration files.

        Returns:
            Dict[str, Any]: Configuration validation results
        """
        validation_results = {
            "timestamp": datetime.now(),
            "config_files": {},
            "integrity_score": 0.0,
            "critical_issues": [],
            "warnings": [],
            "recommendations": []
        }

        # Define required configuration files
        required_configs = {
            "kilo_master_config.json": self._validate_master_config,
            "mcp_unified_config.json": self._validate_mcp_config,
            "agent_routing_rules.json": self._validate_routing_rules
        }

        total_score = 0
        max_score = len(required_configs) * 100

        for config_file, validator_func in required_configs.items():
            config_path = self.config_path / config_file
            file_result = {
                "exists": config_path.exists(),
                "readable": False,
                "valid_json": False,
                "content_valid": False,
                "score": 0,
                "issues": []
            }

            if file_result["exists"]:
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        file_result["readable"] = True

                        # Validate JSON
                        try:
                            config_data = json.loads(content)
                            file_result["valid_json"] = True

                            # Validate content
                            content_validation = validator_func(config_data)
                            file_result["content_valid"] = content_validation["valid"]
                            file_result["score"] = content_validation["score"]
                            file_result["issues"] = content_validation["issues"]

                        except json.JSONDecodeError as e:
                            file_result["issues"].append(f"JSON decode error: {str(e)}")

                except Exception as e:
                    file_result["issues"].append(f"File read error: {str(e)}")
            else:
                file_result["issues"].append("Configuration file not found")
                validation_results["critical_issues"].append(f"Missing required config: {config_file}")

            validation_results["config_files"][config_file] = file_result
            total_score += file_result["score"]

        validation_results["integrity_score"] = (total_score / max_score) * 100

        # Generate recommendations
        if validation_results["integrity_score"] < 80:
            validation_results["recommendations"].append("Review and fix configuration issues")
        if validation_results["integrity_score"] < 60:
            validation_results["critical_issues"].append("Configuration integrity below acceptable threshold")

        return validation_results

    def _validate_master_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validates the master configuration file.
        """
        validation = {"valid": True, "score": 100, "issues": []}

        # Check required sections
        required_sections = ["system", "agents", "performance_targets", "domains"]
        for section in required_sections:
            if section not in config:
                validation["issues"].append(f"Missing required section: {section}")
                validation["score"] -= 20
                validation["valid"] = False

        # Validate agents configuration
        if "agents" in config:
            agents = config["agents"]
            required_agents = ["TechnicalArchitect", "OperationsCoordinator", "ResearchStrategist", "QualityGuardian"]
            for agent in required_agents:
                if agent not in agents:
                    validation["issues"].append(f"Missing required agent: {agent}")
                    validation["score"] -= 10
                    validation["valid"] = False

        # Validate performance targets
        if "performance_targets" in config:
            targets = config["performance_targets"]
            required_targets = ["startup_time_ms", "config_access_time_ms", "agent_routing_time_ms"]
            for target in required_targets:
                if target not in targets:
                    validation["issues"].append(f"Missing performance target: {target}")
                    validation["score"] -= 5

        return validation

    def _validate_mcp_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validates the MCP unified configuration file.
        """
        validation = {"valid": True, "score": 100, "issues": []}

        # Check required sections
        if "mcp_integration" not in config:
            validation["issues"].append("Missing mcp_integration section")
            validation["score"] -= 50
            validation["valid"] = False
            return validation

        mcp_config = config["mcp_integration"]

        # Validate hierarchy
        if "hierarchy" not in mcp_config:
            validation["issues"].append("Missing hierarchy configuration")
            validation["score"] -= 30
            validation["valid"] = False
        else:
            hierarchy = mcp_config["hierarchy"]
            required_tiers = ["tier_1_advanced_reasoning", "tier_2_coordination", "tier_3_research", "tier_4_specialized"]
            for tier in required_tiers:
                if tier not in hierarchy:
                    validation["issues"].append(f"Missing tier: {tier}")
                    validation["score"] -= 15

        # Validate routing rules
        if "routing_rules" not in mcp_config:
            validation["issues"].append("Missing routing rules")
            validation["score"] -= 20

        return validation

    def _validate_routing_rules(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validates the agent routing rules configuration.
        """
        validation = {"valid": True, "score": 100, "issues": []}

        # Check required sections
        if "agent_routing_rules" not in config:
            validation["issues"].append("Missing agent_routing_rules section")
            validation["score"] -= 50
            validation["valid"] = False
            return validation

        routing_config = config["agent_routing_rules"]

        # Validate agents
        if "agents" not in routing_config:
            validation["issues"].append("Missing agents configuration")
            validation["score"] -= 30
            validation["valid"] = False
        else:
            agents = routing_config["agents"]
            required_agents = ["TechnicalArchitect", "OperationsCoordinator", "ResearchStrategist", "QualityGuardian"]
            for agent in required_agents:
                if agent not in agents:
                    validation["issues"].append(f"Missing agent configuration: {agent}")
                    validation["score"] -= 15

        # Validate routing algorithms
        if "routing_algorithms" not in routing_config:
            validation["issues"].append("Missing routing algorithms")
            validation["score"] -= 20

        return validation

    def validate_mcp_servers_connectivity(self) -> Dict[str, Any]:
        """
        Validates MCP server connectivity and tool availability.
        """
        connectivity_results = {
            "timestamp": datetime.now(),
            "servers": {},
            "overall_status": "unknown",
            "available_tools": 0,
            "unavailable_tools": 0,
            "connectivity_score": 0.0
        }

        # Define expected MCP servers and tools
        expected_servers = {
            "sequential-thinking": ["sequentialthinking"],
            "mcp-shrimp-task-manager": ["plan_task", "analyze_task", "execute_task"],
            "tavily": ["tavily-search", "tavily-extract"],
            "exa": ["web_search_exa"],
            "desktop-commander": ["read_file", "write_file", "execute_command"],
            "context7": ["get-library-docs", "resolve-library-id"]
        }

        total_tools = sum(len(tools) for tools in expected_servers.values())
        available_count = 0

        for server_name, tools in expected_servers.items():
            server_result = {
                "connected": False,
                "tools_available": 0,
                "tools_total": len(tools),
                "response_time_ms": 0,
                "error_message": None
            }

            # Simulate connectivity check (in real implementation, would use actual MCP calls)
            try:
                start_time = time.time()
                # Simulated connectivity check
                connection_success = True  # Would be actual connection test
                response_time = (time.time() - start_time) * 1000

                if connection_success:
                    server_result["connected"] = True
                    server_result["tools_available"] = len(tools)  # Assume all tools available if connected
                    server_result["response_time_ms"] = response_time
                    available_count += len(tools)
                else:
                    server_result["error_message"] = "Connection failed"

            except Exception as e:
                server_result["error_message"] = str(e)

            connectivity_results["servers"][server_name] = server_result

        connectivity_results["available_tools"] = available_count
        connectivity_results["unavailable_tools"] = total_tools - available_count
        connectivity_results["connectivity_score"] = (available_count / total_tools) * 100

        # Determine overall status
        if connectivity_results["connectivity_score"] >= 90:
            connectivity_results["overall_status"] = "excellent"
        elif connectivity_results["connectivity_score"] >= 75:
            connectivity_results["overall_status"] = "good"
        elif connectivity_results["connectivity_score"] >= 50:
            connectivity_results["overall_status"] = "degraded"
        else:
            connectivity_results["overall_status"] = "critical"

        return connectivity_results

    def validate_agent_registration(self) -> Dict[str, Any]:
        """
        Validates that all agents are properly registered and configured.
        """
        registration_results = {
            "timestamp": datetime.now(),
            "agents": {},
            "registration_score": 0.0,
            "issues": [],
            "recommendations": []
        }

        # Define expected agents
        expected_agents = {
            "TechnicalArchitect": {
                "model": "anthropic/claude-sonnet-4",
                "complexity_range": {"min": 6, "max": 10},
                "required_capabilities": ["architecture", "design", "analysis"]
            },
            "OperationsCoordinator": {
                "model": "google/gemini-2.5-pro",
                "complexity_range": {"min": 1, "max": 7},
                "required_capabilities": ["coordination", "execution", "management"]
            },
            "ResearchStrategist": {
                "model": "google/gemini-flash",
                "complexity_range": {"min": 3, "max": 8},
                "required_capabilities": ["research", "analysis", "information_gathering"]
            },
            "QualityGuardian": {
                "model": "anthropic/claude-sonnet-4",
                "complexity_range": {"min": 2, "max": 9},
                "required_capabilities": ["quality_assurance", "validation", "testing"]
            }
        }

        total_agents = len(expected_agents)
        registered_count = 0

        for agent_name, expected_config in expected_agents.items():
            agent_result = {
                "registered": False,
                "model_correct": False,
                "complexity_range_valid": False,
                "capabilities_valid": False,
                "score": 0,
                "issues": []
            }

            # Simulate agent registration check
            try:
                # In real implementation, would check actual agent registration
                agent_registered = True  # Simulated check

                if agent_registered:
                    agent_result["registered"] = True
                    agent_result["model_correct"] = True  # Simulated validation
                    agent_result["complexity_range_valid"] = True
                    agent_result["capabilities_valid"] = True
                    agent_result["score"] = 100
                    registered_count += 1
                else:
                    agent_result["issues"].append(f"Agent {agent_name} not registered")

            except Exception as e:
                agent_result["issues"].append(f"Error checking agent {agent_name}: {str(e)}")

            registration_results["agents"][agent_name] = agent_result

        registration_results["registration_score"] = (registered_count / total_agents) * 100

        # Generate recommendations
        if registration_results["registration_score"] < 100:
            registration_results["recommendations"].append("Complete agent registration process")
        if registration_results["registration_score"] < 75:
            registration_results["issues"].append("Critical agent registration issues")

        return registration_results

    def run_startup_validation(self) -> Dict[str, Any]:
        """
        Runs comprehensive startup validation checks.

        Returns:
            Dict[str, Any]: Complete startup validation results
        """
        start_time = time.time()

        validation_results = {
            "timestamp": datetime.now(),
            "startup_time_ms": 0,
            "overall_status": "unknown",
            "overall_score": 0.0,
            "validations": {},
            "critical_issues": [],
            "warnings": [],
            "recommendations": []
        }

        # Run all validation checks
        validation_checks = {
            "configuration_integrity": self.validate_configuration_integrity,
            "mcp_connectivity": self.validate_mcp_servers_connectivity,
            "agent_registration": self.validate_agent_registration
        }

        total_score = 0
        max_score = len(validation_checks) * 100

        for check_name, check_func in validation_checks.items():
            try:
                check_result = check_func()
                validation_results["validations"][check_name] = check_result

                # Extract score from check result
                if "integrity_score" in check_result:
                    total_score += check_result["integrity_score"]
                elif "connectivity_score" in check_result:
                    total_score += check_result["connectivity_score"]
                elif "registration_score" in check_result:
                    total_score += check_result["registration_score"]

                # Collect issues
                if "critical_issues" in check_result:
                    validation_results["critical_issues"].extend(check_result["critical_issues"])
                if "warnings" in check_result:
                    validation_results["warnings"].extend(check_result["warnings"])
                if "recommendations" in check_result:
                    validation_results["recommendations"].extend(check_result["recommendations"])

            except Exception as e:
                validation_results["critical_issues"].append(f"Validation check {check_name} failed: {str(e)}")

        # Calculate overall metrics
        validation_results["startup_time_ms"] = (time.time() - start_time) * 1000
        validation_results["overall_score"] = (total_score / max_score) * 100

        # Determine overall status
        if validation_results["overall_score"] >= 90 and not validation_results["critical_issues"]:
            validation_results["overall_status"] = "healthy"
        elif validation_results["overall_score"] >= 75 and len(validation_results["critical_issues"]) <= 2:
            validation_results["overall_status"] = "warning"
        else:
            validation_results["overall_status"] = "critical"

        # Performance validation
        if validation_results["startup_time_ms"] > 200:  # Target: <200ms
            validation_results["warnings"].append(f"Startup time {validation_results['startup_time_ms']:.1f}ms exceeds target of 200ms")

        self.startup_time = validation_results["startup_time_ms"]
        return validation_results

    def generate_validation_report(self, validation_results: Dict[str, Any]) -> str:
        """
        Generates a human-readable validation report.

        Args:
            validation_results (Dict[str, Any]): Validation results

        Returns:
            str: Formatted validation report
        """
        report = []
        report.append("=" * 60)
        report.append("KILO CODE SYSTEM VALIDATION REPORT")
        report.append("=" * 60)
        report.append(f"Timestamp: {validation_results['timestamp']}")
        report.append(f"Overall Status: {validation_results['overall_status'].upper()}")
        report.append(f"Overall Score: {validation_results['overall_score']:.1f}%")
        report.append(f"Startup Time: {validation_results['startup_time_ms']:.1f}ms")
        report.append("")

        # Validation details
        if "validations" in validation_results:
            report.append("VALIDATION DETAILS:")
            report.append("-" * 40)
            for check_name, check_result in validation_results["validations"].items():
                report.append(f"\n{check_name.upper()}:")
                if "integrity_score" in check_result:
                    report.append(f"  Score: {check_result['integrity_score']:.1f}%")
                elif "connectivity_score" in check_result:
                    report.append(f"  Score: {check_result['connectivity_score']:.1f}%")
                elif "registration_score" in check_result:
                    report.append(f"  Score: {check_result['registration_score']:.1f}%")

        # Critical issues
        if validation_results["critical_issues"]:
            report.append("\nCRITICAL ISSUES:")
            report.append("-" * 40)
            for issue in validation_results["critical_issues"]:
                report.append(f"  ❌ {issue}")

        # Warnings
        if validation_results["warnings"]:
            report.append("\nWARNINGS:")
            report.append("-" * 40)
            for warning in validation_results["warnings"]:
                report.append(f"  ⚠️  {warning}")

        # Recommendations
        if validation_results["recommendations"]:
            report.append("\nRECOMMENDATIONS:")
            report.append("-" * 40)
            for recommendation in validation_results["recommendations"]:
                report.append(f"  💡 {recommendation}")

        report.append("\n" + "=" * 60)

        return "\n".join(report)

if __name__ == '__main__':
    # Example usage
    validator = KiloSystemValidator()

    print("Running Kilo Code System Validation...")
    results = validator.run_startup_validation()

    print("\nValidation Results:")
    print(validator.generate_validation_report(results))

    print(f"\nSystem Status: {results['overall_status']}")
    print(f"Overall Score: {results['overall_score']:.1f}%")
    print(f"Startup Time: {results['startup_time_ms']:.1f}ms")
