{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "build:analyze": "ANALYZE=true next build", "dev": "next dev", "lint": "next lint", "start": "next start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test && npm run test:e2e", "type-check": "tsc --noEmit", "sentry:upload-sourcemaps": "sentry-cli sourcemaps upload --validate --log-level=info ./.next/static", "sentry:create-release": "sentry-cli releases new $npm_package_version", "sentry:finalize-release": "sentry-cli releases finalize $npm_package_version", "deploy": "npm run build && npm run sentry:create-release && npm run sentry:upload-sourcemaps && npm run sentry:finalize-release"}, "dependencies": {"@hookform/resolvers": "latest", "@sentry/nextjs": "^8.40.0", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@react-pdf/renderer": "^4.3.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.5.0", "@supabase/auth-helpers-nextjs": "latest", "@supabase/ssr": "latest", "@supabase/supabase-js": "^2.52.0", "@types/recharts": "^2.0.1", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "embla-carousel-react": "latest", "input-otp": "latest", "lucide-react": "^0.454.0", "micro": "^10.0.1", "next": "15.2.4", "next-themes": "latest", "openai": "^4.52.0", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-resizable-panels": "latest", "recharts": "^3.1.0", "resend": "^4.7.0", "sonner": "latest", "stripe": "^18.3.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "web-push": "^3.6.7", "zod": "^3.23.8"}, "devDependencies": {"@jest/globals": "^30.0.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/web-push": "^3.6.3", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.5", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.0", "typescript": "^5"}}