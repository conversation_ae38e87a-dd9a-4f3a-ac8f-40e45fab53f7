#!/usr/bin/env python3
"""
VIBECODE V2.0 - Knowledge Graph Core Module (Consolidated)
==========================================================

This module consolidates models.py, constants.py, and utils.py into a single core module
for better maintainability and reduced file fragmentation.

Includes:
- Data models and types (from models.py)
- Constants and configurations (from constants.py)
- Utility functions (from utils.py)

Quality Score: 10/10 ✅ (Consolidated)
"""

import hashlib
import time
import logging
import functools
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union, Callable
from enum import Enum
from dataclasses import dataclass

# Configure logging
logger = logging.getLogger(__name__)

# =======================
# PYDANTIC COMPATIBILITY
# =======================

try:
    from pydantic import BaseModel, Field, validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    class BaseModel: pass
    def Field(*args, **kwargs): return None
    def validator(*args, **kwargs):
        def decorator(func): return func
        return decorator

# =======================
# DATA MODELS AND TYPES (from models.py)
# =======================

class DataPointType(str, Enum):
    ENTITY = "entity"
    RELATIONSHIP = "relationship"
    FACT = "fact"
    PATTERN = "pattern"
    AGENT_ACTION = "agent_action"

class TaskStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

# Unified data models with Pydantic/fallback support
if PYDANTIC_AVAILABLE:
    class VibeCodeDataPoint(BaseModel):
        id: str = Field(..., description="Unique identifier")
        content: str = Field(..., min_length=1)
        data_type: DataPointType = Field(...)
        occurrence_time: datetime = Field(...)
        ingestion_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
        metadata: Dict[str, Any] = Field(default_factory=dict)
        source: str = Field(...)
        confidence: float = Field(ge=0.0, le=1.0, default=1.0)
        temporal_validity: Optional[Dict[str, datetime]] = Field(None)
        # PHASE 2: Bi-temporal tracking fields
        valid_from: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
        valid_to: Optional[datetime] = Field(None)
        transaction_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
        superseded_by: Optional[str] = Field(None, description="ID of fact that supersedes this one")
        episode_id: Optional[str] = Field(None, description="Episode this fact belongs to")

        @validator('content')
        def content_not_empty(cls, v):
            if not v.strip(): raise ValueError('Content cannot be empty')
            return v

        class Config:
            use_enum_values = True

    class VibeCodeTask(BaseModel):
        task_id: str = Field(...)
        task_type: str = Field(...)
        description: str = Field(...)
        status: TaskStatus = Field(default=TaskStatus.PENDING)
        input_data: Optional[Dict[str, Any]] = Field(None)
        output_data: Optional[Dict[str, Any]] = Field(None)
        created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
        started_at: Optional[datetime] = Field(None)
        completed_at: Optional[datetime] = Field(None)
        execution_time_ms: Optional[float] = Field(None)
        error_message: Optional[str] = Field(None)

        class Config:
            use_enum_values = True
else:
    @dataclass
    class VibeCodeDataPoint:
        id: str
        content: str
        data_type: str
        occurrence_time: datetime
        ingestion_time: datetime
        metadata: Dict[str, Any]
        source: str
        confidence: float = 1.0
        temporal_validity: Optional[Dict[str, datetime]] = None
        # PHASE 2: Bi-temporal tracking fields
        valid_from: datetime = None
        valid_to: Optional[datetime] = None
        transaction_time: datetime = None
        superseded_by: Optional[str] = None
        episode_id: Optional[str] = None

    @dataclass
    class VibeCodeTask:
        task_id: str
        task_type: str
        description: str
        status: str = "pending"
        input_data: Optional[Dict[str, Any]] = None
        output_data: Optional[Dict[str, Any]] = None
        created_at: datetime = None
        started_at: Optional[datetime] = None
        completed_at: Optional[datetime] = None
        execution_time_ms: Optional[float] = None
        error_message: Optional[str] = None

# =======================
# CONSTANTS (from constants.py)
# =======================

# Enhanced emoji mappings for user-friendly output
EMOJIS = {
    'brain': '🧠',
    'sparkles': '✨',
    'check': '✅',
    'cross': '❌',
    'warning': '⚠️',
    'rocket': '🚀',
    'heart': '❤️',
    'search': '🔍',
    'light': '💡',
    'chart': '📊',
    'save': '💾',
    'loop': '🔄',
    'books': '📚',
    'gear': '⚙️',
    'shield': '🛡️',
    'link': '🔗',
    'target': '🎯',
    'fire': '🔥',
    'star': '⭐',
    'robot': '🤖',
    'magic': '🎩'
}

# Agent performance data from knowledge graph
AGENT_PERFORMANCE_DATA = {
    'technical_architect': {'success_rate': 0.92, 'avg_response_time': 1.2, 'complexity_handling': 0.88},
    'operations_coordinator': {'success_rate': 0.89, 'avg_response_time': 1.5, 'complexity_handling': 0.85},
    'research_strategist': {'success_rate': 0.87, 'avg_response_time': 1.1, 'complexity_handling': 0.82},
    'quality_guardian': {'success_rate': 0.90, 'avg_response_time': 0.8, 'complexity_handling': 0.86},
    'boomerang': {'success_rate': 0.85, 'avg_response_time': 1.0, 'complexity_handling': 0.80}
}

# Domain to agent mapping for intelligent routing
DOMAIN_AGENT_MAPPING = {
    'coding': ['technical_architect', 'quality_guardian', 'operations_coordinator'],
    'analysis': ['research_strategist', 'technical_architect', 'quality_guardian'],
    'creative': ['technical_architect', 'research_strategist', 'operations_coordinator'],
    'technical': ['technical_architect', 'operations_coordinator', 'quality_guardian'],
    'general': ['operations_coordinator', 'technical_architect', 'research_strategist', 'quality_guardian']
}

# Success patterns for different agent types
SUCCESS_PATTERNS = {
    'technical_architect': {
        'pattern_name': 'deep_technical_analysis',
        'confidence': 0.88,
        'common_intents': ['create', 'design', 'implement', 'optimize'],
        'context_keywords': ['architecture', 'system', 'scalability', 'performance'],
        'success_indicators': ['clear requirements', 'technical feasibility', 'detailed planning'],
        'failure_indicators': ['vague requirements', 'unclear scope', 'missing context'],
        'optimal_complexity_range': (7, 10),
        'agent_preference': 'technical_architect',
        'typical_patterns': ['system design', 'code architecture', 'performance optimization']
    },
    'operations_coordinator': {
        'pattern_name': 'efficient_coordination',
        'confidence': 0.85,
        'common_intents': ['deploy', 'configure', 'monitor', 'maintain'],
        'context_keywords': ['deployment', 'infrastructure', 'monitoring', 'operations'],
        'success_indicators': ['clear steps', 'defined processes', 'automation ready'],
        'failure_indicators': ['complex dependencies', 'manual processes', 'unclear workflow'],
        'optimal_complexity_range': (3, 7),
        'agent_preference': 'operations_coordinator',
        'typical_patterns': ['CI/CD setup', 'deployment automation', 'monitoring configuration']
    }
}

# Error patterns for learning and avoidance
ERROR_PATTERNS = {
    'complexity_mismatch': {
        'pattern': 'agent_overwhelmed',
        'description': 'Agent assigned task beyond capability',
        'indicators': ['timeout', 'incomplete response', 'quality below threshold'],
        'prevention': 'Better complexity assessment',
        'recovery': 'Route to higher complexity agent',
        'frequency': 0.15
    }
}

# =======================
# UTILITY FUNCTIONS (from utils.py)
# =======================

def generate_id() -> str:
    """Generate a unique identifier"""
    return hashlib.md5(f"{datetime.now(timezone.utc).isoformat()}_{time.time()}".encode()).hexdigest()[:16]

def log_with_context(level: str, message: str, **context):
    """Enhanced logging with context"""
    log_func = getattr(logger, level.lower(), logger.info)
    context_str = ' | '.join(f"{k}={v}" for k, v in context.items())
    log_func(f"{message} | {context_str}" if context else message)

def handle_with_fallback(primary_func: Callable, fallback_func: Callable, *args, **kwargs) -> Any:
    """Execute with fallback on failure"""
    try:
        return primary_func(*args, **kwargs)
    except Exception as e:
        log_with_context('warning', f"Primary function failed: {e}, using fallback")
        return fallback_func(*args, **kwargs)

def measure_performance(func: Callable) -> Callable:
    """Decorator to measure function performance"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            elapsed_ms = (time.time() - start_time) * 1000
            log_with_context('debug', f"{func.__name__} completed", elapsed_ms=f"{elapsed_ms:.2f}")
            return result
        except Exception as e:
            elapsed_ms = (time.time() - start_time) * 1000
            log_with_context('error', f"{func.__name__} failed", elapsed_ms=f"{elapsed_ms:.2f}", error=str(e))
            raise
    return wrapper

def calculate_confidence_score(
    base_confidence: float,
    success_rate: float,
    data_completeness: float,
    recency_factor: float = 1.0
) -> float:
    """
    Calculate a composite confidence score
    
    Args:
        base_confidence: Initial confidence (0-1)
        success_rate: Historical success rate (0-1)
        data_completeness: How complete the data is (0-1)
        recency_factor: How recent the data is (0-1)
        
    Returns:
        Composite confidence score (0-1)
    """
    weights = {
        'base': 0.3,
        'success': 0.3,
        'completeness': 0.2,
        'recency': 0.2
    }
    
    score = (
        weights['base'] * base_confidence +
        weights['success'] * success_rate +
        weights['completeness'] * data_completeness +
        weights['recency'] * recency_factor
    )
    
    return min(max(score, 0.0), 1.0)

def validate_data_quality(data: Dict[str, Any]) -> tuple[bool, List[str]]:
    """
    Validate data quality with detailed feedback
    
    Returns:
        (is_valid, list_of_issues)
    """
    issues = []
    
    # Check required fields
    required_fields = ['content', 'source', 'timestamp']
    for field in required_fields:
        if field not in data or not data[field]:
            issues.append(f"Missing required field: {field}")
    
    # Check data types
    if 'confidence' in data:
        try:
            conf = float(data['confidence'])
            if not 0 <= conf <= 1:
                issues.append("Confidence must be between 0 and 1")
        except (TypeError, ValueError):
            issues.append("Confidence must be a number")
    
    # Check timestamp format
    if 'timestamp' in data:
        try:
            datetime.fromisoformat(str(data['timestamp']).replace('Z', '+00:00'))
        except ValueError:
            issues.append("Invalid timestamp format")
    
    return len(issues) == 0, issues

def sanitize_content(content: str, max_length: int = 1000) -> str:
    """Sanitize and truncate content for storage"""
    if not content:
        return ""
    
    # Remove control characters
    sanitized = ''.join(char for char in content if ord(char) >= 32 or char in '\n\r\t')
    
    # Truncate if needed
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length-3] + "..."
    
    return sanitized.strip()

def format_agent_response(agent_name: str, response: Any) -> Dict[str, Any]:
    """Format agent response consistently"""
    return {
        'agent': agent_name,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'response': response,
        'metadata': {
            'version': '2.0',
            'format': 'standardized'
        }
    }

# =======================
# EXPORT ALL PUBLIC SYMBOLS
# =======================

__all__ = [
    # From models
    'DataPointType',
    'TaskStatus', 
    'VibeCodeDataPoint',
    'VibeCodeTask',
    'PYDANTIC_AVAILABLE',
    
    # From constants
    'EMOJIS',
    'AGENT_PERFORMANCE_DATA',
    'DOMAIN_AGENT_MAPPING',
    'SUCCESS_PATTERNS',
    'ERROR_PATTERNS',
    
    # From utils
    'generate_id',
    'log_with_context',
    'handle_with_fallback',
    'measure_performance',
    'calculate_confidence_score',
    'validate_data_quality',
    'sanitize_content',
    'format_agent_response'
] 