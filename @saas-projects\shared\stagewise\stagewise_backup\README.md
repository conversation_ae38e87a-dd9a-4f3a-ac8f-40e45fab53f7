# 🎯 Stagewise Centralized Solution - VIBECODE V1.0

## 📋 **Visão Geral**

Solução centralizada para integração do Stagewise Toolbar em projetos Next.js seguindo os padrões VIBECODE V1.0 de arquitetura monorepo com 95% de reuso de infraestrutura.

## 🏗️ **Arquitetura**

```
@project-core/shared/stagewise/
├── config/
│   ├── stagewise.config.ts      # Configuração centralizada
│   └── environment.config.ts    # Detecção de ambiente
├── components/
│   ├── StagewiseProvider.tsx    # Provider reutilizável
│   └── index.ts                 # Exports centralizados
├── utils/
│   ├── installation.ts          # Utilitários de instalação
│   └── validation.ts            # Validação de setup
├── scripts/
│   └── install-stagewise.js     # Script de instalação automática
└── README.md                    # Esta documentação
```

## 🚀 **Como Usar**

### **1. Em Projetos Existentes**

```typescript
// app/layout.tsx
import { StagewiseProvider } from '@project-core/shared/stagewise';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <StagewiseProvider />
      </body>
    </html>
  );
}
```

### **2. Em Novos Projetos**

1. Instalar dependências:
```bash
npm install @stagewise/toolbar-next @stagewise-plugins/react
```

2. Importar e usar o provider no layout raiz
3. O toolbar aparecerá automaticamente apenas em desenvolvimento

## ⚙️ **Configuração**

A configuração é centralizada e automática:
- ✅ **Desenvolvimento**: Toolbar ativo
- ✅ **Produção**: Toolbar desabilitado
- ✅ **SSR**: Otimizado automaticamente
- ✅ **Performance**: Zero impacto em produção

## 🎯 **Benefícios**

- **Manutenibilidade**: Configuração única para todos os projetos
- **Consistência**: Comportamento padronizado
- **Escalabilidade**: Novos projetos herdam automaticamente
- **Performance**: Otimizado para produção

## 🔧 **Projetos Integrados**

- ✅ **aegiswallet** - Crypto Wallet Application
- ✅ **agendatrintae3** - Sistema de Agendamento Médico
- ✅ **neonpro** - Aesthetic Clinic SaaS

## 📚 **Documentação Técnica**

Para detalhes técnicos e configurações avançadas, consulte os arquivos de configuração em `config/`.
