# 📋 KIRO SPEC WORKFLOW - FEATURE DEVELOPMENT

## Spec-Driven Development Process

### Overview
Transform rough ideas into detailed specifications with implementation plans using systematic refinement, research, and design.

### Core Principle
Rely on user establishing ground-truths as we progress. Always ensure user approval before moving to next phase.

## Workflow Phases

### 1. Requirements Gathering
- Generate initial requirements in EARS format based on feature idea
- Iterate with user to refine until complete and accurate
- Focus on user stories and acceptance criteria
- **User approval required before proceeding**

### 2. Design Document Creation
- Develop comprehensive design based on approved requirements
- Conduct necessary research during design process
- Include: Overview, Architecture, Components, Data Models, Error Handling, Testing Strategy
- **User approval required before proceeding**

### 3. Implementation Plan
- Create actionable task list based on design
- Focus ONLY on coding tasks (writing, modifying, testing code)
- Use numbered checkbox format with clear objectives
- Reference specific requirements for each task
- **User approval required before execution**

## File Structure
```
.kiro/specs/{feature-name}/
├── requirements.md    # User stories and acceptance criteria
├── design.md         # Comprehensive design document  
└── tasks.md          # Implementation task checklist
```

## Requirements Format
```markdown
# Requirements Document

## Introduction
[Feature summary]

## Requirements

### Requirement 1
**User Story:** As a [role], I want [feature], so that [benefit]

#### Acceptance Criteria
1. WHEN [event] THEN [system] SHALL [response]
2. IF [precondition] THEN [system] SHALL [response]
```

## Task Format
```markdown
# Implementation Plan

- [ ] 1. Task description
  - Implementation details
  - References to requirements
  - _Requirements: 1.1, 2.3_

- [ ] 2. Next task
  - More details
  - _Requirements: 1.2_
```

## Quality Gates
- Each phase requires explicit user approval
- Feedback-revision cycle until approval received
- No skipping phases without completion
- All requirements must be covered by tasks

## Execution Rules
- Execute ONE task at a time
- Stop after each task completion
- Wait for user review before continuing
- Focus only on specified task scope

---

**Remember**: User approval is MANDATORY at each phase. One task at a time during execution.