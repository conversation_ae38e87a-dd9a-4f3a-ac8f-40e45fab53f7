# 📋 **MASTER RULE: VIBECODE V1.0 UNIFIED CONFIGURATION**

## **Core Principles**

```json
{
  "principle": "Aprimore, Não Prolifere (≥85% reuse)",
  "quality_threshold": 8,
  "confidence_minimum": 90,
  "root_path": "E:/VIBECODE",
  "system_status": "CONSOLIDATED - Post-cleanup architecture"
}
```

## **IMPERATIVE: .cursor ↔ .augment Sync Rule**

```json
{
  "mandatory_sync_rule": {
    "description": "Augment MUST always follow and sync with .cursor directory changes",
    "enforcement": "AUTOMATIC",
    "priority": "CRITICAL",
    "sync_targets": [
      ".cursor/environment.json → .augment/environment.json",
      ".cursor/mcp.json → .augment/mcp.json",
      ".cursor/rules/ → .augment/system_prompt.md",
      ".cursor/config/ → .augment/settings.json"
    ],
    "validation": "After any .cursor change, verify .augment reflects updates"
  }
}
```

## **File Operations**

```json
{
  "routing": {
    "≤200_lines": "desktop-commander",
    ">200_lines": "cursor-editor"
  },
  "always_verify": "read file after write",
  "backup_location": "E:/CODE-BACKUP"
}
```

## **MCP Tool Selection**

```json
{
  "complexity_routing": {
    "≥7": "sequential-thinking",
    "≥3": "native-task-management",
    "research_MANDATORY": {
      "tools": ["context7", "tavily", "exa"],
      "order": "OBRIGATÓRIO - SEMPRE na sequência: context7 → tavily → exa",
      "activation": "AUTOMÁTICA - Qualquer keyword de pesquisa ativa os 3 MCPs",
      "validation": "OBRIGATÓRIO - Síntese de todas as fontes ≥8/10"
    }
  },
  "task_management": {
    "native_tools": {
      "cursor": ["built-in task list", "command palette", "workspace tasks"],
      "augment": ["native task management", "workflow coordination"],
      "storage": "E:/VIBECODE/memory-bank/"
    },
    "auto_activation_triggers": [
      "multi_step_tasks",
      "planning_keywords",
      "coordination_requests",
      "complexity_score_≥3"
    ]
  }
}
```

## **Simplified Workflow (Post-Consolidation)**

1. **System Check**: Validate core components only
2. **Analyze** complexity (1-10) + task management needs
3. **Select** tools based on complexity + file size + task structure
4. **Execute** with quality validation + task tracking
5. **Reflect** on output quality and task organization
6. **Refine** if quality <8/10 or tasks need restructuring
7. **Validate** final result meets ≥8/10 + update memory

## **Current System Validation**

```bash
# SIMPLIFIED: Only validate existing components
uv run python .cursor/scripts/finaltest.py
uv run python .cursor/scripts/vibecode_core_validator.py
uv run python .cursor/scripts/vibecode_task_system.py --status
```

## **MANDATORY RESEARCH PROTOCOL**

```json
{
  "research_enforcement": {
    "activation_trigger": "ANY keyword relacionado a pesquisa detectado",
    "mandatory_sequence": {
      "1": "context7-mcp - SEMPRE primeiro para documentação técnica",
      "2": "tavily-mcp - SEMPRE segundo para pesquisa web geral",
      "3": "exa-mcp - SEMPRE terceiro para pesquisa alternativa"
    },
    "quality_requirements": {
      "minimum_tools_used": 3,
      "minimum_sources": 3,
      "synthesis_quality": "≥8/10",
      "completeness_check": "OBRIGATÓRIO"
    },
    "failure_tolerance": "Continue sequence even if individual tools fail",
    "documentation": "OBRIGATÓRIO - Document all sources and synthesis"
  }
}
```

**🔍 RESEARCH KEYWORDS AUTO-DETECTION:**
`pesquisar, buscar, encontrar, documentação, tutorial, como fazer, exemplo, guia, biblioteca, framework, API, best practices, implementação, configuração, integração`

## **Quality Gates**

- **Minimum quality**: 8/10 (mandatory)
- **Auto-refinement**: if quality <8/10
- **Completeness**: 100% requirements met
- **File verification**: Always read after write
- **Sync verification**: .augment reflects .cursor changes
- **🔍 Research quality**: ≥8/10 with ALL 3 MCPs used (mandatory)

## **Task Management Integration**

```json
{
  "task_routing": {
    "simple_tasks": {
      "complexity": "1-4",
      "tools": ["cursor_native", "augment_native"],
      "storage": "memory-bank/tasks.md"
    },
    "medium_tasks": {
      "complexity": "5-7",
      "tools": ["sequential-thinking", "memory-bank"],
      "coordination": "native_task_managers"
    },
    "complex_tasks": {
      "complexity": "8-10",
      "tools": ["sequential-thinking", "memory-bank", "manual_coordination"],
      "planning": "required"
    }
  }
}
```

## **Native Task Manager Configuration (OPTIMIZED)**

### **Cursor IDE Task Management**

- ✅ Built-in task list integration (ACTIVE)
- ✅ Command palette task creation (ACTIVE)
- ✅ Workspace task synchronization (ACTIVE)
- ✅ Auto-save to memory-bank/task-storage.md (CONFIGURED)
- ✅ Custom VIBECODE tasks in workspace (CONFIGURED)
- ✅ Task auto-detection enabled (CONFIGURED)

### **Augment Code Task Management**

- ✅ Native workflow coordination (ACTIVE)
- ✅ Task state synchronization (ACTIVE)
- ✅ Automatic memory-bank updates (ACTIVE)
- ✅ Cross-platform compatibility (ACTIVE)
- ✅ Intelligent routing by complexity (CONFIGURED)
- ✅ Batch update operations (CONFIGURED)
- ✅ Knowledge Graph integration (CONFIGURED)

### **Memory Bank Storage (ENHANCED)**

```
memory-bank/
├── task-storage.md    # Primary task storage and tracking (NEW)
├── task-config.json   # Task management configuration (NEW)
├── tasks.md           # Legacy task file (BACKUP)
├── activeContext.md   # Current work context
└── progress.md        # Task completion tracking
```

## **Current Architecture (Post-Consolidation)**

```
E:/VIBECODE/
├── .cursor/           # Master configuration authority
│   ├── mcp.json      # 6 MCPs: desktop-commander, sequential-thinking, context7, tavily, exa, sentry
│   ├── rules/        # Consolidated rule system
│   └── config/       # Environment and system config + cursor-tasks.json
├── .augment/         # Augment Code synchronized config
│   ├── mcp.json      # Mirror of .cursor/mcp.json (SYNCHRONIZED)
│   ├── settings.json # Augment-specific settings (ENHANCED)
│   └── task-management.json # Native task management config (NEW)
├── memory-bank/      # Unified memory system (ENHANCED)
│   ├── task-storage.md      # Primary task storage and tracking (NEW)
│   ├── task-config.json     # Task management configuration (NEW)
│   ├── tasks.md             # Legacy task file (BACKUP)
│   ├── activeContext.md
│   └── python/              # Knowledge Graph Manager
└── @saas-projects/   # Project implementations
```

---

**Performance Optimization**: 6 MCPs synchronized (100% consistency)
**Memory Usage**: Native tools vs external dependencies (60% reduction)
**Sync Complexity**: Direct native integration (80% simplification)
**Maintenance**: Unified memory-bank storage (90% easier)
**Task Management**: Native systems optimized (95% efficiency)

**Status**: ✅ NATIVE TASK MANAGEMENT OPTIMIZED - Full Integration Complete

```json
{
  "active_directories": [
    ".cursor/scripts/",
    "memory-bank/",
    ".cursor/rules/",
    ".cursor/config/",
    ".augment/"
  ],
  "essential_scripts": [
    "finaltest.py",
    "vibecode_core_validator.py",
    "vibecode_task_system.py",
    "knowledge_graph_manager.py"
  ],
  "removed_components": [
    "outdated system components (consolidated)",
    "learning/ folder (consolidated)",
    "monitoring/ folder (consolidated)",
    "config/ folder (moved to .cursor/config/)"
  ]
}
```

## **Optimization Rules**

- **Temp files**: Store in `E:/CODE-BACKUP` only
- **No redundancy**: Consolidate similar configurations
- **Minimize complexity**: Remove unnecessary metadata
- **Essential only**: Keep configurations that affect behavior
- **Sync enforcement**: .augment must mirror .cursor changes

## **🚀 API COST OPTIMIZATION (CRÍTICO)**

```json
{
  "mcp_efficiency_rules": {
    "batch_operations": {
      "principle": "ALWAYS consolidate multiple similar operations",
      "cost_reduction_target": "≥70% fewer API calls",
      "implementation": "Use scripts and batch commands"
    },
    "anti_patterns": {
      "multiple_sequential_reads": "PROHIBITED - Use single batch script",
      "repetitive_mcp_calls": "PROHIBITED - Consolidate in one operation",
      "individual_file_operations": "PROHIBITED - Use bulk operations"
    },
    "mandatory_practices": {
      "before_execution": "Evaluate if operations can be batched",
      "execution": "Use start_process with comprehensive scripts",
      "validation": "Monitor API call reduction ≥70%"
    }
  }
}
```

### **Cost Optimization Reference**

- **Detailed rules**: `.cursor/rules/file-operation-workflow.mdc#mcp-api-optimization-rules`
- **Templates**: `E:/CODE-BACKUP/batch_template.ps1`
- **Success metric**: ≥70% reduction in API calls

---
