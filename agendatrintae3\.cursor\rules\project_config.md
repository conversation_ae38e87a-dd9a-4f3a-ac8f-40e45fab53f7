# AgendaTrintaE3 Project Configuration

## Project Overview
- **Project Name**: AgendaTrintaE3
- **Type**: Next.js Scheduling and Calendar Application
- **Architecture**: Event management system with real-time capabilities
- **Primary Focus**: Advanced scheduling, calendar management, and event coordination

## Development Guidelines

### Frontend Development
- **Framework**: Next.js 14+ with App Router
- **Styling**: Tailwind CSS with calendar-optimized components
- **UI Components**: Calendar-specific component library with date/time widgets
- **State Management**: React Query for server state, Zustand for client state
- **TypeScript**: Strict mode with date/time type safety
- **Real-time**: WebSocket or Server-Sent Events for live updates

### Backend Development
- **API Routes**: Next.js API routes with calendar-specific endpoints
- **Database**: PostgreSQL with optimized date/time indexing
- **Authentication**: Role-based access control for different user types
- **Calendar Integration**: Google Calendar, Outlook, and other calendar APIs
- **Notifications**: Email, SMS, and push notification systems
- **Timezone Handling**: Proper timezone conversion and management

### Calendar-Specific Standards
- **Date/Time Handling**: Use proper timezone-aware libraries (date-fns, dayjs)
- **Recurring Events**: Implement RRULE standard for recurring events
- **Conflict Detection**: Prevent scheduling conflicts and double-bookings
- **Performance**: Optimize for large calendar datasets and date ranges
- **Accessibility**: Screen reader support for calendar navigation

### Project-Specific Rules
1. Always handle timezones correctly in all date/time operations
2. Implement proper validation for date ranges and scheduling conflicts
3. Use standardized calendar formats (iCal, CalDAV) where applicable
4. Ensure real-time synchronization across multiple users
5. Implement proper caching for calendar data and recurring events
6. Follow accessibility guidelines for calendar interfaces
7. Provide multiple calendar views (day, week, month, year)
8. Implement proper search and filtering for events
9. Handle edge cases for daylight saving time transitions
10. Ensure data consistency in multi-user scheduling scenarios

## Memory Management
- Store project-specific context in `agendatrintae3/.cursor/memory/`
- Track calendar feature development progress
- Document scheduling algorithm decisions
- Maintain integration and API documentation