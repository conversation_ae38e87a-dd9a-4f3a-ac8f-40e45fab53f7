# 7 · Release Roadmap

| Fase | Duração | Entregáveis | Critério <PERSON>ída |
|------|---------|-------------|-------------------|
| Sprint 0.a – DevOps Foundations | 2 sem | Repo, CI/CD, Supabase Auth + RLS | Pipelines verdes |
| Sprint 0.b – UX Wireframes | 2 sem (paralelo) | Protótipo Figma, teste usabilidade ≥ 80 % | Aprovado |
| Sprint 1 – Autenticação & Agenda Core | 2 sem | Login/OAuth, Agenda CRUD, Portal Paciente α | Agendar ≤ 2 min |
| Sprint 2 – Financeiro Essencial | 2 sem | Contas + Caixa + Conciliação β | Caixa < 2 h |
| Sprint 3 – BI Core + Export | 2 sem | KPI dashboard + CSV/PDF | KPI load < 2 s |
| Sprint RC – QA/Perf | 1 sem | Stress × 2 pico, pentest, bug fix | p95 API ≤ 800 ms |
| Beta Fechado (3 clínicas) | 1 sem | Deploy staging, feedback | NPS β ≥ 65 |
| Buffer Sprint | 1 sem | Refatorações, Next.js 15 hardening | Zero crit bugs |
| Sprint 4 – CRM & Campanhas | 2 sem | Segmentação, lembretes cobrança | Campanha ≤ 5 cliques |
| Sprint 5 – Estoque Simplificado | 2 sem | Entradas/saídas, alertas | Notif ≤ 60 s |
| **MVP Público** | — | P0 + CRM no ar, LGPD ok | No‑show −25 %; MRR +25 % |
