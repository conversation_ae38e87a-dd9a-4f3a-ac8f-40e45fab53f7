/**
 * VIBECODE V1.0 - Consolidated Utilities
 * Replaces: sync-rules.js, health-check.js, test-reporter.js
 * Version: 1.0.0
 */

const fs = require('fs').promises;
const path = require('path');

class VibecodeUtilities {
  constructor() {
    this.projectRoot = process.cwd();
    this.rulesDir = path.join(this.projectRoot, '.cursor', 'rules');
    this.configDir = path.join(this.projectRoot, '.cursor', 'config');
  }

  // ===== SYNC RULES =====
  async syncRules() {
    console.log('🔄 Syncing VIBECODE rules...');
    
    try {
      const masterConfig = await this.loadConfig('master-config.json');
      const rules = await this.loadAllRules();
      
      console.log(`✅ Loaded ${Object.keys(rules).length} rules`);
      console.log(`✅ Master config version: ${masterConfig.vibecode.version}`);
      
      return { success: true, rules: Object.keys(rules).length };
    } catch (error) {
      console.error('❌ Rule sync failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  async loadAllRules() {
    const ruleFiles = await fs.readdir(this.rulesDir);
    const rules = {};
    
    for (const file of ruleFiles) {
      if (file.endsWith('.mdc')) {
        const content = await fs.readFile(path.join(this.rulesDir, file), 'utf8');
        rules[file] = {
          name: file,
          size: content.length,
          lines: content.split('\\n').length
        };
      }
    }
    
    return rules;
  }

  // ===== HEALTH CHECK =====
  async healthCheck() {
    console.log('🏥 Running VIBECODE health check...');
    
    const checks = {
      files: await this.checkEssentialFiles(),
      mcps: await this.checkMCPs(),
      rules: await this.checkRules(),
      config: await this.checkConfig()
    };
    
    const overall = Object.values(checks).every(check => check.status === 'ok');
    
    console.log(`\\n📊 Health Check Summary:`);
    console.log(`Files: ${checks.files.status === 'ok' ? '✅' : '❌'}`);
    console.log(`MCPs: ${checks.mcps.status === 'ok' ? '✅' : '❌'}`);
    console.log(`Rules: ${checks.rules.status === 'ok' ? '✅' : '❌'}`);
    console.log(`Config: ${checks.config.status === 'ok' ? '✅' : '❌'}`);
    console.log(`Overall: ${overall ? '✅ HEALTHY' : '❌ ISSUES FOUND'}`);
    
    return { overall, checks };
  }

  async checkEssentialFiles() {
    const essentialFiles = [
      '.cursor/master-config.json',
      '.cursor/mcp.json',
      '.cursor/README-MASTER.md',
      '.cursor/SETUP-GUIDE.md',
      '.cursor/config/essential.json'
    ];
    
    const missing = [];
    
    for (const file of essentialFiles) {
      try {
        await fs.access(path.join(this.projectRoot, file));
      } catch {
        missing.push(file);
      }
    }
    
    return {
      status: missing.length === 0 ? 'ok' : 'error',
      missing: missing,
      message: missing.length === 0 ? 'All essential files present' : `Missing: ${missing.join(', ')}`
    };
  }

  async checkMCPs() {
    try {
      const mcpConfig = await this.loadConfig('mcp.json');
      const serverCount = Object.keys(mcpConfig.mcpServers || {}).length;
      
      return {
        status: serverCount >= 3 ? 'ok' : 'warning',
        count: serverCount,
        message: `${serverCount} MCP servers configured`
      };
    } catch (error) {
      return {
        status: 'error',
        message: `MCP config error: ${error.message}`
      };
    }
  }

  async checkRules() {
    try {
      const rules = await this.loadAllRules();
      const ruleCount = Object.keys(rules).length;
      
      return {
        status: ruleCount >= 8 ? 'ok' : 'warning',
        count: ruleCount,
        message: `${ruleCount} rule files found`
      };
    } catch (error) {
      return {
        status: 'error',
        message: `Rules check error: ${error.message}`
      };
    }
  }

  async checkConfig() {
    try {
      const essential = await this.loadConfig('config/essential.json');
      const hasOptimization = !!essential.optimization;
      
      return {
        status: hasOptimization ? 'ok' : 'warning',
        message: hasOptimization ? 'Configuration complete' : 'Missing optimization config'
      };
    } catch (error) {
      return {
        status: 'error',
        message: `Config check error: ${error.message}`
      };
    }
  }

  // ===== TEST REPORTER =====
  async generateTestReport() {
    console.log('📋 Generating VIBECODE test report...');
    
    const report = {
      timestamp: new Date().toISOString(),
      system: 'VIBECODE V1.0',
      tests: {
        structure: await this.testStructure(),
        functionality: await this.testFunctionality(),
        performance: await this.testPerformance()
      }
    };
    
    const overall = Object.values(report.tests).every(test => test.passed);
    report.overall = overall ? 'PASSED' : 'FAILED';
    
    console.log(`\\n📊 Test Report Summary:`);
    console.log(`Structure: ${report.tests.structure.passed ? '✅' : '❌'}`);
    console.log(`Functionality: ${report.tests.functionality.passed ? '✅' : '❌'}`);
    console.log(`Performance: ${report.tests.performance.passed ? '✅' : '❌'}`);
    console.log(`Overall: ${overall ? '✅ PASSED' : '❌ FAILED'}`);
    
    return report;
  }

  async testStructure() {
    const health = await this.healthCheck();
    return {
      name: 'File Structure',
      passed: health.overall,
      details: health.checks
    };
  }

  async testFunctionality() {
    try {
      const rules = await this.loadAllRules();
      const config = await this.loadConfig('master-config.json');
      
      return {
        name: 'Core Functionality',
        passed: true,
        details: {
          rules_loaded: Object.keys(rules).length,
          config_version: config.vibecode.version
        }
      };
    } catch (error) {
      return {
        name: 'Core Functionality',
        passed: false,
        error: error.message
      };
    }
  }

  async testPerformance() {
    const start = Date.now();
    
    try {
      await this.loadAllRules();
      await this.loadConfig('master-config.json');
      await this.loadConfig('config/essential.json');
      
      const loadTime = Date.now() - start;
      const passed = loadTime < 100; // Target: <100ms
      
      return {
        name: 'Performance',
        passed: passed,
        loadTime: loadTime,
        target: '< 100ms',
        details: `Load time: ${loadTime}ms`
      };
    } catch (error) {
      return {
        name: 'Performance',
        passed: false,
        error: error.message
      };
    }
  }

  // ===== UTILITY METHODS =====
  async loadConfig(configPath) {
    const fullPath = path.join(this.projectRoot, '.cursor', configPath);
    const content = await fs.readFile(fullPath, 'utf8');
    return JSON.parse(content);
  }

  // ===== CLI INTERFACE =====
  static async run() {
    const utils = new VibecodeUtilities();
    const command = process.argv[2];
    
    switch (command) {
      case 'sync':
        await utils.syncRules();
        break;
      case 'health':
        await utils.healthCheck();
        break;
      case 'test':
        await utils.generateTestReport();
        break;
      case 'all':
        console.log('🚀 Running all VIBECODE utilities...\\n');
        await utils.syncRules();
        console.log('');
        await utils.healthCheck();
        console.log('');
        await utils.generateTestReport();
        break;
      default:
        console.log('VIBECODE V1.0 Utilities');
        console.log('Usage: node utilities.js [sync|health|test|all]');
        console.log('');
        console.log('Commands:');
        console.log('  sync   - Sync rules and configuration');
        console.log('  health - Run health check');
        console.log('  test   - Generate test report'); 
        console.log('  all    - Run all utilities');
    }
  }
}

// Run if called directly
if (require.main === module) {
  VibecodeUtilities.run().catch(console.error);
}

module.exports = VibecodeUtilities;