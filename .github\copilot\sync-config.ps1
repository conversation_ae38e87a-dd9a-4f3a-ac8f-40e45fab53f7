#!/bin/pwsh

# Sync script for VIBECODE Copilot integration
$ErrorActionPreference = "Stop"

# Define paths
$cursorPath = "E:\VIBECODE\.cursor"
$augmentPath = "E:\VIBECODE\.augment"
$copilotPath = "E:\VIBECODE\.github\copilot"

# Function to sync files
function Sync-Files {
    param (
        [string]$source,
        [string]$target
    )

    if (Test-Path $source) {
        $targetParent = Split-Path -Parent $target
        if (-not (Test-Path $targetParent)) {
            New-Item -ItemType Directory -Path $targetParent -Force | Out-Null
        }

        if ((Get-Item $source) -is [System.IO.DirectoryInfo]) {
            if (Test-Path $target) {
                Remove-Item -Path $target -Recurse -Force
            }
            Copy-Item -Path $source -Destination $target -Force -Recurse
        } else {
            Copy-Item -Path $source -Destination $target -Force
        }
        Write-Host "Synced: $source -> $target"
    } else {
        Write-Warning "Source not found: $source"
    }
}

# Sync configurations
Sync-Files "$cursorPath\environment.json" "$augmentPath\environment.json"
Sync-Files "$cursorPath\mcp.json" "$augmentPath\mcp.json"
Sync-Files "$cursorPath\rules" "$augmentPath\system_prompt.md"
Sync-Files "$cursorPath\config" "$augmentPath\settings.json"

# Update Copilot configurations
Sync-Files "$copilotPath\agent-rules.md" "$cursorPath\rules\copilot-integration.md"
Sync-Files "$copilotPath\settings.json" "$cursorPath\config\copilot-settings.json"

Write-Host "Sync complete. Validating..."

# Validate sync
$validationErrors = @()

function Test-FileSync {
    param (
        [string]$source,
        [string]$target
    )

    if (-not (Test-Path $source) -or -not (Test-Path $target)) {
        $validationErrors += "Missing file pair: $source <-> $target"
        return
    }

    $sourceHash = Get-FileHash $source
    $targetHash = Get-FileHash $target

    if ($sourceHash.Hash -ne $targetHash.Hash) {
        $validationErrors += "File mismatch: $source <-> $target"
    }
}

# Run validation checks
Test-FileSync "$cursorPath\environment.json" "$augmentPath\environment.json"
Test-FileSync "$cursorPath\mcp.json" "$augmentPath\mcp.json"

# Report results
if ($validationErrors.Count -gt 0) {
    Write-Error "Validation failed:`n$($validationErrors -join "`n")"
    exit 1
} else {
    Write-Host "All validations passed successfully."
}
