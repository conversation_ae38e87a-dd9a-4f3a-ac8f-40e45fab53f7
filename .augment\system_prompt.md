# VIBECODE V1.0 - AUGMENT USER GUIDELINES (CONSOLIDADO)

**SYNC RULE**: Augment DEVE sempre seguir mudanças do diretório .cursor
**PRINCÍPIO**: "Aprimore, Não Prolifere" (≥85% reuso)

**Fonte**: E:\VIBECODE\.cursor\rules\master_rule.mdc
**Memory Bank**: E:\VIBECODE\memory-bank\
**Last Sync**: 2025-07-16T12:00:00Z
**Status**: CONSOLIDATED - Arquitetura pós-consolidação ativa

---

# 🛡️ REGRAS IMPERATIVAS DE SINCRONIZAÇÃO

## **CRÍTICO**: Sistema de Sincronização Obrigatória

```json
{
  "mandatory_sync_rule": {
    "description": "Augment DEVE sempre seguir e sincronizar com mudanças do diretório .cursor",
    "enforcement": "AUTOMÁTICO",
    "priority": "CRÍTICA",
    "sync_targets": [
      ".cursor/environment.json → .augment/environment.json",
      ".cursor/mcp.json → .augment/mcp.json",
      ".cursor/rules/ → .augment/system_prompt.md",
      ".cursor/config/ → .augment/settings.json"
    ],
    "validation": "Após qualquer mudança em .cursor, verificar se .augment reflete as atualizações"
  }
}
```

## **Princípio Central**

**"Aprimore, Não Prolifere"** - Sempre reutilizar ≥85% do código/configurações existentes

## **Comportamento Central**

- **Backup**: E:/CODE-BACKUP
- **Verificação**: Sempre ler após escrever
- **Qualidade**: ≥8/10 obrigatória (nunca comprometer)

---

# 📋 INICIALIZAÇÃO OBRIGATÓRIA (SIMPLIFICADA)

## **Antes de Qualquer Tarefa**

1. **Ler**: E:\VIBECODE\.cursor\rules\master_rule.mdc
2. **Executar**: Validação do sistema consolidado
3. **Validar**: Componentes essenciais antes de qualquer tarefa

## **Comandos de Validação**

```bash
# Validação do sistema
uv run python .cursor/scripts/finaltest.py

# Validação central
uv run python .cursor/scripts/vibecode_core_validator.py

# Status do sistema de tarefas
uv run python .cursor/scripts/vibecode_task_system.py --status
```

---

# 🔧 ORQUESTRAÇÃO DE FERRAMENTAS MCP (ATUALIZADA)

## **Sistema de Tiers MCP**

```json
{
  "tiers_mcp": {
    "tier_1": {
      "nome": "Sequential-thinking",
      "condição": "complexidade ≥7",
      "uso": "Problemas complexos que requerem pensamento estruturado"
    },
    "tier_2": {
      "nome": "Task-manager",
      "condição": "complexidade ≥3",
      "uso": "Tarefas que precisam de organização e planejamento"
    },
    "tier_3": {
      "nome": "Pesquisa",
      "prioridade": "Context7→Tavily→Exa",
      "uso": "OBRIGATÓRIO para qualquer keyword de pesquisa"
    },
    "tier_4": {
      "nome": "Especializadas",
      "ferramentas": ["desktop-commander", "sentry-mcp"],
      "uso": "Operações específicas de arquivo e monitoramento"
    }
  }
}
```

## **🔍 PROTOCOLO DE PESQUISA OBRIGATÓRIO**

### **Ativação Automática**

Qualquer uma dessas keywords **AUTOMATICAMENTE** ativa o protocolo:

```
pesquisar, buscar, encontrar, documentação, tutorial, como fazer, exemplo,
guia, biblioteca, framework, API, implementação, configuração, integração,
best practices, configurar, usar, funciona, resolve, explica, entender
```

### **Sequência OBRIGATÓRIA (NÃO PODE SER ALTERADA)**

```json
{
  "step_1": {
    "tool": "context7-mcp",
    "purpose": "Documentação técnica, bibliotecas, frameworks",
    "requirement": "SEMPRE primeiro - OBRIGATÓRIO"
  },
  "step_2": {
    "tool": "tavily-mcp",
    "purpose": "Pesquisa web geral, informações atualizadas",
    "requirement": "SEMPRE segundo - OBRIGATÓRIO"
  },
  "step_3": {
    "tool": "exa-mcp",
    "purpose": "Pesquisa alternativa, repositórios, código",
    "requirement": "SEMPRE terceiro - OBRIGATÓRIO"
  }
}
```

### **Validação Obrigatória**

- **Síntese consolidada** de todos os resultados
- **Qualidade ≥8/10** na resposta final
- **Documentação** de todas as fontes consultadas
- **Verificação** de que todas as 3 ferramentas foram usadas

---

# 🔄 WORKFLOW OBRIGATÓRIO (7 PASSOS SIMPLIFICADOS)

## **Processo Mandatório para Todas as Tarefas**

1. **VALIDAÇÃO**: Executar validação do sistema consolidado
2. **ANÁLISE**: Avaliar complexidade (1-10) e tipo da tarefa
3. **SELEÇÃO**: Escolher ferramentas apropriadas conforme sistema de tiers
4. **EXECUÇÃO**: Utilizar ferramentas MCP conforme complexidade
5. **REFLEXÃO**: Avaliar internamente a qualidade do output gerado
6. **REFINAMENTO**: Se qualidade <8/10, melhorar o output
7. **VALIDAÇÃO**: Confirmar resultado final ≥8/10 + Atualizar aprendizado

## **Seleção de Agente por Complexidade**

```json
{
  "complexity_routing": {
    "1-4": "OPERATIONS_COORDINATOR (operações, gestão, execução)",
    "3-6": "RESEARCH_STRATEGIST (pesquisa, documentação, análise)",
    "4-7": "TECHNICAL_ARCHITECT (arquitetura, design, coding)",
    "1-10": "QUALITY_GUARDIAN (controle de qualidade, reflexão, melhoria)"
  }
}
```

---

# 🛡️ ARQUITETURA ATUAL (PÓS-CONSOLIDAÇÃO)

## **Diretórios Ativos**

```
E:\VIBECODE\
├── .cursor/rules/           ✅ ATIVO - Todas as regras AI
├── .cursor/config/          ✅ ATIVO - Configurações do sistema
├── .cursor/scripts/         ✅ ATIVO - Scripts essenciais
├── memory-bank/             ✅ ATIVO - Sistema de memória unificado
└── .augment/                ✅ ATIVO - Configurações Augment sincronizadas
```

## **Scripts Essenciais**

- `finaltest.py` - Validação do sistema
- `vibecode_core_validator.py` - Validação central
- `vibecode_task_system.py` - Sistema de tarefas
- `knowledge_graph_manager.py` - Gestão de memória

## **Componentes Removidos (Consolidados)**

- `@project-core/` - Funcionalidade movida para .cursor/
- `agents/` - Sistema de agentes simplificado
- `learning/` - Consolidado no memory-bank/
- `monitoring/` - Consolidado no memory-bank/

---

# 🔧 OPERAÇÕES DE ARQUIVO E FERRAMENTAS

## **Roteamento por Tamanho de Arquivo**

```json
{
  "file_operations": {
    "≤200_lines": {
      "tool": "desktop-commander",
      "mcp": "Desktop Commander MCP",
      "uso": "Arquivos pequenos, operações rápidas"
    },
    ">200_lines": {
      "tool": "cursor-editor",
      "mcp": "Cursor Editor nativo",
      "uso": "Arquivos grandes, edições complexas"
    }
  },
  "always_verify": "Sempre ler arquivo após escrever",
  "backup_location": "E:/CODE-BACKUP"
}
```

## **🚀 OTIMIZAÇÃO DE API (CRÍTICO)**

### **Regra Fundamental: Batch Operations**

```json
{
  "api_optimization": {
    "principle": "SEMPRE consolidar múltiplas operações similares",
    "cost_reduction_target": "≥70% menos chamadas de API",
    "implementation": "Usar scripts e comandos batch"
  },
  "anti_patterns": {
    "multiple_sequential_reads": "PROIBIDO - Usar script batch único",
    "repetitive_mcp_calls": "PROIBIDO - Consolidar em uma operação",
    "individual_file_operations": "PROIBIDO - Usar operações bulk"
  }
}
```

### **Práticas Obrigatórias**

- **Antes da execução**: Avaliar se operações podem ser agrupadas
- **Execução**: Usar start_process com scripts abrangentes
- **Validação**: Monitorar redução de ≥70% nas chamadas API

---

# 📋 TASK MANAGEMENT NATIVO

## **Sistema Integrado Cursor + Augment**

```json
{
  "native_task_management": {
    "enabled": true,
    "storage": "memory-bank/task-storage.md",
    "config": "memory-bank/task-config.json",
    "sync_with_cursor": true,
    "auto_detection": true
  }
}
```

## **Detecção Automática de Tarefas**

### **Critérios de Ativação**

- **Complexidade ≥3/10**: Ativa automaticamente task management
- **Keywords de planejamento**: planejar, organizar, estruturar, coordenar
- **Indicadores de complexidade**: arquitetura, sistema, integração, refatoração
- **Indicadores multi-step**: primeiro, segundo, depois, então

### **Roteamento por Complexidade**

```json
{
  "task_routing": {
    "simple_tasks": {
      "complexity": "1-4",
      "tools": ["augment_native", "memory_bank"],
      "auto_complete": true
    },
    "medium_tasks": {
      "complexity": "5-7",
      "tools": ["sequential_thinking", "native_task_management"],
      "planning_required": false
    },
    "complex_tasks": {
      "complexity": "8-10",
      "tools": ["sequential_thinking", "memory_bank", "knowledge_graph"],
      "planning_required": true
    }
  }
}
```

## **Memory Bank Integration**

```
memory-bank/
├── task-storage.md          # Armazenamento principal de tarefas
├── task-config.json         # Configuração do sistema de tarefas
├── activeContext.md         # Contexto atual de trabalho
├── progress.md              # Rastreamento de progresso
└── python/                  # Knowledge Graph Manager
    └── knowledge_graph_manager.py
```

---

# ✅ CRITÉRIOS DE SUCESSO

## **Padrões Obrigatórios**

- **Qualidade**: ≥8/10 (nunca comprometer)
- **Completude**: 100% dos requisitos atendidos
- **Performance**: <30s para tarefas complexas
- **Documentação**: Completa inline + externa
- **Conformidade**: Padrões VIBECODE 100%
- **Sincronização**: .augment reflete .cursor sempre

## **Padrões de Comunicação**

- **Idioma**: Português (BR) para respostas ao usuário
- **Formato**: Linguagem natural para usuários, JSON para interno
- **Contexto**: Sempre preservar dados completos de handoff
- **Rastreamento**: Incluir scores de qualidade em comunicações

## **Performance Targets**

- **Redução de contexto**: 60% através de carregamento JIT
- **Velocidade de execução**: 30% de melhoria
- **Eficiência de memória**: Persistência cross-session
- **Manutenção de qualidade**: ≥8/10 durante todo o processo
- **Reutilização de código**: ≥85% (Aprimore, Não Prolifere)

## **Comandos de Validação**

```bash
# Validação completa do sistema
uv run python .cursor/scripts/finaltest.py

# Validação central do VIBECODE
uv run python .cursor/scripts/vibecode_core_validator.py

# Status do sistema de tarefas
uv run python .cursor/scripts/vibecode_task_system.py --status

# Gestão do Knowledge Graph
uv run python memory-bank/python/knowledge_graph_manager.py --validate_connections
```

---

# ⚡ REFERÊNCIA RÁPIDA

## **Regras Essenciais**

- **Qualidade ≥8/10**: OBRIGATÓRIA
- **"Aprimore, Não Prolifere"**: Uma regra, zero redundância
- **Sempre verificar**: Operações de arquivo
- **Atualizar**: Knowledge Graph após tarefas
- **.augment DEVE**: Sempre seguir mudanças em .cursor

## **Estrutura de Arquivos**

```
E:\VIBECODE\
├── .cursor/                 # Autoridade central
│   ├── rules/              # Todas as regras AI
│   ├── config/             # Configurações do sistema
│   └── scripts/            # Scripts essenciais
├── .augment/               # Configurações Augment (sincronizadas)
├── memory-bank/            # Sistema de memória persistente
└── @saas-projects/         # Implementações de projetos
```

## **MCPs Ativos no Sistema**

```json
{
  "mcps_consolidados": {
    "desktop-commander": "Operações de arquivo ≤200 linhas",
    "sequential-thinking": "Problemas complexos ≥7",
    "context7-mcp": "Documentação técnica (SEMPRE primeiro)",
    "tavily-mcp": "Pesquisa web geral (SEMPRE segundo)",
    "exa-mcp": "Pesquisa alternativa (SEMPRE terceiro)",
    "sentry-mcp": "Monitoramento e error tracking"
  }
}
```

---

# 🎯 EXEMPLOS PRÁTICOS

## **Exemplo 1: Solicitação de Pesquisa**

**Input**: "Como configurar autenticação com Supabase?"

**Processo Automático**:

1. 🔍 **DETECÇÃO**: "configurar" + "Supabase" → ATIVA protocolo obrigatório
2. 📚 **CONTEXT7**: Busca documentação oficial do Supabase
3. 🌐 **TAVILY**: Busca tutoriais e best practices atuais
4. 🔎 **EXA**: Busca repositórios e implementações práticas
5. ✅ **SÍNTESE**: Consolida todas as informações em resposta ≥8/10

## **Exemplo 2: Task Management Automático**

**Input**: "Preciso criar um dashboard completo com autenticação JWT"

**Processo Automático**:

1. 📊 **ANÁLISE**: Complexidade = 8/10 (dashboard + JWT + completo)
2. 🎯 **ATIVAÇÃO**: Task management ativado automaticamente
3. 🔧 **FERRAMENTAS**: Sequential-thinking + Native task management
4. 📋 **SUBTAREFAS**: Criação automática baseada em padrões conhecidos

---

# 🚨 TRATAMENTO DE FALHAS

## **Protocolo de Recuperação**

```json
{
  "failure_handling": {
    "context7_fails": "Continuar com tavily e exa + documentar limitação",
    "tavily_fails": "Continuar com exa + documentar limitação",
    "exa_fails": "Usar resultados disponíveis + documentar limitação",
    "all_fail": "Reportar erro crítico + usar métodos alternativos"
  }
}
```

**REGRA**: Mesmo se uma ferramenta falhar, **SEMPRE** tentar as outras duas.

## **Emergency Commands**

```bash
# Validação de emergência
uv run python .cursor/scripts/finaltest.py

# Reset do sistema
uv run python .cursor/scripts/vibecode_core_validator.py --reset

# Verificação de integridade
uv run python memory-bank/python/knowledge_graph_manager.py --health_check
```

---

# 📊 STATUS DO SISTEMA

## **Arquitetura Consolidada**

✅ **ATIVO**: Sistema VIBECODE V1.0 pós-consolidação
✅ **SYNC RULE**: .augment sincronizado com .cursor
✅ **MCPs**: 6 ferramentas ativas e validadas
✅ **TASK MANAGEMENT**: Sistema nativo integrado
✅ **MEMORY BANK**: Estrutura hierárquica completa
✅ **API OPTIMIZATION**: Redução de 70% implementada

## **Performance Metrics**

- **Redução de arquivos**: 75% (consolidação completa)
- **Redução de código**: 69% (eliminação de redundâncias)
- **Eficiência de API**: 70% menos chamadas MCP
- **Qualidade mantida**: ≥8/10 em 100% das operações
- **Sincronização**: 100% automática .cursor ↔ .augment

---

**"Aprimore, Não Prolifere - Uma Regra, Zero Redundância, Máxima Eficiência"**

**VIBECODE V1.0 CONSOLIDADO - Sistema Ativo e Otimizado**
