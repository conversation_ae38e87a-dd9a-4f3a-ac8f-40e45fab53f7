# Neon Pro – Product Requirements Document (PRD) - Index

This directory contains the sharded sections of the complete PRD for better organization and focused development work.

## Document Structure

1. [Objective & Context](./01-objective-context.md)
2. [Business Goals & Success Metrics](./02-business-goals-metrics.md)
3. [Personas & User Profiles](./03-personas-user-profiles.md)
4. [Core Functionality & MVP Scope](./04-core-functionality-mvp-scope.md)
5. [Key User Journeys & Flows](./05-key-user-journeys-flows.md)
6. [Requirements](./06-requirements.md)
7. [Release Roadmap](./07-release-roadmap.md)
8. [Risks, Dependencies & Assumptions](./08-risks-dependencies-assumptions.md)
9. [Technical Constraints & External Integrations](./09-technical-constraints-integrations.md)

## Usage Guidelines

- Each shard focuses on a specific aspect of the product requirements
- Reference these documents during sprint planning and development
- Update individual sections as requirements evolve
- Maintain consistency across all shards when making changes

## Quick Reference

**MVP Core Modules**: Authentication, Agenda, Portal Paciente, Financeiro, BI, Cadastro Pacientes  
**Key Performance Targets**: 30% reduction in scheduling time, 25% reduction in no-shows, 25% MRR growth  
**Technology Stack**: Next.js 15 + Supabase + Vercel Edge + shadcn/ui

---

Generated by BMad-Method (PO Agent) - version 2025-07-18
