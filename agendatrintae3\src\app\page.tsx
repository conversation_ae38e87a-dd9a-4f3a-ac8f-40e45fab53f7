import { Suspense } from 'react'
import { getAppointmentsByUser } from '@/lib/actions/appointments'

// Enable Partial Prerendering for this page
export const experimental_ppr = true

// Static Components (Prerendered)
function StaticHeroSection() {
  return (
    <header className="text-center mb-12">
      <h1 className="text-4xl font-bold text-primary mb-4">
        AGENDATRINTAE3
      </h1>
      <p className="text-xl text-text-secondary">
        Sistema de Agendamento Médico Inteligente
      </p>
      <p className="text-lg text-text-muted mt-2">
        Powered by Next.js 15 + PPR + Drizzle ORM + Supabase
      </p>
    </header>
  )
}

function StaticFeatureCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
      <div className="bg-surface p-6 rounded-lg border border-border shadow-card">
        <h2 className="text-xl font-semibold text-primary mb-3">
          📅 Agendamento Online
        </h2>
        <p className="text-text-secondary">
          Agende consultas 24/7 com médicos especialistas de forma rápida e segura.
        </p>
      </div>

      <div className="bg-surface p-6 rounded-lg border border-border shadow-card">
        <h2 className="text-xl font-semibold text-secondary mb-3">
          👨‍⚕️ Especialistas Qualificados
        </h2>
        <p className="text-text-secondary">
          Acesso a médicos especialistas com CRM validado e anos de experiência.
        </p>
      </div>

      <div className="bg-surface p-6 rounded-lg border border-border shadow-card">
        <h2 className="text-xl font-semibold text-info mb-3">
          💊 Prescrições Digitais
        </h2>
        <p className="text-text-secondary">
          Receba prescrições digitais e acompanhe seu histórico médico completo.
        </p>
      </div>

      <div className="bg-surface p-6 rounded-lg border border-border shadow-card">
        <h2 className="text-xl font-semibold text-success mb-3">
          🏥 Telemedicina
        </h2>
        <p className="text-text-secondary">
          Consultas por vídeo para maior comodidade e acessibilidade.
        </p>
      </div>

      <div className="bg-surface p-6 rounded-lg border border-border shadow-card">
        <h2 className="text-xl font-semibold text-warning mb-3">
          📱 Lembretes Automáticos
        </h2>
        <p className="text-text-secondary">
          Receba lembretes por SMS e email para não perder suas consultas.
        </p>
      </div>

      <div className="bg-surface p-6 rounded-lg border border-border shadow-card">
        <h2 className="text-xl font-semibold text-error mb-3">
          🔒 Dados Seguros
        </h2>
        <p className="text-text-secondary">
          Seus dados médicos protegidos com criptografia de ponta a ponta.
        </p>
      </div>
    </div>
  )
}

// Dynamic Components (Streamed)
async function DynamicAppointmentOverview() {
  // Simulate appointment data fetch
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  const appointmentsResult = await getAppointmentsByUser()
  const appointmentCount = appointmentsResult.success ? appointmentsResult.appointments?.length || 0 : 0
  
  return (
    <div className="bg-gradient-primary text-white p-8 rounded-xl text-center">
      <h2 className="text-2xl font-bold mb-4">
        Painel de Consultas
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white/10 p-4 rounded-lg">
          <div className="text-3xl font-bold">{appointmentCount}</div>
          <div className="text-sm opacity-90">Consultas Agendadas</div>
        </div>
        <div className="bg-white/10 p-4 rounded-lg">
          <div className="text-3xl font-bold">0</div>
          <div className="text-sm opacity-90">Consultas Hoje</div>
        </div>
        <div className="bg-white/10 p-4 rounded-lg">
          <div className="text-3xl font-bold">0</div>
          <div className="text-sm opacity-90">Histórico Médico</div>
        </div>
      </div>
    </div>
  )
}

async function DynamicNextAppointment() {
  // Simulate next appointment fetch
  await new Promise(resolve => setTimeout(resolve, 800))
  
  return (
    <div className="bg-info/10 border border-info/20 p-6 rounded-lg">
      <h3 className="text-info font-semibold text-lg mb-2">Próxima Consulta</h3>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm">Data</span>
          <span className="text-text-muted">Nenhuma consulta agendada</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm">Médico</span>
          <span className="text-text-muted">-</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm">Especialidade</span>
          <span className="text-text-muted">-</span>
        </div>
      </div>
    </div>
  )
}

// Skeleton Components
function AppointmentOverviewSkeleton() {
  return (
    <div className="bg-gray-200 animate-pulse p-8 rounded-xl">
      <div className="h-8 bg-gray-300 rounded mb-4"></div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="h-20 bg-gray-300 rounded"></div>
        <div className="h-20 bg-gray-300 rounded"></div>
        <div className="h-20 bg-gray-300 rounded"></div>
      </div>
    </div>
  )
}

function NextAppointmentSkeleton() {
  return (
    <div className="bg-gray-200 animate-pulse p-6 rounded-lg">
      <div className="h-6 bg-gray-300 rounded mb-4"></div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-300 rounded"></div>
        <div className="h-4 bg-gray-300 rounded"></div>
        <div className="h-4 bg-gray-300 rounded"></div>
      </div>
    </div>
  )
}

export default function Home() {
  return (
    <main className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Static Content - Prerendered */}
        <StaticHeroSection />
        <StaticFeatureCards />
        
        {/* Dynamic Content - Streamed */}
        <div className="space-y-8">
          <Suspense fallback={<AppointmentOverviewSkeleton />}>
            <DynamicAppointmentOverview />
          </Suspense>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Suspense fallback={<NextAppointmentSkeleton />}>
              <DynamicNextAppointment />
            </Suspense>
            
            <div className="bg-success/10 border border-success/20 p-6 rounded-lg">
              <h3 className="text-success font-semibold text-lg mb-2">Ações Rápidas</h3>
              <div className="space-y-3">
                <button className="w-full bg-primary text-white py-2 px-4 rounded hover:bg-primary/90 transition-colors">
                  Agendar Nova Consulta
                </button>
                <button className="w-full bg-secondary text-white py-2 px-4 rounded hover:bg-secondary/90 transition-colors">
                  Ver Histórico Médico
                </button>
                <button className="w-full bg-info text-white py-2 px-4 rounded hover:bg-info/90 transition-colors">
                  Buscar Especialistas
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-success/10 border border-success/20 p-4 rounded-lg text-center">
            <div className="text-success font-semibold">Migração</div>
            <div className="text-sm text-text-muted">Phase 6 Complete</div>
          </div>
          <div className="bg-warning/10 border border-warning/20 p-4 rounded-lg text-center">
            <div className="text-warning font-semibold">Stack</div>
            <div className="text-sm text-text-muted">Next.js 15 + PPR</div>
          </div>
          <div className="bg-error/10 border border-error/20 p-4 rounded-lg text-center">
            <div className="text-error font-semibold">Database</div>
            <div className="text-sm text-text-muted">Drizzle + Supabase</div>
          </div>
          <div className="bg-info/10 border border-info/20 p-4 rounded-lg text-center">
            <div className="text-info font-semibold">Sistema</div>
            <div className="text-sm text-text-muted">Agendamento</div>
          </div>
        </div>
      </div>
    </main>
  )
}
