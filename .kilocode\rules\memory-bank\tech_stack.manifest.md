---
source_id: tech_stack
data_file: @project-core/memory/global_standards.md
priority: 5
enabled: true
tags_func: tech_standards_tags
description: Padrões técnicos e stack tecnológico do projeto
---

# Tech Stack Manifest

Este manifesto carrega os padrões técnicos e informações sobre a stack tecnológica.

## Configuração

- **Source ID**: `tech_stack`
- **Data File**: Padrões globais e tecnologias
- **Priority**: 5 (prioridade baixa)
- **Tags Function**: `tech_standards_tags` para categorização técnica

## Funcionalidade

Carrega informações sobre tecnologias utilizadas, padrões de desenvolvimento e configurações técnicas do projeto.
