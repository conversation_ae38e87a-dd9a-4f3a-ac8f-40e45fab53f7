{"vibecode": {"version": "1.0.0", "description": "VIBECODE V1.0 - Master Configuration (Consolidated)", "created": "2025-01-15", "status": "PRODUCTION_READY"}, "mcp_servers": {"desktop-commander": "@mcp/desktop-commander", "sequential-thinking": "@mcp/sequential-thinking", "context7": "@mcp/context7-mcp", "tavily": "@mcp/tavily-mcp", "exa": "@mcp/exa-mcp"}, "workflows": {"mandatory_steps": 7, "quality_threshold": 8, "principle": "Aprimore, Não Prolifere"}, "paths": {"rules": ".cursor/rules/", "config": ".cursor/config/", "memory": "memory-bank/", "scripts": ".cursor/scripts/", "saas_projects": "@saas-projects/"}, "agents": {"complexity_routing": {"1-4": "manager", "2-5": "advisor", "3-6": "strategist", "4-7": "executor", "6-9": "coder", "8-10": "architect"}}, "environment": {"language": "pt-BR", "platform": "windows", "shell": "powershell"}, "optimization": {"cache_ttl": 300, "max_entries": 1000, "timeout_ms": 100, "sampling_rate": 0.1}}