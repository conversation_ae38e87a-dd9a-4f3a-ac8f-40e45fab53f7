/**
 * 🔍 Stagewise Validation Utilities
 * VIBECODE V1.0 - Configuration and Setup Validation
 * 
 * Provides validation utilities for Stagewise setup across projects
 */

import { StagewiseConfig } from '../config/stagewise.config';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * Validate Stagewise configuration
 */
export function validateConfiguration(config: StagewiseConfig): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: []
  };

  // Validate plugins
  if (!config.plugins || config.plugins.length === 0) {
    result.errors.push('No plugins configured. ReactPlugin is required for Next.js projects.');
    result.isValid = false;
  }

  // Validate settings
  const validPositions = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
  if (!validPositions.includes(config.settings.position)) {
    result.errors.push(`Invalid position: ${config.settings.position}. Must be one of: ${validPositions.join(', ')}`);
    result.isValid = false;
  }

  const validThemes = ['light', 'dark', 'auto'];
  if (!validThemes.includes(config.settings.theme)) {
    result.errors.push(`Invalid theme: ${config.settings.theme}. Must be one of: ${validThemes.join(', ')}`);
    result.isValid = false;
  }

  // Validate performance settings
  if (config.performance.debounceMs < 100) {
    result.warnings.push('Debounce time is very low, may impact performance');
  }

  if (config.performance.maxElements > 5000) {
    result.warnings.push('Max elements is very high, may impact performance');
  }

  // Security validations
  if (config.settings.showInProduction) {
    result.errors.push('Stagewise should never be enabled in production');
    result.isValid = false;
  }

  // Suggestions
  if (config.settings.zIndex < 9000) {
    result.suggestions.push('Consider using a higher z-index to ensure toolbar visibility');
  }

  return result;
}

/**
 * Validate project dependencies
 */
export function validateDependencies(): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: []
  };

  try {
    // Check if required packages are available
    require.resolve('@stagewise/toolbar-next');
    require.resolve('@stagewise-plugins/react');
  } catch (error) {
    result.errors.push('Required Stagewise packages not found. Run: npm install @stagewise/toolbar-next @stagewise-plugins/react');
    result.isValid = false;
  }

  return result;
}

/**
 * Validate environment setup
 */
export function validateEnvironment(): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: []
  };

  // Check Node.js environment
  if (typeof process === 'undefined') {
    result.errors.push('Node.js environment not detected');
    result.isValid = false;
  }

  // Check Next.js environment
  if (typeof window !== 'undefined' && !window.next) {
    result.warnings.push('Next.js environment not fully detected');
  }

  // Check development mode
  if (process.env.NODE_ENV === 'production') {
    result.suggestions.push('Stagewise is disabled in production mode (as expected)');
  }

  return result;
}

/**
 * Run comprehensive validation
 */
export function runFullValidation(config: StagewiseConfig): ValidationResult {
  const configValidation = validateConfiguration(config);
  const depsValidation = validateDependencies();
  const envValidation = validateEnvironment();

  return {
    isValid: configValidation.isValid && depsValidation.isValid && envValidation.isValid,
    errors: [...configValidation.errors, ...depsValidation.errors, ...envValidation.errors],
    warnings: [...configValidation.warnings, ...depsValidation.warnings, ...envValidation.warnings],
    suggestions: [...configValidation.suggestions, ...depsValidation.suggestions, ...envValidation.suggestions]
  };
}

/**
 * Log validation results
 */
export function logValidationResults(results: ValidationResult, projectName?: string): void {
  const prefix = projectName ? `[${projectName}]` : '[Stagewise]';
  
  if (results.errors.length > 0) {
    console.error(`❌ ${prefix} Validation Errors:`, results.errors);
  }
  
  if (results.warnings.length > 0) {
    console.warn(`⚠️ ${prefix} Validation Warnings:`, results.warnings);
  }
  
  if (results.suggestions.length > 0) {
    console.info(`💡 ${prefix} Suggestions:`, results.suggestions);
  }
  
  if (results.isValid && results.errors.length === 0) {
    console.log(`✅ ${prefix} Stagewise validation passed`);
  }
}
