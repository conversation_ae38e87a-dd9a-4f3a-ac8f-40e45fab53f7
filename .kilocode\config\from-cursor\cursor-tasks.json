{"version": "2.0.0", "vibecode": {"taskManagement": {"enabled": true, "version": "1.0.0", "type": "native", "integration": "memory-bank"}}, "tasks": [{"label": "VIBECODE: Initialize Task System", "type": "shell", "command": "uv", "args": ["run", "python", ".cursor/scripts/vibecode_task_system.py", "--init"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "VIBECODE: Sync Tasks with Memory Bank", "type": "shell", "command": "uv", "args": ["run", "python", ".cursor/scripts/vibecode_task_system.py", "--sync"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "VIBECODE: Task System Status", "type": "shell", "command": "uv", "args": ["run", "python", ".cursor/scripts/vibecode_task_system.py", "--status"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "VIBECODE: Create Task", "type": "shell", "command": "uv", "args": ["run", "python", ".cursor/scripts/vibecode_task_system.py", "--create", "${input:taskName}", "${input:taskDescription}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}], "inputs": [{"id": "taskName", "description": "Task Name", "default": "New Task", "type": "promptString"}, {"id": "taskDescription", "description": "Task Description", "default": "Task description", "type": "promptString"}]}