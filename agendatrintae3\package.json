{"name": "agendatrintae3", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-slot": "^1.1.1", "@supabase/supabase-js": "^2.50.0", "@stripe/stripe-js": "^4.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "drizzle-orm": "^0.35.0", "lucide-react": "^0.454.0", "next": "15.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.13.3", "sonner": "^1.5.0", "stripe": "^17.4.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "drizzle-kit": "^0.28.0", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}