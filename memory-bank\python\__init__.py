"""
VIBECODE V1.0 - Memory Package
==============================
Memory management, knowledge graph and learning components.
"""

# Knowledge graph components
try:
    from .knowledge_graph_manager import KnowledgeGraphManager
except ImportError:
    pass

# Unified memory system
try:
    from .unified_memory_system import UnifiedMemorySystem
except ImportError:
    pass

# Learning engine components
try:
    from .learning_engine import (
        LearningEngine,
        LearningPattern,
        KnowledgeItem
    )
except ImportError:
    pass

# Enhanced learning patterns - alias for compatibility
try:
    from .learning_engine import LearningEngine as EnhancedLearningSystem
except ImportError:
    pass

# Memory bridge components
try:
    from .augment_memory_bridge import AugmentMemoryBridge
except ImportError:
    pass

# Export main memory components
__all__ = [
    # Core managers
    "KnowledgeGraphManager",
    "UnifiedMemorySystem",

    # Learning components
    "LearningEngine",
    "LearningPattern",
    "KnowledgeItem",
    "EnhancedLearningSystem",

    # Bridge components
    "AugmentMemoryBridge",

    # Legacy exports for compatibility
    "knowledge_graph_manager",
    "unified_memory_system",
    "memory_manager"
]
