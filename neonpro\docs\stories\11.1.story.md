# Story 11.1: Cadastro e Gestão de Produtos

## Story Overview

**Como** gestora da clínica  
**Eu quero** sistema completo de cadastro e gestão de produtos/materiais  
**Para que** eu possa organizar meu estoque, controlar fornecedores e ter informações precisas de todos os materiais utilizados

### Story Details

- **Epic**: Epic 11 - Estoque Simplificado
- **Story Points**: 8
- **Priority**: P1 (High)
- **Theme**: Product Management, Supplier Management & Inventory Foundation
- **Dependencies**: Epic 1-4 (Auth & Base System)

### Acceptance Criteria

#### AC1: Cadastro Completo de Produtos e Materiais

- [ ] **GIVEN** acesso ao sistema de estoque
- [ ] **WHEN** cadastro novo produto/material
- [ ] **THEN** registro com informações completas
- [ ] **AND** código único gerado automaticamente
- [ ] **AND** descrição detalhada e especificações técnicas
- [ ] **AND** categoria e subcategoria (consumível, instrumental, medicamento, cosmético)
- [ ] **AND** unidade de medida (un, ml, g, kg, cx)
- [ ] **AND** informações de validade e controle de lote
- [ ] **AND** pontos de reposição (mínimo, máximo, ideal)
- [ ] **AND** status ativo/inativo com data

#### AC2: Gestão de Fornecedores e Preços

- [ ] **GIVEN** produto cadastrado no sistema
- [ ] **WHEN** associo fornecedores ao produto
- [ ] **THEN** gestão completa de fornecedores
- [ ] **AND** múltiplos fornecedores por produto
- [ ] **AND** preços por fornecedor com histórico
- [ ] **AND** prazo de entrega e condições de pagamento
- [ ] **AND** fornecedor preferencial marcado
- [ ] **AND** avaliação de fornecedor (qualidade, pontualidade)
- [ ] **AND** contatos e informações comerciais
- [ ] **AND** status ativo/inativo por fornecedor

#### AC3: Categorização e Organização Inteligente

- [ ] **GIVEN** múltiplos produtos no sistema
- [ ] **WHEN** organizo o catálogo de produtos
- [ ] **THEN** categorização hierárquica completa
- [ ] **AND** categorias principais (consumíveis, instrumentais, medicamentos, cosméticos)
- [ ] **AND** subcategorias específicas para estética
- [ ] **AND** tags customizáveis para filtros rápidos
- [ ] **AND** busca inteligente por código, nome, categoria
- [ ] **AND** filtros avançados (fornecedor, preço, validade)
- [ ] **AND** ordenação por múltiplos critérios
- [ ] **AND** favoritos para produtos mais utilizados

#### AC4: Controle de Especificações e Documentação

- [ ] **GIVEN** produto que requer documentação especial
- [ ] **WHEN** adiciono especificações técnicas
- [ ] **THEN** gestão completa de documentação
- [ ] **AND** upload de fichas técnicas e bulas
- [ ] **AND** certificados de qualidade e ANVISA
- [ ] **AND** especificações de armazenamento
- [ ] **AND** instruções de uso e dosagem
- [ ] **AND** alertas para documentos vencidos
- [ ] **AND** histórico de atualizações de documentos
- [ ] **AND** download de documentos para consulta

#### AC5: Configuração de Parâmetros de Controle

- [ ] **GIVEN** necessidade de controle específico por produto
- [ ] **WHEN** configuro parâmetros de gestão
- [ ] **THEN** personalização completa de controle
- [ ] **AND** pontos de reposição calculados automaticamente
- [ ] **AND** estoque de segurança configurável
- [ ] **AND** prazo de validade crítico personalizado
- [ ] **AND** controle de lote obrigatório ou opcional
- [ ] **AND** rastreabilidade individual por item
- [ ] **AND** integração com procedimentos específicos
- [ ] **AND** configuração de sazonalidade de uso

#### AC6: Interface de Gestão e Bulk Operations

- [ ] **GIVEN** múltiplos produtos para gerenciar
- [ ] **WHEN** realizo operações em massa
- [ ] **THEN** interface eficiente de gestão
- [ ] **AND** importação de produtos via CSV/Excel
- [ ] **AND** exportação de catálogo completo
- [ ] **AND** atualização em massa de preços
- [ ] **AND** alteração de categorias em lote
- [ ] **AND** ativação/desativação múltipla
- [ ] **AND** duplicação de produtos similares
- [ ] **AND** histórico de alterações auditável

### Technical Requirements

#### Product Management System

```typescript
// Sistema de Gestão de Produtos
interface ProdutoEstoque {
  id: string
  codigo_interno: string
  codigo_ean?: string
  codigo_fornecedor?: string
  
  // Informações Básicas
  nome: string
  descricao: string
  especificacoes_tecnicas?: string
  categoria: CategoriaProduto
  subcategoria: string
  tags: string[]
  
  // Unidades e Medidas
  unidade_medida: UnidadeMedida
  peso_unitario?: number
  volume_unitario?: number
  dimensoes?: DimensoesProduto
  
  // Controle de Estoque
  controle_lote: boolean
  controle_validade: boolean
  controle_individual: boolean
  prazo_validade_critico: number // dias
  
  // Pontos de Reposição
  estoque_minimo: number
  estoque_maximo: number
  estoque_ideal: number
  estoque_seguranca: number
  
  // Status e Metadados
  status: StatusProduto
  ativo: boolean
  favorito: boolean
  criticidade: CriticidadeProduto
  
  // Fornecedores
  fornecedores: FornecedorProduto[]
  fornecedor_preferencial_id?: string
  
  // Documentação
  documentos: DocumentoProduto[]
  
  // Integração com Procedimentos
  procedimentos_associados: string[]
  consumo_padrao: ConsumoPadrao[]
  
  // Sazonalidade
  padrao_sazonalidade?: PadraoSazonalidade
  
  // Auditoria
  criado_em: Date
  criado_por: string
  ultima_atualizacao: Date
  atualizado_por: string
  clinica_id: string
}

// Fornecedor de Produto
interface FornecedorProduto {
  fornecedor_id: string
  produto_id: string
  
  // Informações Comerciais
  codigo_produto_fornecedor: string
  preco_atual: number
  preco_anterior?: number
  data_ultimo_preco: Date
  
  // Condições Comerciais
  prazo_entrega_dias: number
  quantidade_minima: number
  desconto_quantidade?: DescontoQuantidade[]
  condicoes_pagamento: string[]
  
  // Qualidade e Performance
  avaliacao_qualidade: number // 1-5
  avaliacao_pontualidade: number // 1-5
  numero_pedidos_realizados: number
  percentual_entregas_ok: number
  
  // Status
  ativo: boolean
  preferencial: boolean
  
  // Histórico de Preços
  historico_precos: HistoricoPreco[]
  
  // Última Compra
  ultima_compra_data?: Date
  ultima_compra_quantidade?: number
  ultima_compra_preco?: number
  
  // Metadados
  criado_em: Date
  atualizado_em: Date
}

// Categorias de Produtos
interface CategoriaProduto {
  id: string
  nome: string
  nivel: number // 1=principal, 2=subcategoria
  categoria_pai_id?: string
  
  // Configurações da Categoria
  icone: string
  cor: string
  descricao: string
  
  // Regras Específicas
  requer_lote: boolean
  requer_validade: boolean
  requer_documentacao: boolean
  
  // Controles Especiais
  controle_anvisa: boolean
  controle_receituario: boolean
  controle_temperatura: boolean
  
  // Subcategorias
  subcategorias: CategoriaProduto[]
  
  // Metadados
  ativo: boolean
  ordem_exibicao: number
  clinica_id: string
}

// Documento de Produto
interface DocumentoProduto {
  id: string
  produto_id: string
  tipo: TipoDocumento
  
  // Informações do Documento
  nome: string
  descricao?: string
  numero_documento?: string
  
  // Arquivo
  arquivo_url: string
  arquivo_nome: string
  arquivo_tipo: string
  arquivo_tamanho: number
  
  // Validade
  data_validade?: Date
  requer_renovacao: boolean
  dias_alerta_vencimento: number
  
  // Status
  status: StatusDocumento
  versao: number
  
  // Auditoria
  upload_por: string
  upload_em: Date
  aprovado_por?: string
  aprovado_em?: Date
  
  // Multi-tenant
  clinica_id: string
}

// Consumo Padrão por Procedimento
interface ConsumoPadrao {
  produto_id: string
  procedimento_id: string
  
  // Consumo
  quantidade_padrao: number
  quantidade_minima: number
  quantidade_maxima: number
  
  // Variações
  variacao_por_profissional: VariacaoProfissional[]
  variacao_por_complexidade: VariacaoComplexidade[]
  
  // Configuração
  obrigatorio: boolean
  substituivel: boolean
  produtos_substitutos: string[]
  
  // Metadados
  configurado_em: Date
  configurado_por: string
  ativo: boolean
}

// Histórico de Preços
interface HistoricoPreco {
  data: Date
  preco_anterior: number
  preco_novo: number
  motivo_alteracao: string
  usuario_alteracao: string
  fornecedor_id: string
  produto_id: string
}

// Desconto por Quantidade
interface DescontoQuantidade {
  quantidade_minima: number
  percentual_desconto: number
  preco_unitario: number
}

// Dimensões do Produto
interface DimensoesProduto {
  comprimento: number
  largura: number
  altura: number
  unidade: UnidadeMedida
}

// Padrão de Sazonalidade
interface PadraoSazonalidade {
  produtos_verao: boolean
  produtos_inverno: boolean
  picos_sazonais: PicoSazonal[]
  fatores_multiplicadores: FatorSazonal[]
}

interface PicoSazonal {
  mes_inicio: number
  mes_fim: number
  fator_multiplicador: number
  descricao: string
}

// Tipos Enum
type CategoriaProdutoType = 'consumivel' | 'instrumental' | 'medicamento' | 'cosmetico' | 'equipamento'
type UnidadeMedida = 'un' | 'ml' | 'g' | 'kg' | 'cx' | 'l' | 'mg' | 'ampola' | 'seringa'
type StatusProduto = 'ativo' | 'inativo' | 'descontinuado' | 'aguardando_aprovacao'
type CriticidadeProduto = 'baixa' | 'media' | 'alta' | 'critica'
type TipoDocumento = 'ficha_tecnica' | 'bula' | 'certificado_anvisa' | 'laudo_qualidade' | 'manual_uso'
type StatusDocumento = 'valido' | 'vencido' | 'vencendo' | 'pendente_aprovacao'
```

#### Database Schema for Product Management

```sql
-- Categorias de Produtos
CREATE TABLE categorias_produtos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(255) NOT NULL,
  nivel INTEGER DEFAULT 1,
  categoria_pai_id UUID REFERENCES categorias_produtos(id),
  
  -- Configurações Visuais
  icone VARCHAR(100) DEFAULT 'package',
  cor VARCHAR(7) DEFAULT '#6B7280',
  descricao TEXT,
  
  -- Regras da Categoria
  requer_lote BOOLEAN DEFAULT FALSE,
  requer_validade BOOLEAN DEFAULT FALSE,
  requer_documentacao BOOLEAN DEFAULT FALSE,
  
  -- Controles Especiais
  controle_anvisa BOOLEAN DEFAULT FALSE,
  controle_receituario BOOLEAN DEFAULT FALSE,
  controle_temperatura BOOLEAN DEFAULT FALSE,
  
  -- Organização
  ativo BOOLEAN DEFAULT TRUE,
  ordem_exibicao INTEGER DEFAULT 0,
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  atualizado_em TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT chk_categoria_nome_valido CHECK (LENGTH(TRIM(nome)) > 0),
  CONSTRAINT chk_nivel_categoria CHECK (nivel >= 1 AND nivel <= 3)
);

-- Produtos do Estoque
CREATE TABLE produtos_estoque (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Códigos de Identificação
  codigo_interno VARCHAR(50) NOT NULL,
  codigo_ean VARCHAR(20),
  codigo_ncm VARCHAR(10),
  
  -- Informações Básicas
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  especificacoes_tecnicas TEXT,
  
  -- Categorização
  categoria_id UUID NOT NULL REFERENCES categorias_produtos(id),
  subcategoria VARCHAR(100),
  tags TEXT[] DEFAULT '{}',
  
  -- Unidades e Medidas
  unidade_medida unidade_medida_type NOT NULL,
  peso_unitario DECIMAL(10,3),
  volume_unitario DECIMAL(10,3),
  comprimento DECIMAL(8,2),
  largura DECIMAL(8,2),
  altura DECIMAL(8,2),
  
  -- Controles de Estoque
  controle_lote BOOLEAN DEFAULT FALSE,
  controle_validade BOOLEAN DEFAULT FALSE,
  controle_individual BOOLEAN DEFAULT FALSE,
  prazo_validade_critico INTEGER DEFAULT 30, -- dias
  
  -- Pontos de Reposição
  estoque_minimo DECIMAL(10,2) DEFAULT 0,
  estoque_maximo DECIMAL(10,2) DEFAULT 0,
  estoque_ideal DECIMAL(10,2) DEFAULT 0,
  estoque_seguranca DECIMAL(10,2) DEFAULT 0,
  
  -- Status
  status status_produto_type DEFAULT 'ativo',
  ativo BOOLEAN DEFAULT TRUE,
  favorito BOOLEAN DEFAULT FALSE,
  criticidade criticidade_produto_type DEFAULT 'media',
  
  -- Fornecedor Preferencial
  fornecedor_preferencial_id UUID REFERENCES fornecedores(id),
  
  -- Sazonalidade
  padrao_sazonalidade JSONB DEFAULT '{}',
  
  -- Integração com Procedimentos
  procedimentos_associados UUID[] DEFAULT '{}',
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  ultima_atualizacao TIMESTAMPTZ DEFAULT NOW(),
  atualizado_por UUID REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_produto_codigo_clinica UNIQUE (codigo_interno, clinica_id),
  CONSTRAINT chk_produto_nome_valido CHECK (LENGTH(TRIM(nome)) > 0),
  CONSTRAINT chk_pontos_reposicao CHECK (estoque_maximo >= estoque_minimo)
);

-- Fornecedores
CREATE TABLE fornecedores (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Identificação
  razao_social VARCHAR(255) NOT NULL,
  nome_fantasia VARCHAR(255),
  cnpj VARCHAR(18) UNIQUE,
  cpf VARCHAR(14),
  
  -- Contato
  email VARCHAR(255),
  telefone VARCHAR(20),
  celular VARCHAR(20),
  site VARCHAR(500),
  
  -- Endereço
  endereco JSONB DEFAULT '{}',
  
  -- Informações Comerciais
  condicoes_pagamento TEXT[],
  prazo_entrega_padrao INTEGER DEFAULT 7, -- dias
  desconto_padrao DECIMAL(5,2) DEFAULT 0,
  
  -- Avaliação
  avaliacao_geral DECIMAL(3,2) DEFAULT 0,
  numero_avaliacoes INTEGER DEFAULT 0,
  
  -- Contatos
  contatos JSONB DEFAULT '[]',
  
  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  tipo_fornecedor tipo_fornecedor_type DEFAULT 'nacional',
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  ultima_atualizacao TIMESTAMPTZ DEFAULT NOW(),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_fornecedor_documento CHECK (
    (cnpj IS NOT NULL AND LENGTH(cnpj) = 18) OR 
    (cpf IS NOT NULL AND LENGTH(cpf) = 14)
  )
);

-- Produtos por Fornecedor
CREATE TABLE produtos_fornecedores (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id) ON DELETE CASCADE,
  fornecedor_id UUID NOT NULL REFERENCES fornecedores(id) ON DELETE CASCADE,
  
  -- Identificação no Fornecedor
  codigo_produto_fornecedor VARCHAR(100),
  
  -- Preços e Condições
  preco_atual DECIMAL(15,2) NOT NULL,
  preco_anterior DECIMAL(15,2),
  data_ultimo_preco TIMESTAMPTZ DEFAULT NOW(),
  
  -- Condições Comerciais
  prazo_entrega_dias INTEGER DEFAULT 7,
  quantidade_minima DECIMAL(10,2) DEFAULT 1,
  descontos_quantidade JSONB DEFAULT '[]',
  condicoes_pagamento TEXT[],
  
  -- Performance do Fornecedor
  avaliacao_qualidade DECIMAL(3,2) DEFAULT 0,
  avaliacao_pontualidade DECIMAL(3,2) DEFAULT 0,
  numero_pedidos INTEGER DEFAULT 0,
  percentual_entregas_ok DECIMAL(5,2) DEFAULT 100,
  
  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  preferencial BOOLEAN DEFAULT FALSE,
  
  -- Última Compra
  ultima_compra_data TIMESTAMPTZ,
  ultima_compra_quantidade DECIMAL(10,2),
  ultima_compra_preco DECIMAL(15,2),
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  atualizado_em TIMESTAMPTZ DEFAULT NOW(),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_produto_fornecedor UNIQUE (produto_id, fornecedor_id),
  CONSTRAINT chk_preco_positivo CHECK (preco_atual > 0),
  CONSTRAINT chk_avaliacoes_validas CHECK (
    (avaliacao_qualidade >= 0 AND avaliacao_qualidade <= 5) AND
    (avaliacao_pontualidade >= 0 AND avaliacao_pontualidade <= 5)
  )
);

-- Histórico de Preços
CREATE TABLE historico_precos_produtos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id),
  fornecedor_id UUID NOT NULL REFERENCES fornecedores(id),
  
  -- Alteração de Preço
  preco_anterior DECIMAL(15,2) NOT NULL,
  preco_novo DECIMAL(15,2) NOT NULL,
  variacao_percentual DECIMAL(5,2),
  
  -- Contexto da Alteração
  motivo_alteracao VARCHAR(500),
  tipo_alteracao tipo_alteracao_preco_type DEFAULT 'manual',
  
  -- Auditoria
  data_alteracao TIMESTAMPTZ DEFAULT NOW(),
  usuario_alteracao UUID NOT NULL REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_precos_diferentes CHECK (preco_anterior != preco_novo)
);

-- Documentos de Produtos
CREATE TABLE documentos_produtos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id) ON DELETE CASCADE,
  
  -- Informações do Documento
  tipo tipo_documento_type NOT NULL,
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  numero_documento VARCHAR(100),
  
  -- Arquivo
  arquivo_url VARCHAR(1000) NOT NULL,
  arquivo_nome VARCHAR(255) NOT NULL,
  arquivo_tipo VARCHAR(100) NOT NULL,
  arquivo_tamanho INTEGER NOT NULL,
  
  -- Validade
  data_validade DATE,
  requer_renovacao BOOLEAN DEFAULT FALSE,
  dias_alerta_vencimento INTEGER DEFAULT 30,
  
  -- Status e Versão
  status status_documento_type DEFAULT 'valido',
  versao INTEGER DEFAULT 1,
  documento_anterior_id UUID REFERENCES documentos_produtos(id),
  
  -- Aprovação
  aprovado_por UUID REFERENCES auth.users(id),
  aprovado_em TIMESTAMPTZ,
  motivo_recusa TEXT,
  
  -- Auditoria
  upload_por UUID NOT NULL REFERENCES auth.users(id),
  upload_em TIMESTAMPTZ DEFAULT NOW(),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_documento_nome_valido CHECK (LENGTH(TRIM(nome)) > 0),
  CONSTRAINT chk_arquivo_tamanho CHECK (arquivo_tamanho > 0)
);

-- Consumo Padrão por Procedimento
CREATE TABLE consumo_padrao_procedimentos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id),
  procedimento_id UUID NOT NULL REFERENCES procedimentos(id),
  
  -- Quantidades
  quantidade_padrao DECIMAL(10,3) NOT NULL,
  quantidade_minima DECIMAL(10,3) DEFAULT 0,
  quantidade_maxima DECIMAL(10,3),
  
  -- Configurações
  obrigatorio BOOLEAN DEFAULT FALSE,
  substituivel BOOLEAN DEFAULT TRUE,
  produtos_substitutos UUID[] DEFAULT '{}',
  
  -- Variações
  variacao_por_profissional JSONB DEFAULT '{}',
  variacao_por_complexidade JSONB DEFAULT '{}',
  
  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  
  -- Auditoria
  configurado_em TIMESTAMPTZ DEFAULT NOW(),
  configurado_por UUID NOT NULL REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_produto_procedimento UNIQUE (produto_id, procedimento_id),
  CONSTRAINT chk_quantidade_padrao_positiva CHECK (quantidade_padrao > 0)
);

-- Tipos Enum para Produtos
CREATE TYPE unidade_medida_type AS ENUM ('un', 'ml', 'g', 'kg', 'cx', 'l', 'mg', 'ampola', 'seringa', 'frasco');
CREATE TYPE status_produto_type AS ENUM ('ativo', 'inativo', 'descontinuado', 'aguardando_aprovacao');
CREATE TYPE criticidade_produto_type AS ENUM ('baixa', 'media', 'alta', 'critica');
CREATE TYPE tipo_fornecedor_type AS ENUM ('nacional', 'internacional', 'distribuidor', 'fabricante');
CREATE TYPE tipo_documento_type AS ENUM ('ficha_tecnica', 'bula', 'certificado_anvisa', 'laudo_qualidade', 'manual_uso', 'registro_sanitario');
CREATE TYPE status_documento_type AS ENUM ('valido', 'vencido', 'vencendo', 'pendente_aprovacao', 'recusado');
CREATE TYPE tipo_alteracao_preco_type AS ENUM ('manual', 'automatica', 'importacao', 'reajuste');

-- Índices para Performance
CREATE INDEX idx_produtos_categoria ON produtos_estoque(categoria_id);
CREATE INDEX idx_produtos_clinica ON produtos_estoque(clinica_id);
CREATE INDEX idx_produtos_ativo ON produtos_estoque(ativo) WHERE ativo = true;
CREATE INDEX idx_produtos_favorito ON produtos_estoque(favorito) WHERE favorito = true;
CREATE INDEX idx_produtos_codigo ON produtos_estoque(codigo_interno);

-- Índices para Fornecedores
CREATE INDEX idx_fornecedores_ativo ON fornecedores(ativo) WHERE ativo = true;
CREATE INDEX idx_fornecedores_clinica ON fornecedores(clinica_id);
CREATE INDEX idx_produtos_fornecedores_produto ON produtos_fornecedores(produto_id);
CREATE INDEX idx_produtos_fornecedores_fornecedor ON produtos_fornecedores(fornecedor_id);

-- Índices para Documentos
CREATE INDEX idx_documentos_produto ON documentos_produtos(produto_id);
CREATE INDEX idx_documentos_validade ON documentos_produtos(data_validade) WHERE data_validade IS NOT NULL;
CREATE INDEX idx_documentos_status ON documentos_produtos(status);

-- Índices para Histórico
CREATE INDEX idx_historico_precos_produto ON historico_precos_produtos(produto_id);
CREATE INDEX idx_historico_precos_data ON historico_precos_produtos(data_alteracao);

-- Full-text search para produtos
CREATE INDEX idx_produtos_search ON produtos_estoque USING gin(
  to_tsvector('portuguese', coalesce(nome, '') || ' ' || coalesce(descricao, '') || ' ' || coalesce(codigo_interno, ''))
);

-- Full-text search para fornecedores
CREATE INDEX idx_fornecedores_search ON fornecedores USING gin(
  to_tsvector('portuguese', coalesce(razao_social, '') || ' ' || coalesce(nome_fantasia, ''))
);
```

#### Product Management API Endpoints

```typescript
// Products CRUD API
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '50')
  const categoria = searchParams.get('categoria')
  const busca = searchParams.get('busca')
  const status = searchParams.get('status')
  const favoritos = searchParams.get('favoritos') === 'true'
  
  const supabase = createServerClient()
  
  try {
    let query = supabase
      .from('produtos_estoque')
      .select(`
        *,
        categoria:categorias_produtos(nome, icone, cor),
        fornecedor_preferencial:fornecedores(razao_social),
        fornecedores:produtos_fornecedores(
          id,
          preco_atual,
          fornecedor:fornecedores(razao_social)
        )
      `)
    
    // Aplicar filtros
    if (categoria) {
      query = query.eq('categoria_id', categoria)
    }
    
    if (status) {
      query = query.eq('status', status)
    }
    
    if (favoritos) {
      query = query.eq('favorito', true)
    }
    
    if (busca) {
      query = query.or(`nome.ilike.%${busca}%,codigo_interno.ilike.%${busca}%,descricao.ilike.%${busca}%`)
    }
    
    // Paginação
    const from = (page - 1) * limit
    const to = from + limit - 1
    
    const { data: produtos, count } = await query
      .range(from, to)
      .order('nome')
    
    // Estatísticas dos produtos
    const { data: stats } = await supabase
      .from('produtos_estoque')
      .select('status, criticidade')
      .eq('ativo', true)
    
    const estatisticas = {
      total: count || 0,
      ativos: stats?.filter(p => p.status === 'ativo').length || 0,
      criticos: stats?.filter(p => p.criticidade === 'critica').length || 0,
      favoritos: stats?.filter(p => p.favorito).length || 0
    }
    
    return NextResponse.json({
      produtos,
      estatisticas,
      paginacao: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      },
      message: 'Produtos carregados com sucesso'
    })
    
  } catch (error) {
    console.error('Error loading products:', error)
    return NextResponse.json({
      error: 'Erro ao carregar produtos'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  const supabase = createServerClient()
  
  try {
    const {
      nome,
      descricao,
      codigo_interno,
      categoria_id,
      unidade_medida,
      estoque_minimo,
      estoque_maximo,
      controle_lote,
      controle_validade,
      fornecedores
    } = await request.json()
    
    // Validar dados obrigatórios
    if (!nome || !categoria_id || !unidade_medida) {
      return NextResponse.json({
        error: 'Nome, categoria e unidade de medida são obrigatórios'
      }, { status: 400 })
    }
    
    // Verificar se código interno já existe
    if (codigo_interno) {
      const { data: existente } = await supabase
        .from('produtos_estoque')
        .select('id')
        .eq('codigo_interno', codigo_interno)
        .single()
      
      if (existente) {
        return NextResponse.json({
          error: 'Código interno já existe'
        }, { status: 400 })
      }
    }
    
    // Criar produto
    const { data: produto, error } = await supabase
      .from('produtos_estoque')
      .insert({
        nome,
        descricao,
        codigo_interno: codigo_interno || `PRD-${Date.now()}`,
        categoria_id,
        unidade_medida,
        estoque_minimo: estoque_minimo || 0,
        estoque_maximo: estoque_maximo || 0,
        controle_lote: controle_lote || false,
        controle_validade: controle_validade || false
      })
      .select()
      .single()
    
    if (error) throw error
    
    // Associar fornecedores se fornecidos
    if (fornecedores && fornecedores.length > 0) {
      const fornecedoresData = fornecedores.map(f => ({
        produto_id: produto.id,
        fornecedor_id: f.fornecedor_id,
        preco_atual: f.preco_atual,
        codigo_produto_fornecedor: f.codigo_produto_fornecedor
      }))
      
      await supabase
        .from('produtos_fornecedores')
        .insert(fornecedoresData)
    }
    
    return NextResponse.json({
      produto,
      message: 'Produto criado com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json({
      error: 'Erro ao criar produto'
    }, { status: 500 })
  }
}

// Categories Management API
export async function GET() {
  const supabase = createServerClient()
  
  try {
    const { data: categorias } = await supabase
      .from('categorias_produtos')
      .select(`
        *,
        produtos_count:produtos_estoque(count)
      `)
      .eq('ativo', true)
      .order('nivel')
      .order('ordem_exibicao')
    
    // Organizar hierarquicamente
    const categoriasHierarquicas = organizarHierarquia(categorias)
    
    return NextResponse.json({
      categorias: categoriasHierarquicas,
      message: 'Categorias carregadas'
    })
    
  } catch (error) {
    console.error('Error loading categories:', error)
    return NextResponse.json({
      error: 'Erro ao carregar categorias'
    }, { status: 500 })
  }
}

// Suppliers Management API
export async function GET() {
  const supabase = createServerClient()
  
  try {
    const { data: fornecedores } = await supabase
      .from('fornecedores')
      .select(`
        *,
        produtos_count:produtos_fornecedores(count),
        avaliacao_media:produtos_fornecedores(avaliacao_qualidade.avg(), avaliacao_pontualidade.avg())
      `)
      .eq('ativo', true)
      .order('razao_social')
    
    return NextResponse.json({
      fornecedores,
      message: 'Fornecedores carregados'
    })
    
  } catch (error) {
    console.error('Error loading suppliers:', error)
    return NextResponse.json({
      error: 'Erro ao carregar fornecedores'
    }, { status: 500 })
  }
}

// Bulk Operations API
export async function POST(
  request: NextRequest,
  { params }: { params: { operation: string } }
) {
  const supabase = createServerClient()
  
  try {
    if (params.operation === 'import') {
      const { produtos } = await request.json()
      
      // Validar estrutura dos produtos
      const produtosValidos = produtos.filter(p => 
        p.nome && p.categoria_id && p.unidade_medida
      )
      
      if (produtosValidos.length === 0) {
        return NextResponse.json({
          error: 'Nenhum produto válido encontrado'
        }, { status: 400 })
      }
      
      // Inserir produtos em lote
      const { data: produtosCriados, error } = await supabase
        .from('produtos_estoque')
        .insert(produtosValidos)
        .select()
      
      if (error) throw error
      
      return NextResponse.json({
        produtosCriados,
        total: produtosCriados.length,
        message: `${produtosCriados.length} produtos importados com sucesso`
      })
    }
    
    if (params.operation === 'update-prices') {
      const { produtos, fornecedor_id, percentual_reajuste } = await request.json()
      
      // Atualizar preços em lote
      const updates = []
      for (const produto_id of produtos) {
        const { data: atual } = await supabase
          .from('produtos_fornecedores')
          .select('preco_atual')
          .eq('produto_id', produto_id)
          .eq('fornecedor_id', fornecedor_id)
          .single()
        
        if (atual) {
          const novoPreco = atual.preco_atual * (1 + percentual_reajuste / 100)
          updates.push({
            produto_id,
            fornecedor_id,
            preco_anterior: atual.preco_atual,
            preco_atual: novoPreco,
            data_ultimo_preco: new Date()
          })
        }
      }
      
      if (updates.length > 0) {
        await supabase
          .from('produtos_fornecedores')
          .upsert(updates)
      }
      
      return NextResponse.json({
        total: updates.length,
        message: `${updates.length} preços atualizados`
      })
    }
    
  } catch (error) {
    console.error('Error in bulk operation:', error)
    return NextResponse.json({
      error: 'Erro na operação em lote'
    }, { status: 500 })
  }
}
```

### Integration Points

#### Epic 6 Integration (Agenda Inteligente)

- **Product Procedures**: Associação de produtos a tipos de procedimento
- **Standard Consumption**: Definição de consumo padrão por procedimento
- **Automatic Deduction**: Preparação para baixa automática baseada em agenda
- **Stock Validation**: Verificação de disponibilidade antes do agendamento

#### Epic 7 Integration (Financeiro Essencial)

- **Supplier Management**: Gestão unificada de fornecedores
- **Cost Control**: Integração com contas a pagar
- **Purchase Orders**: Preparação para gestão de pedidos de compra
- **Price History**: Histórico de preços para análise financeira

#### Epic 8 Integration (BI & Dashboards)

- **Product Analytics**: Métricas de produtos e categorias
- **Supplier Performance**: Analytics de performance de fornecedores
- **Cost Analysis**: Análise de custos e variações de preços
- **Inventory KPIs**: Preparação para KPIs de estoque

#### Epic 9 Integration (Cadastro & Prontuário)

- **Medical Products**: Controle especial para produtos médicos
- **Procedure Materials**: Associação de materiais a protocolos médicos
- **Compliance Tracking**: Rastreabilidade para auditoria médica
- **Documentation**: Gestão de documentos regulatórios

### Testing Strategy

#### Product Management Tests

```typescript
describe('Product Management System', () => {
  test('creates product with all required information', async () => {
    const productData = {
      nome: 'Ácido Hialurônico',
      categoria_id: 'medicamento-category-id',
      unidade_medida: 'ml',
      estoque_minimo: 10,
      controle_validade: true
    }
    
    const produto = await createProduct(productData)
    
    expect(produto.nome).toBe(productData.nome)
    expect(produto.codigo_interno).toMatch(/^PRD-\d+$/)
    expect(produto.status).toBe('ativo')
  })
  
  test('manages suppliers and prices correctly', async () => {
    const produto = await createTestProduct()
    const fornecedor = await createTestSupplier()
    
    await associateSupplierToProduct(produto.id, {
      fornecedor_id: fornecedor.id,
      preco_atual: 150.00,
      codigo_produto_fornecedor: 'AH-001'
    })
    
    const association = await getProductSupplier(produto.id, fornecedor.id)
    
    expect(association.preco_atual).toBe(150.00)
    expect(association.codigo_produto_fornecedor).toBe('AH-001')
  })
  
  test('tracks price history accurately', async () => {
    const produto = await createTestProduct()
    const fornecedor = await createTestSupplier()
    
    await updateProductPrice(produto.id, fornecedor.id, 120.00, 150.00)
    
    const history = await getPriceHistory(produto.id, fornecedor.id)
    
    expect(history).toHaveLength(1)
    expect(history[0].preco_anterior).toBe(120.00)
    expect(history[0].preco_novo).toBe(150.00)
  })
  
  test('validates category hierarchies', async () => {
    const categorias = await loadCategories()
    const hierarchy = organizarHierarquia(categorias)
    
    expect(hierarchy.every(cat => cat.nivel === 1)).toBe(true)
    expect(hierarchy.some(cat => cat.subcategorias.length > 0)).toBe(true)
  })
  
  test('manages product documentation', async () => {
    const produto = await createTestProduct()
    const documento = await uploadProductDocument(produto.id, {
      tipo: 'certificado_anvisa',
      arquivo: testFile,
      data_validade: new Date('2025-12-31')
    })
    
    expect(documento.status).toBe('valido')
    expect(documento.produto_id).toBe(produto.id)
  })
})
```

### Dev Notes

#### Advanced Features

- **Barcode Integration**: Suporte para códigos de barras e QR codes
- **Batch Management**: Sistema completo de controle de lotes
- **Expiration Control**: Alertas inteligentes de vencimento
- **ABC Analysis**: Classificação automática por importância

#### Performance Optimizations

- **Efficient Search**: Full-text search otimizado para produtos
- **Smart Caching**: Cache de categorias e fornecedores frequentes
- **Bulk Operations**: Operações em lote para importação
- **Lazy Loading**: Carregamento sob demanda de detalhes

#### Compliance Features

- **ANVISA Tracking**: Controle especial para produtos regulamentados
- **Audit Trail**: Rastro completo de alterações
- **Document Management**: Gestão de certificados e documentos
- **Regulatory Alerts**: Alertas para documentos vencidos

---

## Dev Agent Record

### Task Status

- [x] Analyzed product management requirements for Epic 11
- [x] Created comprehensive story with 6 acceptance criteria
- [x] Designed TypeScript interfaces for products, suppliers, and categories
- [x] Specified database schema with full normalization and constraints
- [x] Developed complete API endpoints for CRUD operations
- [x] Added bulk operations for efficiency
- [x] Created supplier management with price history
- [x] Implemented document management for compliance
- [x] Established integration points with Epic 6-9
- [x] Created comprehensive testing strategy for product management

### File List

- `docs/stories/11.1.story.md` - Product and Supplier Management foundation story

### Change Log

- **Story 11.1 Creation**: Complete product catalog management system
- **Product Management**: Comprehensive CRUD with categories and specifications
- **Supplier Management**: Full supplier lifecycle with price tracking
- **Documentation System**: Upload and management of product documents
- **Bulk Operations**: Import/export and mass operations for efficiency
- **Integration Planning**: Preparation for Epic 6-9 integration points
- **Compliance Framework**: ANVISA and regulatory document management

### Completion Notes

Story 11.1 establishes the foundation for Epic 11 with a robust product management system. The story covers complete product lifecycle, supplier relationships, pricing history, and document management while preparing for integration with procedures and automatic stock control.

### Next Steps

Ready to continue with Story 11.2: Controle de Entradas e Compras to build the input control system on this foundation.
