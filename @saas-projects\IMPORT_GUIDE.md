# 📦 Import Guide - Using Shared Components

Complete guide for importing and using shared components in VIBECODE projects.

## 🚀 Installation

### In Project Root Directory

```bash
# Navigate to your project (neonpro, aegiswallet, or agendatrintae3)
cd E:\VIBECODE\neonpro  # or aegiswallet, agendatrintae3

# Install shared components as local dependency
npm install file:../@saas-projects/shared
```

### Package.json Entry
After installation, your `package.json` will include:
```json
{
  "dependencies": {
    "@vibecode/shared": "file:../@saas-projects/shared"
  }
}
```

## 📋 Available Imports

### UI Components

```typescript
// Individual component imports (recommended for tree-shaking)
import { Button } from '@vibecode/shared/ui';
import { Card } from '@vibecode/shared/ui';
import { Input } from '@vibecode/shared/ui';
import { Label } from '@vibecode/shared/ui';

// Multiple component imports
import { Button, Card, Input, Label } from '@vibecode/shared/ui';

// All UI components (use sparingly)
import * as UI from '@vibecode/shared/ui';
```

### Utilities and Helpers

```typescript
// Class name utility
import { cn } from '@vibecode/shared/lib/utils';

// Supabase helpers
import { createClient, getUser } from '@vibecode/shared/lib/supabase';

// Date formatting utilities
import { formatDate, formatDateTime } from '@vibecode/shared/lib/utils';
```

### Providers and Context

```typescript
// Theme provider
import { ThemeProvider } from '@vibecode/shared/providers';

// Theme hook
import { useTheme } from '@vibecode/shared/providers';
```

### Stagewise Integration

```typescript
// Stagewise provider and hook
import { StagewiseProvider, useStagewise } from '@vibecode/shared/stagewise';

// Stagewise configuration
import { getStagewiseConfig } from '@vibecode/shared/stagewise';
```

### Configuration

```typescript
// Theme configuration
import { themeConfig } from '@vibecode/shared/config';
```

## 🎯 Usage Examples

### Basic Component Usage

```typescript
// pages/dashboard.tsx
import { Button, Card } from '@vibecode/shared/ui';
import { cn } from '@vibecode/shared/lib/utils';

export default function Dashboard() {
  return (
    <div className="p-6">
      <Card className="mb-4">
        <Card.Header>
          <h2>Dashboard</h2>
        </Card.Header>
        <Card.Content>
          <p>Welcome to your dashboard</p>
        </Card.Content>
      </Card>
      
      <Button 
        variant="primary" 
        className={cn("w-full", "md:w-auto")}
      >
        Get Started
      </Button>
    </div>
  );
}
```

### Provider Setup

```typescript
// app/layout.tsx
import { ThemeProvider } from '@vibecode/shared/providers';
import { StagewiseProvider } from '@vibecode/shared/stagewise';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ThemeProvider>
          <StagewiseProvider>
            {children}
          </StagewiseProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

### Form with Shared Components

```typescript
// components/ContactForm.tsx
import { Button, Input, Label } from '@vibecode/shared/ui';
import { cn } from '@vibecode/shared/lib/utils';

export function ContactForm() {
  return (
    <form className="space-y-4">
      <div>
        <Label htmlFor="name">Name</Label>
        <Input 
          id="name" 
          type="text" 
          placeholder="Enter your name"
          className={cn("w-full")}
        />
      </div>
      
      <div>
        <Label htmlFor="email">Email</Label>
        <Input 
          id="email" 
          type="email" 
          placeholder="Enter your email"
        />
      </div>
      
      <Button type="submit" variant="primary">
        Submit
      </Button>
    </form>
  );
}
```

## 🔧 TypeScript Support

### Type Imports

```typescript
// Import component prop types
import type { ButtonProps } from '@vibecode/shared/ui';
import type { CardProps } from '@vibecode/shared/ui';

// Use in your components
interface CustomButtonProps extends ButtonProps {
  customProp?: string;
}

function CustomButton({ customProp, ...props }: CustomButtonProps) {
  return <Button {...props} />;
}
```

### Configuration Types

```typescript
// Import configuration types
import type { ThemeConfig } from '@vibecode/shared/config';
import type { StagewiseConfig } from '@vibecode/shared/stagewise';
```

## 🎨 Styling and Customization

### Using with Tailwind CSS

```typescript
import { Button } from '@vibecode/shared/ui';
import { cn } from '@vibecode/shared/lib/utils';

// Extend component styles
<Button 
  className={cn(
    "bg-gradient-to-r from-blue-500 to-purple-600",
    "hover:from-blue-600 hover:to-purple-700",
    "text-white font-semibold"
  )}
>
  Custom Styled Button
</Button>
```

### Theme Customization

```typescript
// app/globals.css
@import '@vibecode/shared/styles/globals.css';

/* Override CSS variables for custom theming */
:root {
  --primary: 220 14.3% 95.9%;
  --primary-foreground: 220.9 39.3% 11%;
  /* Add your custom theme variables */
}
```

## 🚨 Common Issues and Solutions

### Issue: Module Not Found

```bash
Error: Cannot resolve module '@vibecode/shared/ui'
```

**Solution**: Ensure the shared library is properly installed:
```bash
npm install file:../@saas-projects/shared
npm run build  # in the shared directory
```

### Issue: Type Errors

```typescript
// Error: Property 'variant' does not exist on type 'ButtonProps'
```

**Solution**: Check if you're importing the correct component and types:
```typescript
import { Button } from '@vibecode/shared/ui';
import type { ButtonProps } from '@vibecode/shared/ui';
```

### Issue: Styles Not Applied

**Solution**: Ensure Tailwind CSS is configured to scan shared components:
```javascript
// tailwind.config.js
module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}',
    './node_modules/@vibecode/shared/**/*.{js,ts,jsx,tsx}', // Add this line
  ],
  // ... rest of config
};
```

## 🔄 Updating Shared Components

### When Shared Components Are Updated

```bash
# In your project directory
npm update @vibecode/shared

# Or reinstall
npm uninstall @vibecode/shared
npm install file:../@saas-projects/shared
```

### Development Workflow

```bash
# 1. Make changes in shared components
cd E:\VIBECODE\@saas-projects\shared
# Edit components...

# 2. Build shared library
npm run build

# 3. Update in your project
cd E:\VIBECODE\neonpro  # or your project
npm update @vibecode/shared
```

## 📚 Best Practices

### Import Organization

```typescript
// Group imports logically
// 1. React and Next.js imports
import React from 'react';
import { NextPage } from 'next';

// 2. Third-party libraries
import { z } from 'zod';

// 3. Shared components (grouped by type)
import { Button, Card, Input } from '@vibecode/shared/ui';
import { cn, formatDate } from '@vibecode/shared/lib/utils';
import { ThemeProvider } from '@vibecode/shared/providers';

// 4. Local imports
import { CustomComponent } from '../components/CustomComponent';
```

### Performance Optimization

```typescript
// Use specific imports for better tree-shaking
import { Button } from '@vibecode/shared/ui/button';  // ✅ Good
import { Button } from '@vibecode/shared/ui';         // ✅ Also good
import * as UI from '@vibecode/shared/ui';            // ❌ Avoid
```

### Component Extension

```typescript
// Extend shared components properly
import { Button } from '@vibecode/shared/ui';
import type { ButtonProps } from '@vibecode/shared/ui';

interface ProjectButtonProps extends ButtonProps {
  projectSpecificProp?: boolean;
}

export function ProjectButton({ 
  projectSpecificProp, 
  ...buttonProps 
}: ProjectButtonProps) {
  return (
    <Button 
      {...buttonProps}
      className={cn(
        buttonProps.className,
        projectSpecificProp && "project-specific-styles"
      )}
    />
  );
}
```

---

This guide ensures consistent and efficient usage of shared components across all VIBECODE projects.