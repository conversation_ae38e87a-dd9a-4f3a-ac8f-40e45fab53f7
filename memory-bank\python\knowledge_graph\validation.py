#!/usr/bin/env python3

"""
VIBECODE V2.0 - Validation Layer and Data Processing
Validation components and ECL pipeline for data processing

This module contains validation logic, ECL pipeline management,
and task-based processing components.
"""

import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union

try:
    from .models import VibeCodeDataPoint, VibeCodeTask, PYDANTIC_AVAILABLE, generate_id
    from .utils import log_with_context, handle_with_fallback
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    from models import VibeCodeDataPoint, VibeCodeTask, PYDANTIC_AVAILABLE, generate_id
    from utils import log_with_context, handle_with_fallback

# Configure logging
logger = logging.getLogger('vibecode.validation')

class PydanticValidationLayer:
    """Validation layer with consolidated logic"""
    
    def __init__(self):
        self.validation_enabled = PYDANTIC_AVAILABLE
        self.validation_stats = {'total_validations': 0, 'successful_validations': 0, 'validation_errors': 0}

    def validate_data_point(self, data: Union[Dict[str, Any], VibeCodeDataPoint]) -> VibeCodeDataPoint:
        """Validate and create VibeCodeDataPoint"""
        self.validation_stats['total_validations'] += 1
        try:
            if isinstance(data, VibeCodeDataPoint):
                result = data
            elif isinstance(data, dict):
                if self.validation_enabled:
                    result = VibeCodeDataPoint(**data)
                else:
                    result = VibeCodeDataPoint(
                        id=data.get('id', generate_id()),
                        content=data.get('content', ''),
                        data_type=data.get('data_type', 'fact'),
                        occurrence_time=data.get('occurrence_time', datetime.now(timezone.utc)),
                        ingestion_time=data.get('ingestion_time', datetime.now(timezone.utc)),
                        metadata=data.get('metadata', {}),
                        source=data.get('source', 'unknown'),
                        confidence=data.get('confidence', 1.0),
                        temporal_validity=data.get('temporal_validity')
                    )
            else:
                raise ValueError(f"Invalid data type: {type(data)}")

            self.validation_stats['successful_validations'] += 1
            return result
        except Exception as e:
            self.validation_stats['validation_errors'] += 1
            log_with_context('error', f"Validation failed: {e}", component='validation')
            raise

    def validate_task(self, data: Union[Dict[str, Any], VibeCodeTask]) -> VibeCodeTask:
        """Validate and create VibeCodeTask"""
        self.validation_stats['total_validations'] += 1
        try:
            if isinstance(data, VibeCodeTask):
                result = data
            elif isinstance(data, dict):
                if self.validation_enabled:
                    result = VibeCodeTask(**data)
                else:
                    result = VibeCodeTask(
                        task_id=data.get('task_id', generate_id()),
                        task_type=data.get('task_type', 'general'),
                        description=data.get('description', ''),
                        status=data.get('status', 'pending'),
                        input_data=data.get('input_data'),
                        output_data=data.get('output_data'),
                        created_at=data.get('created_at', datetime.now(timezone.utc)),
                        started_at=data.get('started_at'),
                        completed_at=data.get('completed_at'),
                        execution_time_ms=data.get('execution_time_ms'),
                        error_message=data.get('error_message')
                    )
            else:
                raise ValueError(f"Invalid data type: {type(data)}")

            self.validation_stats['successful_validations'] += 1
            return result
        except Exception as e:
            self.validation_stats['validation_errors'] += 1
            log_with_context('error', f"Task validation failed: {e}", component='validation')
            raise

    def get_validation_stats(self) -> Dict[str, Any]:
        """Get validation statistics"""
        success_rate = 0.0
        if self.validation_stats['total_validations'] > 0:
            success_rate = self.validation_stats['successful_validations'] / self.validation_stats['total_validations']
        return {**self.validation_stats, 'success_rate': success_rate, 'pydantic_available': self.validation_enabled}

class ECLPipelineManager:
    """Extract-Cognify-Load pipeline manager inspired by Cognee"""
    
    def __init__(self):
        self.pipeline_stats = {'extractions': 0, 'cognifications': 0, 'loads': 0, 'errors': 0}
        self.validation_layer = PydanticValidationLayer()

    async def extract_phase(self, raw_data: Any, source: str = 'unknown') -> List[VibeCodeDataPoint]:
        """Extract phase: Convert raw data to structured data points"""
        try:
            self.pipeline_stats['extractions'] += 1
            data_points = []
            
            if isinstance(raw_data, str):
                # Simple text extraction
                data_point_data = {
                    'id': generate_id(),
                    'content': raw_data,
                    'data_type': 'fact',
                    'occurrence_time': datetime.now(timezone.utc),
                    'source': source,
                    'confidence': 0.8
                }
                data_points.append(self.validation_layer.validate_data_point(data_point_data))
                
            elif isinstance(raw_data, list):
                # List of items
                for i, item in enumerate(raw_data):
                    if isinstance(item, str):
                        data_point_data = {
                            'id': generate_id(),
                            'content': item,
                            'data_type': 'fact',
                            'occurrence_time': datetime.now(timezone.utc),
                            'source': f"{source}_item_{i}",
                            'confidence': 0.8
                        }
                        data_points.append(self.validation_layer.validate_data_point(data_point_data))
                        
            elif isinstance(raw_data, dict):
                # Dictionary data
                for key, value in raw_data.items():
                    data_point_data = {
                        'id': generate_id(),
                        'content': f"{key}: {value}",
                        'data_type': 'fact',
                        'occurrence_time': datetime.now(timezone.utc),
                        'source': f"{source}_{key}",
                        'confidence': 0.9
                    }
                    data_points.append(self.validation_layer.validate_data_point(data_point_data))
            
            log_with_context('info', f"Extracted {len(data_points)} data points", component='ecl_extract')
            return data_points
            
        except Exception as e:
            self.pipeline_stats['errors'] += 1
            log_with_context('error', f"Extract phase failed: {e}", component='ecl_extract')
            return []

    async def cognify_phase(self, data_points: List[VibeCodeDataPoint]) -> Dict[str, Any]:
        """Cognify phase: Process and enrich data points"""
        try:
            self.pipeline_stats['cognifications'] += 1
            
            knowledge_graph = {
                'entities': [],
                'relationships': [],
                'facts': [],
                'metadata': {
                    'processed_at': datetime.now(timezone.utc).isoformat(),
                    'total_points': len(data_points)
                }
            }
            
            for data_point in data_points:
                # Simple categorization
                if data_point.data_type == 'fact':
                    knowledge_graph['facts'].append({
                        'id': data_point.id,
                        'content': data_point.content,
                        'confidence': data_point.confidence,
                        'source': data_point.source
                    })
                elif data_point.data_type == 'entity':
                    knowledge_graph['entities'].append({
                        'id': data_point.id,
                        'content': data_point.content,
                        'confidence': data_point.confidence
                    })
                elif data_point.data_type == 'relationship':
                    knowledge_graph['relationships'].append({
                        'id': data_point.id,
                        'content': data_point.content,
                        'confidence': data_point.confidence
                    })
            
            log_with_context('info', f"Cognified {len(data_points)} data points", component='ecl_cognify')
            return knowledge_graph
            
        except Exception as e:
            self.pipeline_stats['errors'] += 1
            log_with_context('error', f"Cognify phase failed: {e}", component='ecl_cognify')
            return {}

    async def load_phase(self, knowledge_graph: Dict[str, Any]) -> bool:
        """Load phase: Store processed knowledge graph"""
        try:
            self.pipeline_stats['loads'] += 1
            
            # Simple storage simulation
            total_items = (len(knowledge_graph.get('entities', [])) + 
                          len(knowledge_graph.get('relationships', [])) + 
                          len(knowledge_graph.get('facts', [])))
            
            log_with_context('info', f"Loaded {total_items} items to knowledge graph", component='ecl_load')
            return True
            
        except Exception as e:
            self.pipeline_stats['errors'] += 1
            log_with_context('error', f"Load phase failed: {e}", component='ecl_load')
            return False

    def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get ECL pipeline statistics"""
        return self.pipeline_stats

class TaskBasedProcessor:
    """Task-based processing system for modular operations"""
    
    def __init__(self):
        self.registered_tasks = {}
        self.task_stats = {'tasks_registered': 0, 'tasks_executed': 0, 'task_errors': 0}

    async def register_task(self, task_type: str, task_function) -> bool:
        """Register a task type with its processing function"""
        try:
            self.registered_tasks[task_type] = task_function
            self.task_stats['tasks_registered'] += 1
            log_with_context('info', f"Registered task type: {task_type}", component='task_processor')
            return True
        except Exception as e:
            log_with_context('error', f"Task registration failed: {e}", component='task_processor')
            return False

    async def execute_task(self, task: VibeCodeTask) -> Dict[str, Any]:
        """Execute a task based on its type"""
        try:
            if task.task_type not in self.registered_tasks:
                raise ValueError(f"Unknown task type: {task.task_type}")
            
            start_time = time.time()
            task_function = self.registered_tasks[task.task_type]
            
            # Execute the task
            result = await task_function(task.input_data or {})
            
            execution_time = (time.time() - start_time) * 1000
            self.task_stats['tasks_executed'] += 1
            
            log_with_context(
                'info', 
                f"Task {task.task_id} executed successfully",
                component='task_processor',
                task_type=task.task_type,
                execution_time_ms=f"{execution_time:.2f}"
            )
            
            return {
                'success': True,
                'result': result,
                'execution_time_ms': execution_time,
                'task_id': task.task_id
            }
            
        except Exception as e:
            self.task_stats['task_errors'] += 1
            log_with_context('error', f"Task execution failed: {e}", component='task_processor')
            return {
                'success': False,
                'error': str(e),
                'task_id': task.task_id
            }

    def get_task_stats(self) -> Dict[str, Any]:
        """Get task processing statistics"""
        return self.task_stats

# Export all public symbols
__all__ = [
    'PydanticValidationLayer',
    'ECLPipelineManager',
    'TaskBasedProcessor'
]
