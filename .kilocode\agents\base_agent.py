from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseAgent(ABC):
    """
    Abstract base class for all agents in the Kilo Code system.
    Defines the common interface for agent capabilities and request processing.
    """
    def __init__(self, config: Dict[str, Any]):
        """
        Initializes the BaseAgent.

        Args:
            config (Dict[str, Any]): Agent configuration containing:
                - name (str): The name of the agent
                - model (str): The underlying AI model (e.g., 'Claude Sonnet 4', 'Gemini 2.5 Pro')
                - complexity_range (tuple[int, int]): The min and max complexity this agent can handle
                - domains (List[str]): The primary domains of expertise for this agent
        """
        self.name = config.get("name", "BaseAgent")
        self.model = config.get("model", "default")
        complexity_range = config.get("complexity_range", (1, 10))
        if isinstance(complexity_range, tuple):
            self.complexity_min, self.complexity_max = complexity_range
        else:
            self.complexity_min, self.complexity_max = 1, 10
        self.domains = config.get("domains", [])

    def can_handle(self, request: Dict[str, Any]) -> tuple[bool, float]:
        """
        Determines if the agent is suitable for a given request.

        Args:
            request (Dict[str, Any]): The request to evaluate containing:
                - complexity (int): The estimated complexity of the task (1-10)
                - message (str): The task message/description
                - type (str, optional): The type of request

        Returns:
            tuple[bool, float]: (can_handle, confidence_score)
        """
        complexity = request.get("complexity", 5)
        message = request.get("message", "").lower()

        # Basic complexity matching
        complexity_match = self.complexity_min <= complexity <= self.complexity_max

        # Basic domain matching (if domains are specified)
        domain_match = True
        if self.domains:
            domain_match = any(domain.replace("_", " ") in message for domain in self.domains)

        # Calculate confidence based on complexity and domain match
        if complexity_match and domain_match:
            confidence = 0.7
        elif complexity_match:
            confidence = 0.5
        else:
            confidence = 0.2

        can_handle = confidence >= 0.5
        return can_handle, confidence

    @abstractmethod
    def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes a given request. This method must be implemented by subclasses.

        Args:
            request (Dict[str, Any]): The request payload containing task details.

        Returns:
            Dict[str, Any]: The result of the processing.
        """
        pass

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Returns a dictionary describing the agent's capabilities.

        Returns:
            Dict[str, Any]: A summary of the agent's configuration.
        """
        return {
            "name": self.name,
            "model": self.model,
            "complexity_range": (self.complexity_min, self.complexity_max),
            "domains": self.domains
        }

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.name}', model='{self.model}')"
