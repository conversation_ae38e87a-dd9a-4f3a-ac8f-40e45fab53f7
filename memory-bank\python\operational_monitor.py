"""
📊 VIBECODE V1.0 - OPERATIONAL MONITOR
Sistema de monitoramento em tempo real para uso operacional

Funcionalidades:
- Real-time operational metrics
- Continuous learning monitoring
- Context preservation tracking
- Performance analytics
"""

import json
import time
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass, asdict

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class OperationalMetrics:
    """Métricas operacionais em tempo real"""
    timestamp: str
    memory_usage: Dict[str, int]
    learning_rate: float
    context_preservation_rate: float
    session_efficiency: float
    active_features: List[str]
    performance_score: float

class OperationalMonitor:
    """Monitor de operações em tempo real"""
    
    def __init__(self, memory_bank_path: str = "memory-bank"):
        self.memory_bank_path = Path(memory_bank_path)
        self.metrics_path = self.memory_bank_path / "python" / "operational_metrics"
        self.metrics_path.mkdir(exist_ok=True)
        
        self.start_time = datetime.now(timezone.utc)
        self.metrics_history = []
        
    def collect_current_metrics(self) -> OperationalMetrics:
        """Coleta métricas operacionais atuais"""
        
        # Simula coleta de métricas reais
        memory_usage = {
            "context_cache": len(list(self.memory_bank_path.glob("**/*.pkl"))),
            "learning_insights": len(list((self.memory_bank_path / "python" / "learning_insights").glob("*.json"))),
            "session_files": len(list((self.memory_bank_path / "python" / "sessions").glob("*.json"))),
            "total_files": len(list(self.memory_bank_path.glob("**/*")))
        }
        
        # Calcula taxas baseadas em arquivos existentes
        learning_rate = min(memory_usage["learning_insights"] / 10.0, 1.0)  # Normalizado
        context_preservation_rate = 1.0 if memory_usage["context_cache"] > 0 else 0.0
        session_efficiency = min(memory_usage["session_files"] / 5.0, 1.0)  # Normalizado
        
        # Features ativas
        active_features = [
            "automatic_memory_updates",
            "knowledge_graph_integration", 
            "cross_session_preservation",
            "continuous_learning",
            "operational_monitoring"
        ]
        
        # Score de performance geral
        performance_score = (learning_rate + context_preservation_rate + session_efficiency) / 3.0
        
        metrics = OperationalMetrics(
            timestamp=datetime.now(timezone.utc).isoformat(),
            memory_usage=memory_usage,
            learning_rate=learning_rate,
            context_preservation_rate=context_preservation_rate,
            session_efficiency=session_efficiency,
            active_features=active_features,
            performance_score=performance_score
        )
        
        return metrics
    
    def log_operational_status(self) -> Dict[str, Any]:
        """Registra status operacional atual"""
        metrics = self.collect_current_metrics()
        self.metrics_history.append(metrics)
        
        # Salva métricas
        metrics_file = self.metrics_path / f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(metrics), f, indent=2, ensure_ascii=False)
        
        # Calcula uptime
        uptime = datetime.now(timezone.utc) - self.start_time
        uptime_str = str(uptime).split('.')[0]  # Remove microseconds
        
        status = {
            "operational_mode": "PRODUCTION",
            "uptime": uptime_str,
            "performance_score": round(metrics.performance_score, 3),
            "learning_rate": round(metrics.learning_rate, 3),
            "context_preservation": "ACTIVE" if metrics.context_preservation_rate > 0 else "INACTIVE",
            "active_features_count": len(metrics.active_features),
            "memory_efficiency": "HIGH" if metrics.memory_usage["total_files"] < 100 else "MEDIUM",
            "last_update": metrics.timestamp
        }
        
        logger.info(f"📊 Operational status logged - Performance: {status['performance_score']}")
        return status
    
    def generate_operational_report(self) -> Dict[str, Any]:
        """Gera relatório operacional completo"""
        if not self.metrics_history:
            self.log_operational_status()
        
        latest_metrics = self.metrics_history[-1]
        
        # Análise de tendências (se houver histórico)
        trends = {}
        if len(self.metrics_history) > 1:
            prev_metrics = self.metrics_history[-2]
            trends = {
                "performance_trend": "UP" if latest_metrics.performance_score > prev_metrics.performance_score else "DOWN",
                "learning_trend": "UP" if latest_metrics.learning_rate > prev_metrics.learning_rate else "DOWN",
                "efficiency_trend": "UP" if latest_metrics.session_efficiency > prev_metrics.session_efficiency else "DOWN"
            }
        
        report = {
            "report_timestamp": datetime.now(timezone.utc).isoformat(),
            "operational_summary": {
                "mode": "PRODUCTION",
                "status": "FULLY_OPERATIONAL",
                "uptime": str(datetime.now(timezone.utc) - self.start_time).split('.')[0],
                "performance_score": round(latest_metrics.performance_score, 3)
            },
            "current_metrics": asdict(latest_metrics),
            "trends": trends,
            "recommendations": self._generate_recommendations(latest_metrics),
            "next_actions": [
                "Continue monitoring operational metrics",
                "Optimize learning rate if below 0.8",
                "Maintain context preservation above 0.9",
                "Scale features based on performance"
            ]
        }
        
        # Salva relatório
        report_file = self.memory_bank_path / f"operational_report_{datetime.now().strftime('%Y%m%d_%H%M')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 Operational report generated: {report_file.name}")
        return report
    
    def _generate_recommendations(self, metrics: OperationalMetrics) -> List[str]:
        """Gera recomendações baseadas nas métricas"""
        recommendations = []
        
        if metrics.performance_score < 0.7:
            recommendations.append("⚠️ Performance below optimal - consider system optimization")
        
        if metrics.learning_rate < 0.5:
            recommendations.append("🧠 Learning rate low - increase knowledge graph activity")
        
        if metrics.context_preservation_rate < 0.8:
            recommendations.append("💾 Context preservation needs improvement")
        
        if metrics.session_efficiency < 0.6:
            recommendations.append("⚡ Session efficiency low - optimize operational workflows")
        
        if len(recommendations) == 0:
            recommendations.append("✅ All systems operating optimally")
        
        return recommendations
    
    def get_real_time_dashboard(self) -> Dict[str, Any]:
        """Retorna dashboard em tempo real"""
        status = self.log_operational_status()
        latest_metrics = self.metrics_history[-1] if self.metrics_history else None
        
        dashboard = {
            "🚀 VIBECODE V1.0 - OPERATIONAL DASHBOARD": {
                "Status": status["operational_mode"],
                "Performance": f"{status['performance_score']} / 1.0",
                "Uptime": status["uptime"],
                "Features": f"{status['active_features_count']} Active"
            },
            "📊 Real-time Metrics": {
                "Learning Rate": f"{status['learning_rate']} / 1.0",
                "Context Preservation": status["context_preservation"],
                "Memory Efficiency": status["memory_efficiency"],
                "Last Update": status["last_update"]
            },
            "🔧 Active Features": latest_metrics.active_features if latest_metrics else [],
            "💡 Current Recommendations": self._generate_recommendations(latest_metrics) if latest_metrics else []
        }
        
        return dashboard

# Instância global para monitoramento
operational_monitor = OperationalMonitor()

def get_operational_dashboard():
    """Retorna dashboard operacional"""
    return operational_monitor.get_real_time_dashboard()

def generate_operational_report():
    """Gera relatório operacional"""
    return operational_monitor.generate_operational_report()

def log_operational_metrics():
    """Registra métricas operacionais"""
    return operational_monitor.log_operational_status()

if __name__ == "__main__":
    # Demonstração do monitor operacional
    print("📊 VIBECODE V1.0 - Operational Monitor Demo")
    print("=" * 50)
    
    # Dashboard em tempo real
    dashboard = get_operational_dashboard()
    
    for section, data in dashboard.items():
        print(f"\n{section}:")
        if isinstance(data, dict):
            for key, value in data.items():
                print(f"  {key}: {value}")
        elif isinstance(data, list):
            for item in data:
                print(f"  • {item}")
    
    print("\n" + "=" * 50)
    print("🚀 Operational monitoring active!")
