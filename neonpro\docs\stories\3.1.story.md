# Story 3.1: Patient Medical Records Management

## Status

Approved

## Story

**As a** clinic receptionist, medical professional, and clinic manager,  
**I want** to maintain comprehensive patient medical records with history, allergies, and documentation,  
**so that** I can provide safe, effective treatments with complete patient context and regulatory compliance.

## Acceptance Criteria

1. **Patient Profile Management:**
   - Create and maintain comprehensive patient profiles
   - Record complete medical history and current conditions
   - Track allergies, contraindications, and sensitivities
   - Manage emergency contact information
   - Support patient demographic and insurance data

2. **Medical History Documentation:**
   - Record previous treatments and procedures
   - Track medication history and current prescriptions
   - Document surgical history and medical conditions
   - Manage family medical history when relevant
   - Support timeline-based history viewing

3. **Medical Photography & Documentation:**
   - Capture and store before/during/after treatment photos
   - Organize photos by treatment area and date
   - Support photo comparison and progress tracking
   - Ensure photo consent and privacy compliance
   - Enable secure photo sharing with patients

4. **Integration & Data Flow:**
   - Sync with appointment system for treatment history
   - Connect with financial system for billing accuracy
   - Support data import from external medical systems
   - Enable secure data export for referrals
   - Maintain audit trails for all data access

## Tasks / Subtasks

- [ ] Design patient medical records database schema (AC: 1, 2)
  - [ ] Create extended patient_profiles table with medical fields
  - [ ] Design medical_history table for comprehensive health records
  - [ ] Add allergies_contraindications table for safety tracking
  - [ ] Create medications table for current and past prescriptions
  - [ ] Implement emergency_contacts table for patient safety

- [ ] Build patient profile management interface (AC: 1)
  - [ ] Create comprehensive patient registration form
  - [ ] Implement patient profile editing and updates
  - [ ] Add demographic and insurance information management
  - [ ] Build patient search and filtering capabilities
  - [ ] Create patient profile dashboard with key information

- [ ] Implement medical history tracking system (AC: 2)
  - [ ] Create medical history entry and editing interface
  - [ ] Build medication tracking and prescription management
  - [ ] Add surgical history and medical condition documentation
  - [ ] Implement timeline-based history visualization
  - [ ] Create family medical history tracking

- [ ] Develop allergy and contraindication management (AC: 1, 2)
  - [ ] Build allergy registration and severity tracking
  - [ ] Implement contraindication alert system
  - [ ] Add drug interaction checking capabilities
  - [ ] Create treatment safety verification workflows
  - [ ] Build emergency information quick access

- [ ] Create medical photography system (AC: 3)
  - [ ] Implement secure photo capture and upload
  - [ ] Build photo organization by treatment and date
  - [ ] Add photo comparison and progress tracking tools
  - [ ] Create photo consent management system
  - [ ] Implement secure patient photo sharing

- [ ] Build integration with Epic 1 systems (AC: 4)
  - [ ] Connect patient profiles with appointment system
  - [ ] Sync treatment history with appointment records
  - [ ] Integrate with user authentication and permissions
  - [ ] Enable real-time updates across systems
  - [ ] Create unified patient data dashboard

- [ ] Implement Epic 2 financial integration (AC: 4)
  - [ ] Connect patient records with billing system
  - [ ] Support insurance verification and claims
  - [ ] Enable treatment cost estimation based on history
  - [ ] Create financial history tracking per patient
  - [ ] Build payment method preferences management

- [ ] Add data import/export capabilities (AC: 4)
  - [ ] Create medical record import from external systems
  - [ ] Build secure data export for referrals
  - [ ] Implement backup and restore functionality
  - [ ] Add data migration tools for existing records
  - [ ] Create audit trail for all data transfers

## Dev Notes

### System Architecture Context

[Source: architecture/01-system-overview-context.md]

- Medical records use Edge Functions for secure data processing
- Server Actions handle medical data entry and updates
- Real-time updates via Supabase channels for patient status changes
- PWA offline capability for viewing patient records during consultations

### Medical Data Model Requirements

[Source: architecture/03-data-model-rls-policies.md]

- All medical tables follow UUID + clinic_id + audit pattern with enhanced security
- Strict RLS policies for medical data access based on professional roles
- Comprehensive audit logging for all medical data access and modifications
- Medical data encryption for sensitive health information
- LGPD compliance for healthcare data privacy

### API Surface & Medical Records Endpoints

[Source: architecture/05-api-surface-edge-functions.md]

- POST /v1/medical/patients - Patient profile management
- GET /v1/medical/history/{patient_id} - Medical history retrieval
- POST /v1/medical/photos - Medical photography management
- GET /v1/medical/allergies/{patient_id} - Allergy and contraindication data
- POST /v1/medical/import - External medical record import

### Integration with Epic 1 Components

- Patient authentication system for secure medical data access
- Appointment system integration for treatment history tracking
- Professional calendar sync for medical record access during consultations
- Messaging system for medical alerts and notifications

### Integration with Epic 2 Components

- Financial system integration for treatment billing accuracy
- Insurance claim processing based on medical necessity
- Payment history tracking per patient for financial planning
- Cost estimation based on medical complexity and history

### Brazilian Healthcare Compliance

[Source: PRD Core Functionality]

- ANVISA regulations for medical record keeping
- CFM (Federal Council of Medicine) compliance requirements
- LGPD enhanced privacy controls for sensitive medical data
- Brazilian healthcare registry integration (CRM, CRF validation)

### Medical Data Security Requirements

- End-to-end encryption for all medical data
- Role-based access with medical professional verification
- Comprehensive audit trails for regulatory compliance
- Secure photo storage with patient consent tracking
- Data retention policies compliant with healthcare regulations

### Performance Requirements

[Source: PRD requirements]

- Patient record retrieval ≤ 2 seconds
- Medical photo upload ≤ 5 seconds
- Medical history search ≤ 1 second
- Allergy alert display ≤ 500ms during appointment scheduling
- Bulk data import ≤ 60 seconds per 1000 records

### File Structure Context

- Medical records routes: app/dashboard/medical/patients/
- Patient profile components: components/medical/patients/
- Medical history components: components/medical/history/
- Photo management: components/medical/photography/
- Medical API routes: app/api/medical/

### Database Schema Design

**patient_profiles table (extended):**

- id (UUID, PK)
- clinic_id (UUID, FK)
- user_id (UUID, FK) // Link to authentication
- first_name (VARCHAR)
- last_name (VARCHAR)
- date_of_birth (DATE)
- gender (ENUM: male, female, other, prefer_not_to_say)
- document_number (VARCHAR, encrypted) // CPF/RG
- phone (VARCHAR, encrypted)
- email (VARCHAR, encrypted)
- address (JSONB, encrypted)
- emergency_contact (JSONB, encrypted)
- insurance_info (JSONB, encrypted)
- medical_record_number (VARCHAR, unique)

**medical_history table:**

- id (UUID, PK)
- clinic_id (UUID, FK)
- patient_id (UUID, FK)
- condition_type (ENUM: current, past, family, surgical)
- condition_name (VARCHAR)
- diagnosis_date (DATE, nullable)
- severity (ENUM: low, moderate, high, critical)
- status (ENUM: active, resolved, monitoring, chronic)
- notes (TEXT)
- treating_professional (UUID, FK, nullable)

**allergies_contraindications table:**

- id (UUID, PK)
- clinic_id (UUID, FK)
- patient_id (UUID, FK)
- allergy_type (ENUM: medication, food, environmental, material)
- allergen_name (VARCHAR)
- severity (ENUM: mild, moderate, severe, anaphylactic)
- reaction_description (TEXT)
- first_occurrence (DATE, nullable)
- verified_by (UUID, FK)
- is_active (BOOLEAN)

**medications table:**

- id (UUID, PK)
- clinic_id (UUID, FK)
- patient_id (UUID, FK)
- medication_name (VARCHAR)
- dosage (VARCHAR)
- frequency (VARCHAR)
- start_date (DATE)
- end_date (DATE, nullable)
- prescribing_professional (UUID, FK, nullable)
- status (ENUM: current, discontinued, completed)
- notes (TEXT, nullable)

**medical_photos table:**

- id (UUID, PK)
- clinic_id (UUID, FK)
- patient_id (UUID, FK)
- treatment_id (UUID, FK, nullable)
- photo_type (ENUM: before, during, after, progress)
- body_area (VARCHAR)
- photo_url (VARCHAR, encrypted)
- thumbnail_url (VARCHAR, encrypted)
- capture_date (TIMESTAMP)
- consent_given (BOOLEAN)
- taken_by (UUID, FK)
- notes (TEXT, nullable)

### Security & Compliance

[Source: architecture/06-security-compliance.md]

- LGPD compliance with enhanced controls for medical data
- Role-based access for medical professionals only
- Medical data encryption at rest and in transit
- Comprehensive audit trails for regulatory compliance
- Patient consent management for photo and data usage
- Secure data retention and destruction policies

### Testing

**Testing Standards:**

- Jest unit tests for medical data validation and security
- Integration tests for healthcare system connections
- E2E tests for complete patient record workflows
- Security tests for medical data protection
- Performance tests for large medical record databases

**Testing Requirements for this Story:**

- Unit tests for patient profile data validation
- Integration tests for medical history tracking
- E2E tests for medical photography workflows
- Security tests for healthcare data access controls
- Performance tests for patient record retrieval
- Compliance tests for LGPD and healthcare regulations

**Key Test Scenarios:**

- Patient profile creation and comprehensive data entry
- Medical history timeline tracking and visualization
- Allergy alert system during treatment planning
- Medical photo capture, consent, and secure storage
- Integration with appointment and financial systems
- Data import/export for external healthcare systems
- Audit trail verification for compliance

### Medical Professional Workflow Integration

- Tablet and mobile optimization for bedside use
- Quick access to critical patient information during consultations
- Real-time allergy and contraindication alerts
- Seamless photo capture during treatment sessions
- Integration with treatment planning and documentation

### Patient Privacy and Consent Management

- Granular consent management for different data types
- Patient access to their own medical records
- Secure patient data sharing with family/authorized persons
- Data portability for patient transfers
- Right to be forgotten compliance with medical retention requirements

### Healthcare Interoperability

- HL7 FHIR standard compliance for data exchange
- Integration with Brazilian healthcare networks
- Support for medical device data integration
- Laboratory result integration capabilities
- Telemedicine platform integration readiness

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-18 | 1.0 | Initial story creation from Epic 3 | Scrum Master |

## Dev Agent Record

### Agent Model Used

*To be populated by development agent*

### Debug Log References

*To be populated by development agent*

### Completion Notes List

*To be populated by development agent*

### File List

*To be populated by development agent*

## QA Results

*To be populated by QA agent*
