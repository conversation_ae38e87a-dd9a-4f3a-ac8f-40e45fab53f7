{"system_name": "Kilo Code V1.0", "version": "1.0.0", "description": "Master configuration for the Kilo Code multi-agent system, based on VIBECODE V1.0 architecture.", "agents": [{"name": "TechnicalArchitect", "model": "<PERSON> 4", "complexity_range": [6, 10], "domains": ["architecture", "backend", "database", "complex-logic", "system-design", "api", "migration", "refactor"], "tools": ["sequential-thinking", "desktop-commander", "context7-mcp"]}, {"name": "OperationsCoordinator", "model": "Gemini 2.5 Pro", "complexity_range": [1, 7], "domains": ["coordination", "planning", "execution", "automation", "workflow", "deployment", "monitoring"], "tools": ["mcp-shrimp-task-manager", "desktop-commander"]}, {"name": "ResearchStrategist", "model": "Gemini Flash", "complexity_range": [3, 8], "domains": ["research", "analysis", "documentation", "investigation", "learning", "knowledge-synthesis"], "tools": ["tavily-mcp", "context7-mcp", "exa-mcp"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "model": "<PERSON> 4", "complexity_range": [2, 9], "domains": ["quality-assurance", "code-review", "compliance", "best-practices", "advisory", "standards-enforcement"], "tools": ["sequential-thinking", "context7-mcp"]}], "performance_targets": {"startup_time_ms": 200, "config_access_time_ms": 10, "agent_routing_time_ms": 50, "mcp_activation_time_ms": 100}, "quality_gates": {"enforcement_threshold": 8.0, "fallback_enabled": true}}