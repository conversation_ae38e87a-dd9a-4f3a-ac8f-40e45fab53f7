# WARP CLI - VIBECODE INITIALIZATION SCRIPT
# Version: 1.0.0
# Purpose: Initialize Warp CLI with VIBECODE V1.0 standards

Write-Host "Initializing Warp CLI with VIBECODE V1.0 Configuration..." -ForegroundColor Cyan

# Check if running in correct directory
if (!(Test-Path ".warp")) {
    Write-Host "❌ Error: .warp directory not found. Please run from VIBECODE root directory." -ForegroundColor Red
    exit 1
}

Write-Host "📂 Checking configuration files..." -ForegroundColor Yellow

# Check required configuration files
$requiredFiles = @(
    ".warp/config.json",
    ".warp/master-rules.md", 
    ".warp/coding-standards.md",
    ".warp/project-config.md",
    ".warp/mcps.json",
    ".warp/environment.json",
    ".warp/settings/warp-config.json"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (!(Test-Path $file)) {
        $missingFiles += $file
    } else {
        Write-Host "✅ Found: $file" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "❌ Missing required files:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "   - $file" -ForegroundColor Red
    }
    exit 1
}

# Set environment variables for Warp CLI
Write-Host "⚙️  Setting Warp environment variables..." -ForegroundColor Yellow

$env:WARP_CONFIG_PATH = "$PWD\.warp"
$env:WARP_RULES_PATH = "$PWD\.warp"
$env:WARP_MCPS_CONFIG = "$PWD\.warp\mcps.json"
$env:WARP_QUALITY_THRESHOLD = "8"
$env:WARP_WORKFLOW_ENABLED = "true"
$env:WARP_TASK_MANAGEMENT_THRESHOLD = "3"
$env:WARP_RESEARCH_PROTOCOL_MANDATORY = "true"
$env:WARP_BATCH_OPERATIONS_ENABLED = "true"
$env:WARP_AUTO_REFINEMENT = "true"

Write-Host "✅ Environment variables set!" -ForegroundColor Green

# Load configuration
Write-Host "📋 Loading VIBECODE configuration..." -ForegroundColor Yellow

try {
    $config = Get-Content ".warp/environment.json" | ConvertFrom-Json
    $mcpConfig = Get-Content ".warp/mcps.json" | ConvertFrom-Json
    $warpConfig = Get-Content ".warp/settings/warp-config.json" | ConvertFrom-Json
    
    Write-Host "✅ Configuration loaded successfully!" -ForegroundColor Green
    
    # Display key configuration
    Write-Host "📊 Key Configuration:" -ForegroundColor Cyan
    Write-Host "   - Quality Threshold: ≥$($warpConfig.quality.minimum_score)/10" -ForegroundColor White
    Write-Host "   - Workflow Steps: $($warpConfig.rules.workflow.steps)" -ForegroundColor White
    Write-Host "   - Task Management Threshold: ≥$($warpConfig.rules.workflow.complexity_threshold)" -ForegroundColor White
    Write-Host "   - Research Protocol: $($config.behavior.mandatory_research_protocol)" -ForegroundColor White
    Write-Host "   - Batch Operations: $($config.behavior.batch_api_optimization)" -ForegroundColor White
    Write-Host "   - Auto Refinement: $($config.workflow.auto_refinement)" -ForegroundColor White
    
} catch {
    Write-Host "❌ Error loading configuration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Validate MCP configuration
Write-Host "🔧 Validating MCP configuration..." -ForegroundColor Yellow

$enabledMCPs = ($mcpConfig.mcpServers | Get-Member -Type NoteProperty | Where-Object {$mcpConfig.mcpServers.$($_.Name).enabled -eq $true}).Count

Write-Host "✅ $enabledMCPs MCPs configured and enabled!" -ForegroundColor Green

# Create backup directory if not exists
if (!(Test-Path "E:\CODE-BACKUP")) {
    Write-Host "📁 Creating backup directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path "E:\CODE-BACKUP" -Force | Out-Null
    Write-Host "✅ Backup directory created at E:\CODE-BACKUP" -ForegroundColor Green
}

# Final validation
Write-Host "🔍 Final validation..." -ForegroundColor Yellow

$validationChecks = @{
    "Rules loaded" = (Test-Path ".warp/master-rules.md")
    "Coding standards active" = (Test-Path ".warp/coding-standards.md")
    "Project config loaded" = (Test-Path ".warp/project-config.md")
    "MCPs configured" = (Test-Path ".warp/mcps.json")
    "Environment set" = ($env:WARP_CONFIG_PATH -ne $null)
    "Quality gates active" = ($env:WARP_QUALITY_THRESHOLD -eq "8")
    "Workflow enabled" = ($env:WARP_WORKFLOW_ENABLED -eq "true")
}

$failedChecks = @()
foreach ($check in $validationChecks.GetEnumerator()) {
    if ($check.Value) {
        Write-Host "✅ $($check.Key)" -ForegroundColor Green
    } else {
        Write-Host "❌ $($check.Key)" -ForegroundColor Red
        $failedChecks += $check.Key
    }
}

if ($failedChecks.Count -eq 0) {
    Write-Host ""
    Write-Host "🎉 WARP CLI SUCCESSFULLY CONFIGURED WITH VIBECODE V1.0!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 ACTIVE FEATURES:" -ForegroundColor Cyan
    Write-Host "   ✅ 7-Step Mandatory Workflow" -ForegroundColor White
    Write-Host "   ✅ Quality Gates ≥8/10 (Auto-refinement)" -ForegroundColor White
    Write-Host "   ✅ Task Management (Auto-activation ≥3 complexity)" -ForegroundColor White
    Write-Host "   ✅ Research Protocol (Mandatory 3-source synthesis)" -ForegroundColor White
    Write-Host "   ✅ Batch Operations (≥70% API call reduction)" -ForegroundColor White
    Write-Host "   ✅ TypeScript/Next.js 14 Standards" -ForegroundColor White
    Write-Host "   ✅ File Operations (Auto tool selection)" -ForegroundColor White
    Write-Host "   ✅ Security & Performance Standards" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 System Status: ACTIVE - All rules automatically applied" -ForegroundColor Green
    Write-Host "📍 Environment: GRUPO US Development - E:/VIBECODE" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 All interactions now follow VIBECODE standards automatically!" -ForegroundColor Yellow
    
} else {
    Write-Host ""
    Write-Host "⚠️ CONFIGURATION INCOMPLETE - Failed checks:" -ForegroundColor Yellow
    foreach ($check in $failedChecks) {
        Write-Host "   - $check" -ForegroundColor Red
    }
    exit 1
}

# Success
exit 0
