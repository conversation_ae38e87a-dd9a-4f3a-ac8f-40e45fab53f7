# 📋 KIRO V1.0 - UNIFIED SYSTEM CONFIGURATION

**Version**: 1.0.0 | **Status**: PRODUCTION_READY | **Adapted from**: VIBECODE V1.0

## 🎯 SISTEMA CONSOLIDADO KIRO

Este sistema foi adaptado das regras consolidadas do VIBECODE V1.0, otimizado especificamente para o ambiente Kiro Code.

## 📂 ESTRUTURA KIRO SIMPLIFICADA

```
.kiro/
├── settings/
│   ├── kiro-master-config.json    # ✅ Configuração master
│   └── mcp.json                   # ✅ Configuração MCP
├── steering/
│   ├── master-rule.md             # ✅ Regra principal
│   ├── coding-standards.md        # ✅ Padrões de código
│   ├── spec-workflow.md           # ✅ Workflow de specs
│   ├── research-protocol.md       # ✅ Protocolo de pesquisa
│   ├── quality-gates.md           # ✅ Padrões de qualidade
│   └── task-management.md         # ✅ Gerenciamento de tarefas
└── README-KIRO-SYSTEM.md         # ✅ Esta documentação
```

## ⚙️ CONFIGURAÇÃO DE MCPs

### **MCPs Essenciais Configurados**
- `desktop-commander` - Operações de arquivo/terminal
- `sequential-thinking` - <PERSON><PERSON>cínio complexo
- `context7` - Documentação de bibliotecas
- `tavily` - Pesquisa web
- `exa` - Pesquisa alternativa

### **Instalação**
```bash
# Instalar MCPs usando uvx
uvx @mcp/desktop-commander
uvx @mcp/sequential-thinking
uvx @mcp/context7-mcp
uvx @mcp/tavily-mcp
uvx @mcp/exa-mcp
```

## 🔄 WORKFLOW OBRIGATÓRIO (7 PASSOS)

1. **ANALYZE** → Avaliar complexidade (1-10)
2. **SELECT** → Escolher ferramentas baseado na complexidade
3. **EXECUTE** → Usar ferramentas MCP apropriadas
4. **REFLECT** → Avaliar qualidade da saída
5. **REFINE** → Melhorar se qualidade <8/10
6. **VALIDATE** → Confirmar qualidade ≥8/10
7. **LEARN** → Atualizar conhecimento e contexto

## 🛠️ SELEÇÃO DE FERRAMENTAS

### **Roteamento por Complexidade**
```json
{
  "1-4": "operações básicas (ferramentas nativas Kiro)",
  "5-7": "workflow padrão (+ sequential-thinking)",
  "8-10": "workflow avançado (orquestração completa MCP)"
}
```

### **Operações de Arquivo**
- **≤200 linhas**: Ferramentas nativas Kiro
- **>200 linhas**: Editor Kiro
- **Sempre**: Verificar após escrita

### **Protocolo de Pesquisa (OBRIGATÓRIO)**
1. **Context7** (docs técnicas) - Prioridade 1
2. **Tavily** (pesquisa web) - Prioridade 2
3. **Exa** (pesquisa alternativa) - Prioridade 3

## ✅ PADRÕES DE QUALIDADE

### **Obrigatório**
- Qualidade ≥8/10 (nunca comprometa)
- 100% dos requisitos atendidos
- Verificação de arquivos após escrita
- Documentação de todas as fontes de pesquisa

### **Princípios**
- **"Enhance, Don't Proliferate"** - ≥85% reutilização
- **Context First** - Entender completamente antes de implementar
- **Quality Gates** - Validação obrigatória
- **One Task at a Time** - Uma tarefa por vez durante execução

## 📋 DESENVOLVIMENTO DE SPECS

### **Workflow de Specs**
1. **Requirements** → Criar requisitos detalhados com user stories
2. **Design** → Desenvolver documento de design abrangente
3. **Tasks** → Criar plano de implementação acionável
4. **Execute** → Implementar uma tarefa por vez
5. **Review** → Aprovação do usuário em cada fase

### **Estrutura de Arquivos**
```
.kiro/specs/{nome-da-feature}/
├── requirements.md    # User stories e critérios de aceitação
├── design.md         # Documento de design abrangente
└── tasks.md          # Lista de tarefas de implementação
```

## 🔍 PROTOCOLO DE PESQUISA

### **Ativação Automática**
Palavras-chave que ativam automaticamente o protocolo de pesquisa:
`search, find, documentation, tutorial, how to, example, guide, library, framework, API, best practices, implementation, configuration, integration`

### **Sequência Obrigatória**
1. **Context7-MCP** - SEMPRE primeiro para documentação técnica
2. **Tavily-MCP** - SEMPRE segundo para pesquisa web geral
3. **Exa-MCP** - SEMPRE terceiro para pesquisa alternativa

### **Requisitos de Qualidade**
- Mínimo 3 fontes documentadas
- Síntese com qualidade ≥8/10
- Verificação de completude obrigatória

## 🎯 GERENCIAMENTO DE TAREFAS

### **Execução de Tarefas**
- **Uma tarefa por vez**: Foco em completar antes de prosseguir
- **Qualidade ≥8/10**: Garantir qualidade antes de continuar
- **Revisão do usuário**: Parar após cada tarefa para feedback
- **Sem suposições**: Não continuar automaticamente para próxima tarefa

### **Roteamento por Complexidade**
- **Tarefas simples (1-4)**: Ferramentas nativas Kiro
- **Tarefas médias (5-7)**: + Sequential-thinking
- **Tarefas complexas (8-10)**: Orquestração completa + coordenação manual

## 🚨 TROUBLESHOOTING

### **MCPs Não Funcionam**
1. Verificar instalação: `uvx @mcp/nome-do-mcp`
2. Verificar configuração em `.kiro/settings/mcp.json`
3. Reiniciar Kiro se necessário

### **Erro de Qualidade**
- Se qualidade <8/10, aplicar ciclo de refinamento
- Máximo 3 ciclos de refinamento para operações padrão
- Sem limite para síntese de pesquisa até atingir ≥8/10

## 📊 COMANDOS E VALIDAÇÃO

### **Estrutura de Validação**
- Verificação de qualidade automática
- Refinamento automático se qualidade <8/10
- Documentação obrigatória de fontes de pesquisa
- Aprovação do usuário em fases de spec

---

## 📈 STATUS DE ADAPTAÇÃO

**✅ CONCLUÍDO**: Sistema VIBECODE adaptado para Kiro
**✅ CONFIGURAÇÃO**: MCPs configurados e otimizados
**✅ STEERING**: Regras de steering implementadas
**✅ QUALIDADE**: Padrão ≥8/10 mantido
**✅ WORKFLOW**: 7 passos implementados

---

**LEMBRE-SE**: 
- Qualidade ≥8/10 é OBRIGATÓRIA
- Sempre seguir o workflow de 7 passos
- "Enhance, Don't Proliferate" - ≥85% reutilização
- Uma tarefa por vez durante execução
- Protocolo de pesquisa é AUTOMÁTICO e OBRIGATÓRIO

**"Enhance, Don't Proliferate - Maximum Efficiency"**