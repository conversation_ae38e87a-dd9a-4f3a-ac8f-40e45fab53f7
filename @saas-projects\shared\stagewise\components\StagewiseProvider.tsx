'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { StagewiseConfig, StageConfig } from '../config/stagewise.config';
import { useStageNavigation } from '../hooks/useStageNavigation';
import { useStageValidation } from '../hooks/useStageValidation';

interface StagewiseContextValue {
  config: StagewiseConfig;
  navigation: ReturnType<typeof useStageNavigation>;
  validation: ReturnType<typeof useStageValidation>;
}

const StagewiseContext = createContext<StagewiseContextValue | null>(null);

export interface StagewiseProviderProps {
  children: ReactNode;
  config: StagewiseConfig;
  initialStage?: string;
}

export function StagewiseProvider({ 
  children, 
  config,
  initialStage 
}: StagewiseProviderProps) {
  const navigation = useStageNavigation(config.stages, initialStage);
  const validation = useStageValidation();

  const value: StagewiseContextValue = {
    config,
    navigation,
    validation
  };

  return (
    <StagewiseContext.Provider value={value}>
      {children}
    </StagewiseContext.Provider>
  );
}

export function useStagewise() {
  const context = useContext(StagewiseContext);
  if (!context) {
    throw new Error('useStagewise must be used within a StagewiseProvider');
  }
  return context;
}

export function useCurrentStage(): StageConfig | undefined {
  const { config, navigation } = useStagewise();
  return config.stages.find(stage => stage.id === navigation.currentStage);
}