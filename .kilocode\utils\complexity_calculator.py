import re
from typing import Dict, Any

class ComplexityCalculator:
    """
    Calculates the complexity of a given task request.
    This is a simplified version for initial implementation.
    """

    # More complex patterns can be added here
    KEYWORD_WEIGHTS = {
        "refactor": 8,
        "architecture": 9,
        "database": 7,
        "migrate": 9,
        "implement": 5,
        "fix": 3,
        "debug": 4,
        "optimize": 7,
        "create": 2,
        "update": 3,
        "research": 6,
        "analyze": 7,
        "document": 2,
        "test": 4,
        "deploy": 6
    }

    def calculate_complexity(self, request: Dict[str, Any]) -> int:
        """
        Calculates the complexity score (1-10) for a given request.

        Args:
            request (Dict[str, Any]): The request payload, expected to have a 'description' key.

        Returns:
            int: The calculated complexity score.
        """
        description = request.get("description", "").lower()
        if not description:
            return 1

        # Initial score based on length
        score = self._score_from_length(description)

        # Add points for keywords
        keyword_score = self._score_from_keywords(description)

        # Combine scores (example of a simple combination logic)
        # This can be evolved into a more sophisticated model
        final_score = (score * 0.4) + (keyword_score * 0.6)

        # Normalize to a 1-10 scale
        return max(1, min(10, int(round(final_score))))

    def _score_from_length(self, text: str) -> float:
        """Scores based on text length."""
        length = len(text)
        if length < 50:
            return 1
        elif length < 150:
            return 3
        elif length < 300:
            return 5
        elif length < 600:
            return 7
        else:
            return 9

    def _score_from_keywords(self, text: str) -> float:
        """Scores based on the highest weight of found keywords."""
        max_weight = 0
        words = re.findall(r'\b\w+\b', text)
        for word in words:
            if word in self.KEYWORD_WEIGHTS:
                if self.KEYWORD_WEIGHTS[word] > max_weight:
                    max_weight = self.KEYWORD_WEIGHTS[word]

        return max_weight if max_weight > 0 else 2 # Default score if no keywords found

if __name__ == '__main__':
    # Example Usage
    calculator = ComplexityCalculator()

    req1 = {"description": "Create a simple button component."}
    print(f"Request 1 complexity: {calculator.calculate_complexity(req1)}") # Expected low

    req2 = {"description": "Refactor the entire authentication module and migrate the database schema."}
    print(f"Request 2 complexity: {calculator.calculate_complexity(req2)}") # Expected high

    req3 = {"description": "Analyze the performance of the API and optimize the main endpoints."}
    print(f"Request 3 complexity: {calculator.calculate_complexity(req3)}") # Expected medium-high
