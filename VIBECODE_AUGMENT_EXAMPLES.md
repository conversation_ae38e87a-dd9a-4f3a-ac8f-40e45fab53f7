# EXEMPLOS PRÁTICOS - VIBECODE RULES PARA AUGMENT VSCODE

**Versão**: 1.0.0 - Exemplos práticos de aplicação das regras VIBECODE no Augment
**Referência**: VIBECODE_AUGMENT_RULES.md

---

## 🎯 EXEMPLOS DE APLICAÇÃO DO WORKFLOW

### **Exemplo 1: Solicitação de Pesquisa (Complexidade 4/10)**

**Input do Usuário**: "Como configurar autenticação JWT com Supabase?"

**Processo Automático**:

1. **ANALYZE**: 
   - Complexidade: 4/10 (configuração + integração)
   - Tipo: Pesquisa + implementação
   - Keywords detectadas: "configurar", "autenticação", "Supabase"

2. **STRATEGY**:
   - Ativar protocolo de pesquisa obrigatório
   - Usar task management (complexidade ≥3)
   - Preparar para síntese consolidada

3. **EXECUTE**:
   ```
   🔍 PROTOCOLO DE PESQUISA ATIVADO
   ├── 1. codebase-retrieval: "JWT authentication Supabase configuration"
   ├── 2. web-search: "Supabase JWT authentication setup tutorial"
   └── 3. tavily-search: "Supabase authentication best practices 2024"
   ```

4. **REFLECT**: Avaliar qualidade das fontes encontradas

5. **REFINE**: Consolidar informações em resposta estruturada

6. **VALIDATE**: Confirmar qualidade ≥8/10 da síntese

7. **LEARN**: Documentar padrões de configuração encontrados

---

### **Exemplo 2: Task Management Automático (Complexidade 8/10)**

**Input do Usuário**: "Preciso criar um dashboard completo com autenticação, CRUD de usuários e relatórios"

**Processo Automático**:

1. **ANALYZE**:
   - Complexidade: 8/10 (dashboard + auth + CRUD + relatórios)
   - Indicadores multi-step detectados
   - Task management OBRIGATÓRIO

2. **STRATEGY**:
   - Ativar sequential-thinking
   - Usar task management nativo
   - Planejar arquitetura completa

3. **EXECUTE**:
   ```
   📋 TASK MANAGEMENT ATIVADO
   ├── Tarefa Principal: Dashboard Completo
   ├── Subtarefa 1: Configurar autenticação
   ├── Subtarefa 2: Implementar CRUD usuários
   ├── Subtarefa 3: Criar sistema de relatórios
   └── Subtarefa 4: Integrar componentes
   ```

4. **REFLECT**: Avaliar viabilidade do plano

5. **REFINE**: Ajustar cronograma e dependências

6. **VALIDATE**: Confirmar plano completo e executável

7. **LEARN**: Atualizar padrões de arquitetura

---

## 🔧 EXEMPLOS DE SELEÇÃO DE FERRAMENTAS

### **Cenário 1: Arquivo Pequeno (≤200 linhas)**
```json
{
  "situation": "Editar componente React de 150 linhas",
  "selected_tools": ["codebase-retrieval", "view", "str-replace-editor"],
  "approach": "Edição direta com verificação",
  "verification": "view após str-replace-editor"
}
```

### **Cenário 2: Arquivo Grande (>200 linhas)**
```json
{
  "situation": "Refatorar arquivo de 500 linhas",
  "selected_tools": ["codebase-retrieval", "str-replace-editor"],
  "approach": "Edições incrementais focadas",
  "strategy": "Múltiplas edições pequenas com verificação"
}
```

### **Cenário 3: Pesquisa Complexa**
```json
{
  "situation": "Implementar padrão arquitetural específico",
  "selected_tools": ["codebase-retrieval", "web-search", "tavily-search"],
  "sequence": "OBRIGATÓRIA - sempre nesta ordem",
  "synthesis": "Consolidar todas as fontes ≥8/10"
}
```

---

## 🚀 EXEMPLOS DE BATCH OPERATIONS

### **Anti-Pattern (PROIBIDO)**
```javascript
// ❌ ERRADO - Múltiplas chamadas individuais
view("file1.js")
view("file2.js") 
view("file3.js")
str-replace-editor("file1.js", ...)
str-replace-editor("file2.js", ...)
```

### **Pattern Correto (OBRIGATÓRIO)**
```javascript
// ✅ CORRETO - Operação consolidada
codebase-retrieval("components architecture patterns")
// Depois uma única sessão de edições planejadas
str-replace-editor com múltiplas edições planejadas
```

---

## 📋 EXEMPLOS DE TASK MANAGEMENT

### **Task Simples (Complexidade 1-4)**
```markdown
- [ ] Corrigir bug no componente Header
  - Usar ferramentas nativas do Augment
  - Auto-complete quando resolvido
```

### **Task Média (Complexidade 5-7)**
```markdown
- [ ] Implementar sistema de notificações
  - [ ] Pesquisar bibliotecas disponíveis
  - [ ] Configurar componente base
  - [ ] Integrar com backend
  - [ ] Testar funcionalidade
```

### **Task Complexa (Complexidade 8-10)**
```markdown
- [ ] Arquitetura completa de e-commerce
  - [ ] Análise de requisitos
    - [ ] Pesquisar padrões de e-commerce
    - [ ] Definir arquitetura de dados
  - [ ] Implementação do backend
    - [ ] Configurar autenticação
    - [ ] Implementar API de produtos
    - [ ] Sistema de pagamentos
  - [ ] Frontend
    - [ ] Componentes de produto
    - [ ] Carrinho de compras
    - [ ] Checkout
  - [ ] Testes e deploy
```

---

## 🔍 EXEMPLOS DE PROTOCOLO DE PESQUISA

### **Pesquisa Técnica Completa**
```
Usuário: "Como implementar WebSockets com Next.js?"

Sequência OBRIGATÓRIA:
1. codebase-retrieval("WebSocket Next.js implementation patterns")
2. web-search("Next.js WebSocket setup tutorial 2024")
3. tavily-search("WebSocket Next.js best practices real-time")

Síntese: Consolidar todas as fontes em resposta ≥8/10
```

### **Pesquisa de Configuração**
```
Usuário: "Configurar Tailwind CSS no projeto"

Sequência OBRIGATÓRIA:
1. codebase-retrieval("Tailwind CSS configuration existing setup")
2. web-search("Tailwind CSS installation guide latest")
3. tavily-search("Tailwind CSS configuration best practices")

Resultado: Guia completo adaptado ao projeto atual
```

---

## ✅ EXEMPLOS DE VALIDAÇÃO DE QUALIDADE

### **Checklist de Qualidade ≥8/10**
```json
{
  "quality_validation": {
    "completeness": "Todos os requisitos atendidos?",
    "accuracy": "Informações corretas e atualizadas?",
    "clarity": "Explicação clara e compreensível?",
    "practicality": "Solução implementável?",
    "documentation": "Código bem documentado?",
    "testing": "Inclui estratégia de testes?",
    "maintenance": "Fácil de manter e estender?",
    "performance": "Otimizado para performance?"
  }
}
```

### **Exemplo de Refinamento**
```
Qualidade Inicial: 6/10
Problemas identificados:
- Falta documentação inline
- Não inclui tratamento de erros
- Performance não otimizada

Ações de Refinamento:
1. Adicionar comentários explicativos
2. Implementar try-catch apropriado
3. Otimizar queries e renderização

Qualidade Final: 9/10 ✅
```

---

## 🚨 EXEMPLOS DE TRATAMENTO DE FALHAS

### **Falha na Pesquisa**
```
Cenário: codebase-retrieval falha
Ação: Continuar com web-search e tavily-search
Documentação: "Limitação: base de código não acessível"
Resultado: Síntese baseada em fontes externas
```

### **Falha Parcial**
```
Cenário: 2 de 3 ferramentas funcionam
Ação: Usar resultados disponíveis
Documentação: "Fontes consultadas: web-search, tavily-search"
Qualidade: Ainda buscar ≥8/10 com fontes disponíveis
```

---

## 📊 MÉTRICAS DE EXEMPLO

### **Otimização Bem-Sucedida**
```
Antes: 15 chamadas de ferramentas individuais
Depois: 4 chamadas consolidadas
Redução: 73% ✅ (target: ≥70%)
Qualidade mantida: 8.5/10 ✅
Tempo: 25s ✅ (target: <30s)
```

### **Reutilização de Código**
```
Código novo: 150 linhas
Código reutilizado: 850 linhas
Reutilização: 85% ✅ (target: ≥85%)
Princípio: "Aprimore, Não Prolifere" ✅
```

---

**"Exemplos Práticos para Máxima Eficiência e Qualidade"**

**VIBECODE EXAMPLES FOR AUGMENT VSCODE - Guia de Implementação**
