# AegisWallet Project Configuration

## Project Overview
- **Project Name**: AegisWallet
- **Type**: Next.js Cryptocurrency Wallet Application
- **Architecture**: Secure financial application with blockchain integration
- **Primary Focus**: Digital wallet management and cryptocurrency transactions

## Development Guidelines

### Frontend Development
- **Framework**: Next.js 14+ with App Router
- **Styling**: Tailwind CSS with security-focused design
- **UI Components**: Secure component library with encryption indicators
- **State Management**: Zustand or Redux for complex wallet state
- **TypeScript**: Strict mode with additional security type checks

### Backend Development
- **API Routes**: Next.js API routes with enhanced security
- **Database**: PostgreSQL with encrypted sensitive data storage
- **Authentication**: Multi-factor authentication with biometric support
- **Blockchain Integration**: Web3 libraries for cryptocurrency operations
- **Security**: End-to-end encryption and secure key management

### Security Standards
- **Encryption**: All sensitive data encrypted at rest and in transit
- **Key Management**: Secure private key storage and handling
- **Audit Logging**: Comprehensive transaction and access logging
- **Compliance**: Financial regulations and security standards
- **Testing**: Security-focused testing including penetration testing

### Project-Specific Rules
1. Never log or expose private keys or sensitive data
2. Implement proper input validation for all financial data
3. Use secure random number generation for cryptographic operations
4. Implement proper session management and timeout
5. Follow cryptocurrency best practices for transaction handling
6. Ensure proper backup and recovery mechanisms
7. Implement rate limiting for API endpoints
8. Use secure communication protocols only
9. Regular security audits and vulnerability assessments
10. Proper error handling without information leakage

## Memory Management
- Store project-specific context in `aegiswallet/.cursor/memory/`
- Track security implementation progress
- Document security decisions and threat models
- Maintain compliance and audit documentation