---
source_id: session_context
data_file: @project-core/memory/current_session_context.md
priority: 3
enabled: true
tags_func: session_context_tags
description: Contexto da sessão atual de trabalho
---

# Session Context Manifest

Este manifesto gerencia o contexto da sessão atual.

## Configuração

- **Source ID**: `session_context`
- **Data File**: Contexto da sessão ativa
- **Priority**: 3 (prioridade média)
- **Tags Function**: `session_context_tags` para categorização de contexto

## Funcionalidade

Mantém o estado da sessão atual, incluindo tarefas ativas, decisões recentes e progresso atual.
