{"mcpServers": {"desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "disabled": false, "autoApprove": ["get_config", "list_directory", "execute_command", "read_file", "read_multiple_files", "edit_block", "search_code", "write_file"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequentialthinking"]}, "context7-mcp": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"UPSTASH_CONTEXT7_API_KEY": "63205ff1-e717-4eae-ad3e-0719a8bd9fa0"}, "disabled": false, "autoApprove": ["resolve-library-id"]}, "tavily-mcp": {"command": "npx", "args": ["-y", "tavily-mcp@0.2.4"], "env": {"TAVILY_API_KEY": "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"}, "disabled": false, "autoApprove": ["tavily-search"]}, "exa-mcp": {"command": "npx", "args": ["-y", "exa-mcp-server"], "env": {"EXA_API_KEY": "fae6582d-4562-45be-8ce9-f6c0c3518c66"}, "disabled": false, "autoApprove": ["web_search_exa"]}, "sentry-mcp": {"command": "npx", "args": ["-y", "mcp-remote@latest", "https://mcp.sentry.dev/sse"], "env": {"SENTRY_ACCESS_TOKEN": "***********************************************************************"}, "disabled": false, "autoApprove": ["whoami"]}}}