import json
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from .base_agent import BaseAgent

class OperationsCoordinator(BaseAgent):
    """
    Operations Coordinator agent specialized in task coordination, workflow management,
    and system operations using Gemini 2.5 Pro.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Operations Coordinator agent.

        Args:
            config (Dict[str, Any]): Agent configuration
        """
        super().__init__(config)
        self.model = "google/gemini-2.5-pro"
        self.complexity_range = {"min": 1, "max": 7}
        self.optimal_complexity = [3, 4, 5, 6]
        self.quality_threshold = 7.5

        # Specialized capabilities
        self.domain_expertise = {
            "primary": [
                "task_coordination",
                "workflow_management",
                "file_operations",
                "system_administration",
                "process_execution"
            ],
            "secondary": [
                "deployment_coordination",
                "resource_management",
                "monitoring_setup",
                "automation_scripts"
            ]
        }

        # Operations-specific keywords
        self.operations_keywords = {
            "high_priority": [
                "execute", "run", "deploy", "coordinate", "manage",
                "organize", "schedule", "automate", "monitor", "control"
            ],
            "medium_priority": [
                "file", "directory", "folder", "create", "copy",
                "move", "delete", "backup", "sync", "upload"
            ],
            "low_priority": [
                "list", "check", "status", "info", "details"
            ],
            "workflow_operations": [
                "workflow", "pipeline", "process", "batch", "queue",
                "job", "task", "operation", "sequence", "chain"
            ]
        }

        # Task execution patterns
        self.execution_patterns = {
            "sequential": "Execute tasks in order",
            "parallel": "Execute tasks concurrently",
            "conditional": "Execute based on conditions",
            "retry": "Execute with retry logic",
            "scheduled": "Execute on schedule"
        }

    def can_handle(self, request: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Determines if this agent can handle the given request.

        Args:
            request (Dict[str, Any]): The request to evaluate

        Returns:
            Tuple[bool, float]: (can_handle, confidence_score)
        """
        complexity = request.get("complexity", 0)
        message = request.get("message", "").lower()
        request_type = request.get("type", "general")

        # Base confidence from complexity
        if complexity < self.complexity_range["min"]:
            base_confidence = 0.6  # Simple tasks are acceptable
        elif complexity > self.complexity_range["max"]:
            base_confidence = 0.3  # Too complex for operations focus
        elif complexity in self.optimal_complexity:
            base_confidence = 0.9  # Optimal range
        else:
            base_confidence = 0.7  # Good range

        # Keyword matching boost
        keyword_boost = 0.0

        # High priority keywords (operations focus)
        high_priority_matches = sum(1 for keyword in self.operations_keywords["high_priority"] if keyword in message)
        keyword_boost += high_priority_matches * 0.20

        # Medium priority keywords (file operations)
        medium_priority_matches = sum(1 for keyword in self.operations_keywords["medium_priority"] if keyword in message)
        keyword_boost += medium_priority_matches * 0.15

        # Workflow operations keywords
        workflow_matches = sum(1 for keyword in self.operations_keywords["workflow_operations"] if keyword in message)
        keyword_boost += workflow_matches * 0.25

        # Domain expertise boost
        domain_boost = 0.0
        for domain in self.domain_expertise["primary"]:
            if domain.replace("_", " ") in message:
                domain_boost += 0.25
        for domain in self.domain_expertise["secondary"]:
            if domain.replace("_", " ") in message:
                domain_boost += 0.15

        # Request type boost
        type_boost = 0.0
        if request_type in ["execution", "coordination", "file_operations"]:
            type_boost = 0.25
        elif request_type in ["deployment", "automation", "monitoring"]:
            type_boost = 0.20

        # Calculate final confidence
        confidence = min(base_confidence + keyword_boost + domain_boost + type_boost, 1.0)

        # Decision threshold
        can_handle = confidence >= 0.5

        return can_handle, confidence

    def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes a request using operations coordination expertise.

        Args:
            request (Dict[str, Any]): The request to process

        Returns:
            Dict[str, Any]: Processed response
        """
        start_time = time.time()

        # Extract request details
        message = request.get("message", "")
        complexity = request.get("complexity", 0)
        context = request.get("context", {})

        # Determine processing strategy
        processing_strategy = self._determine_processing_strategy(message, complexity)

        # Process based on strategy
        if processing_strategy == "workflow_coordination":
            response = self._process_workflow_coordination(message, context)
        elif processing_strategy == "file_operations":
            response = self._process_file_operations(message, context)
        elif processing_strategy == "system_execution":
            response = self._process_system_execution(message, context)
        elif processing_strategy == "deployment_coordination":
            response = self._process_deployment_coordination(message, context)
        elif processing_strategy == "monitoring_setup":
            response = self._process_monitoring_setup(message, context)
        else:
            response = self._process_general_operations(message, context)

        # Calculate processing time
        processing_time = (time.time() - start_time) * 1000

        # Enhance response with operations-specific metadata
        response.update({
            "agent": "OperationsCoordinator",
            "model": self.model,
            "processing_time_ms": processing_time,
            "complexity_handled": complexity,
            "processing_strategy": processing_strategy,
            "quality_score": self._calculate_quality_score(response, complexity),
            "execution_plan": self._generate_execution_plan(message, complexity),
            "resource_requirements": self._estimate_resource_requirements(message),
            "success_criteria": self._define_success_criteria(message),
            "timestamp": datetime.now().isoformat()
        })

        return response

    def _determine_processing_strategy(self, message: str, complexity: int) -> str:
        """
        Determines the best processing strategy based on message content and complexity.
        """
        message_lower = message.lower()

        # Workflow coordination keywords
        if any(keyword in message_lower for keyword in ["workflow", "coordinate", "orchestrate", "manage"]):
            return "workflow_coordination"

        # File operations keywords
        elif any(keyword in message_lower for keyword in ["file", "directory", "folder", "copy", "move"]):
            return "file_operations"

        # System execution keywords
        elif any(keyword in message_lower for keyword in ["execute", "run", "command", "script", "process"]):
            return "system_execution"

        # Deployment keywords
        elif any(keyword in message_lower for keyword in ["deploy", "deployment", "release", "rollout"]):
            return "deployment_coordination"

        # Monitoring keywords
        elif any(keyword in message_lower for keyword in ["monitor", "alert", "metric", "log", "track"]):
            return "monitoring_setup"

        # Default to general operations
        else:
            return "general_operations"

    def _process_workflow_coordination(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes workflow coordination requests.
        """
        return {
            "type": "workflow_coordination",
            "content": f"Workflow coordination plan for: {message}",
            "workflow_stages": [
                "Requirements gathering and validation",
                "Resource allocation and planning",
                "Task decomposition and scheduling",
                "Execution monitoring and control",
                "Quality validation and reporting"
            ],
            "coordination_strategy": self._design_coordination_strategy(message),
            "task_dependencies": self._identify_task_dependencies(message),
            "resource_allocation": self._plan_resource_allocation(message),
            "progress_tracking": self._setup_progress_tracking(message),
            "contingency_plans": self._create_contingency_plans(message)
        }

    def _process_file_operations(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes file operations requests.
        """
        return {
            "type": "file_operations",
            "content": f"File operations plan for: {message}",
            "operation_type": self._determine_file_operation_type(message),
            "file_handling_strategy": [
                "Validate file paths and permissions",
                "Implement backup procedures",
                "Execute operations with error handling",
                "Verify operation completion",
                "Clean up temporary files"
            ],
            "safety_measures": self._implement_file_safety_measures(message),
            "batch_processing": self._plan_batch_processing(message),
            "error_recovery": self._design_error_recovery(message),
            "performance_optimization": self._optimize_file_performance(message)
        }

    def _process_system_execution(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes system execution requests.
        """
        return {
            "type": "system_execution",
            "content": f"System execution plan for: {message}",
            "execution_strategy": self._design_execution_strategy(message),
            "command_sequence": self._plan_command_sequence(message),
            "environment_setup": [
                "Verify system requirements",
                "Set environment variables",
                "Initialize required services",
                "Configure security permissions"
            ],
            "execution_monitoring": self._setup_execution_monitoring(message),
            "rollback_procedures": self._design_rollback_procedures(message),
            "success_validation": self._plan_success_validation(message)
        }

    def _process_deployment_coordination(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes deployment coordination requests.
        """
        return {
            "type": "deployment_coordination",
            "content": f"Deployment coordination plan for: {message}",
            "deployment_strategy": self._design_deployment_strategy(message),
            "environment_preparation": [
                "Backup current system state",
                "Verify deployment prerequisites",
                "Configure deployment environment",
                "Test deployment procedures"
            ],
            "rollout_plan": self._create_rollout_plan(message),
            "validation_checkpoints": self._setup_validation_checkpoints(message),
            "rollback_strategy": self._design_rollback_strategy(message),
            "post_deployment": self._plan_post_deployment_tasks(message)
        }

    def _process_monitoring_setup(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes monitoring setup requests.
        """
        return {
            "type": "monitoring_setup",
            "content": f"Monitoring setup plan for: {message}",
            "monitoring_strategy": self._design_monitoring_strategy(message),
            "metrics_collection": [
                "System performance metrics",
                "Application health metrics",
                "Business process metrics",
                "Error and exception tracking"
            ],
            "alerting_rules": self._configure_alerting_rules(message),
            "dashboard_setup": self._plan_dashboard_setup(message),
            "data_retention": self._configure_data_retention(message),
            "maintenance_procedures": self._establish_maintenance_procedures(message)
        }

    def _process_general_operations(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes general operations requests.
        """
        return {
            "type": "general_operations",
            "content": f"Operations plan for: {message}",
            "operational_approach": self._design_operational_approach(message),
            "execution_phases": [
                "Planning and preparation",
                "Resource provisioning",
                "Implementation execution",
                "Validation and testing",
                "Documentation and handover"
            ],
            "quality_assurance": self._plan_quality_assurance(message),
            "risk_mitigation": self._identify_risk_mitigation(message),
            "documentation_requirements": self._define_documentation_requirements(message)
        }

    def _calculate_quality_score(self, response: Dict[str, Any], complexity: int) -> float:
        """
        Calculates quality score for the response.
        """
        base_score = 7.5  # OperationsCoordinator baseline

        # Complexity handling bonus
        if complexity in self.optimal_complexity:
            base_score += 0.5
        elif complexity <= 7:
            base_score += 0.3

        # Response completeness bonus
        if len(response.get("content", "")) > 80:
            base_score += 0.2

        # Operations-specific content bonus
        if any(key in response for key in ["workflow_stages", "execution_strategy", "coordination_strategy"]):
            base_score += 0.4

        # Practical implementation bonus
        if any(key in response for key in ["resource_allocation", "safety_measures", "rollback_procedures"]):
            base_score += 0.3

        return min(base_score, 10.0)

    def _generate_execution_plan(self, message: str, complexity: int) -> Dict[str, Any]:
        """
        Generates a comprehensive execution plan.
        """
        plan = {
            "phases": [],
            "timeline": "To be determined based on scope",
            "dependencies": [],
            "resources": [],
            "checkpoints": []
        }

        if complexity <= 3:
            plan["phases"] = ["Preparation", "Execution", "Validation"]
            plan["timeline"] = "1-2 hours"
        elif complexity <= 5:
            plan["phases"] = ["Planning", "Preparation", "Execution", "Validation", "Documentation"]
            plan["timeline"] = "4-8 hours"
        else:
            plan["phases"] = ["Analysis", "Planning", "Preparation", "Execution", "Monitoring", "Validation", "Documentation"]
            plan["timeline"] = "1-3 days"

        return plan

    def _estimate_resource_requirements(self, message: str) -> Dict[str, Any]:
        """
        Estimates resource requirements for the operation.
        """
        return {
            "computational": "Standard processing requirements",
            "storage": "Temporary storage for intermediate results",
            "network": "Standard network connectivity",
            "human": "Single operator for monitoring and validation",
            "time": "Based on complexity and scope analysis"
        }

    def _define_success_criteria(self, message: str) -> List[str]:
        """
        Defines success criteria for the operation.
        """
        return [
            "All planned operations completed successfully",
            "No system errors or failures during execution",
            "Expected outcomes achieved within tolerance",
            "Proper documentation and logging maintained",
            "System remains stable post-operation"
        ]

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Returns the agent's capabilities.
        """
        return {
            "agent_type": "OperationsCoordinator",
            "model": self.model,
            "complexity_range": self.complexity_range,
            "optimal_complexity": self.optimal_complexity,
            "domain_expertise": self.domain_expertise,
            "specializations": [
                "Workflow Coordination",
                "File Operations Management",
                "System Execution",
                "Deployment Coordination",
                "Monitoring Setup",
                "Process Automation",
                "Resource Management"
            ],
            "quality_threshold": self.quality_threshold,
            "processing_strategies": [
                "workflow_coordination",
                "file_operations",
                "system_execution",
                "deployment_coordination",
                "monitoring_setup",
                "general_operations"
            ],
            "execution_patterns": self.execution_patterns
        }

    # Helper methods for specific operations
    def _design_coordination_strategy(self, message: str) -> Dict[str, Any]:
        """Designs coordination strategy."""
        return {
            "approach": "Centralized coordination with distributed execution",
            "communication": "Regular status updates and checkpoint reviews",
            "escalation": "Defined escalation paths for issues",
            "decision_making": "Clear decision authority and approval processes"
        }

    def _identify_task_dependencies(self, message: str) -> List[str]:
        """Identifies task dependencies."""
        return [
            "System prerequisites verification",
            "Resource availability confirmation",
            "Permission and access validation",
            "Dependent service availability"
        ]

    def _plan_resource_allocation(self, message: str) -> Dict[str, Any]:
        """Plans resource allocation."""
        return {
            "compute_resources": "Allocated based on workload analysis",
            "storage_resources": "Temporary and persistent storage planning",
            "network_resources": "Bandwidth and connectivity requirements",
            "human_resources": "Skilled personnel for monitoring and intervention"
        }

    def _setup_progress_tracking(self, message: str) -> Dict[str, Any]:
        """Sets up progress tracking."""
        return {
            "metrics": ["Completion percentage", "Time elapsed", "Resource utilization"],
            "reporting": "Regular progress reports and milestone updates",
            "alerts": "Automated alerts for deviations and issues",
            "dashboards": "Real-time visibility into operation status"
        }

    def _create_contingency_plans(self, message: str) -> List[str]:
        """Creates contingency plans."""
        return [
            "Resource shortage mitigation strategies",
            "Technical failure recovery procedures",
            "Timeline adjustment protocols",
            "Quality issue resolution processes"
        ]

    def _determine_file_operation_type(self, message: str) -> str:
        """Determines file operation type."""
        message_lower = message.lower()

        if "copy" in message_lower:
            return "file_copy"
        elif "move" in message_lower:
            return "file_move"
        elif "delete" in message_lower:
            return "file_delete"
        elif "backup" in message_lower:
            return "file_backup"
        elif "sync" in message_lower:
            return "file_sync"
        else:
            return "file_general"

    def _implement_file_safety_measures(self, message: str) -> List[str]:
        """Implements file safety measures."""
        return [
            "Create backup copies before operations",
            "Validate file paths and permissions",
            "Implement atomic operations where possible",
            "Use checksums for integrity verification"
        ]

    def _plan_batch_processing(self, message: str) -> Dict[str, Any]:
        """Plans batch processing for file operations."""
        return {
            "batch_size": "Optimal batch size based on system capacity",
            "processing_strategy": "Sequential processing with error handling",
            "progress_tracking": "Batch completion monitoring and reporting",
            "error_handling": "Individual item error handling within batches"
        }

    def _design_error_recovery(self, message: str) -> Dict[str, Any]:
        """Designs error recovery procedures."""
        return {
            "retry_strategy": "Exponential backoff with maximum retry limits",
            "recovery_actions": "Automated recovery for common error types",
            "manual_intervention": "Clear procedures for manual intervention",
            "rollback_procedures": "Safe rollback to previous state"
        }

    def _optimize_file_performance(self, message: str) -> List[str]:
        """Optimizes file operation performance."""
        return [
            "Use efficient file I/O patterns",
            "Implement parallel processing where appropriate",
            "Optimize buffer sizes for operations",
            "Minimize filesystem fragmentation"
        ]

    def _design_execution_strategy(self, message: str) -> Dict[str, Any]:
        """Designs system execution strategy."""
        return {
            "execution_mode": "Controlled execution with monitoring",
            "isolation": "Appropriate isolation for safety",
            "resource_limits": "Resource limits to prevent system impact",
            "logging": "Comprehensive logging for audit and debugging"
        }

    def _plan_command_sequence(self, message: str) -> List[str]:
        """Plans command execution sequence."""
        return [
            "Environment validation commands",
            "Preparation and setup commands",
            "Main execution commands",
            "Validation and verification commands",
            "Cleanup and finalization commands"
        ]

    def _setup_execution_monitoring(self, message: str) -> Dict[str, Any]:
        """Sets up execution monitoring."""
        return {
            "real_time_monitoring": "Live monitoring of execution progress",
            "performance_metrics": "CPU, memory, and I/O monitoring",
            "error_detection": "Automated error detection and alerting",
            "logging": "Detailed execution logging for analysis"
        }

    def _design_rollback_procedures(self, message: str) -> List[str]:
        """Designs rollback procedures."""
        return [
            "Capture system state before execution",
            "Identify rollback trigger conditions",
            "Implement automated rollback procedures",
            "Validate rollback completion"
        ]

    def _plan_success_validation(self, message: str) -> List[str]:
        """Plans success validation procedures."""
        return [
            "Functional validation of expected outcomes",
            "Performance validation against benchmarks",
            "System stability validation",
            "Data integrity validation"
        ]

    def _design_deployment_strategy(self, message: str) -> Dict[str, Any]:
        """Designs deployment strategy."""
        return {
            "approach": "Blue-green deployment with gradual rollout",
            "phases": ["Preparation", "Deployment", "Validation", "Completion"],
            "risk_mitigation": "Comprehensive testing and rollback procedures",
            "communication": "Stakeholder communication and status updates"
        }

    def _create_rollout_plan(self, message: str) -> Dict[str, Any]:
        """Creates rollout plan."""
        return {
            "stages": ["Development", "Staging", "Production"],
            "criteria": "Quality gates and approval criteria for each stage",
            "timeline": "Phased rollout with appropriate intervals",
            "monitoring": "Continuous monitoring during rollout"
        }

    def _setup_validation_checkpoints(self, message: str) -> List[str]:
        """Sets up validation checkpoints."""
        return [
            "Pre-deployment validation",
            "Post-deployment functional validation",
            "Performance validation",
            "Security validation",
            "User acceptance validation"
        ]

    def _design_rollback_strategy(self, message: str) -> Dict[str, Any]:
        """Designs rollback strategy."""
        return {
            "trigger_conditions": "Automated and manual rollback triggers",
            "rollback_procedures": "Step-by-step rollback procedures",
            "data_handling": "Data consistency during rollback",
            "communication": "Stakeholder communication during rollback"
        }

    def _plan_post_deployment_tasks(self, message: str) -> List[str]:
        """Plans post-deployment tasks."""
        return [
            "Monitor system stability and performance",
            "Collect and analyze deployment metrics",
            "Update documentation and procedures",
            "Conduct post-deployment review and lessons learned"
        ]

    def _design_monitoring_strategy(self, message: str) -> Dict[str, Any]:
        """Designs monitoring strategy."""
        return {
            "scope": "Comprehensive monitoring across all system layers",
            "frequency": "Real-time monitoring with appropriate sampling",
            "storage": "Efficient storage and retention of monitoring data",
            "analysis": "Automated analysis and anomaly detection"
        }

    def _configure_alerting_rules(self, message: str) -> List[str]:
        """Configures alerting rules."""
        return [
            "Critical system failures - immediate alerts",
            "Performance degradation - threshold-based alerts",
            "Security incidents - automated security alerts",
            "Capacity planning - trend-based alerts"
        ]

    def _plan_dashboard_setup(self, message: str) -> Dict[str, Any]:
        """Plans dashboard setup."""
        return {
            "executive_dashboard": "High-level system health overview",
            "operational_dashboard": "Detailed operational metrics",
            "technical_dashboard": "Technical performance metrics",
            "custom_dashboards": "Role-specific dashboard views"
        }

    def _configure_data_retention(self, message: str) -> Dict[str, Any]:
        """Configures data retention policies."""
        return {
            "real_time_data": "24-48 hours for immediate analysis",
            "historical_data": "30-90 days for trend analysis",
            "archived_data": "1-2 years for compliance and audit",
            "aggregated_data": "Long-term storage of aggregated metrics"
        }

    def _establish_maintenance_procedures(self, message: str) -> List[str]:
        """Establishes maintenance procedures."""
        return [
            "Regular system health checks",
            "Performance optimization and tuning",
            "Log rotation and cleanup procedures",
            "Monitoring system updates and patches"
        ]

    def _design_operational_approach(self, message: str) -> Dict[str, Any]:
        """Designs operational approach."""
        return {
            "methodology": "Structured approach with clear phases",
            "collaboration": "Cross-functional collaboration and communication",
            "quality_focus": "Quality assurance throughout the process",
            "continuous_improvement": "Feedback loops and process optimization"
        }

    def _plan_quality_assurance(self, message: str) -> List[str]:
        """Plans quality assurance procedures."""
        return [
            "Define quality standards and criteria",
            "Implement quality checkpoints and gates",
            "Conduct regular quality reviews",
            "Establish quality metrics and reporting"
        ]

    def _identify_risk_mitigation(self, message: str) -> List[str]:
        """Identifies risk mitigation strategies."""
        return [
            "Identify potential risks and impact",
            "Develop mitigation strategies for high-risk areas",
            "Implement monitoring for risk indicators",
            "Establish contingency plans for risk scenarios"
        ]

    def _define_documentation_requirements(self, message: str) -> List[str]:
        """Defines documentation requirements."""
        return [
            "Process documentation and procedures",
            "Technical documentation and specifications",
            "User documentation and guides",
            "Audit documentation and compliance records"
        ]
