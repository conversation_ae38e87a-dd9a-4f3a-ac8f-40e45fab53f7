// AGENDATRINTAE3 Middleware - AI Integration V2.0
// GRUPO US VIBECODE SYSTEM V4.0 - Phase 7 AI Integration
// Next.js middleware with shared AI services integration

// import { processRequest } from "@project-core/shared-services/middleware/shared-middleware";
// import { processSecurityRequest } from "@project-core/shared-services/security/security-middleware";
import { NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  try {
    // Temporary simplified middleware until shared services are configured
    const response = NextResponse.next();

    // Basic security headers
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("X-Frame-Options", "DENY");
    response.headers.set("X-XSS-Protection", "1; mode=block");
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

    return response;
  } catch (error) {
    console.error("AGENDATRINTAE3 Middleware error:", error);

    // Fallback to basic security headers
    const response = NextResponse.next();
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("X-Frame-Options", "DENY");
    response.headers.set("X-XSS-Protection", "1; mode=block");
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

    return response;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
