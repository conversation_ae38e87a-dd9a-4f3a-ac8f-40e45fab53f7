'use client';

import React from 'react';
import { StagewiseProvider, useStagewise, useCurrentStage } from '../components/StagewiseProvider';
import { StagewiseConfig } from '../config/stagewise.config';

// Example onboarding configuration
const onboardingConfig: StagewiseConfig = {
  id: 'user-onboarding',
  name: 'User Onboarding',
  version: '1.0.0',
  stages: [
    {
      id: 'welcome',
      name: 'Welcome',
      description: 'Welcome the user to the platform',
      type: 'info'
    },
    {
      id: 'profile',
      name: 'Profile Setup',
      description: 'Set up your profile information',
      type: 'form',
      validation: {
        requiredFields: ['name', 'email'],
        customValidator: (data) => {
          const errors: string[] = [];
          if (data.email && !data.email.includes('@')) {
            errors.push('Invalid email format');
          }
          return errors;
        }
      }
    },
    {
      id: 'preferences',
      name: 'Preferences',
      description: 'Choose your preferences',
      type: 'form'
    },
    {
      id: 'complete',
      name: 'Complete',
      description: 'Onboarding complete!',
      type: 'info'
    }
  ]
};

// Stage component examples
function WelcomeStage() {
  const { navigation } = useStagewise();
  
  return (
    <div className="text-center space-y-4">
      <h2 className="text-2xl font-bold">Welcome to VIBECODE!</h2>
      <p>Let's get you set up in just a few steps.</p>
      <button
        onClick={navigation.goNext}
        className="px-6 py-2 bg-blue-600 text-white rounded-lg"
      >
        Get Started
      </button>
    </div>
  );
}

function ProfileStage() {
  const { navigation, validation } = useStagewise();
  const currentStage = useCurrentStage();
  const [formData, setFormData] = React.useState({ name: '', email: '' });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (currentStage) {
      const result = validation.validateStage(currentStage.id, formData, currentStage);
      
      if (result.isValid) {
        navigation.markStageAsCompleted(currentStage.id);
        navigation.goNext();
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Name</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full px-3 py-2 border rounded-lg"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-1">Email</label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          className="w-full px-3 py-2 border rounded-lg"
          required
        />
      </div>

      {validation.validationState[currentStage?.id || '']?.errors.map((error, i) => (
        <p key={i} className="text-red-500 text-sm">{error}</p>
      ))}

      <div className="flex gap-4">
        <button
          type="button"
          onClick={navigation.goBack}
          className="px-6 py-2 border rounded-lg"
        >
          Back
        </button>
        <button
          type="submit"
          className="px-6 py-2 bg-blue-600 text-white rounded-lg"
        >
          Continue
        </button>
      </div>
    </form>
  );
}

// Main onboarding component
export function OnboardingFlow() {
  return (
    <StagewiseProvider config={onboardingConfig}>
      <OnboardingContent />
    </StagewiseProvider>
  );
}

function OnboardingContent() {
  const currentStage = useCurrentStage();
  
  if (!currentStage) return null;

  return (
    <div className="max-w-2xl mx-auto p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{currentStage.name}</h1>
        <p className="text-gray-600">{currentStage.description}</p>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        {currentStage.id === 'welcome' && <WelcomeStage />}
        {currentStage.id === 'profile' && <ProfileStage />}
        {/* Add other stage components here */}
      </div>
    </div>
  );
}