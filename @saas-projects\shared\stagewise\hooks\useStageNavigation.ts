import { useCallback, useState } from 'react';
import { StageConfig } from '../config/stagewise.config';

interface NavigationState {
  currentStage: string;
  previousStages: string[];
  completedStages: string[];
  canGoBack: boolean;
  canGoForward: boolean;
}

export function useStageNavigation(stages: StageConfig[], initialStage: string = '') {
  const [navigationState, setNavigationState] = useState<NavigationState>({
    currentStage: initialStage || stages[0]?.id || '',
    previousStages: [],
    completedStages: [],
    canGoBack: false,
    canGoForward: false
  });

  const navigateToStage = useCallback((stageId: string) => {
    setNavigationState(prev => {
      const stageIndex = stages.findIndex(s => s.id === stageId);
      if (stageIndex === -1) return prev;

      return {
        ...prev,
        currentStage: stageId,
        previousStages: [...prev.previousStages, prev.currentStage],
        canGoBack: true,
        canGoForward: stageIndex < stages.length - 1
      };
    });
  }, [stages]);

  const goBack = useCallback(() => {
    setNavigationState(prev => {
      if (prev.previousStages.length === 0) return prev;

      const newPreviousStages = [...prev.previousStages];
      const previousStage = newPreviousStages.pop()!;

      return {
        ...prev,
        currentStage: previousStage,
        previousStages: newPreviousStages,
        canGoBack: newPreviousStages.length > 0
      };
    });
  }, []);

  const goNext = useCallback(() => {
    const currentIndex = stages.findIndex(s => s.id === navigationState.currentStage);
    if (currentIndex === -1 || currentIndex >= stages.length - 1) return;

    const nextStage = stages[currentIndex + 1];
    navigateToStage(nextStage.id);
  }, [stages, navigationState.currentStage, navigateToStage]);

  const markStageAsCompleted = useCallback((stageId: string) => {
    setNavigationState(prev => ({
      ...prev,
      completedStages: [...new Set([...prev.completedStages, stageId])]
    }));
  }, []);

  return {
    ...navigationState,
    navigateToStage,
    goBack,
    goNext,
    markStageAsCompleted
  };
}