import networkx as nx
from typing import Dict, Any, List, Optional
import json
from datetime import datetime

class KnowledgeGraphManager:
    """
    Manages a knowledge graph using NetworkX to track patterns, relationships,
    and learning from agent interactions. Simplified version of the original.
    """

    def __init__(self):
        """
        Initializes the Knowledge Graph Manager with an empty directed graph.
        """
        self.graph = nx.DiGraph()
        self.success_patterns: Dict[str, int] = {}
        self.error_patterns: Dict[str, int] = {}

    def record_agent_action(self, agent_name: str, action: Dict[str, Any], result: Dict[str, Any]):
        """
        Records an agent action and its result in the knowledge graph.

        Args:
            agent_name (str): Name of the agent that performed the action
            action (Dict[str, Any]): The action/request details
            result (Dict[str, Any]): The result of the action
        """
        action_id = f"{agent_name}_{datetime.now().timestamp()}"

        # Add nodes for agent, action, and result
        self.graph.add_node(agent_name, type="agent")
        self.graph.add_node(action_id, type="action", **action)

        # Create relationships
        self.graph.add_edge(agent_name, action_id, relationship="performed")

        # Track success/failure patterns
        if result.get("success", True):
            complexity = action.get("complexity", 5)
            domain = action.get("domain", "general")
            pattern_key = f"{agent_name}_{domain}_{complexity}"
            self.success_patterns[pattern_key] = self.success_patterns.get(pattern_key, 0) + 1
        else:
            error_type = result.get("error_type", "unknown")
            pattern_key = f"{agent_name}_{error_type}"
            self.error_patterns[pattern_key] = self.error_patterns.get(pattern_key, 0) + 1

    def get_recommendations(self, current_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Provides recommendations based on historical patterns in the knowledge graph.

        Args:
            current_context (Dict[str, Any]): Current task context

        Returns:
            List[Dict[str, Any]]: List of recommendations
        """
        recommendations = []

        task_complexity = current_context.get("complexity", 5)
        task_domain = current_context.get("domain", "general")

        # Find successful patterns for similar tasks
        for pattern_key, success_count in self.success_patterns.items():
            if task_domain in pattern_key and success_count > 2:
                agent_name = pattern_key.split("_")[0]
                recommendations.append({
                    "type": "agent_suggestion",
                    "agent": agent_name,
                    "confidence": min(success_count / 10.0, 1.0),
                    "reason": f"High success rate for {task_domain} tasks"
                })

        return sorted(recommendations, key=lambda x: x["confidence"], reverse=True)

    def generate_insights(self, topic: Optional[str] = None) -> Dict[str, Any]:
        """
        Generates insights from the knowledge graph.

        Args:
            topic (Optional[str]): Specific topic to focus insights on

        Returns:
            Dict[str, Any]: Generated insights
        """
        insights = {
            "graph_stats": {
                "total_nodes": self.graph.number_of_nodes(),
                "total_edges": self.graph.number_of_edges(),
                "agent_count": len([n for n, d in self.graph.nodes(data=True) if d.get("type") == "agent"])
            },
            "top_success_patterns": sorted(
                self.success_patterns.items(),
                key=lambda x: x[1],
                reverse=True
            )[:5],
            "common_error_patterns": sorted(
                self.error_patterns.items(),
                key=lambda x: x[1],
                reverse=True
            )[:3]
        }

        return insights

    def self_correct(self, error_pattern: str, correction: Dict[str, Any]):
        """
        Implements self-correction by updating patterns based on feedback.

        Args:
            error_pattern (str): The error pattern to correct
            correction (Dict[str, Any]): The correction information
        """
        # Reduce the weight of error patterns when corrections are applied
        if error_pattern in self.error_patterns:
            self.error_patterns[error_pattern] = max(0, self.error_patterns[error_pattern] - 1)

        # Record the successful correction
        correction_key = f"correction_{error_pattern}"
        self.success_patterns[correction_key] = self.success_patterns.get(correction_key, 0) + 1

    def export_graph(self, filepath: str):
        """
        Exports the knowledge graph to a JSON file.

        Args:
            filepath (str): Path to save the graph
        """
        graph_data = nx.node_link_data(self.graph)
        graph_data["success_patterns"] = self.success_patterns
        graph_data["error_patterns"] = self.error_patterns

        with open(filepath, 'w') as f:
            json.dump(graph_data, f, indent=2)

    def import_graph(self, filepath: str):
        """
        Imports a knowledge graph from a JSON file.

        Args:
            filepath (str): Path to load the graph from
        """
        try:
            with open(filepath, 'r') as f:
                graph_data = json.load(f)

            self.graph = nx.node_link_graph(graph_data)
            self.success_patterns = graph_data.get("success_patterns", {})
            self.error_patterns = graph_data.get("error_patterns", {})
        except FileNotFoundError:
            print(f"Knowledge graph file {filepath} not found. Starting with empty graph.")

if __name__ == '__main__':
    # Example Usage
    kg = KnowledgeGraphManager()

    # Simulate some agent actions
    kg.record_agent_action(
        "TechnicalArchitect",
        {"description": "Refactor database", "complexity": 8, "domain": "architecture"},
        {"success": True, "quality_score": 9.2}
    )

    kg.record_agent_action(
        "OperationsCoordinator",
        {"description": "Deploy application", "complexity": 4, "domain": "deployment"},
        {"success": False, "error_type": "permission_denied"}
    )

    print("Knowledge Graph Insights:")
    print(json.dumps(kg.generate_insights(), indent=2))

    print("\nRecommendations for architecture task:")
    recommendations = kg.get_recommendations({"complexity": 7, "domain": "architecture"})
    print(json.dumps(recommendations, indent=2))
