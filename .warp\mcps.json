{"metadata": {"name": "Warp CLI - VIBECODE MCP Configuration", "version": "1.0.0", "description": "MCP configuration optimized for Warp CLI based on VIBECODE standards", "lastUpdated": "2025-07-19T22:30:00.000Z", "environment": "warp-windows-optimized"}, "mcpServers": {"file-operations": {"name": "File Operations", "description": "File operations with automatic tool selection", "enabled": true, "tier": 1, "size_threshold": 200, "auto_verify": true}, "research-protocol": {"name": "Research Protocol", "description": "Multi-source research with mandatory synthesis", "enabled": true, "tier": 1, "sources": ["docs", "web", "alternative"], "synthesis_required": true}, "task-management": {"name": "Task Management", "description": "Automatic task management for complexity ≥3", "enabled": true, "tier": 2, "complexity_threshold": 3, "auto_breakdown": true}, "quality-gates": {"name": "Quality Gates", "description": "Quality enforcement ≥8/10 with auto-refinement", "enabled": true, "tier": 1, "minimum_score": 8, "max_refinements": 3}, "coding-standards": {"name": "Coding Standards", "description": "TypeScript/Next.js 14 standards enforcement", "enabled": true, "tier": 2, "standards": "typescript-nextjs14", "strict_mode": true}, "batch-operations": {"name": "Batch Operations", "description": "API optimization with batch processing", "enabled": true, "tier": 3, "reduction_target": 70, "consolidation": true}}, "workflow": {"enabled": true, "steps": [{"step": 1, "name": "analyze", "description": "Analyze complexity and requirements", "mcps": ["task-management", "quality-gates"]}, {"step": 2, "name": "select", "description": "Select tools based on complexity", "mcps": ["file-operations", "research-protocol"]}, {"step": 3, "name": "execute", "description": "Execute with quality tracking", "mcps": ["coding-standards", "batch-operations"]}, {"step": 4, "name": "reflect", "description": "Evaluate quality ≥8/10", "mcps": ["quality-gates"]}, {"step": 5, "name": "refine", "description": "Improve if quality <8/10", "mcps": ["quality-gates", "coding-standards"]}, {"step": 6, "name": "validate", "description": "Final validation", "mcps": ["quality-gates", "file-operations"]}, {"step": 7, "name": "learn", "description": "Document patterns", "mcps": ["task-management"]}]}, "keywords": {"research": ["pesquisar", "buscar", "encontrar", "documentação", "tutorial", "como fazer", "exemplo", "guia", "biblioteca", "framework", "API", "best practices", "implementação", "configuração", "integração"], "planning": ["planejar", "organizar", "estruturar", "coordenar", "etapas", "fases", "sequência", "workflow", "tare<PERSON>s", "subtarefas", "implementar", "desenvolver"], "complexity": ["arquitetura", "sistema", "integração", "refatoração", "migra<PERSON>", "otimização", "database", "api", "frontend", "backend", "deployment"]}, "rules": {"auto_detect": true, "research_mandatory": true, "quality_enforced": true, "task_management_threshold": 3, "batch_operations_enabled": true}, "performance": {"targets": {"rule_lookup": 100, "mcp_response": 200, "batch_reduction": 70}, "optimization": {"api_calls": "minimize", "consolidation": "maximize", "caching": "enabled"}}}