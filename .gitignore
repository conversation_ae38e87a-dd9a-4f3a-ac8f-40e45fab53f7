# =============================================================================================
# 🚨 CRITICAL SECURITY - ENVIRONMENT VARIABLES & SECRETS
# =============================================================================================
# NEVER COMMIT ANY OF THESE FILES - THEY CONTAIN SENSITIVE DATA

# Environment files - ALL variations
.env
.env.*
*.env
.env.local
.env.development
.env.production
.env.test
.env.staging
env/
.env/
envs/
environments/

# Project-specific environment files
@project-core/.env
@project-core/.env.*
@project-core/env/
@project-core/configs/*.env
**/environment-complete.env
**/environment-security.env

# API Keys and Tokens - CRITICAL PROTECTION
*token*
*key*
*secret*
*credential*
*password*
*.pem
*.p12
*.pfx
*.key
*.cert
*.crt

# Configuration files with potential secrets
config.json
config.local.json
secrets.json
credentials.json
auth.json
database.json
firebase.json
supabase.json
stripe.json

# AWS credentials
.aws/
aws.json
credentials.csv

# Google Cloud credentials
gcloud/
service-account*.json
client_secret*.json

# Azure credentials
.azure/
azure.json

# Database files
*.db
*.sqlite
*.sqlite3
*.sql

# Private keys and certificates
ssl/
certs/
private/
*.key
*.pem
*.p12
id_rsa*
id_dsa*
id_ecdsa*
id_ed25519*

# Sensitive documentation
**/SECRETS.md
**/PASSWORDS.md
**/API_KEYS.md
**/CREDENTIALS.md

# Node.js
node_modules/
**/node_modules/
*/node_modules/
# NO EXCEPTIONS for node_modules - everything must be ignored
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist/

# Cache directories
.cache/
.parcel-cache/
.nyc_output/
coverage/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE specific files
.vscode/
.idea/
*.swp
*.swo
*~

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.venv/
venv/

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Project specific
@project-core/.env
@project-core/env/
@project-core/configs/*.env
backup_*/
# =============================================================================================
# 🚨 EXTRA SECURITY PATTERNS - CATCH ALL
# =============================================================================================

# Backup files that might contain secrets
*.backup
*.bak
*.old
*.orig
*.save

# Archive files that might contain secrets
*.zip
*.tar
*.tar.gz
*.7z
*.rar

# Common secret file patterns
.secrets/
secrets/
private/
confidential/
sensitive/

# Common API key file patterns
apikeys/
api-keys/
api_keys/
keys/

# Docker secrets
docker-compose.override.yml
.dockerignore

# Terraform secrets
*.tfvars
terraform.tfstate*
.terraform/

# Ansible secrets
**/vault.yml
**/vault.yaml
ansible-vault*

# Kubernetes secrets
**/secrets.yaml
**/secrets.yml

# Never allow these specific files
.netrc
.npmrc
.pypirc
.gemrc
.gitconfig
.git-credentials

# =============================================================================================
# 🚀 VIBECODE V1.0 - GITHUB FILE SIZE COMPLIANCE
# =============================================================================================
# Large files that are split into chunks for GitHub compliance
# Original files are excluded, but chunks and metadata are included

# Exclude original large files (they will be reassembled from chunks)
**/next-swc.win32-x64-msvc.node

# Include split chunks and metadata (these are under 100MB)
!*.chunk*
!*.split_metadata.json

# Git backup files from cleanup operations
git_backup_*.bundle

# =============================================================================================
# 🛡️ FINAL SAFETY NET
# =============================================================================================
# If you see ANY file with sensitive data in git status, add it here immediately!
# Better safe than sorry - when in doubt, exclude it from version control
