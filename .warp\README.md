# 🚀 WARP CLI - VIBECODE CONFIGURATION V1.0

**Status**: ✅ INSTALADO E ATIVO
**Version**: 1.0.0 | **Environment**: Windows + PowerShell + Warp CLI
**Based on**: VIBECODE V1.0 Unified Configuration

## 📋 CONFIGURAÇÃO INSTALADA

Esta configuração aplica todos os padrões, regras e standards do sistema VIBECODE V1.0 para uso no Warp CLI, garantindo consistência e qualidade em todas as interações de desenvolvimento.

### **🎯 Princípios Fundamentais Aplicados**
- **"Aprimore, Não Prolifere"** (≥85% reuso)
- **Quality Threshold** ≥8/10 (obrigatório)
- **Confidence Minimum** ≥90%
- **Context First, Quality Always**

## 📂 ESTRUTURA DE ARQUIVOS

```
.warp/
├── README.md                 # Esta documentação
├── config.json              # Configuração principal
├── master-rules.md           # Regras mestras do sistema  
├── coding-standards.md       # Padrões de codificação
└── project-config.md         # Configuração do projeto
```

## 🔄 WORKFLOW OBRIGATÓRIO ATIVO

### **7 Etapas Automatizadas**

1. **ANALYZE** → Avaliar complexidade (1-10) + task management needs
2. **SELECT** → Escolher ferramentas baseado na complexidade
3. **EXECUTE** → Implementar com tracking de qualidade
4. **REFLECT** → Avaliar qualidade ≥8/10
5. **REFINE** → Melhorar se necessário (máx 3 iterações)
6. **VALIDATE** → Confirmar qualidade final
7. **LEARN** → Documentar padrões para reuso

### **Task Management Automático**
- **Complexity ≥3**: Task management ativado automaticamente
- **Keywords planning**: Detecção e ativação automática
- **Multi-step indicators**: Breakdown automático

## 🛠️ TOOL SELECTION AUTOMÁTICA

### **Por Complexidade**
```json
{
  "1-3": "Operações básicas",
  "4-6": "Task management + ferramentas intermediárias", 
  "7-10": "Ferramentas avançadas + raciocínio sequencial"
}
```

### **File Operations**
```json
{
  "≤200_lines": "Ferramentas rápidas",
  ">200_lines": "Editores robustos",
  "always_verify": "Verificar após write operations"
}
```

### **Research Protocol OBRIGATÓRIO**
Para qualquer keyword de pesquisa:
1. **Documentação técnica** - Sempre primeiro
2. **Pesquisa web geral** - Sempre segundo  
3. **Fontes alternativas** - Sempre terceiro
4. **Síntese obrigatória** - Consolidação de todas as fontes

## 💻 CODING STANDARDS ATIVOS

### **TypeScript/Next.js 14**
- ✅ **Function keyword** para componentes
- ✅ **Interfaces over types** 
- ✅ **Early returns + guard clauses**
- ✅ **Server Components** por padrão
- ✅ **RORO pattern** (Receive Object, Return Object)
- ✅ **TypeScript strict mode**

### **Stack Tecnológica**
- **Frontend**: Next.js 14, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase, Prisma ORM, tRPC
- **Tools**: Warp CLI, Cursor IDE, Vercel
- **State**: Zustand + React Query
- **Forms**: React Hook Form + Zod

## 🎯 QUALITY GATES ATIVOS

### **Obrigatório para Toda Task**
- [ ] Quality score ≥8/10
- [ ] 100% dos requisitos atendidos
- [ ] Error handling implementado
- [ ] TypeScript strict mode
- [ ] Testes ≥80% coverage
- [ ] Accessibility compliance
- [ ] Performance targets met

### **Performance Targets**
- **LCP**: <2.5s
- **FID**: <100ms  
- **CLS**: <0.1
- **Bundle**: <200KB gzipped
- **Rule lookup**: <100ms

## 🔍 KEYWORDS DE DETECÇÃO AUTOMÁTICA

### **Research Keywords**
```
pesquisar, buscar, encontrar, documentação, tutorial, como fazer,
exemplo, guia, biblioteca, framework, API, best practices,
implementação, configuração, integração
```

### **Planning Keywords** 
```
planejar, organizar, estruturar, coordenar, etapas, fases,
sequência, workflow, tarefas, subtarefas, implementar, desenvolver
```

### **Complexity Indicators**
```
arquitetura, sistema, integração, refatoração, migração,
otimização, database, api, frontend, backend, deployment
```

## 📊 BATCH OPERATIONS ATIVAS

### **API Optimization**
- **Target**: ≥70% redução em chamadas de API
- **Consolidar** operações similares
- **Scripts batch** para operações complexas
- **Uma chamada** em vez de múltiplas sequenciais

## 🔒 SECURITY & COMPLIANCE

### **Security Standards Aplicados**
- ✅ Input validation (Zod)
- ✅ Sanitização de dados
- ✅ Authentication middleware
- ✅ Security headers
- ✅ Environment variables protection
- ✅ RLS no Supabase

## 📱 DESIGN PRINCIPLES ATIVOS

- ✅ **Mobile-first** design
- ✅ **Accessibility** (ARIA, keyboard navigation)
- ✅ **SEO** optimization  
- ✅ **Dark mode** support
- ✅ **Responsive** breakpoints
- ✅ **Performance** optimization

## 🎨 NAMING CONVENTIONS

```typescript
// ✅ ATIVOS: Padrões de nomenclatura
- Componentes: PascalCase
- Arquivos: PascalCase.tsx  
- Hooks: camelCase (use prefix)
- Utilitários: camelCase
- Constantes: UPPER_SNAKE_CASE
- Environment: NEXT_PUBLIC_ (client-side)
```

## 🚀 COMO USAR

### **Automatic Activation**
Todas as regras são **automaticamente aplicadas** em todas as interações no Warp CLI. Não é necessário configuração manual.

### **Quality Enforcement**
- Toda resposta deve atingir ≥8/10 de qualidade
- Refinamento automático se qualidade <8/10
- Workflow de 7 etapas sempre aplicado

### **Task Management**  
- Ativação automática para complexity ≥3
- Breakdown automático de tasks complexas
- Progress tracking integrado

### **Research Protocol**
- Detecção automática de keywords de pesquisa
- Sequência obrigatória: docs → web → alternativo → síntese
- Consolidação de múltiplas fontes

## ✅ STATUS DE VALIDAÇÃO

**🎯 CONFIGURAÇÃO COMPLETA**
- [x] Master rules aplicadas
- [x] Coding standards ativos
- [x] Project configuration definida
- [x] Quality gates configurados
- [x] Workflow de 7 etapas ativo
- [x] Task management automático
- [x] Research protocol obrigatório
- [x] Performance targets definidos
- [x] Security standards aplicados

## 🔧 TROUBLESHOOTING

### **Se Quality Score <8/10**
- Sistema aplica refinement automático (máx 3 iterações)
- Identifica pontos de melhoria específicos
- Re-executa até atingir ≥8/10

### **Se Task Complexity ≥3**
- Task management ativa automaticamente
- Breakdown automático de subtasks
- Progress tracking habilitado

### **Se Keywords de Research Detectadas**
- Protocolo obrigatório: 3 fontes sequenciais
- Síntese automática de todos os resultados
- Documentação completa das fontes

---

## 📈 MÉTRICAS DE SUCESSO

**🎯 TARGETS ATIVOS**
- Quality Score: ≥8/10 (100% compliance)
- Performance: <100ms rule lookup
- Coverage: ≥80% tests
- Reuse: ≥85% code reuse
- Security: 100% compliance

**🚀 OTIMIZAÇÕES**
- 70% redução em API calls (batch operations)
- 50% melhoria em performance (optimization)  
- 90% consistência (unified standards)
- 95% quality achievement rate

---

**LEMBRE-SE**: 
- Todas as regras são **automaticamente aplicadas**
- Quality ≥8/10 é **obrigatória** em toda interação
- "Aprimore, Não Prolifere" - sempre reutilizar padrões
- Context First, Quality Always, Performance Matters

**Status**: ✅ **SISTEMA ATIVO** - Configuração completa do VIBECODE V1.0 para Warp CLI
**Environment**: GRUPO US Development - E:/VIBECODE
