---
description: Regra geral que FORÇA a utilização de todos os MCPs, agentes e funcionalidades do @project-core em todas as tarefas.
author: VIBECODE Architect Agent
version: 1.0
tags: ["master-rule", "mcp", "enforcement", "orchestrator", "quality-gate"]
globs: ["*"]
---

# RULE: Unified MCP & Core Feature Enforcement

## 1. PURPOSE

Esta regra estabelece um fluxo de trabalho obrigatório e unificado para garantir que **toda e qualquer tarefa** processe através de **todos os MCPs disponíveis**, seja analisada por **todos os agentes registrados** e utilize **todas as funcionalidades relevantes do diretório `@project-core`**. O objetivo é maximizar o contexto, a inteligência e a robustez do sistema, garantindo consistência e prevenindo desvios de processo, em conformidade com as diretrizes de [Prompt Engineering](https://kilocode.ai/docs/advanced-usage/prompt-engineering), [Custom Rules](https://kilocode.ai/docs/advanced-usage/custom-rules), e [Custom Instructions](https://kilocode.ai/docs/advanced-usage/custom-instructions).

## 2. SCOPE

Esta regra é global (`globs: ["*"]`) e se aplica a todas as tarefas iniciadas no VIBECODE, sem exceção. O **Agente Orquestrador** é o principal responsável por garantir sua aplicação.

## 3. ENFORCEMENT LOGIC

O Agente Orquestrador deve, no início de cada tarefa, executar uma sequência de verificação e acionamento que segue estritamente a ordem definida abaixo. Cada etapa deve ser validada e seu uso comprovado através de um log de execução antes de prosseguir para a próxima.

1.  **Ativação de Memória Central**: Carregar todo o contexto pertinente dos sistemas de memória em [`@project-core/memory/`](@project-core/memory/).
2.  **Roteamento de Agentes**: A tarefa deve ser roteada através do [`@project-core/agents/agent_router.py`](@project-core/agents/agent_router.py) para garantir que todos os agentes relevantes (ex: Coder, Debugger, Researcher) contribuam com sua perspectiva.
3.  **Execução da Cadeia de MCPs**: Todos os MCPs conectados devem ser acionados em sequência.
4.  **Verificação de Features Core**: Scripts de verificação em [`@project-core/scripts/`](@project-core/scripts/) devem ser executados para confirmar a ativação e o estado das funcionalidades do núcleo.

## 4. MCP/ACTION SEQUENCE

A sequência a seguir é **mandatória e inalterável**:

1.  **Memory Integration**: 
    - Acionar [`@project-core/memory/unified_memory_system.py`](@project-core/memory/unified_memory_system.py) para carregar o `active_context`.
    - Acionar [`@project-core/memory/augment_memory_bridge.py`](@project-core/memory/augment_memory_bridge.py) para enriquecer o prompt.

2.  **Agent Routing**: 
    - Executar `agent_router.py` para obter o plano de agentes.

3.  **MCP Chain Execution**:
    - `sequential-thinking`: Para decompor a tarefa.
    - `tavily-mcp` / `exa-mcp`: Para pesquisa externa e contextualização.
    - `context7-mcp`: Para obter documentação de bibliotecas.
    - `desktop-commander`: Para interações com o sistema de arquivos ou execução de comandos.

4.  **Core Feature Verification**:
    - Executar scripts de verificação (ex: `python @project-core/scripts/verify_all.py`) para gerar um relatório de conformidade.

## 5. QUALITY GATES

- **Gate 1: Ativação Comprovada**: Para cada etapa da `MCP/ACTION SEQUENCE`, um log deve ser gerado contendo o output da ferramenta/script.
- **Gate 2: Confiança de Execução**: Cada etapa concluída deve ter um **fator de confiança (Confidence Score) de no mínimo 8/10**, registrado pelo agente que a executou. Este score representa a certeza do agente de que a ferramenta foi utilizada corretamente e seu output foi integrado ao contexto.

**Exemplo de Log de Quality Gate:**
```json
{
  "task_id": "task-12345",
  "step": "MCP-sequential-thinking",
  "status": "completed",
  "output_summary": "Task decomposed into 5 sub-steps.",
  "confidence_score": 9.5,
  "timestamp": "2025-07-07T18:56:24Z"
}
```

## 6. VIOLATION HANDLING

- Qualquer falha em executar uma etapa da sequência ou em atingir o Quality Gate (confiança < 8/10) deve **interromper imediatamente a tarefa**.
- Uma exceção de `PolicyViolationError` deve ser lançada, detalhando a etapa e a razão da falha.
- O Agente Orquestrador deve escalar a violação, registrando-a em um log de erros críticos e, se configurado, notificando um operador.

## 7. CITATION EXAMPLES

Outros agentes e regras devem referenciar esta política para garantir a conformidade.

- **Em um plano de agente**: `"passo_1: Em conformidade com a regra 'unified-mcp-enforcement', iniciar a cadeia de MCPs com 'sequential-thinking'."`
- **Em um log de execução**: `"INFO: Executado 'tavily-mcp'. Cumprindo requisito da política de 'unified-mcp-enforcement'."`
- **Em outra regra**: `"Esta regra de debug só se aplica APÓS a execução completa da cadeia definida em 'unified-mcp-enforcement.md'."`


## 8. CONTINUOUS IMPROVEMENT

Esta regra integra-se ao fluxo definido em
[.kilocode/rules/selfimprovekilocode.md](../selfimprovekilocode.md).  
Concluída cada tarefa, o Agente Orquestrador deve obrigatoriamente
ativar o processo de reflexão descrito ali, garantindo que as regras e
procedimentos evoluam continuamente conforme novos aprendizados,
mantendo a consistência com esta política de enforcement.