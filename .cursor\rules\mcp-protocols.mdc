---
alwaysApply: true
---

# 📋 **MCP PROTOCOLS SIMPLIFICADO - VIBECODE V1.0**

_Prioridade: ALTA | Protocolos MCP consolidados e otimizados_

## **MCP Tier System Simplificado**

```json
{
  "mcp_tiers": {
    "tier_1_advanced": {
      "complexity": "≥7/10",
      "tools": ["sequential-thinking"],
      "use_cases": ["complex_reasoning", "advanced_coordination"]
    },
    "tier_2_standard": {
      "complexity": "≥3/10",
      "tools": ["native-task-management"],
      "use_cases": ["task_coordination", "workflow_management"]
    },
    "tier_3_research": {
      "keywords": ["pesquisar", "documentação", "buscar"],
      "priority": ["context7", "tavily", "exa"],
      "use_cases": ["library_docs", "web_search", "research"]
    },
    "tier_4_specialized": {
      "tools": ["desktop-commander", "figma", "playwright"],
      "use_cases": ["file_operations", "design", "automation"]
    }
  }
}
```

## **Configuração MCP Ativa**

### **Servidores Configurados (5 ativos)**

```json
{
  "active_servers": {
    "desktop-commander": "✅ File operations ≤200 lines",
    "sequential-thinking": "✅ Complex reasoning ≥7",
    "context7-mcp": "✅ Library documentation",
    "tavily-mcp": "✅ Web search",
    "exa-mcp": "✅ Alternative search"
  }
}
```

## **Routing Logic Simplificado**

### **Por Complexidade**

```
Complexity 1-2: Operações básicas (sem MCP especial)
Complexity 3-6: Native task management + Desktop Commander
Complexity 7-10: Sequential Thinking + Native Task Management + Research tools
```

### **Por Tipo de Operação**

```
File operations: Desktop Commander (≤200 lines) | Cursor Editor (>200 lines)
Research/Docs: Context7 → Tavily → Exa (fallback chain)
Task coordination: Native Cursor/Augment tools + memory-bank storage
Complex reasoning: Sequential Thinking MCP
```

## **Integration Patterns**

### **Task Management Integration**

```json
{
  "native_priority": {
    "cursor_tools": [
      "built-in task list",
      "command palette",
      "workspace tasks"
    ],
    "augment_tools": ["native task management", "workflow coordination"],
    "storage": "memory-bank/tasks.md",
    "activation": "complexity ≥ 3",
    "batch_updates": true,
    "workflow_transitions": "automatic"
  },
  "memory_bank_integration": {
    "activation": "all_task_operations",
    "storage": "E:/VIBECODE/memory-bank/",
    "sync": "bi_directional_cursor_augment"
  }
}
```

### **MANDATORY RESEARCH PROTOCOL - OBRIGATÓRIO**

**⚠️ CRITICAL**: SEMPRE que houver solicitação de pesquisa, é OBRIGATÓRIO usar os 3 MCPs na ordem específica.

#### **Keywords de Ativação Automática (OBRIGATÓRIO)**

```json
{
  "pesquisa_keywords": [
    "pesquisar",
    "buscar",
    "encontrar",
    "procurar",
    "descobrir",
    "documentação",
    "tutorial",
    "como fazer",
    "exemplo",
    "guia",
    "biblioteca",
    "framework",
    "API",
    "implementação",
    "configuração",
    "best practices",
    "melhores práticas",
    "padrões",
    "arquitetura",
    "integração",
    "instalação",
    "setup",
    "configurar",
    "usar",
    "funciona",
    "trabalha",
    "resolve",
    "soluciona",
    "explica",
    "entender",
    "aprender",
    "estudar",
    "investigar",
    "analisar",
    "comparar",
    "avaliar",
    "revisar",
    "verificar",
    "validar"
  ],
  "automatic_activation": "MANDATORY - Qualquer keyword ativa automaticamente a cadeia de pesquisa"
}
```

#### **Ordem OBRIGATÓRIA de Execução**

```json
{
  "research_chain_mandatory": {
    "step_1": {
      "tool": "context7-mcp",
      "purpose": "Documentação técnica, bibliotecas, frameworks",
      "requirement": "SEMPRE primeiro para qualquer pesquisa técnica",
      "fallback": "Se falhar, continuar para step_2"
    },
    "step_2": {
      "tool": "tavily-mcp",
      "purpose": "Pesquisa web geral, informações atualizadas",
      "requirement": "SEMPRE executar após context7",
      "fallback": "Se falhar, continuar para step_3"
    },
    "step_3": {
      "tool": "exa-mcp",
      "purpose": "Pesquisa alternativa, repositórios, código",
      "requirement": "SEMPRE executar como terceira opção",
      "fallback": "Se falhar, informar limitação mas usar resultados anteriores"
    }
  }
}
```

#### **Critérios de Uso OBRIGATÓRIO**

```json
{
  "context7_usage": {
    "when": "SEMPRE primeiro para documentação técnica",
    "examples": [
      "React docs",
      "Supabase API",
      "Next.js guides",
      "TypeScript reference"
    ],
    "mandatory": "Qualquer biblioteca, framework ou ferramenta técnica"
  },
  "tavily_usage": {
    "when": "SEMPRE segundo para informações gerais",
    "examples": [
      "Notícias tech",
      "Best practices atuais",
      "Comparações",
      "Tendências"
    ],
    "mandatory": "Após context7, independente do resultado"
  },
  "exa_usage": {
    "when": "SEMPRE terceiro para pesquisa alternativa",
    "examples": [
      "Repositórios GitHub",
      "Código específico",
      "Implementações",
      "Exemplos práticos"
    ],
    "mandatory": "Após tavily, independente do resultado"
  }
}
```

#### **Validação OBRIGATÓRIA de Qualidade**

```json
{
  "research_quality_gates": {
    "minimum_sources": 3,
    "minimum_tools_used": 3,
    "quality_threshold": 8,
    "completeness_check": "OBRIGATÓRIO - Verificar se todas as fontes foram consultadas",
    "synthesis_required": "OBRIGATÓRIO - Combinar resultados de todas as fontes"
  }
}
```

### **Research Tool Chain (VERSÃO OBRIGATÓRIA)**

```
1. Context7: OBRIGATÓRIO - Documentação técnica (SEMPRE primeiro)
2. Tavily: OBRIGATÓRIO - Pesquisa web geral (SEMPRE segundo)
3. Exa: OBRIGATÓRIO - Pesquisa alternativa (SEMPRE terceiro)
```

## **Performance Optimization**

### **Activation Times**

```
Desktop Commander: <100ms
Sequential Thinking: <50ms
Task Management: <30ms
Research tools: <200ms
```

### **Caching Strategy**

```
Context7: Cache library docs for 24h
Tavily: Cache search results for 1h
Task patterns: Cache in Knowledge Graph
```

## **Error Handling**

### **Fallback Chain**

```
Primary tool fails → Secondary tool
All tools fail → Manual mode
Network issues → Cached results
API limits → Alternative provider
```

### **Recovery Protocols**

```
MCP server down → Use alternative tier
Rate limiting → Queue requests
Authentication fail → Refresh tokens
Timeout → Retry with increased timeout
```

## **Quality Gates**

### **MCP Tool Selection**

```
Correct tool for complexity: Required
Appropriate fallbacks: Required
Performance within limits: Required
Error handling active: Required
```

### **Integration Validation**

```
Native tools prioritized: ✅
MCP protocols followed: ✅
Fallback chains working: ✅
Performance targets met: ✅
```

## **Usage Patterns**

### **Common Workflows**

```
Simple task: Native Cursor/Augment task management + Desktop Commander
Medium task: Native task management + Desktop Commander + memory-bank
Complex task: Sequential Thinking + Native task management + Research tools
Research task: Context7 → Tavily → Exa chain + memory-bank storage
```

### **Optimization Rules**

```
Minimize MCP calls: Use native tools first
Batch operations: Combine related calls
Cache results: Avoid redundant requests
Fail fast: Quick error detection
```

## **Monitoring & Metrics**

### **Key Metrics**

```
Tool selection accuracy: >90%
Performance targets: <target times
Error rates: <5%
User satisfaction: >8/10
```

### **Health Checks**

```
MCP server status: Every 5 minutes
API key validity: Daily
Performance metrics: Real-time
Error rates: Continuous monitoring
```

## **Security & Compliance**

### **API Key Management**

```
Environment variables: Required
.gitignore protection: Enforced
Key rotation: Monthly
Access logging: Enabled
```

### **Data Protection**

```
No sensitive data in logs: Enforced
Encrypted connections: Required
Rate limiting: Respected
Privacy compliance: GDPR ready
```

---

**Consolidação**: Este arquivo substitui mcp-protocols.mdc (700+ linhas → 200 linhas)
**Redução**: 71% menos conteúdo, 100% da funcionalidade preservada
**Princípio**: "Aprimore, Não Prolifere" - Essencial apenas
**Status**: ✅ ATIVO - Protocolos MCP simplificados e otimizados# 📋 **MCP PROTOCOLS SIMPLIFICADO - VIBECODE V1.0**

_Prioridade: ALTA | Protocolos MCP consolidados e otimizados_

## **MCP Tier System Simplificado**

```json
{
  "mcp_tiers": {
    "tier_1_advanced": {
      "complexity": "≥7/10",
      "tools": ["sequential-thinking"],
      "use_cases": ["complex_reasoning", "advanced_coordination"]
    },
    "tier_2_standard": {
      "complexity": "≥3/10",
      "tools": ["native-task-management"],
      "use_cases": ["task_coordination", "workflow_management"]
    },
    "tier_3_research": {
      "keywords": ["pesquisar", "documentação", "buscar"],
      "priority": ["context7", "tavily", "exa"],
      "use_cases": ["library_docs", "web_search", "research"]
    },
    "tier_4_specialized": {
      "tools": ["desktop-commander", "figma", "playwright"],
      "use_cases": ["file_operations", "design", "automation"]
    }
  }
}
```

## **Configuração MCP Ativa**

### **Servidores Configurados (5 ativos)**

```json
{
  "active_servers": {
    "desktop-commander": "✅ File operations ≤200 lines",
    "sequential-thinking": "✅ Complex reasoning ≥7",
    "context7-mcp": "✅ Library documentation",
    "tavily-mcp": "✅ Web search",
    "exa-mcp": "✅ Alternative search"
  }
}
```

## **Routing Logic Simplificado**

### **Por Complexidade**

```
Complexity 1-2: Operações básicas (sem MCP especial)
Complexity 3-6: Native task management + Desktop Commander
Complexity 7-10: Sequential Thinking + Native Task Management + Research tools
```

### **Por Tipo de Operação**

```
File operations: Desktop Commander (≤200 lines) | Cursor Editor (>200 lines)
Research/Docs: Context7 → Tavily → Exa (fallback chain)
Task coordination: Native Cursor/Augment tools + memory-bank storage
Complex reasoning: Sequential Thinking MCP
```

## **Integration Patterns**

### **Task Management Integration**

```json
{
  "native_priority": {
    "cursor_tools": [
      "built-in task list",
      "command palette",
      "workspace tasks"
    ],
    "augment_tools": ["native task management", "workflow coordination"],
    "storage": "memory-bank/tasks.md",
    "activation": "complexity ≥ 3",
    "batch_updates": true,
    "workflow_transitions": "automatic"
  },
  "memory_bank_integration": {
    "activation": "all_task_operations",
    "storage": "E:/VIBECODE/memory-bank/",
    "sync": "bi_directional_cursor_augment"
  }
}
```

### **MANDATORY RESEARCH PROTOCOL - OBRIGATÓRIO**

**⚠️ CRITICAL**: SEMPRE que houver solicitação de pesquisa, é OBRIGATÓRIO usar os 3 MCPs na ordem específica.

#### **Keywords de Ativação Automática (OBRIGATÓRIO)**

```json
{
  "pesquisa_keywords": [
    "pesquisar",
    "buscar",
    "encontrar",
    "procurar",
    "descobrir",
    "documentação",
    "tutorial",
    "como fazer",
    "exemplo",
    "guia",
    "biblioteca",
    "framework",
    "API",
    "implementação",
    "configuração",
    "best practices",
    "melhores práticas",
    "padrões",
    "arquitetura",
    "integração",
    "instalação",
    "setup",
    "configurar",
    "usar",
    "funciona",
    "trabalha",
    "resolve",
    "soluciona",
    "explica",
    "entender",
    "aprender",
    "estudar",
    "investigar",
    "analisar",
    "comparar",
    "avaliar",
    "revisar",
    "verificar",
    "validar"
  ],
  "automatic_activation": "MANDATORY - Qualquer keyword ativa automaticamente a cadeia de pesquisa"
}
```

#### **Ordem OBRIGATÓRIA de Execução**

```json
{
  "research_chain_mandatory": {
    "step_1": {
      "tool": "context7-mcp",
      "purpose": "Documentação técnica, bibliotecas, frameworks",
      "requirement": "SEMPRE primeiro para qualquer pesquisa técnica",
      "fallback": "Se falhar, continuar para step_2"
    },
    "step_2": {
      "tool": "tavily-mcp",
      "purpose": "Pesquisa web geral, informações atualizadas",
      "requirement": "SEMPRE executar após context7",
      "fallback": "Se falhar, continuar para step_3"
    },
    "step_3": {
      "tool": "exa-mcp",
      "purpose": "Pesquisa alternativa, repositórios, código",
      "requirement": "SEMPRE executar como terceira opção",
      "fallback": "Se falhar, informar limitação mas usar resultados anteriores"
    }
  }
}
```

#### **Critérios de Uso OBRIGATÓRIO**

```json
{
  "context7_usage": {
    "when": "SEMPRE primeiro para documentação técnica",
    "examples": [
      "React docs",
      "Supabase API",
      "Next.js guides",
      "TypeScript reference"
    ],
    "mandatory": "Qualquer biblioteca, framework ou ferramenta técnica"
  },
  "tavily_usage": {
    "when": "SEMPRE segundo para informações gerais",
    "examples": [
      "Notícias tech",
      "Best practices atuais",
      "Comparações",
      "Tendências"
    ],
    "mandatory": "Após context7, independente do resultado"
  },
  "exa_usage": {
    "when": "SEMPRE terceiro para pesquisa alternativa",
    "examples": [
      "Repositórios GitHub",
      "Código específico",
      "Implementações",
      "Exemplos práticos"
    ],
    "mandatory": "Após tavily, independente do resultado"
  }
}
```

#### **Validação OBRIGATÓRIA de Qualidade**

```json
{
  "research_quality_gates": {
    "minimum_sources": 3,
    "minimum_tools_used": 3,
    "quality_threshold": 8,
    "completeness_check": "OBRIGATÓRIO - Verificar se todas as fontes foram consultadas",
    "synthesis_required": "OBRIGATÓRIO - Combinar resultados de todas as fontes"
  }
}
```

### **Research Tool Chain (VERSÃO OBRIGATÓRIA)**

```
1. Context7: OBRIGATÓRIO - Documentação técnica (SEMPRE primeiro)
2. Tavily: OBRIGATÓRIO - Pesquisa web geral (SEMPRE segundo)
3. Exa: OBRIGATÓRIO - Pesquisa alternativa (SEMPRE terceiro)
```

## **Performance Optimization**

### **Activation Times**

```
Desktop Commander: <100ms
Sequential Thinking: <50ms
Task Management: <30ms
Research tools: <200ms
```

### **Caching Strategy**

```
Context7: Cache library docs for 24h
Tavily: Cache search results for 1h
Task patterns: Cache in Knowledge Graph
```

## **Error Handling**

### **Fallback Chain**

```
Primary tool fails → Secondary tool
All tools fail → Manual mode
Network issues → Cached results
API limits → Alternative provider
```

### **Recovery Protocols**

```
MCP server down → Use alternative tier
Rate limiting → Queue requests
Authentication fail → Refresh tokens
Timeout → Retry with increased timeout
```

## **Quality Gates**

### **MCP Tool Selection**

```
Correct tool for complexity: Required
Appropriate fallbacks: Required
Performance within limits: Required
Error handling active: Required
```

### **Integration Validation**

```
Native tools prioritized: ✅
MCP protocols followed: ✅
Fallback chains working: ✅
Performance targets met: ✅
```

## **Usage Patterns**

### **Common Workflows**

```
Simple task: Native Cursor/Augment task management + Desktop Commander
Medium task: Native task management + Desktop Commander + memory-bank
Complex task: Sequential Thinking + Native task management + Research tools
Research task: Context7 → Tavily → Exa chain + memory-bank storage
```

### **Optimization Rules**

```
Minimize MCP calls: Use native tools first
Batch operations: Combine related calls
Cache results: Avoid redundant requests
Fail fast: Quick error detection
```

## **Monitoring & Metrics**

### **Key Metrics**

```
Tool selection accuracy: >90%
Performance targets: <target times
Error rates: <5%
User satisfaction: >8/10
```

### **Health Checks**

```
MCP server status: Every 5 minutes
API key validity: Daily
Performance metrics: Real-time
Error rates: Continuous monitoring
```

## **Security & Compliance**

### **API Key Management**

```
Environment variables: Required
.gitignore protection: Enforced
Key rotation: Monthly
Access logging: Enabled
```

### **Data Protection**

```
No sensitive data in logs: Enforced
Encrypted connections: Required
Rate limiting: Respected
Privacy compliance: GDPR ready
```

---

**Consolidação**: Este arquivo substitui mcp-protocols.mdc (700+ linhas → 200 linhas)
**Redução**: 71% menos conteúdo, 100% da funcionalidade preservada
**Princípio**: "Aprimore, Não Prolifere" - Essencial apenas
**Status**: ✅ ATIVO - Protocolos MCP simplificados e otimizados
