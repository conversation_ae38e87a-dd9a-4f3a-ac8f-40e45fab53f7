---
description: Memory system and continuous learning protocols
globs: **/*
alwaysApply: true
---

# 📋 **REGRA 7: MEMORY & LEARNING SYSTEM**

_Prioridade: MÉDIA | Sistema de memória e aprendizado contínuo_

## **Knowledge Graph Manager**

```json
{
  "kg_system": {
    "core_component": "memory-bank/python/knowledge_graph_manager.py",
    "authority_level": "Central cognitive system",
    "consultation_requirement": "Mandatory Phase 0.5",
    "learning_mechanism": "Continuous pattern capture and storage"
  }
}
```

## **Memory Integration**

```json
{
  "memory_protocols": {
    "pattern_storage": "Successful patterns stored in KG",
    "failure_analysis": "Failed approaches documented for avoidance",
    "context_preservation": "Complete handoff data maintained",
    "bidirectional_learning": "Cross-platform knowledge sharing"
  }
}
```

- **OBRIGATÓRIO**: Atualizar @self.mdc com erros e correções encontradas
- **OBRIGATÓRIO**: Atualizar @project.mdc com novas preferências descobertas
- **CONDICIONAL**: Atualizar @database-schema.mdc se houve mudanças no schema
- **CONDICIONAL**: Atualizar @apis.mdc se APIs foram criadas/modificadas

## WORKFLOW OBRIGATÓRIO DE MCPs

### Sequência Mandatória para Execução de Tarefas:

1. **0.5 CAG** - Cache memory of important things
2. **plan_task** - Planejar a tarefa usando MCP task manager
3. **analyze_task** - Analisar requisitos e complexidade
4. **research_mode** - Pesquisar se necessário (obrigatório para complexidade ≥7)
5. **3.5 CAG** - Cache research findings
6. **process_thought** - Processar pensamentos e soluções
7. **reflect_task** - Refletir sobre qualidade da solução
8. **split_tasks** - Dividir em subtarefas se necessário
9. **execute_task** - Executar a tarefa final

### Regras de Integração:

- **OBRIGATÓRIO**: Usar sequentialthinking_Sequential_Thinking ENTRE cada MCP call
- **OBRIGATÓRIO**: Aplicar este padrão para CADA tarefa
- **OBRIGATÓRIO**: Usar desktop commander para TODAS operações de terminal/arquivo
- **PROIBIDO**: Pular etapas do workflow
- **PROIBIDO**: Executar tarefas diretamente sem seguir o workflow

### Desktop Commander Obrigatório Para:

- Comandos de terminal com streaming output
- Suporte a timeout
- Gerenciamento de processos/sessões
- Gerenciamento de configuração do servidor
- Operações completas de filesystem
- Capacidades de edição de código
- Busca recursiva
- Audit logging abrangente

## REGRA: Aprender com Erros

### Em Cada Erro, Warning ou Output Subótimo:

1. **Detectar** saídas incorretas, warnings ou subótimas
2. **Corrigir** o erro imediatamente
3. **Salvar** erro e correção em @self.mdc usando o formato **EXATO**:

```markdown
### Erro: [Descrição Curta]

**Errado**:
```

[código incorreto ou abordagem incorreta]

```

**Correto**:
```

[código corrigido ou abordagem correta]

```

```

### Tipos de Erros a Documentar:

- Código que não funciona ou gera erro
- Warnings de deprecação
- Abordagens subótimas
- Padrões inconsistentes
- Violações de preferências do usuário

## REGRA: Respeitar e Referenciar Preferências do Projeto

### Em Cada Requisição:

1. **Ler** @project.mdc para:
   - Stack preferida (Next.js 14, TypeScript, Tailwind, Supabase)
   - Padrões de código e nomenclatura
   - Estrutura de pastas do GRUPO US
   - Ferramentas preferidas (Yarn vs npm, etc.)
   - Estilo de codificação e formatação
2. **Aplicar** preferências sem exceção
3. **Salvar** novas preferências descobertas durante o desenvolvimento

### Exemplo de Preferências:

- TypeScript > JavaScript
- Yarn > npm
- Componentes funcionais > classes
- Tailwind CSS > CSS modules

## REGRA: Consultar e Manter Schema do Banco Atualizado

### Em Requisições com Banco de Dados:

1. **Ler** @database-schema.mdc para entender:
   - Tabelas, colunas, tipos e relacionamentos atuais
   - Constraints, indexes e políticas RLS
   - Funções RPC disponíveis
   - Triggers e procedures
2. **Aplicar** conhecimento ao gerar queries SQL e código backend
3. **Atualizar** @database-schema.mdc se modificações foram feitas

### Informações Obrigatórias no Schema:

- Estrutura completa das tabelas
- Relacionamentos (FK/PK)
- Políticas RLS do Supabase
- Funções e procedures customizadas

## REGRA: Consultar e Manter Documentação de APIs

### Em Requisições com APIs:

1. **Ler** @apis.mdc para entender:
   - Endpoints existentes e suas finalidades
   - Formatos de request/response
   - Requisitos de autenticação
   - Parâmetros e validações
2. **Aplicar** padrões ao criar/consumir APIs
3. **Atualizar** @apis.mdc com novos endpoints criados

### Formato de Documentação de API:

```markdown
### [METHOD] /api/endpoint

- **Propósito**: O que faz
- **Auth**: Tipo de autenticação
- **Request**: Formato da requisição
- **Response**: Formato da resposta
- **Erros**: Códigos de erro possíveis
```

## REGRA: Prevenir Repetição

### Em Cada Requisição:

- **SEMPRE** ler @self.mdc antes de aplicar lógica
- Se erro relacionado foi encontrado:
  - **USAR** a correção salva
  - **REGISTRAR** que correção anterior foi aplicada
- **NUNCA** repetir erros já documentados

## REGRA: Manter Memória Limpa e Atualizada

### Manutenção Contínua:

- **Atualizar** seções existentes com soluções melhores
- **Estruturar** com headers `###` claros
- **Agrupar** por tópico relacionado
- **Manter** apenas informações gerais e reutilizáveis
- **REMOVER** informações específicas demais ou obsoletas

### Critérios de Qualidade:

- Informação deve ser **geral** e **reutilizável**
- Exemplos devem ser **claros** e **concisos**
- Correções devem ser **verificadas** e **testadas**

## INTEGRAÇÃO COM TASK MASTER

- Sincronizar com .taskmaster/tasks/
- Atualizar progress-log após cada task
- Manter contexto entre handoffs de 90%

## INTEGRAÇÃO CURSOR MEMORY BANK

### Filosofia de Reset de Memória

Minha memória reseta completamente entre sessões. Isso não é limitação - é o que me impulsiona a manter documentação perfeita. Após cada reset, dependo INTEIRAMENTE do Memory Bank para entender o projeto.

### Arquivos Memory Bank (Obrigatórios)

```json
{
  "memory_bank_files": {
    "memory-bank/projectbrief.md": "Documento fundacional - escopo e objetivos",
    "memory-bank/productContext.md": "Por que o projeto existe - problemas e soluções",
    "memory-bank/activeContext.md": "Foco atual - mudanças recentes e próximos passos",
    "memory-bank/systemPatterns.md": "Arquitetura - decisões técnicas e padrões",
    "memory-bank/techContext.md": "Tecnologias - setup e dependências",
    "memory-bank/progress.md": "Status - o que funciona e o que falta"
  }
}
```

### Workflows Integrados

- **Plan Mode** (`/plan`): Ler Memory Bank → Analisar código → 4-6 perguntas → Plano → Aprovação → Implementação
- **Act Mode** (`/act`): Verificar Memory Bank → Atualizar docs → Executar → Sincronizar → Documentar
- **Sync Mode** (`/sync`): Sincronização bidirecional Memory Bank ↔ Augment Memories

### Sincronização com Augment

- **Bidirectional**: Memory Bank ↔ Sistema nativo Augment
- **Auto-sync**: Após mudanças significativas
- **Priority mapping**: critical=1.0, high=0.8, medium=0.6, low=0.4

## CAMINHOS DE ARMAZENAMENTO

- `.cursor/rules/self.mdc`: Log de erros e correções
- `.cursor/rules/project.mdc`: Preferências e regras customizadas
- `.cursor/rules/database-schema.mdc`: Schema Supabase
- `.cursor/rules/apis.mdc`: Documentação de endpoints
- `memory-bank/`: Sistema único de memória (Memory Bank + Python components)
- `memory-bank/python/`: Componentes Python do sistema de memória consolidado

## ENFORCEMENT E COMPLIANCE

### Violações Críticas:

- **Falhar** em ler arquivos de memória antes da execução
- **Falhar** em atualizar memória após erros/descobertas
- **Repetir** erros já documentados
- **Ignorar** preferências documentadas do projeto

### Consequências:

- Failing to read or update memory files is a **CRITICAL ERROR**
- Todas requisições **DEVEM** usar conhecimento armazenado
- Compliance com preferências é **OBRIGATÓRIO**
- Minimizar tokens e API calls **SEMPRE**

### Auditoria:

- Verificar se memória foi consultada a cada requisição
- Validar se correções estão sendo aplicadas
- Monitorar qualidade das atualizações de memória
- Assegurar que preferências são respeitadas
