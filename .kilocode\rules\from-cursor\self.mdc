---
description: Workflow orchestration and execution protocols
globs: **/*
alwaysApply: true
---

# 📋 **REGRA 8: WORKFLOW & EXECUTION ORCHESTRATION**

_Prioridade: ALTA | Fluxo de trabalho e orquestração de execução_

## **7-Step Mandatory Workflow**

```json
{
  "workflow_steps": {
    "1_analyze": "Assess complexity (1-10) and task type",
    "2_select": "Choose appropriate approach based on analysis",
    "3_execute": "Use selected tools and methodology",
    "4_reflect": "Evaluate output quality internally",
    "5_refine": "Improve if quality <8/10",
    "6_validate": "Confirm final result meets ≥8/10",
    "7_learn": "Update Knowledge Graph with insights"
  }
}
```

## Workflow de MCPs Obrigatório

### Erro: Não seguir workflow obrigatório de MCPs

**Errado**:

```
// Executar tarefa diretamente sem usar MCPs na ordem correta
// Pular etapas do workflow
// Não usar sequential thinking entre MCPs
```

**Correto**:

```
// WORKFLOW OBRIGATÓRIO (7 STEPS + CAG):
0.5. CAG (cache memory of important things)
1. plan_task - Planejar a tarefa
2. analyze_task - Analisar requisitos
3. research_mode - Pesquisar se necessário
3.5. CAG (cache research)
4. process_thought - Processar pensamentos
5. reflect_task - Refletir sobre a solução
6. split_tasks - Dividir em subtarefas se necessário
7. execute_task - Executar a tarefa

// USAR sequentialthinking_Sequential_Thinking ENTRE cada MCP call
// USAR desktop commander para operações de terminal/arquivo
```

## TypeScript & Next.js

### Erro: Import de componente shadcn/ui incorreto

**Errado**:

```typescript
import { Button } from "shadcn/ui";
```

**Correto**:

```typescript
import { Button } from "@/components/ui/button";
```

### Erro: Uso de 'use client' desnecessário

**Errado**:

```typescript
"use client";

export function StaticComponent() {
  return <div>Conteúdo estático</div>;
}
```

**Correto**:

```typescript
// Sem 'use client' - Server Component por padrão
export function StaticComponent() {
  return <div>Conteúdo estático</div>;
}
```

## Supabase & Database

### Erro: Fetch sem tratamento de erro

**Errado**:

```typescript
const data = await supabase.from("users").select("*");
```

**Correto**:

```typescript
const { data, error } = await supabase.from("users").select("*");
if (error) throw error;
```

## State Management

### Erro: Estado não tipado no Zustand

**Errado**:

```typescript
const useStore = create((set) => ({
  user: null,
  setUser: (user) => set({ user }),
}));
```

**Correto**:

```typescript
interface StoreState {
  user: User | null;
  setUser: (user: User | null) => void;
}

const useStore = create<StoreState>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
}));
```

## Regras Relacionadas

- @memory.mdc: Regras principais do sistema de memória
- @project.mdc: Padrões de código e nomenclatura aplicados
- @database-schema.mdc: Schema para consultas SQL corretas
- @apis.mdc: Padrões de API para evitar erros
