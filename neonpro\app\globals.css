@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: oklch(0.9232 0.0026 48.7171);
    --foreground: oklch(0.2795 0.0368 260.0310);
    --card: oklch(0.9699 0.0013 106.4238);
    --card-foreground: oklch(0.2795 0.0368 260.0310);
    --popover: oklch(0.9699 0.0013 106.4238);
    --popover-foreground: oklch(0.2795 0.0368 260.0310);
    --primary: oklch(0.5854 0.2041 277.1173);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.8687 0.0043 56.3660);
    --secondary-foreground: oklch(0.4461 0.0263 256.8018);
    --muted: oklch(0.9232 0.0026 48.7171);
    --muted-foreground: oklch(0.5510 0.0234 264.3637);
    --accent: oklch(0.9376 0.0260 321.9388);
    --accent-foreground: oklch(0.3729 0.0306 259.7328);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.8687 0.0043 56.3660);
    --input: oklch(0.8687 0.0043 56.3660);
    --ring: oklch(0.5854 0.2041 277.1173);
    --chart-1: oklch(0.5854 0.2041 277.1173);
    --chart-2: oklch(0.5106 0.2301 276.9656);
    --chart-3: oklch(0.4568 0.2146 277.0229);
    --chart-4: oklch(0.3984 0.1773 277.3662);
    --chart-5: oklch(0.3588 0.1354 278.6973);
    --radius: 1.25rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: oklch(0.2795 0.0368 260.0310);
    --sidebar-primary: oklch(0.5854 0.2041 277.1173);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.9376 0.0260 321.9388);
    --sidebar-accent-foreground: oklch(0.3729 0.0306 259.7328);
    --sidebar-border: oklch(0.8687 0.0043 56.3660);
    --sidebar-ring: oklch(0.5854 0.2041 277.1173);
    --sidebar: oklch(0.8687 0.0043 56.3660);
    --font-sans: Inter, sans-serif;
    --font-serif: Lora, serif;
    --font-mono: Libre Baskerville, serif;
    --shadow-color: hsl(240 4% 60%);
    --shadow-opacity: 0.18;
    --shadow-blur: 10px;
    --shadow-spread: 4px;
    --shadow-offset-x: 2px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 2px 2px 10px 4px hsl(240 4% 60% / 0.09);
    --shadow-xs: 2px 2px 10px 4px hsl(240 4% 60% / 0.09);
    --shadow-sm: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 1px 2px 3px hsl(240 4% 60% / 0.18);
    --shadow: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 1px 2px 3px hsl(240 4% 60% / 0.18);
    --shadow-md: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 2px 4px 3px hsl(240 4% 60% / 0.18);
    --shadow-lg: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 4px 6px 3px hsl(240 4% 60% / 0.18);
    --shadow-xl: 2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 8px 10px 3px hsl(240 4% 60% / 0.18);
    --shadow-2xl: 2px 2px 10px 4px hsl(240 4% 60% / 0.45);
    --tracking-normal: 0em;
  }
  .dark {
    --background: oklch(0.2392 0.0388 252.5089);
    --foreground: oklch(0.8569 0.0111 95.1836);
    --card: oklch(0.2392 0.0388 252.5089);
    --card-foreground: oklch(0.8569 0.0111 95.1836);
    --popover: oklch(0.2392 0.0388 252.5089);
    --popover-foreground: oklch(0.8569 0.0111 95.1836);
    --primary: oklch(0.6776 0.0653 81.7406);
    --primary-foreground: oklch(0.8569 0.0111 95.1836);
    --secondary: oklch(0.2392 0.0388 252.5089);
    --secondary-foreground: oklch(0.8569 0.0111 95.1836);
    --muted: oklch(0.6776 0.0653 81.7406);
    --muted-foreground: oklch(0.8569 0.0111 95.1836);
    --accent: oklch(0.3896 0.0074 59.4734);
    --accent-foreground: oklch(0.8717 0.0093 258.3382);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(0.2244 0.0074 67.4370);
    --border: oklch(0.3359 0.0077 59.4197);
    --input: oklch(0.3359 0.0077 59.4197);
    --ring: oklch(0.6776 0.0653 81.7406);
    --chart-1: oklch(0.6801 0.1583 276.9349);
    --chart-2: oklch(0.5854 0.2041 277.1173);
    --chart-3: oklch(0.5106 0.2301 276.9656);
    --chart-4: oklch(0.4568 0.2146 277.0229);
    --chart-5: oklch(0.3984 0.1773 277.3662);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
    --sidebar-primary: oklch(0.7466 0.0242 84.5921);
    --sidebar-primary-foreground: oklch(0.3720 0.0497 245.2138);
    --sidebar-accent: oklch(0.3896 0.0074 59.4734);
    --sidebar-accent-foreground: oklch(0.8569 0.0111 95.1836);
    --sidebar-border: oklch(0.6776 0.0653 81.7406);
    --sidebar-ring: oklch(0.6776 0.0653 81.7406);
    --radius: 1.25rem;
    --sidebar: oklch(0.2392 0.0388 252.5089);
    --font-sans: Inter, sans-serif;
    --font-serif: Lora, serif;
    --font-mono: Libre Baskerville, serif;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.18;
    --shadow-blur: 10px;
    --shadow-spread: 4px;
    --shadow-offset-x: 2px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 2px 2px 10px 4px hsl(0 0% 0% / 0.09);
    --shadow-xs: 2px 2px 10px 4px hsl(0 0% 0% / 0.09);
    --shadow-sm: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18);
    --shadow: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18);
    --shadow-md: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 2px 4px 3px hsl(0 0% 0% / 0.18);
    --shadow-lg: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 4px 6px 3px hsl(0 0% 0% / 0.18);
    --shadow-xl: 2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 8px 10px 3px hsl(0 0% 0% / 0.18);
    --shadow-2xl: 2px 2px 10px 4px hsl(0 0% 0% / 0.45);
  }
  .theme {
    --font-sans: Inter, sans-serif;
    --font-mono: Libre Baskerville, serif;
    --font-serif: Lora, serif;
    --radius: 1.25rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}