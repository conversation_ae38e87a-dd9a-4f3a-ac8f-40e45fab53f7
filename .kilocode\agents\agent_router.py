import json
from typing import List, Dict, Any, Optional

from agents.base_agent import BaseAgent
from utils.complexity_calculator import ComplexityCalculator

class UnifiedAgentRouter:
    """
    Routes requests to the most appropriate agent based on complexity, domain, and other factors.
    """
    def __init__(self, config_path: str):
        """
        Initializes the UnifiedAgentRouter.

        Args:
            config_path (str): Path to the master configuration file.
        """
        self.agents: List[BaseAgent] = []
        self.complexity_calculator = ComplexityCalculator()
        self._load_config(config_path)

    def _load_config(self, config_path: str):
        """Loads agent configurations and initializes agent instances."""
        with open(config_path, 'r') as f:
            config = json.load(f)

        agent_classes = {
            # This would dynamically import agent classes in a real scenario
            "TechnicalArchitect": BaseAgent, # Placeholder
            "OperationsCoordinator": BaseAgent, # Placeholder
            "ResearchStrategist": BaseAgent, # Placeholder
            "QualityGuardian": BaseAgent # Placeholder
        }

        for agent_config in config.get("agents", []):
            agent_name = agent_config["name"]
            if agent_name in agent_classes:
                # In a real implementation, we would instantiate the specific agent class
                # For now, we use BaseAgent as a placeholder
                agent_class = agent_classes[agent_name]
                self.agents.append(
                    agent_class(
                        name=agent_name,
                        model=agent_config["model"],
                        complexity_range=tuple(agent_config["complexity_range"]),
                        domains=agent_config["domains"]
                    )
                )

    def route_request(self, request: Dict[str, Any]) -> Optional[BaseAgent]:
        """
        Routes a request to the best available agent.

        Args:
            request (Dict[str, Any]): The request payload.

        Returns:
            Optional[BaseAgent]: The selected agent, or None if no suitable agent is found.
        """
        task_complexity = self.complexity_calculator.calculate_complexity(request)
        task_domain = self._extract_domain(request) # Simplified domain extraction

        suitable_agents = []
        for agent in self.agents:
            if agent.can_handle(task_complexity, task_domain):
                suitable_agents.append(agent)

        if not suitable_agents:
            return None

        # Simple selection logic: return the first suitable agent.
        # This can be enhanced with confidence scoring, performance history, etc.
        return suitable_agents[0]

    def _extract_domain(self, request: Dict[str, Any]) -> str:
        """
        A simplified method to extract the primary domain from a request.
        In a real system, this would use more advanced NLP techniques.
        """
        description = request.get("description", "").lower()
        # This is a placeholder for a more sophisticated domain detection logic
        if "architecture" in description or "database" in description:
            return "architecture"
        if "plan" in description or "coordinate" in description:
            return "coordination"
        if "research" in description or "analyze" in description:
            return "research"
        if "quality" in description or "review" in description:
            return "quality-assurance"

        return "general" # Default domain

if __name__ == '__main__':
    # Example Usage
    router = UnifiedAgentRouter(config_path='../config/kilo_master_config.json')

    print(f"Loaded {len(router.agents)} agents.")
    for agent in router.agents:
        print(f"- {agent.name}")

    test_request = {"description": "Refactor the database connection pool for the main API."}
    selected_agent = router.route_request(test_request)

    if selected_agent:
        print(f"\nRequest routed to: {selected_agent.name}")
    else:
        print("\nNo suitable agent found for the request.")
