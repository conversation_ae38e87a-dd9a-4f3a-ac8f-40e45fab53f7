{"$schema": "https://biomejs.dev/schemas/1.10.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "defaultBranch": "main"}, "files": {"ignore": ["**/node_modules", "**/.next", "**/dist", "**/build", "**/.turbo", "**/coverage", "**/*.min.js", "**/*.d.ts", "**/generated"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "multiline"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noUselessConstructor": "error", "noUselessFragments": "error", "noUselessLabel": "error", "noUselessRename": "error", "noVoid": "error", "useOptionalChain": "error", "useSimplifiedLogicExpression": "error"}, "correctness": {"noConstAssign": "error", "noConstructorReturn": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidConstructorSuper": "error", "noNewSymbol": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnusedVariables": "error", "useIsNan": "error"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error", "useDefaultParameterLast": "error", "useExponentiationOperator": "error", "useNumericLiterals": "error", "useTemplate": "error"}, "suspicious": {"noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noUnsafeNegation": "error"}}}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "semicolons": "asNeeded", "trailingCommas": "es5", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false}}}