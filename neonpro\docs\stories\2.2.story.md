# Story 2.2: Accounts Receivable Management

## Status
Approved

## Story
**As a** clinic financial manager,  
**I want** to track patient payments and outstanding balances,  
**so that** I can optimize cash flow and reduce receivables aging.

## Acceptance Criteria

1. **Patient Billing:**
   - Generate invoices from appointment and service data
   - Support multiple billing cycles and terms
   - Handle insurance billing and claims
   - Track patient payment history
   - Support payment plans and installments

2. **Payment Processing:**
   - Record patient payments across multiple channels
   - Handle cash, card, PIX, and bank transfer payments
   - Support partial payments and adjustments
   - Process refunds and credit memos
   - Generate payment confirmations

3. **Receivables Tracking:**
   - Monitor aging of outstanding balances
   - Generate dunning letters and payment reminders
   - Track collection efforts and outcomes
   - Support write-offs and bad debt management
   - Maintain payment terms and credit limits

4. **Revenue Analytics:**
   - Track revenue by service type and professional
   - Monitor collection rates and DSO metrics
   - Generate patient financial statements
   - Support revenue forecasting
   - Provide receivables aging reports

## Tasks / Subtasks

- [ ] Design accounts receivable database schema (AC: 1, 3)
  - [ ] Create patient_invoices table with billing details
  - [ ] Design payment_history table for tracking all payments
  - [ ] Add payment_plans table for installment arrangements
  - [ ] Create receivables_aging table for balance tracking
  - [ ] Implement insurance_claims table for third-party billing

- [ ] Build invoice generation system (AC: 1)
  - [ ] Create automated invoice generation from appointments
  - [ ] Implement service-based billing calculations
  - [ ] Add tax calculation and compliance features
  - [ ] Build invoice template customization
  - [ ] Create invoice delivery system (email/print)

- [ ] Implement payment processing interface (AC: 2)
  - [ ] Build multi-channel payment entry form
  - [ ] Add PIX integration for Brazilian payments
  - [ ] Implement credit card processing integration
  - [ ] Create cash and bank transfer recording
  - [ ] Build payment confirmation and receipt system

- [ ] Develop receivables tracking system (AC: 3)
  - [ ] Create aging buckets and classification logic
  - [ ] Build dunning letter generation and delivery
  - [ ] Implement collection workflow management
  - [ ] Add write-off and bad debt processing
  - [ ] Create payment reminder automation

- [ ] Build revenue analytics dashboard (AC: 4)
  - [ ] Create revenue tracking by service and professional
  - [ ] Implement DSO (Days Sales Outstanding) calculations
  - [ ] Build collection rate monitoring
  - [ ] Add revenue forecasting algorithms
  - [ ] Create patient financial statement generation

- [ ] Implement payment plans and installments (AC: 1, 2)
  - [ ] Create payment plan setup and configuration
  - [ ] Build installment scheduling and tracking
  - [ ] Add automated payment plan reminders
  - [ ] Implement payment plan modifications
  - [ ] Create payment plan performance reporting

- [ ] Develop insurance billing system (AC: 1)
  - [ ] Build insurance provider management
  - [ ] Create claims submission and tracking
  - [ ] Implement insurance payment processing
  - [ ] Add claims denial and appeal workflows
  - [ ] Build insurance reporting and analytics

- [ ] Add financial reporting and compliance (AC: 3, 4)
  - [ ] Create receivables aging reports
  - [ ] Build collection effectiveness reports
  - [ ] Implement patient financial statements
  - [ ] Add regulatory compliance reporting
  - [ ] Create revenue recognition reporting

## Dev Notes

### System Architecture Context
[Source: architecture/01-system-overview-context.md]
- Financial operations use Edge Functions for secure payment processing
- Server Actions handle invoice generation and payment recording
- Real-time updates for payment status via Supabase channels
- PWA offline capability for payment entry and receivables viewing

### Financial Data Model Requirements
[Source: architecture/03-data-model-rls-policies.md]
- All receivables tables follow UUID + clinic_id + audit pattern
- RLS policies ensure patient data access only by authorized staff
- Comprehensive audit logging for all payment transactions
- Soft delete for historical financial data preservation
- Payment data encryption for PCI compliance

### API Surface & Receivables Endpoints
[Source: architecture/05-api-surface-edge-functions.md]
- POST /v1/finance/invoices - Invoice generation and management
- POST /v1/finance/payments/receive - Record patient payments
- GET /v1/finance/receivables - Receivables tracking and aging
- POST /v1/finance/payment-plans - Payment plan management
- GET /v1/finance/reports/revenue - Revenue analytics and reporting

### Integration with Epic 1 Components
- Patient Portal (Story 1.3) integration for patient payment self-service
- Appointment system (Story 1.1) for service billing automation
- Authentication system for role-based financial access
- Messaging system for payment reminders and invoice delivery

### Integration with Epic 2 Components
- Story 2.1 (Accounts Payable) for complete financial picture
- Story 2.3 (Daily Cash Flow) for cash receipt recording
- Story 2.4 (Bank Reconciliation) for payment verification

### Business Rules Context
[Source: PRD Core Functionality]
- Financeiro Essencial module: Match ≥ 95% reconciliation accuracy
- P0 priority for revenue management and cash flow optimization
- Success metric: Receivables aging ≤ 30 days average
- Collection rate improvement target ≥ 90%

### Payment Processing Requirements
- Support for multiple Brazilian payment methods (PIX, boleto, cards)
- Real-time payment validation and confirmation
- Integration with popular payment gateways
- Compliance with Brazilian financial regulations
- Automated payment matching and reconciliation

### Performance Requirements
[Source: PRD requirements]
- Invoice generation ≤ 2 seconds
- Payment processing ≤ 3 seconds
- Receivables aging calculation ≤ 5 seconds
- Revenue reporting ≤ 5 seconds
- Payment plan calculations ≤ 1 second

### File Structure Context
- Receivables routes: app/dashboard/finance/receivables/
- Invoice components: components/finance/invoices/
- Payment processing: components/finance/payments/receive/
- Revenue analytics: components/finance/analytics/
- Payment plans: components/finance/payment-plans/

### Database Schema Design

**patient_invoices table:**
- id (UUID, PK)
- clinic_id (UUID, FK)
- patient_id (UUID, FK)
- appointment_id (UUID, FK, nullable)
- invoice_number (VARCHAR, unique)
- issue_date (DATE)
- due_date (DATE)
- total_amount (DECIMAL)
- paid_amount (DECIMAL, default 0)
- status (ENUM: draft, sent, paid, overdue, cancelled)
- payment_terms (INTEGER, days)
- insurance_claim_id (UUID, FK, nullable)

**payment_history table:**
- id (UUID, PK)
- clinic_id (UUID, FK)
- patient_id (UUID, FK)
- invoice_id (UUID, FK, nullable)
- amount (DECIMAL)
- payment_date (DATE)
- payment_method (ENUM: cash, card, pix, transfer, insurance)
- reference_number (VARCHAR, nullable)
- notes (TEXT, nullable)

**payment_plans table:**
- id (UUID, PK)
- clinic_id (UUID, FK)
- patient_id (UUID, FK)
- total_amount (DECIMAL)
- installment_amount (DECIMAL)
- installment_count (INTEGER)
- start_date (DATE)
- status (ENUM: active, completed, cancelled, defaulted)
- created_by (UUID, FK)

**insurance_claims table:**
- id (UUID, PK)
- clinic_id (UUID, FK)
- patient_id (UUID, FK)
- provider_id (UUID, FK)
- claim_number (VARCHAR)
- submission_date (DATE)
- amount_claimed (DECIMAL)
- amount_approved (DECIMAL, nullable)
- status (ENUM: submitted, processing, approved, denied, paid)

### Security & Compliance
[Source: architecture/06-security-compliance.md]
- PCI DSS compliance for payment card processing
- LGPD compliance for patient financial data
- Role-based access for financial operations
- Encryption of sensitive payment information
- Audit trails for all financial transactions
- Secure API endpoints for payment processing

### Testing
**Testing Standards:**
- Jest unit tests for invoice calculations and payment processing
- Integration tests for payment gateway connections
- E2E tests for complete billing and collection workflows
- Security tests for payment data protection
- Performance tests for high-volume payment processing

**Testing Requirements for this Story:**
- Unit tests for invoice generation logic
- Integration tests for payment method processing
- E2E tests for patient billing and payment workflows
- Security tests for financial data access controls
- Performance tests for receivables aging calculations
- Payment gateway integration testing

**Key Test Scenarios:**
- Automated invoice generation from appointments
- Multi-channel payment processing and recording
- Payment plan setup and installment tracking
- Insurance claim submission and processing
- Receivables aging and collection workflows
- Revenue analytics accuracy validation
- Patient financial statement generation

### Brazilian Payment Integration
- PIX instant payment system integration
- Boleto bancário generation and tracking
- Credit/debit card processing with local acquirers
- Bank transfer (TED/DOC) handling
- Compliance with Central Bank regulations
- Support for popular Brazilian payment gateways

### Revenue Recognition & Compliance
- Proper revenue recognition accounting
- Tax calculation and reporting (ISS, PIS, COFINS)
- Integration with accounting systems
- Financial audit trail maintenance
- Regulatory reporting capabilities
- Chart of accounts integration

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-18 | 1.0 | Initial story creation from Epic 2 | Scrum Master |

## Dev Agent Record

### Agent Model Used
*To be populated by development agent*

### Debug Log References
*To be populated by development agent*

### Completion Notes List
*To be populated by development agent*

### File List
*To be populated by development agent*

## QA Results
*To be populated by QA agent*
