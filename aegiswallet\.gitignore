# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# drizzle
drizzle.log
/node_modules
/node_modules/next
node_modules
node_modules
node_modules
node_modules
node_modules/minimatch/LICENSE
/node_modules
node_modules
node_modules
node_modules
node_modules
node_modules
node_modules
