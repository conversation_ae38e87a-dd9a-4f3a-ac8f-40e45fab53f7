# AegisWallet Frontend Development Rules

## Security-First UI Architecture

### Component Security
```typescript
// Secure component patterns
interface SecureComponentProps {
  sensitiveData?: never;  // Never pass sensitive data as props
  onSecureAction: (encryptedPayload: string) => void;
  securityLevel: 'low' | 'medium' | 'high' | 'critical';
}
```

### State Management Security
- Never store private keys in React state
- Use secure storage for sensitive session data
- Implement automatic state cleanup on logout
- Encrypt sensitive data in local storage

## Cryptographic UI Components

### Key Display Components
```typescript
// Secure key display with masking
const PrivateKeyDisplay = ({ encryptedKey }: Props) => {
  const [isRevealed, setIsRevealed] = useState(false);
  const [decryptedKey, setDecryptedKey] = useState<string | null>(null);
  
  // Only decrypt when user explicitly requests
  const revealKey = async () => {
    const key = await decryptWithUserPassword(encryptedKey);
    setDecryptedKey(key);
    
    // Auto-hide after 30 seconds
    setTimeout(() => {
      setDecryptedKey(null);
      setIsRevealed(false);
    }, 30000);
  };
};
```

### Transaction Components
- Real-time transaction status updates
- Multi-signature transaction workflows
- Gas fee estimation and optimization
- Transaction confirmation dialogs

## Security UX Patterns

### Authentication Flow
```typescript
// Progressive authentication based on action sensitivity
const SecurityGate = ({ children, requiredLevel }: Props) => {
  const authLevel = useAuthenticationLevel();
  
  if (authLevel < requiredLevel) {
    return <AuthenticationChallenge level={requiredLevel} />;
  }
  
  return children;
};
```

### Secure Input Handling
- Password strength indicators
- Secure clipboard handling
- Input validation with real-time feedback
- Protection against keyloggers (virtual keyboards)

## Wallet Interface Design

### Balance Display
```typescript
// Secure balance display with privacy options
const BalanceDisplay = () => {
  const [isPrivacyMode, setIsPrivacyMode] = usePrivacyMode();
  const balance = useEncryptedBalance();
  
  return (
    <div className="balance-container">
      {isPrivacyMode ? (
        <span className="balance-hidden">••••••</span>
      ) : (
        <span className="balance-visible">{formatCurrency(balance)}</span>
      )}
    </div>
  );
};
```

### Transaction History
- Paginated transaction lists
- Advanced filtering and search
- Export capabilities (encrypted)
- Real-time status updates

## Security Indicators

### Visual Security Cues
```typescript
// Security level indicators
const SecurityIndicator = ({ level }: { level: SecurityLevel }) => {
  const indicators = {
    low: { color: 'yellow', icon: 'shield-alert' },
    medium: { color: 'orange', icon: 'shield-check' },
    high: { color: 'green', icon: 'shield-lock' },
    critical: { color: 'red', icon: 'shield-x' }
  };
  
  return (
    <div className={`security-indicator ${indicators[level].color}`}>
      <Icon name={indicators[level].icon} />
      <span>Security Level: {level.toUpperCase()}</span>
    </div>
  );
};
```

### Trust Indicators
- SSL certificate validation display
- Connection security status
- Device trust indicators
- Session security information

## Error Handling & User Feedback

### Security Error Messages
```typescript
// User-friendly security error messages
const SecurityErrorHandler = ({ error }: Props) => {
  const userFriendlyMessages = {
    'INVALID_SIGNATURE': 'Transaction signature is invalid. Please try again.',
    'INSUFFICIENT_BALANCE': 'Insufficient funds for this transaction.',
    'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait before trying again.',
    'SUSPICIOUS_ACTIVITY': 'Unusual activity detected. Additional verification required.'
  };
  
  return (
    <ErrorMessage 
      message={userFriendlyMessages[error.code] || 'An error occurred'}
      severity="error"
      actionable={true}
    />
  );
};
```

## Performance & Security Balance

### Secure Loading States
- Skeleton screens for sensitive data
- Progressive data loading
- Secure caching strategies
- Optimistic updates with rollback

### Memory Management
```typescript
// Secure cleanup of sensitive data
useEffect(() => {
  return () => {
    // Clear sensitive data from memory
    if (sensitiveData) {
      // Overwrite memory locations
      sensitiveData.fill(0);
      setSensitiveData(null);
    }
  };
}, []);
```

## Accessibility & Security

### Secure Accessibility
- Screen reader support without exposing sensitive data
- Keyboard navigation for security features
- High contrast mode for security indicators
- Voice control with security confirmations