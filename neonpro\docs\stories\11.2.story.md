# Story 11.2: Controle de Entradas e Compras

## Story Overview

**Como** responsável pelo estoque da clínica  
**Eu quero** sistema completo de controle de entradas e compras de materiais  
**Para que** eu possa registrar recebimentos, controlar custos, gerenciar notas fiscais e manter histórico preciso de compras

### Story Details

- **Epic**: Epic 11 - Estoque Simplificado
- **Story Points**: 9
- **Priority**: P1 (High)
- **Theme**: Purchase Management, Stock Intake & Cost Control
- **Dependencies**: Story 11.1 (Product Management)

### Acceptance Criteria

#### AC1: Registro Completo de Entradas de Material

- [ ] **GIVEN** recebimento de materiais na clínica
- [ ] **WHEN** registro entrada no sistema
- [ ] **THEN** controle completo de recebimento
- [ ] **AND** seleção de produto com autocompletar
- [ ] **AND** quantidade recebida com validação
- [ ] **AND** informação de lote e validade (se aplicável)
- [ ] **AND** preço unitário e custo total
- [ ] **AND** fornecedor e número da nota fiscal
- [ ] **AND** data de recebimento e responsável
- [ ] **AND** observações sobre qualidade/condições
- [ ] **AND** status de conferência (pendente/conferido/aprovado)

#### AC2: Gestão de Notas Fiscais e Documentação

- [ ] **GIVEN** nota fiscal de compra recebida
- [ ] **WHEN** processo documentação da compra
- [ ] **THEN** gestão completa de documentos fiscais
- [ ] **AND** upload de arquivo da nota fiscal
- [ ] **AND** OCR automático para extração de dados
- [ ] **AND** validação da NF via SEFAZ (se disponível)
- [ ] **AND** associação automática de produtos da NF
- [ ] **AND** conferência de divergências quantidade/preço
- [ ] **AND** cálculo automático de impostos
- [ ] **AND** status fiscal (pendente/validada/rejeitada)

#### AC3: Controle de Custos e Precificação

- [ ] **GIVEN** entrada de materiais com custos
- [ ] **WHEN** analiso impacto financeiro
- [ ] **THEN** controle detalhado de custos
- [ ] **AND** cálculo de custo médio ponderado automático
- [ ] **AND** comparação com último preço de compra
- [ ] **AND** análise de variação de preços por fornecedor
- [ ] **AND** alertas para variações significativas (>20%)
- [ ] **AND** impacto no custo total do estoque
- [ ] **AND** margem de lucro por produto atualizada
- [ ] **AND** relatório de rentabilidade por categoria

#### AC4: Processo de Conferência e Aprovação

- [ ] **GIVEN** entrada registrada no sistema
- [ ] **WHEN** realizo conferência física dos materiais
- [ ] **THEN** workflow completo de aprovação
- [ ] **AND** lista de conferência impressa/digital
- [ ] **AND** marcação de itens conferidos individualmente
- [ ] **AND** registro de divergências encontradas
- [ ] **AND** fotos de produtos recebidos (opcional)
- [ ] **AND** aprovação final por responsável autorizado
- [ ] **AND** notificação automática para fornecedor (se divergência)
- [ ] **AND** integração com financeiro após aprovação

#### AC5: Gestão de Pedidos de Compra e Follow-up

- [ ] **GIVEN** necessidade de compra identificada
- [ ] **WHEN** processo pedido de compra
- [ ] **THEN** gestão completa do ciclo de compra
- [ ] **AND** criação de pedido baseado em pontos de reposição
- [ ] **AND** cotação automática com múltiplos fornecedores
- [ ] **AND** aprovação de pedidos com workflow
- [ ] **AND** envio automático de pedido para fornecedor
- [ ] **AND** acompanhamento de status do pedido
- [ ] **AND** alertas de atraso na entrega
- [ ] **AND** recebimento parcial com controle

#### AC6: Análise e Relatórios de Compras

- [ ] **GIVEN** histórico de compras acumulado
- [ ] **WHEN** analiso performance de compras
- [ ] **THEN** relatórios completos de análise
- [ ] **AND** relatório de compras por período
- [ ] **AND** análise de fornecedores (preço, prazo, qualidade)
- [ ] **AND** evolução de preços por produto
- [ ] **AND** giro de estoque por categoria
- [ ] **AND** identificação de sazonalidades
- [ ] **AND** sugestões automáticas de compra
- [ ] **AND** análise de ROI por fornecedor

### Technical Requirements

#### Purchase Management System

```typescript
// Sistema de Gestão de Compras
interface EntradaEstoque {
  id: string
  numero_entrada: string
  tipo_entrada: TipoEntrada
  
  // Documento Fiscal
  nota_fiscal_id?: string
  numero_nf?: string
  serie_nf?: string
  chave_acesso_nf?: string
  
  // Fornecedor
  fornecedor_id: string
  fornecedor_nome: string
  
  // Data e Responsável
  data_entrada: Date
  data_recebimento: Date
  responsavel_recebimento: string
  
  // Items da Entrada
  itens: ItemEntrada[]
  
  // Totais
  quantidade_total: number
  valor_total: number
  valor_impostos: number
  valor_frete: number
  valor_desconto: number
  
  // Status e Controle
  status: StatusEntrada
  conferido: boolean
  aprovado: boolean
  aprovado_por?: string
  aprovado_em?: Date
  
  // Observações
  observacoes?: string
  condicoes_recebimento?: string
  
  // Auditoria
  criado_em: Date
  criado_por: string
  clinica_id: string
}

// Item de Entrada
interface ItemEntrada {
  id: string
  entrada_id: string
  produto_id: string
  
  // Quantidade e Preço
  quantidade: number
  preco_unitario: number
  valor_total: number
  percentual_desconto: number
  valor_desconto: number
  
  // Lote e Validade
  numero_lote?: string
  data_fabricacao?: Date
  data_validade?: Date
  
  // Conferência
  quantidade_conferida?: number
  conferido: boolean
  divergencia: boolean
  motivo_divergencia?: string
  
  // Qualidade
  condicoes_produto: CondicoesProduto
  observacoes_qualidade?: string
  fotos_recebimento?: string[]
  
  // Custo e Impacto
  custo_anterior: number
  novo_custo_medio: number
  impacto_custo_total: number
  
  // Auditoria
  conferido_por?: string
  conferido_em?: Date
}

// Nota Fiscal Eletrônica
interface NotaFiscalEletronica {
  id: string
  chave_acesso: string
  numero: string
  serie: string
  
  // Fornecedor (Emitente)
  cnpj_emitente: string
  razao_social_emitente: string
  
  // Dados da NF
  data_emissao: Date
  valor_total: number
  valor_produtos: number
  valor_impostos: number
  valor_frete: number
  
  // Arquivo e OCR
  arquivo_xml?: string
  arquivo_pdf?: string
  dados_ocr?: DadosOCR
  
  // Validação SEFAZ
  validada_sefaz: boolean
  status_sefaz: StatusSefaz
  protocolo_autorizacao?: string
  
  // Produtos da NF
  itens_nf: ItemNotaFiscal[]
  
  // Processo de Importação
  importada_automaticamente: boolean
  conferencia_manual_necessaria: boolean
  divergencias_identificadas: Divergencia[]
  
  // Status
  status: StatusNotaFiscal
  processada: boolean
  entrada_id?: string
  
  // Auditoria
  recebida_em: Date
  processada_em?: Date
  processada_por?: string
  clinica_id: string
}

// Item da Nota Fiscal
interface ItemNotaFiscal {
  codigo_produto: string
  descricao_produto: string
  ncm: string
  unidade: string
  quantidade: number
  valor_unitario: number
  valor_total: number
  
  // Impostos
  cfop: string
  cst_icms: string
  aliquota_icms: number
  valor_icms: number
  aliquota_ipi: number
  valor_ipi: number
  
  // Matching com Cadastro
  produto_id?: string
  match_automatico: boolean
  confianca_match: number
  requer_conferencia: boolean
}

// Pedido de Compra
interface PedidoCompra {
  id: string
  numero_pedido: string
  
  // Fornecedor
  fornecedor_id: string
  
  // Dados do Pedido
  data_pedido: Date
  data_necessidade: Date
  data_entrega_prevista?: Date
  
  // Itens
  itens: ItemPedidoCompra[]
  
  // Totais
  quantidade_total: number
  valor_total: number
  valor_desconto: number
  valor_frete: number
  
  // Condições
  condicoes_pagamento: string
  prazo_entrega: number
  observacoes?: string
  
  // Status e Workflow
  status: StatusPedido
  aprovado: boolean
  aprovado_por?: string
  aprovado_em?: Date
  enviado_para_fornecedor: boolean
  enviado_em?: Date
  
  // Recebimento
  recebimento_parcial: boolean
  entradas_vinculadas: string[]
  totalmente_recebido: boolean
  
  // Auditoria
  criado_por: string
  criado_em: Date
  clinica_id: string
}

// Item do Pedido de Compra
interface ItemPedidoCompra {
  id: string
  pedido_id: string
  produto_id: string
  
  // Quantidade e Preço
  quantidade_solicitada: number
  preco_cotado: number
  valor_total: number
  
  // Recebimento
  quantidade_recebida: number
  quantidade_pendente: number
  
  // Status
  status: StatusItemPedido
  totalmente_recebido: boolean
  
  // Observações
  especificacoes?: string
  observacoes?: string
}

// Análise de Compras
interface AnaliseCompras {
  periodo: PeriodoAnalise
  
  // Métricas Gerais
  total_compras: number
  valor_total_comprado: number
  numero_fornecedores: number
  numero_produtos_diferentes: number
  
  // Performance de Fornecedores
  ranking_fornecedores: RankingFornecedor[]
  fornecedores_pontuais: FornecedorPerformance[]
  fornecedores_em_atraso: FornecedorPerformance[]
  
  // Análise de Preços
  produtos_maior_variacao: VariacaoPreco[]
  tendencias_preco: TendenciaPreco[]
  economia_gerada: number
  
  // Padrões de Compra
  sazonalidade_identificada: PadraoSazonal[]
  produtos_giro_alto: ProdutoGiro[]
  produtos_giro_baixo: ProdutoGiro[]
  
  // Recomendações
  recomendacoes_compra: RecomendacaoCompra[]
  alertas_estoque: AlertaEstoque[]
  oportunidades_economia: OportunidadeEconomia[]
}

// Dados OCR da Nota Fiscal
interface DadosOCR {
  confianca_geral: number
  campos_extraidos: CampoOCR[]
  texto_completo: string
  requer_revisao_manual: boolean
  
  // Dados Extraídos
  numero_nf_extraido?: string
  cnpj_emitente_extraido?: string
  valor_total_extraido?: number
  data_emissao_extraida?: Date
  
  // Produtos Identificados
  produtos_extraidos: ProdutoOCR[]
}

interface CampoOCR {
  campo: string
  valor: string
  confianca: number
  posicao: BoundingBox
}

interface ProdutoOCR {
  descricao: string
  quantidade: number
  valor_unitario: number
  codigo_produto?: string
  confianca: number
}

// Divergência de Nota Fiscal
interface Divergencia {
  tipo: TipoDivergencia
  campo: string
  valor_nf: any
  valor_sistema: any
  descricao: string
  criticidade: CriticidadeDivergencia
  requer_acao: boolean
}

// Tipos Enum
type TipoEntrada = 'compra' | 'devolucao' | 'transferencia' | 'ajuste' | 'doacao'
type StatusEntrada = 'registrada' | 'conferindo' | 'aprovada' | 'rejeitada' | 'finalizada'
type CondicoesProduto = 'perfeito' | 'bom' | 'avariado' | 'vencido' | 'rejeitado'
type StatusSefaz = 'autorizada' | 'cancelada' | 'rejeitada' | 'pendente'
type StatusNotaFiscal = 'recebida' | 'processando' | 'processada' | 'erro' | 'rejeitada'
type StatusPedido = 'rascunho' | 'aguardando_aprovacao' | 'aprovado' | 'enviado' | 'recebido_parcial' | 'concluido' | 'cancelado'
type StatusItemPedido = 'pendente' | 'recebido_parcial' | 'recebido_total' | 'cancelado'
type TipoDivergencia = 'quantidade' | 'preco' | 'produto' | 'fornecedor' | 'valor_total'
type CriticidadeDivergencia = 'baixa' | 'media' | 'alta' | 'bloqueante'
```

#### Database Schema for Purchase Management

```sql
-- Entradas de Estoque
CREATE TABLE entradas_estoque (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  numero_entrada VARCHAR(50) NOT NULL,
  tipo_entrada tipo_entrada_type DEFAULT 'compra',
  
  -- Documento Fiscal
  nota_fiscal_id UUID REFERENCES notas_fiscais(id),
  numero_nf VARCHAR(20),
  serie_nf VARCHAR(10),
  chave_acesso_nf VARCHAR(44),
  
  -- Fornecedor
  fornecedor_id UUID NOT NULL REFERENCES fornecedores(id),
  
  -- Datas
  data_entrada DATE NOT NULL DEFAULT CURRENT_DATE,
  data_recebimento TIMESTAMPTZ DEFAULT NOW(),
  
  -- Responsável
  responsavel_recebimento UUID NOT NULL REFERENCES auth.users(id),
  
  -- Totais
  quantidade_total DECIMAL(15,3) DEFAULT 0,
  valor_total DECIMAL(15,2) DEFAULT 0,
  valor_impostos DECIMAL(15,2) DEFAULT 0,
  valor_frete DECIMAL(15,2) DEFAULT 0,
  valor_desconto DECIMAL(15,2) DEFAULT 0,
  
  -- Status e Controle
  status status_entrada_type DEFAULT 'registrada',
  conferido BOOLEAN DEFAULT FALSE,
  aprovado BOOLEAN DEFAULT FALSE,
  aprovado_por UUID REFERENCES auth.users(id),
  aprovado_em TIMESTAMPTZ,
  
  -- Observações
  observacoes TEXT,
  condicoes_recebimento TEXT,
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  ultima_atualizacao TIMESTAMPTZ DEFAULT NOW(),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_numero_entrada_clinica UNIQUE (numero_entrada, clinica_id),
  CONSTRAINT chk_valor_total_positivo CHECK (valor_total >= 0)
);

-- Itens das Entradas
CREATE TABLE itens_entrada_estoque (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  entrada_id UUID NOT NULL REFERENCES entradas_estoque(id) ON DELETE CASCADE,
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id),
  
  -- Quantidade e Preço
  quantidade DECIMAL(15,3) NOT NULL,
  preco_unitario DECIMAL(15,2) NOT NULL,
  valor_total DECIMAL(15,2) GENERATED ALWAYS AS (quantidade * preco_unitario) STORED,
  percentual_desconto DECIMAL(5,2) DEFAULT 0,
  valor_desconto DECIMAL(15,2) DEFAULT 0,
  
  -- Lote e Validade
  numero_lote VARCHAR(50),
  data_fabricacao DATE,
  data_validade DATE,
  
  -- Conferência
  quantidade_conferida DECIMAL(15,3),
  conferido BOOLEAN DEFAULT FALSE,
  divergencia BOOLEAN DEFAULT FALSE,
  motivo_divergencia TEXT,
  
  -- Qualidade
  condicoes_produto condicoes_produto_type DEFAULT 'perfeito',
  observacoes_qualidade TEXT,
  fotos_recebimento TEXT[],
  
  -- Custo e Impacto
  custo_anterior DECIMAL(15,2) DEFAULT 0,
  novo_custo_medio DECIMAL(15,2) DEFAULT 0,
  impacto_custo_total DECIMAL(15,2) DEFAULT 0,
  
  -- Conferência
  conferido_por UUID REFERENCES auth.users(id),
  conferido_em TIMESTAMPTZ,
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_quantidade_positiva CHECK (quantidade > 0),
  CONSTRAINT chk_preco_positivo CHECK (preco_unitario > 0),
  CONSTRAINT chk_desconto_valido CHECK (percentual_desconto >= 0 AND percentual_desconto <= 100)
);

-- Notas Fiscais Eletrônicas
CREATE TABLE notas_fiscais (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  chave_acesso VARCHAR(44) UNIQUE NOT NULL,
  numero VARCHAR(20) NOT NULL,
  serie VARCHAR(10) NOT NULL,
  
  -- Emitente
  cnpj_emitente VARCHAR(18) NOT NULL,
  razao_social_emitente VARCHAR(255) NOT NULL,
  
  -- Dados da NF
  data_emissao DATE NOT NULL,
  valor_total DECIMAL(15,2) NOT NULL,
  valor_produtos DECIMAL(15,2) NOT NULL,
  valor_impostos DECIMAL(15,2) DEFAULT 0,
  valor_frete DECIMAL(15,2) DEFAULT 0,
  
  -- Arquivos
  arquivo_xml TEXT,
  arquivo_pdf TEXT,
  dados_ocr JSONB DEFAULT '{}',
  
  -- Validação SEFAZ
  validada_sefaz BOOLEAN DEFAULT FALSE,
  status_sefaz status_sefaz_type DEFAULT 'pendente',
  protocolo_autorizacao VARCHAR(50),
  
  -- Processo de Importação
  importada_automaticamente BOOLEAN DEFAULT FALSE,
  conferencia_manual_necessaria BOOLEAN DEFAULT TRUE,
  divergencias_identificadas JSONB DEFAULT '[]',
  
  -- Status
  status status_nota_fiscal_type DEFAULT 'recebida',
  processada BOOLEAN DEFAULT FALSE,
  entrada_id UUID REFERENCES entradas_estoque(id),
  
  -- Auditoria
  recebida_em TIMESTAMPTZ DEFAULT NOW(),
  processada_em TIMESTAMPTZ,
  processada_por UUID REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_valor_nf_positivo CHECK (valor_total > 0)
);

-- Itens das Notas Fiscais
CREATE TABLE itens_nota_fiscal (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nota_fiscal_id UUID NOT NULL REFERENCES notas_fiscais(id) ON DELETE CASCADE,
  
  -- Dados do Item
  codigo_produto VARCHAR(100),
  descricao_produto VARCHAR(500) NOT NULL,
  ncm VARCHAR(10),
  unidade VARCHAR(10),
  quantidade DECIMAL(15,3) NOT NULL,
  valor_unitario DECIMAL(15,2) NOT NULL,
  valor_total DECIMAL(15,2) NOT NULL,
  
  -- Impostos
  cfop VARCHAR(10),
  cst_icms VARCHAR(10),
  aliquota_icms DECIMAL(5,2) DEFAULT 0,
  valor_icms DECIMAL(15,2) DEFAULT 0,
  aliquota_ipi DECIMAL(5,2) DEFAULT 0,
  valor_ipi DECIMAL(15,2) DEFAULT 0,
  
  -- Matching com Cadastro
  produto_id UUID REFERENCES produtos_estoque(id),
  match_automatico BOOLEAN DEFAULT FALSE,
  confianca_match DECIMAL(3,2) DEFAULT 0,
  requer_conferencia BOOLEAN DEFAULT TRUE,
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_quantidade_nf_positiva CHECK (quantidade > 0),
  CONSTRAINT chk_valor_nf_positivo CHECK (valor_unitario > 0)
);

-- Pedidos de Compra
CREATE TABLE pedidos_compra (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  numero_pedido VARCHAR(50) NOT NULL,
  
  -- Fornecedor
  fornecedor_id UUID NOT NULL REFERENCES fornecedores(id),
  
  -- Datas
  data_pedido DATE NOT NULL DEFAULT CURRENT_DATE,
  data_necessidade DATE NOT NULL,
  data_entrega_prevista DATE,
  
  -- Totais
  quantidade_total DECIMAL(15,3) DEFAULT 0,
  valor_total DECIMAL(15,2) DEFAULT 0,
  valor_desconto DECIMAL(15,2) DEFAULT 0,
  valor_frete DECIMAL(15,2) DEFAULT 0,
  
  -- Condições
  condicoes_pagamento TEXT,
  prazo_entrega INTEGER DEFAULT 7, -- dias
  observacoes TEXT,
  
  -- Status e Workflow
  status status_pedido_type DEFAULT 'rascunho',
  aprovado BOOLEAN DEFAULT FALSE,
  aprovado_por UUID REFERENCES auth.users(id),
  aprovado_em TIMESTAMPTZ,
  enviado_para_fornecedor BOOLEAN DEFAULT FALSE,
  enviado_em TIMESTAMPTZ,
  
  -- Recebimento
  recebimento_parcial BOOLEAN DEFAULT FALSE,
  totalmente_recebido BOOLEAN DEFAULT FALSE,
  
  -- Auditoria
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  ultima_atualizacao TIMESTAMPTZ DEFAULT NOW(),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_numero_pedido_clinica UNIQUE (numero_pedido, clinica_id),
  CONSTRAINT chk_data_necessidade CHECK (data_necessidade >= data_pedido)
);

-- Itens dos Pedidos de Compra
CREATE TABLE itens_pedido_compra (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  pedido_id UUID NOT NULL REFERENCES pedidos_compra(id) ON DELETE CASCADE,
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id),
  
  -- Quantidade e Preço
  quantidade_solicitada DECIMAL(15,3) NOT NULL,
  preco_cotado DECIMAL(15,2) NOT NULL,
  valor_total DECIMAL(15,2) GENERATED ALWAYS AS (quantidade_solicitada * preco_cotado) STORED,
  
  -- Recebimento
  quantidade_recebida DECIMAL(15,3) DEFAULT 0,
  quantidade_pendente DECIMAL(15,3) GENERATED ALWAYS AS (quantidade_solicitada - quantidade_recebida) STORED,
  
  -- Status
  status status_item_pedido_type DEFAULT 'pendente',
  totalmente_recebido BOOLEAN DEFAULT FALSE,
  
  -- Observações
  especificacoes TEXT,
  observacoes TEXT,
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_quantidade_solicitada_positiva CHECK (quantidade_solicitada > 0),
  CONSTRAINT chk_preco_cotado_positivo CHECK (preco_cotado > 0),
  CONSTRAINT chk_quantidade_recebida_valida CHECK (quantidade_recebida >= 0 AND quantidade_recebida <= quantidade_solicitada)
);

-- Relacionamento Pedidos-Entradas
CREATE TABLE pedidos_entradas (
  pedido_id UUID NOT NULL REFERENCES pedidos_compra(id),
  entrada_id UUID NOT NULL REFERENCES entradas_estoque(id),
  
  -- Informações do Relacionamento
  valor_recebido DECIMAL(15,2) NOT NULL,
  percentual_atendimento DECIMAL(5,2),
  
  -- Auditoria
  vinculado_em TIMESTAMPTZ DEFAULT NOW(),
  vinculado_por UUID NOT NULL REFERENCES auth.users(id),
  
  PRIMARY KEY (pedido_id, entrada_id)
);

-- Análises de Compras (Cache de Relatórios)
CREATE TABLE analises_compras (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Período da Análise
  data_inicio DATE NOT NULL,
  data_fim DATE NOT NULL,
  tipo_periodo tipo_periodo_analise_type,
  
  -- Dados da Análise
  metricas_gerais JSONB NOT NULL,
  performance_fornecedores JSONB DEFAULT '{}',
  analise_precos JSONB DEFAULT '{}',
  padroes_compra JSONB DEFAULT '{}',
  recomendacoes JSONB DEFAULT '[]',
  
  -- Metadados
  gerada_em TIMESTAMPTZ DEFAULT NOW(),
  gerada_por UUID NOT NULL REFERENCES auth.users(id),
  valida_ate TIMESTAMPTZ,
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_periodo_analise CHECK (data_fim >= data_inicio)
);

-- Tipos Enum para Compras
CREATE TYPE tipo_entrada_type AS ENUM ('compra', 'devolucao', 'transferencia', 'ajuste', 'doacao');
CREATE TYPE status_entrada_type AS ENUM ('registrada', 'conferindo', 'aprovada', 'rejeitada', 'finalizada');
CREATE TYPE condicoes_produto_type AS ENUM ('perfeito', 'bom', 'avariado', 'vencido', 'rejeitado');
CREATE TYPE status_sefaz_type AS ENUM ('autorizada', 'cancelada', 'rejeitada', 'pendente');
CREATE TYPE status_nota_fiscal_type AS ENUM ('recebida', 'processando', 'processada', 'erro', 'rejeitada');
CREATE TYPE status_pedido_type AS ENUM ('rascunho', 'aguardando_aprovacao', 'aprovado', 'enviado', 'recebido_parcial', 'concluido', 'cancelado');
CREATE TYPE status_item_pedido_type AS ENUM ('pendente', 'recebido_parcial', 'recebido_total', 'cancelado');
CREATE TYPE tipo_periodo_analise_type AS ENUM ('semanal', 'mensal', 'trimestral', 'semestral', 'anual');

-- Índices para Performance de Compras
CREATE INDEX idx_entradas_data ON entradas_estoque(data_entrada);
CREATE INDEX idx_entradas_fornecedor ON entradas_estoque(fornecedor_id);
CREATE INDEX idx_entradas_status ON entradas_estoque(status);
CREATE INDEX idx_entradas_clinica ON entradas_estoque(clinica_id);

-- Índices para Itens de Entrada
CREATE INDEX idx_itens_entrada_produto ON itens_entrada_estoque(produto_id);
CREATE INDEX idx_itens_entrada_lote ON itens_entrada_estoque(numero_lote) WHERE numero_lote IS NOT NULL;
CREATE INDEX idx_itens_entrada_validade ON itens_entrada_estoque(data_validade) WHERE data_validade IS NOT NULL;

-- Índices para Notas Fiscais
CREATE INDEX idx_nf_chave_acesso ON notas_fiscais(chave_acesso);
CREATE INDEX idx_nf_cnpj_emitente ON notas_fiscais(cnpj_emitente);
CREATE INDEX idx_nf_data_emissao ON notas_fiscais(data_emissao);
CREATE INDEX idx_nf_status ON notas_fiscais(status);

-- Índices para Pedidos
CREATE INDEX idx_pedidos_fornecedor ON pedidos_compra(fornecedor_id);
CREATE INDEX idx_pedidos_status ON pedidos_compra(status);
CREATE INDEX idx_pedidos_data_pedido ON pedidos_compra(data_pedido);
CREATE INDEX idx_pedidos_necessidade ON pedidos_compra(data_necessidade);

-- Full-text search para notas fiscais
CREATE INDEX idx_nf_search ON notas_fiscais USING gin(
  to_tsvector('portuguese', coalesce(razao_social_emitente, '') || ' ' || coalesce(numero, ''))
);
```

#### Purchase Management API Endpoints

```typescript
// Stock Entries API
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '50')
  const dataInicio = searchParams.get('data_inicio')
  const dataFim = searchParams.get('data_fim')
  const fornecedorId = searchParams.get('fornecedor_id')
  const status = searchParams.get('status')
  
  const supabase = createServerClient()
  
  try {
    let query = supabase
      .from('entradas_estoque')
      .select(`
        *,
        fornecedor:fornecedores(razao_social),
        responsavel:auth.users(nome),
        itens:itens_entrada_estoque(
          *,
          produto:produtos_estoque(nome, codigo_interno)
        )
      `)
    
    // Aplicar filtros
    if (dataInicio) {
      query = query.gte('data_entrada', dataInicio)
    }
    
    if (dataFim) {
      query = query.lte('data_entrada', dataFim)
    }
    
    if (fornecedorId) {
      query = query.eq('fornecedor_id', fornecedorId)
    }
    
    if (status) {
      query = query.eq('status', status)
    }
    
    // Paginação
    const from = (page - 1) * limit
    const to = from + limit - 1
    
    const { data: entradas, count } = await query
      .range(from, to)
      .order('data_entrada', { ascending: false })
    
    // Estatísticas das entradas
    const { data: stats } = await supabase
      .from('entradas_estoque')
      .select('status, valor_total')
    
    const estatisticas = {
      total: count || 0,
      valor_total: stats?.reduce((sum, e) => sum + (e.valor_total || 0), 0) || 0,
      pendentes_conferencia: stats?.filter(e => e.status === 'registrada').length || 0,
      aprovadas: stats?.filter(e => e.status === 'aprovada').length || 0
    }
    
    return NextResponse.json({
      entradas,
      estatisticas,
      paginacao: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      },
      message: 'Entradas carregadas com sucesso'
    })
    
  } catch (error) {
    console.error('Error loading stock entries:', error)
    return NextResponse.json({
      error: 'Erro ao carregar entradas'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  const supabase = createServerClient()
  
  try {
    const {
      fornecedor_id,
      data_entrada,
      nota_fiscal,
      itens,
      observacoes
    } = await request.json()
    
    // Validar dados obrigatórios
    if (!fornecedor_id || !itens || itens.length === 0) {
      return NextResponse.json({
        error: 'Fornecedor e itens são obrigatórios'
      }, { status: 400 })
    }
    
    // Gerar número da entrada
    const numeroEntrada = await gerarNumeroEntrada()
    
    // Calcular totais
    const totais = calcularTotaisEntrada(itens)
    
    // Criar entrada
    const { data: entrada, error: entradaError } = await supabase
      .from('entradas_estoque')
      .insert({
        numero_entrada: numeroEntrada,
        fornecedor_id,
        data_entrada: data_entrada || new Date(),
        quantidade_total: totais.quantidade,
        valor_total: totais.valor,
        observacoes,
        numero_nf: nota_fiscal?.numero,
        serie_nf: nota_fiscal?.serie
      })
      .select()
      .single()
    
    if (entradaError) throw entradaError
    
    // Criar itens da entrada
    const itensData = itens.map(item => ({
      entrada_id: entrada.id,
      produto_id: item.produto_id,
      quantidade: item.quantidade,
      preco_unitario: item.preco_unitario,
      numero_lote: item.numero_lote,
      data_validade: item.data_validade
    }))
    
    const { data: itensCreated, error: itensError } = await supabase
      .from('itens_entrada_estoque')
      .insert(itensData)
      .select()
    
    if (itensError) throw itensError
    
    // Atualizar custos médios dos produtos
    await atualizarCustosMedios(itensCreated)
    
    return NextResponse.json({
      entrada: { ...entrada, itens: itensCreated },
      message: 'Entrada criada com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating stock entry:', error)
    return NextResponse.json({
      error: 'Erro ao criar entrada'
    }, { status: 500 })
  }
}

// Invoice Management API
export async function POST(
  request: NextRequest,
  { params }: { params: { action: string } }
) {
  const supabase = createServerClient()
  
  try {
    if (params.action === 'upload-nf') {
      const formData = await request.formData()
      const file = formData.get('file') as File
      const fornecedorId = formData.get('fornecedor_id') as string
      
      // Upload do arquivo
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('notas-fiscais')
        .upload(`${Date.now()}-${file.name}`, file)
      
      if (uploadError) throw uploadError
      
      // Extrair dados com OCR
      const dadosOCR = await extrairDadosNotaFiscal(uploadData.path)
      
      // Criar registro da nota fiscal
      const { data: notaFiscal } = await supabase
        .from('notas_fiscais')
        .insert({
          chave_acesso: dadosOCR.chave_acesso,
          numero: dadosOCR.numero,
          serie: dadosOCR.serie,
          cnpj_emitente: dadosOCR.cnpj_emitente,
          razao_social_emitente: dadosOCR.razao_social,
          data_emissao: dadosOCR.data_emissao,
          valor_total: dadosOCR.valor_total,
          valor_produtos: dadosOCR.valor_produtos,
          arquivo_pdf: uploadData.path,
          dados_ocr: dadosOCR
        })
        .select()
        .single()
      
      // Processar itens da nota fiscal
      await processarItensNotaFiscal(notaFiscal.id, dadosOCR.itens)
      
      return NextResponse.json({
        notaFiscal,
        dadosOCR,
        message: 'Nota fiscal processada com sucesso'
      })
    }
    
    if (params.action === 'validar-sefaz') {
      const { chave_acesso } = await request.json()
      
      // Validar na SEFAZ
      const validacao = await validarNotaFiscalSEFAZ(chave_acesso)
      
      // Atualizar status no banco
      await supabase
        .from('notas_fiscais')
        .update({
          validada_sefaz: validacao.valida,
          status_sefaz: validacao.status,
          protocolo_autorizacao: validacao.protocolo
        })
        .eq('chave_acesso', chave_acesso)
      
      return NextResponse.json({
        validacao,
        message: 'Validação SEFAZ concluída'
      })
    }
    
    if (params.action === 'criar-entrada-nf') {
      const { nota_fiscal_id } = await request.json()
      
      // Buscar nota fiscal e itens
      const { data: notaFiscal } = await supabase
        .from('notas_fiscais')
        .select(`
          *,
          itens:itens_nota_fiscal(*)
        `)
        .eq('id', nota_fiscal_id)
        .single()
      
      // Criar entrada baseada na nota fiscal
      const entrada = await criarEntradaDeNotaFiscal(notaFiscal)
      
      return NextResponse.json({
        entrada,
        message: 'Entrada criada a partir da nota fiscal'
      })
    }
    
  } catch (error) {
    console.error('Error processing invoice:', error)
    return NextResponse.json({
      error: 'Erro ao processar nota fiscal'
    }, { status: 500 })
  }
}

// Purchase Orders API
export async function GET() {
  const supabase = createServerClient()
  
  try {
    const { data: pedidos } = await supabase
      .from('pedidos_compra')
      .select(`
        *,
        fornecedor:fornecedores(razao_social),
        criador:auth.users(nome),
        itens:itens_pedido_compra(
          *,
          produto:produtos_estoque(nome, codigo_interno)
        )
      `)
      .order('data_pedido', { ascending: false })
    
    return NextResponse.json({
      pedidos,
      message: 'Pedidos carregados'
    })
    
  } catch (error) {
    console.error('Error loading purchase orders:', error)
    return NextResponse.json({
      error: 'Erro ao carregar pedidos'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  const supabase = createServerClient()
  
  try {
    const {
      fornecedor_id,
      data_necessidade,
      itens,
      condicoes_pagamento,
      observacoes
    } = await request.json()
    
    // Gerar número do pedido
    const numeroPedido = await gerarNumeroPedido()
    
    // Calcular totais
    const totais = calcularTotaisPedido(itens)
    
    // Criar pedido
    const { data: pedido, error: pedidoError } = await supabase
      .from('pedidos_compra')
      .insert({
        numero_pedido: numeroPedido,
        fornecedor_id,
        data_necessidade,
        quantidade_total: totais.quantidade,
        valor_total: totais.valor,
        condicoes_pagamento,
        observacoes
      })
      .select()
      .single()
    
    if (pedidoError) throw pedidoError
    
    // Criar itens do pedido
    const itensData = itens.map(item => ({
      pedido_id: pedido.id,
      produto_id: item.produto_id,
      quantidade_solicitada: item.quantidade,
      preco_cotado: item.preco_unitario,
      especificacoes: item.especificacoes
    }))
    
    const { data: itensCreated } = await supabase
      .from('itens_pedido_compra')
      .insert(itensData)
      .select()
    
    return NextResponse.json({
      pedido: { ...pedido, itens: itensCreated },
      message: 'Pedido criado com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating purchase order:', error)
    return NextResponse.json({
      error: 'Erro ao criar pedido'
    }, { status: 500 })
  }
}

// Purchase Analytics API
export async function GET(
  request: NextRequest,
  { params }: { params: { tipo: string } }
) {
  const { searchParams } = new URL(request.url)
  const periodo = parseInt(searchParams.get('periodo') || '90')
  
  const supabase = createServerClient()
  
  try {
    const dataInicio = new Date()
    dataInicio.setDate(dataInicio.getDate() - periodo)
    
    if (params.tipo === 'fornecedores') {
      // Análise de performance de fornecedores
      const analise = await analisarPerformanceFornecedores(dataInicio)
      
      return NextResponse.json({
        analise,
        message: 'Análise de fornecedores concluída'
      })
    }
    
    if (params.tipo === 'precos') {
      // Análise de evolução de preços
      const analise = await analisarEvolucaoPrecos(dataInicio)
      
      return NextResponse.json({
        analise,
        message: 'Análise de preços concluída'
      })
    }
    
    if (params.tipo === 'compras') {
      // Análise geral de compras
      const analise = await analisarComprasGeral(dataInicio)
      
      return NextResponse.json({
        analise,
        message: 'Análise geral de compras concluída'
      })
    }
    
  } catch (error) {
    console.error('Error generating purchase analytics:', error)
    return NextResponse.json({
      error: 'Erro ao gerar análise de compras'
    }, { status: 500 })
  }
}
```

### Integration Points

#### Epic 7 Integration (Financeiro Essencial)

- **Accounts Payable**: Integração automática com contas a pagar
- **Supplier Management**: Gestão unificada de fornecedores
- **Cost Control**: Controle de custos de materiais
- **Invoice Processing**: Processamento de notas fiscais

#### Epic 8 Integration (BI & Dashboards)

- **Purchase KPIs**: Métricas de compras nos dashboards
- **Cost Analytics**: Análise de custos e variações
- **Supplier Performance**: Dashboards de performance de fornecedores
- **Purchase Trends**: Análise de tendências de compra

#### Story 11.1 Integration

- **Product Updates**: Atualização automática de custos médios
- **Supplier Pricing**: Atualização de preços por fornecedor
- **Document Upload**: Upload de certificados e documentos
- **Inventory Foundation**: Base para controle de estoque

### Testing Strategy

#### Purchase Management Tests

```typescript
describe('Purchase Management System', () => {
  test('creates stock entry with proper cost calculation', async () => {
    const produto = await createTestProduct()
    const fornecedor = await createTestSupplier()
    
    const entrada = await createStockEntry({
      fornecedor_id: fornecedor.id,
      itens: [{
        produto_id: produto.id,
        quantidade: 10,
        preco_unitario: 25.00
      }]
    })
    
    expect(entrada.valor_total).toBe(250.00)
    expect(entrada.quantidade_total).toBe(10)
    expect(entrada.status).toBe('registrada')
  })
  
  test('processes invoice with OCR correctly', async () => {
    const invoice = await uploadInvoice({
      file: testInvoiceFile,
      fornecedor_id: testSupplierId
    })
    
    expect(invoice.dados_ocr.confianca_geral).toBeGreaterThan(0.8)
    expect(invoice.numero).toBeDefined()
    expect(invoice.valor_total).toBeGreaterThan(0)
  })
  
  test('tracks price history accurately', async () => {
    const produto = await createTestProduct()
    await createStockEntry({
      itens: [{ produto_id: produto.id, preco_unitario: 20.00 }]
    })
    await createStockEntry({
      itens: [{ produto_id: produto.id, preco_unitario: 25.00 }]
    })
    
    const history = await getPriceHistory(produto.id)
    
    expect(history).toHaveLength(1)
    expect(history[0].preco_anterior).toBe(20.00)
    expect(history[0].preco_novo).toBe(25.00)
  })
  
  test('manages purchase orders workflow', async () => {
    const pedido = await createPurchaseOrder({
      fornecedor_id: testSupplierId,
      itens: [{ produto_id: testProductId, quantidade: 20 }]
    })
    
    expect(pedido.status).toBe('rascunho')
    
    await approvePurchaseOrder(pedido.id)
    const approved = await getPurchaseOrder(pedido.id)
    
    expect(approved.status).toBe('aprovado')
    expect(approved.aprovado).toBe(true)
  })
  
  test('calculates supplier performance metrics', async () => {
    const analise = await analyzeSupplierPerformance(testSupplierId, 90)
    
    expect(analise.numero_pedidos).toBeGreaterThan(0)
    expect(analise.percentual_entregas_ok).toBeGreaterThanOrEqual(0)
    expect(analise.avaliacao_geral).toBeGreaterThanOrEqual(0)
  })
})
```

### Dev Notes

#### Advanced Features

- **OCR Integration**: Extração automática de dados de notas fiscais
- **SEFAZ Validation**: Validação de notas fiscais na receita federal
- **Cost Averaging**: Cálculo automático de custo médio ponderado
- **Purchase Recommendations**: Sugestões automáticas de compra

#### Performance Optimizations

- **Batch Processing**: Processamento em lote de múltiplas entradas
- **Smart Caching**: Cache de dados de fornecedores e produtos
- **Async Processing**: Processamento assíncrono de análises
- **Efficient Queries**: Queries otimizadas para relatórios

#### Compliance Features

- **Fiscal Control**: Controle fiscal completo de notas
- **Audit Trail**: Rastro completo de alterações
- **Document Management**: Gestão de documentos fiscais
- **SPED Integration**: Preparação para integração SPED

---

## Dev Agent Record

### Task Status

- [x] Analyzed purchase management requirements for Epic 11
- [x] Created comprehensive story with 6 acceptance criteria  
- [x] Designed TypeScript interfaces for purchases, invoices, and purchase orders
- [x] Specified database schema with fiscal compliance
- [x] Developed complete API endpoints for purchase management
- [x] Added OCR integration for invoice processing
- [x] Created cost control and price tracking
- [x] Implemented purchase order workflow
- [x] Established integration points with Epic 7-8 and Story 11.1
- [x] Created comprehensive testing strategy for purchase operations

### File List

- `docs/stories/11.2.story.md` - Purchase Management and Stock Intake system

### Change Log

- **Story 11.2 Creation**: Complete purchase management with cost control
- **Invoice Processing**: OCR and SEFAZ validation for fiscal compliance
- **Cost Management**: Automatic cost averaging and price tracking
- **Purchase Orders**: Complete workflow from creation to delivery
- **Supplier Analytics**: Performance tracking and analysis
- **Fiscal Compliance**: Full Brazilian fiscal requirements support

### Completion Notes

Story 11.2 builds on the product foundation from 11.1 to create a comprehensive purchase management system. The story covers complete purchase lifecycle from order creation to receipt, with advanced features like OCR processing and fiscal compliance.

### Next Steps

Ready to continue with Story 11.3: Controle de Saídas e Consumo to complete the inventory movement system.
