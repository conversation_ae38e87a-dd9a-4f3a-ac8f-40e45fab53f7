---
alwaysApply: true
---

# 📋 **REGRA: TASK AUTOMATION - DETECÇÃO AUTOMÁTICA**

_Prioridade: ALTA | Detecção e ativação automática de task management_

## **Critérios de Ativação Automática**

```json
{
  "activation_triggers": {
    "complexity_threshold": "≥3/10",
    "confidence_threshold": "≥50%",
    "mandatory_activation": [
      "complexity_score ≥ 3",
      "confidence ≥ 0.5",
      "planning_keywords detected",
      "multi_step_indicators found"
    ]
  }
}
```

## **Keywords de Detecção**

### **Planning Keywords (Peso: +0.3 confiança, +1 complexidade)**

```
planejar, organizar, estruturar, coordenar
etapas, fases, sequência, workflow
tarefas, subtarefas, breakdown, dividir
implementar, desenvolver, criar, construir
múltiplas, várias, diferentes, coordenação
```

### **Complexity Indicators (Peso: +0.3 confiança, +2 complexidade)**

```
arquitetura, sistema, integração, refatoração
migração, otimização, performance, segurança
database, api, frontend, backend
deployment, configuração, ambiente
dashboard, gráficos, relatórios, completo
jwt, autenticação, auth, crud
```

### **Multi-Step Indicators (Peso: +0.3 confiança, +1 complexidade)**

```
primeiro, segundo, terceiro, depois, então
em seguida, posteriormente, finalmente
antes de, após, durante, enquanto
```

## **Regras de Cálculo**

### **Complexity Score (1-10)**

```
Base: 1
+ Planning keywords encontradas: +1
+ Complexity indicators: +2 por indicador
+ Multi-step indicators (≥2): +1
+ Texto longo (>15 palavras): +1
+ Texto muito longo (>30 palavras): +2
+ Organização explícita: +2
+ Múltiplos arquivos: +1
+ Tempo estimado >1h: +2
```

### **Confidence Score (0-1)**

```
Base: 0.0
+ Planning keywords (≥1): +0.3
+ Complexity indicators (≥1): +0.3
+ Multi-step indicators (≥2): +0.3
+ Texto detalhado (≥3 sentenças): +0.2
+ Texto muito longo (>30 palavras): +0.3
+ Organização explícita: +0.4
+ Múltiplos arquivos: +0.2
+ Tempo estimado >1h: +0.3
```

## **Decisão de Ativação**

```json
{
  "activation_logic": {
    "should_use_task_management": "complexity_score ≥ 3 OR confidence ≥ 0.5",
    "recommended_tools": [
      "view_tasklist",
      "add_tasks",
      "update_tasks",
      "reorganize_tasklist"
    ]
  }
}
```

## **Padrões de Subtarefas Automáticas**

### **API/Backend Detection**

```
Keywords: api, backend, servidor
Subtarefas:
- Configurar estrutura do projeto
- Implementar endpoints principais
- Configurar banco de dados
- Implementar testes
```

### **Frontend/UI Detection**

```
Keywords: frontend, interface, ui
Subtarefas:
- Criar componentes base
- Configurar roteamento
- Implementar estado global
- Estilização e responsividade
```

### **Authentication Detection**

```
Keywords: autenticação, login, auth
Subtarefas:
- Configurar provider de auth
- Implementar middleware de segurança
- Criar páginas de login/registro
```

## **Mensagem de Ativação**

```
🎯 Task management ativado automaticamente
Complexidade: {score}/10
Confiança: {confidence}%
Triggers: {triggers_list}
Ferramentas: {recommended_tools}
```

## **Integração com Knowledge Graph**

```json
{
  "kg_integration": {
    "learning_enabled": true,
    "pattern_recognition": true,
    "recommendation_source": "knowledge_graph",
    "fallback_confidence": 0.5
  }
}
```

## **Validação de Qualidade**

- **Precisão mínima**: 75% de detecções corretas
- **Falsos positivos**: <25%
- **Falsos negativos**: <25%
- **Tempo de resposta**: <100ms

---

**Princípio**: "Aprimore, Não Prolifere" - Regras simples substituem código complexo
**Status**: ✅ ATIVO - Substitui task_management_detector.py (196 linhas)# 📋 **REGRA: TASK AUTOMATION - DETECÇÃO AUTOMÁTICA**

_Prioridade: ALTA | Detecção e ativação automática de task management_

## **Critérios de Ativação Automática**

```json
{
  "activation_triggers": {
    "complexity_threshold": "≥3/10",
    "confidence_threshold": "≥50%",
    "mandatory_activation": [
      "complexity_score ≥ 3",
      "confidence ≥ 0.5",
      "planning_keywords detected",
      "multi_step_indicators found"
    ]
  }
}
```

## **Keywords de Detecção**

### **Planning Keywords (Peso: +0.3 confiança, +1 complexidade)**

```
planejar, organizar, estruturar, coordenar
etapas, fases, sequência, workflow
tarefas, subtarefas, breakdown, dividir
implementar, desenvolver, criar, construir
múltiplas, várias, diferentes, coordenação
```

### **Complexity Indicators (Peso: +0.3 confiança, +2 complexidade)**

```
arquitetura, sistema, integração, refatoração
migração, otimização, performance, segurança
database, api, frontend, backend
deployment, configuração, ambiente
dashboard, gráficos, relatórios, completo
jwt, autenticação, auth, crud
```

### **Multi-Step Indicators (Peso: +0.3 confiança, +1 complexidade)**

```
primeiro, segundo, terceiro, depois, então
em seguida, posteriormente, finalmente
antes de, após, durante, enquanto
```

## **Regras de Cálculo**

### **Complexity Score (1-10)**

```
Base: 1
+ Planning keywords encontradas: +1
+ Complexity indicators: +2 por indicador
+ Multi-step indicators (≥2): +1
+ Texto longo (>15 palavras): +1
+ Texto muito longo (>30 palavras): +2
+ Organização explícita: +2
+ Múltiplos arquivos: +1
+ Tempo estimado >1h: +2
```

### **Confidence Score (0-1)**

```
Base: 0.0
+ Planning keywords (≥1): +0.3
+ Complexity indicators (≥1): +0.3
+ Multi-step indicators (≥2): +0.3
+ Texto detalhado (≥3 sentenças): +0.2
+ Texto muito longo (>30 palavras): +0.3
+ Organização explícita: +0.4
+ Múltiplos arquivos: +0.2
+ Tempo estimado >1h: +0.3
```

## **Decisão de Ativação**

```json
{
  "activation_logic": {
    "should_use_task_management": "complexity_score ≥ 3 OR confidence ≥ 0.5",
    "recommended_tools": [
      "view_tasklist",
      "add_tasks",
      "update_tasks",
      "reorganize_tasklist"
    ]
  }
}
```

## **Padrões de Subtarefas Automáticas**

### **API/Backend Detection**

```
Keywords: api, backend, servidor
Subtarefas:
- Configurar estrutura do projeto
- Implementar endpoints principais
- Configurar banco de dados
- Implementar testes
```

### **Frontend/UI Detection**

```
Keywords: frontend, interface, ui
Subtarefas:
- Criar componentes base
- Configurar roteamento
- Implementar estado global
- Estilização e responsividade
```

### **Authentication Detection**

```
Keywords: autenticação, login, auth
Subtarefas:
- Configurar provider de auth
- Implementar middleware de segurança
- Criar páginas de login/registro
```

## **Mensagem de Ativação**

```
🎯 Task management ativado automaticamente
Complexidade: {score}/10
Confiança: {confidence}%
Triggers: {triggers_list}
Ferramentas: {recommended_tools}
```

## **Integração com Knowledge Graph**

```json
{
  "kg_integration": {
    "learning_enabled": true,
    "pattern_recognition": true,
    "recommendation_source": "knowledge_graph",
    "fallback_confidence": 0.5
  }
}
```

## **Validação de Qualidade**

- **Precisão mínima**: 75% de detecções corretas
- **Falsos positivos**: <25%
- **Falsos negativos**: <25%
- **Tempo de resposta**: <100ms

---

**Princípio**: "Aprimore, Não Prolifere" - Regras simples substituem código complexo
**Status**: ✅ ATIVO - Substitui task_management_detector.py (196 linhas)
