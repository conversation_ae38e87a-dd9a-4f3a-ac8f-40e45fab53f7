# System Patterns - VIBECODE V1.0 Architecture

## 🏗️ CORE ARCHITECTURE PATTERNS

### **Enhanced Script Validation Pattern**

**Date Added**: 2025-01-16
**Impact**: High - System Reliability & Memory Integration

Novo padrão implementado para validação completa do sistema:

```python
# Enhanced Validation Pattern
class EnhancedValidator:
    def __init__(self):
        self.workspace_root = get_workspace_root()
        self.memory_root = workspace_root / "memory-bank"
        self.cursor_dir = workspace_root / ".cursor"
        self.augment_dir = workspace_root / ".augment"
        self.validation_metrics = {}

    def comprehensive_validation(self):
        # 1. Environment validation
        # 2. MCP comprehensive check (all 6 MCPs)
        # 3. .cursor ↔ .augment sync validation (IMPERATIVE)
        # 4. Memory bank structure validation
        # 5. Knowledge Graph integration check
        # 6. Automatic memory bank updates
        # 7. Quality gate validation (≥8/10)
```

#### Key Features:

- **Comprehensive MCP Validation**: All 6 MCPs (desktop-commander, sequential-thinking, context7, tavily, exa, sentry)
- **Imperative Sync Rule**: .cursor ↔ .augment synchronization validation
- **Memory Bank Integration**: Automatic updates to hierarchical files
- **Knowledge Graph Metrics**: Operational metrics collection and storage
- **API Cost Optimization**: ≥70% reduction through batch operations

### **Native Task Management Pattern**

**Date Added**: 2025-01-16
**Impact**: High - Task System Integration

Padrão para integração com native task management:

```json
{
  "native_task_pattern": {
    "augment_config": ".augment/task-management.json",
    "cursor_config": ".cursor/config/cursor-tasks.json",
    "primary_storage": "memory-bank/task-storage.md",
    "fallback_storage": "memory-bank/tasks.md",
    "integration_scoring": "native_integration_score >= 70%"
  }
}
```

## 🏗️ PREVIOUS ARCHITECTURE PATTERNS

### **Hybrid Memory System**

VIBECODE V1.0 implementa um sistema híbrido que combina:

- **VIBECODE Core**: Autoridade central via master_rule.mdc
- **Cursor Memory Bank**: Persistência e otimização de contexto
- **Knowledge Graph**: Aprendizado contínuo entre sessões
- **MCP Orchestration**: Coordenação multi-tier de ferramentas

### **Agent Consolidation Pattern**

Redução de 9→4 agentes especializados:

```
TECHNICAL_ARCHITECT (7-10)    ← architect + coder + ruler
OPERATIONS_COORDINATOR (1-6)  ← manager + executor + prd_specialist
RESEARCH_STRATEGIST (3-8)     ← enhanced search_strategist
QUALITY_GUARDIAN (1-10)       ← boomerang + self_reflection + prompt_creator
```

## 🔄 WORKFLOW PATTERNS

### **7-Step Mandatory Process**

Padrão obrigatório para todas as tarefas:

```mermaid
graph TD
    A[1. ANALYZE] --> B[2. SELECT]
    B --> C[3. EXECUTE]
    C --> D[4. REFLECT]
    D --> E[5. REFINE]
    E --> F[6. VALIDATE]
    F --> G[7. LEARN]

    D -->|Quality <8/10| E
    E -->|Still <8/10| D
    F -->|≥8/10| G
```

### **Phase 0.5 Initialization**

Consulta obrigatória antes de qualquer tarefa:

```bash
# Mandatory scripts execution
uv run python @project-core/scripts/sync_ai_rules.py
uv run python @project-core/scripts/validate_knowledge_graph.py
uv run python @project-core/scripts/vibecode_main.py --status
```

## 🔧 MCP ORCHESTRATION PATTERNS

### **Tier-Based Tool Selection**

```
TIER 0: Sentry          → All operations (monitoring)
TIER 1: Sequential      → Complexity ≥7 (complex thinking)
TIER 2: Task Manager    → Complexity ≥3 (organization)
TIER 3: Research        → Context7→Tavily→Exa (priority order)
TIER 4: Specialized     → desktop-commander, figma, playwright
```

### **File Operation Matrix**

```
≤200 lines  → Desktop Commander MCP
>200 lines  → Cursor Editor
Verification → Always read after write
Chunking    → ≤30 lines for optimal performance
```

## 📁 DIRECTORY STRUCTURE PATTERNS

### **Rule Organization**

```
.cursor/rules/
├── master_rule.mdc           # Central authority
├── visual-maps/              # Process visualization
│   ├── van-mode-map.mdc
│   ├── plan-mode-map.mdc
│   ├── creative-mode-map.mdc
│   ├── implement-mode-map.mdc
│   └── qa-mode-map.mdc
├── phases/                   # Phase-specific rules
│   ├── analysis/
│   ├── planning/
│   ├── creative/
│   ├── implementation/
│   └── validation/
└── levels/                   # Complexity levels
    ├── level-1/
    ├── level-2/
    ├── level-3/
    └── level-4/
```

### **Universal Scripts Standard**

```
@project-core/scripts/       # UNIVERSAL DEFAULT (MANDATORY)
├── vibecode_main.py         # Main system script
├── sync_ai_rules.py         # Enhanced rule synchronization
├── validate_*.py            # Validation scripts
├── mcp_*.py                # MCP-related scripts
└── ...                     # All Python execution scripts
```

### **Memory Bank Structure**

```
memory-bank/
├── projectbrief.md          # Foundation document
├── activeContext.md         # Current focus
├── progress.md              # Implementation status
├── systemPatterns.md        # This file
├── techContext.md           # Technical details
└── tasks.md                 # Source of truth
```

## 🎯 QUALITY PATTERNS

### **Quality Gate System**

```
Minimum Standard: ≥8/10 (mandatory)
Target Standard:  ≥9/10 (preferred)
Validation:       100% compliance required
Documentation:    100% coverage required
```

### **"Enhance, Don't Proliferate" Pattern**

```
Code Reuse Target: ≥85%
File Enhancement:  Preferred over creation
Deduplication:     Mandatory before new files
Consolidation:     Continuous optimization
```

## 🧠 MEMORY INTEGRATION PATTERNS

### **JIT Rule Loading**

```mermaid
graph LR
    Entry[Entry Point] --> Core[Core Rules]
    Core --> Phase[Phase Rules]
    Phase --> Level[Level Rules]
    Level --> Execute[Execute]
```

### **Context Optimization**

- **60% Reduction Target**: Through JIT loading
- **Visual Process Maps**: Reduce cognitive load
- **Mode Specialization**: Targeted rule sets
- **Memory Persistence**: Cross-session continuity

## 🔄 MODE TRANSITION PATTERNS

### **Cursor Memory Bank Integration**

```
VAN Mode      → Initialization & context setup
PLAN Mode     → Task planning & breakdown
CREATIVE Mode → Design decisions & architecture
IMPLEMENT Mode → Code implementation & testing
QA Mode       → Validation & quality assurance
```

### **VIBECODE Agent Mapping**

```
Manager (1-4)     → VAN Mode
Advisor (2-5)     → PLAN Mode
Strategist (3-6)  → CREATIVE Mode
Executor (4-7)    → IMPLEMENT Mode
Coder (6-9)       → IMPLEMENT Mode
Architect (8-10)  → CREATIVE Mode
```

## 🔐 SECURITY PATTERNS

### **UV Package Manager Compliance**

```python
# All Python scripts MUST use UV
uv run python script.py
uv add package-name
uv remove package-name
```

### **Environment Management**

```
Environment File: @project-core/config/environment-complete.env
API Keys:         Centralized and secure
MCP Config:       .cursor/mcp.json
Validation:       Mandatory before execution
```

## 📊 MONITORING PATTERNS

### **Sentry Integration**

```
Error Tracking:   All operations monitored
Performance:      Execution time tracking
Quality:          Continuous quality assessment
Compliance:       VIBECODE standard validation
```

### **Knowledge Graph Updates**

```
Learning Capture: After each task completion
Pattern Storage:  Successful approaches documented
Failure Analysis: Errors analyzed and stored
Continuous Improvement: Patterns refined over time
```

## 🚀 PERFORMANCE PATTERNS

### **Optimization Strategies**

- **Chunked File Operations**: ≤30 lines optimal
- **Lazy Loading**: Rules loaded on demand
- **Caching**: Frequent patterns cached
- **Parallel Processing**: Where applicable

### **Scalability Patterns**

- **Modular Architecture**: Independent components
- **Plugin System**: MCP tool integration
- **Configuration Driven**: Behavior via config
- **Version Management**: Backward compatibility

## 🔄 INTEGRATION PATTERNS

### **Cursor Memory Bank Bridge**

```python
# cursor_memory_bridge.py pattern
class CursorMemoryBridge:
    def sync_context(self) -> None
    def update_memory(self, data: Dict) -> None
    def load_phase_rules(self, phase: str) -> List[Rule]
    def optimize_context(self) -> ContextOptimization
```

### **Cross-Session Continuity**

```
Session Start:    Load memory bank state
Task Execution:   Update memory continuously
Session End:      Persist all changes
Next Session:     Resume from exact state
```

## 📈 SUCCESS PATTERNS

### **Validated Approaches**

- ✅ **Context7 Research**: Deep technical analysis
- ✅ **Phased Implementation**: Reduced complexity
- ✅ **Quality Gates**: Consistent high standards
- ✅ **MCP Orchestration**: Tool coordination
- ✅ **Memory Persistence**: Cross-session state

### **Emerging Patterns**

- 🔄 **Visual Process Maps**: Workflow clarity
- 🔄 **JIT Rule Loading**: Context optimization
- 🔄 **Mode Specialization**: Targeted workflows
- 🔄 **Hybrid Architecture**: Best of both systems

---

**Pattern Quality**: 9/10 | **Implementation**: 70% | **Validation**: Ongoing
**Last Updated**: 2025-07-16 00:18 UTC

## Augment Integration Patterns

- NUNCA quebrar regras do sistema - sempre encontrar soluções técnicas para cumprir limitações como ta...
