# 📋 KIRO MASTER RULE - UNIFIED CONFIGURATION

## Core Principles

```json
{
  "principle": "Enhance, Don't Proliferate (≥85% reuse)",
  "quality_threshold": 8,
  "confidence_minimum": 90,
  "system_status": "PRODUCTION_READY"
}
```

## Mandatory Workflow (7 Steps)

1. **ANALYZE** → Evaluate complexity (1-10)
2. **SELECT** → Choose tools based on complexity
3. **EXECUTE** → Use appropriate MCP tools
4. **REFLECT** → Evaluate output quality
5. **REFINE** → Improve if quality <8/10
6. **VALIDATE** → Confirm quality ≥8/10
7. **LEARN** → Update knowledge and context

## File Operations

```json
{
  "routing": {
    "≤200_lines": "native-kiro-tools",
    ">200_lines": "kiro-editor"
  },
  "always_verify": "read file after write",
  "quality_check": "mandatory"
}
```

## MCP Tool Selection

```json
{
  "complexity_routing": {
    "≥7": "sequential-thinking",
    "≥3": "native-task-management",
    "research_MANDATORY": {
      "tools": ["context7", "tavily", "exa"],
      "order": "ALWAYS in sequence: context7 → tavily → exa",
      "activation": "AUTOMATIC - Any research keyword activates all 3 MCPs",
      "validation": "MANDATORY - Synthesis from all sources ≥8/10"
    }
  }
}
```

## Research Protocol (MANDATORY)

**Auto-detection keywords:**
`search, find, documentation, tutorial, how to, example, guide, library, framework, API, best practices, implementation, configuration, integration`

**Enforcement:**
- **Step 1**: context7-mcp - ALWAYS first for technical documentation
- **Step 2**: tavily-mcp - ALWAYS second for general web search  
- **Step 3**: exa-mcp - ALWAYS third for alternative search
- **Quality**: ≥8/10 synthesis required
- **Sources**: Minimum 3 sources documented

## Quality Gates

- **Minimum quality**: 8/10 (mandatory)
- **Auto-refinement**: if quality <8/10
- **Completeness**: 100% requirements met
- **File verification**: Always read after write
- **Research quality**: ≥8/10 with ALL 3 MCPs used (mandatory)

## Spec Development Workflow

When working with specs:
1. **Requirements** → Create detailed requirements with user stories
2. **Design** → Develop comprehensive design document
3. **Tasks** → Create actionable implementation plan
4. **Execute** → Implement one task at a time
5. **Review** → User approval at each phase

## Task Management

- **Simple tasks (1-4)**: Use native Kiro tools
- **Medium tasks (5-7)**: Add sequential-thinking
- **Complex tasks (8-10)**: Full MCP orchestration + manual coordination

## Performance Standards

- **Response time**: <30 seconds for standard operations
- **Quality score**: ≥8/10 for all outputs
- **Completeness**: 100% requirement coverage
- **Consistency**: Follow established patterns

---

**Remember**: Quality ≥8/10 is MANDATORY. Always follow the 7-step workflow. "Enhance, Don't Proliferate".