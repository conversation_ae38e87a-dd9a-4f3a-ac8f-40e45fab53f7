# 📋 **WARP CLI - MASTER RULES V1.0**

_Baseado no VIBECODE V1.0 Unified Configuration_

## **Core Principles**

```json
{
  "principle": "Aprimore, Não Prolifere (≥85% reuse)",
  "quality_threshold": 8,
  "confidence_minimum": 90,
  "root_path": "E:/VIBECODE",
  "system_status": "WARP_OPTIMIZED"
}
```

## **WORKFLOW OBRIGATÓRIO - 7 ETAPAS**

### **Phase 1: Analyze**
- Avaliar complexidade da tarefa (1-10)
- Determinar se precisa de task management (≥3)
- Identificar tipo de operação

### **Phase 2: Select**
- **Complexidade 1-3**: Operações básicas
- **Complexidade 4-6**: Task management + ferramentas intermediárias
- **Complexidade 7-10**: Ferramentas avançadas + raciocínio sequencial

### **Phase 3: Execute**
- Implementar com tracking de qualidade
- Usar file operations baseado em tamanho:
  - ≤200 linhas: Ferramentas rápidas
  - >200 linhas: Editores robustos

### **Phase 4: Reflect**
- Avaliar qualidade da saída (≥8/10)
- Verificar completude (100% requisitos)

### **Phase 5: Refine (Se necessário)**
- Se qualidade <8/10: aplicar melhorias
- Máximo 3 iterações de refinamento

### **Phase 6: Validate**
- Confirmar qualidade final ≥8/10
- Validar funcionamento

### **Phase 7: Learn**
- Documentar padrões para reuso
- Atualizar knowledge base

## **RESEARCH PROTOCOL OBRIGATÓRIO**

Para keywords de pesquisa detectadas:
1. **Buscar documentação técnica primeiro**
2. **Pesquisa web geral segundo**
3. **Fontes alternativas terceiro**
4. **Síntese obrigatória** de todas as fontes

### **Keywords de Detecção**
```
pesquisar, buscar, encontrar, documentação, tutorial, como fazer, 
exemplo, guia, biblioteca, framework, API, best practices, 
implementação, configuração, integração
```

## **TASK AUTOMATION**

### **Ativação Automática**
- **Complexity ≥3**: Task management ativo
- **Planning keywords**: Automaticamente ativado
- **Multi-step indicators**: Breakdown automático

### **Keywords Planning**
```
planejar, organizar, estruturar, coordenar, etapas, fases, 
sequência, workflow, tarefas, subtarefas, implementar, desenvolver
```

### **Complexity Indicators**
```
arquitetura, sistema, integração, refatoração, migração, 
otimização, database, api, frontend, backend, deployment
```

## **CODING STANDARDS**

### **TypeScript/Next.js 14**
- **Function keyword** para componentes
- **Interfaces over types**
- **Early returns + guard clauses**
- **Server Components** por padrão
- **RORO pattern** (Receive Object, Return Object)

### **Error Handling**
```typescript
// ✅ CORRETO: Guard clauses + early returns
function processUser(user: User | null): ProcessedUser {
  if (!user) throw new Error('User is required')
  if (!user.isActive) throw new Error('User is not active')
  
  // Happy path last
  return {
    id: user.id,
    displayName: user.profile.name
  }
}
```

### **Component Structure**
```typescript
// ✅ CORRETO: Functional component
function UserProfile({ user }: UserProfileProps) {
  return (
    <div className="space-y-4">
      <h1>{user.name}</h1>
    </div>
  )
}

interface UserProfileProps {
  user: User
}
```

## **FILE OPERATIONS**

### **Automatic Tool Selection**
```json
{
  "routing": {
    "≤200_lines": "fast_tools",
    ">200_lines": "robust_editors"
  },
  "always_verify": "read file after write",
  "backup_location": "E:/CODE-BACKUP"
}
```

## **PERFORMANCE TARGETS**

- **Rule lookup**: <100ms
- **Quality score**: ≥8/10 (obrigatório)
- **Completeness**: 100% requirements met
- **Bundle size**: <200KB gzipped
- **Core Web Vitals**: LCP <2.5s, FID <100ms

## **PROJECT STRUCTURE**

```
src/
├── app/              # Next.js App Router
├── components/       # Componentes reutilizáveis
│   ├── ui/          # shadcn/ui
│   └── features/    # Feature components
├── lib/             # Utilitários
├── hooks/           # Custom hooks
├── types/           # TypeScript types
└── styles/          # Estilos globais
```

## **NAMING CONVENTIONS**

- **Componentes**: PascalCase
- **Arquivos**: PascalCase.tsx
- **Hooks**: camelCase com 'use' prefix
- **Utilitários**: camelCase
- **Constantes**: UPPER_SNAKE_CASE

## **QUALITY GATES**

### **Obrigatório**
- ✅ Qualidade ≥8/10
- ✅ 100% dos requisitos atendidos  
- ✅ Error handling implementado
- ✅ TypeScript strict mode
- ✅ Testes passando (≥80% coverage)

### **Security**
- ✅ Input validation (Zod)
- ✅ Sanitização de dados
- ✅ Authentication middleware
- ✅ Security headers

## **BATCH OPERATIONS**

### **API Optimization**
- **Consolidar** múltiplas operações similares
- **Target**: ≥70% redução em chamadas
- **Scripts** para operações complexas
- **Una chamada** em vez de múltiplas sequenciais

---

**LEMBRE-SE**: 
- Qualidade ≥8/10 é **OBRIGATÓRIA**
- Sempre seguir o workflow de 7 passos
- "Aprimore, Não Prolifere"
- Verificar após operações de arquivo

**Status**: ✅ ATIVO - Otimizado para Warp CLI
