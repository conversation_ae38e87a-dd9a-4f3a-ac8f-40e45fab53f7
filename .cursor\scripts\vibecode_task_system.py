#!/usr/bin/env python3
"""
🎯 VIBECODE V1.0 - Task System Status Checker ENHANCED
Verificação completa do sistema de task management com memory bank integration
Melhorado: Comprehensive task validation, .augment integration, KG metrics
Princípio: "Aprimore, Não Prolifere" - Máxima consolidação, 100% funcionalidade

🚀 API COST OPTIMIZATION: Batch status checking para reduzir ≥70% chamadas MCP
📊 KNOWLEDGE GRAPH: Integração completa com operational metrics e learning insights
🔄 TASK MANAGEMENT: Validação native tools + .augment/task-management.json
🎯 CURSOR INTEGRATION: Validação .cursor/config/cursor-tasks.json

NOTA: Sistema completo integrado com memory-bank/python/ e native task managers
"""

import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('vibecode.task_system')


def get_workspace_root() -> Path:
    """
    Detecta o workspace VIBECODE dinamicamente
    Busca pelo diretório que contém .cursor e tem nome VIBECODE
    """
    current = Path.cwd()

    # Primeiro tenta encontrar VIBECODE subindo na hierarquia
    while current != current.parent:
        if (current / '.cursor').exists() and current.name == 'VIBECODE':
            return current
        current = current.parent

    # Se não encontrou, assume o diretório atual se contém .cursor
    if (Path.cwd() / '.cursor').exists():
        return Path.cwd()

    # Fallback: assume estrutura padrão
    return Path.cwd()


class TaskSystemChecker:
    """
    Verificador simplificado do sistema de task management
    Foco em status e validações básicas apenas
    """

    def __init__(self):
        self.workspace_root = get_workspace_root()
        self.memory_root = self.workspace_root / "memory-bank"
        self.python_dir = self.memory_root / "python"
        self.cursor_dir = self.workspace_root / ".cursor"
        self.augment_dir = self.workspace_root / ".augment"
        self.task_metrics = {}

        logger.info(f"🎯 Enhanced Task System Checker - Workspace: {self.workspace_root}")
        logger.info(f"📊 Memory bank integration: {self.memory_root}")
        logger.info(f"🔄 Native task management validation: Cursor + Augment")

    def check_task_system_availability(self) -> Dict[str, Any]:
        """Verifica disponibilidade dos componentes do sistema de task"""
        status = {
            "timestamp": datetime.now().isoformat(),
            "workspace_root": str(self.workspace_root),
            "system_available": False,
            "components": {},
            "errors": []
        }

        try:
            # Verifica memory-bank
            if self.memory_root.exists():
                status["components"]["memory_bank"] = "available"
            else:
                status["components"]["memory_bank"] = "missing"
                status["errors"].append("Memory bank directory not found")

            # Verifica python components
            if self.python_dir.exists():
                status["components"]["python_dir"] = "available"

                # Verifica arquivos Python essenciais (conforme master_rule.mdc)
                essential_files = [
                    "knowledge_graph_manager.py",
                    "operational_context_manager.py",
                    "automatic_memory_updater.py",
                    "cursor_memory_bridge.py"
                ]

                found_files = []
                for file in essential_files:
                    if (self.python_dir / file).exists():
                        found_files.append(file)

                status["components"]["python_files"] = {
                    "found": found_files,
                    "total": len(essential_files)
                }
            else:
                status["components"]["python_dir"] = "missing"
                status["errors"].append("Python directory not found in memory-bank")

            # Verifica task-storage.md (primary) or tasks.md (fallback)
            tasks_file = self.memory_root / "task-storage.md"
            fallback_file = self.memory_root / "tasks.md"
            if tasks_file.exists():
                status["components"]["tasks_file"] = "available"
            elif fallback_file.exists():
                status["components"]["tasks_file"] = "available (fallback)"
                status["warnings"] = status.get("warnings", [])
                status["warnings"].append("Using fallback tasks.md file")
            else:
                status["components"]["tasks_file"] = "missing"
                status["errors"].append("task-storage.md not found in memory-bank")

            # Determina disponibilidade geral
            critical_components = [
                status["components"].get("memory_bank") == "available",
                status["components"].get("python_dir") == "available",
                status["components"].get("tasks_file") == "available"
            ]

            status["system_available"] = all(critical_components)

        except Exception as e:
            status["errors"].append(f"Error checking system: {e}")
            status["system_available"] = False

        return status

    def get_simple_task_info(self) -> Dict[str, Any]:
        """Obtém informações básicas sobre tasks"""
        info = {
            "tasks_file_exists": False,
            "tasks_count": 0,
            "last_modified": None,
            "errors": []
        }

        try:
            tasks_file = self.memory_root / "task-storage.md"
            fallback_file = self.memory_root / "tasks.md"

            # Try primary file first, then fallback
            active_file = tasks_file if tasks_file.exists() else fallback_file if fallback_file.exists() else None

            if active_file:
                info["tasks_file_exists"] = True
                info["active_file"] = active_file.name
                info["last_modified"] = datetime.fromtimestamp(
                    active_file.stat().st_mtime
                ).isoformat()

                # Conta tasks simples (linhas que começam com - [ ] ou - [x])
                try:
                    with open(active_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        task_lines = [l for l in lines if l.strip().startswith('- [')]
                        info["tasks_count"] = len(task_lines)
                except Exception as e:
                    info["errors"].append(f"Error reading tasks file: {e}")
            else:
                info["errors"].append("task-storage.md not found")

        except Exception as e:
            info["errors"].append(f"Error getting task info: {e}")

        return info

    def check_knowledge_graph_integration(self) -> Dict[str, Any]:
        """Verifica integração com Knowledge Graph Manager"""
        integration_status = {
            "kg_manager_available": False,
            "kg_directory_exists": False,
            "recent_insights": 0,
            "recent_metrics": 0,
            "errors": []
        }

        try:
            # Verifica Knowledge Graph Manager
            kg_manager_path = self.python_dir / "knowledge_graph_manager.py"
            if kg_manager_path.exists():
                integration_status["kg_manager_available"] = True

            # Verifica diretório knowledge_graph
            kg_dir = self.python_dir / "knowledge_graph"
            if kg_dir.exists():
                integration_status["kg_directory_exists"] = True

            # Verifica insights recentes
            insights_dir = self.python_dir / "learning_insights"
            if insights_dir.exists():
                insight_files = list(insights_dir.glob("*.json"))
                integration_status["recent_insights"] = len(insight_files)

            # Verifica métricas recentes
            metrics_dir = self.python_dir / "operational_metrics"
            if metrics_dir.exists():
                metric_files = list(metrics_dir.glob("*.json"))
                integration_status["recent_metrics"] = len(metric_files)

        except Exception as e:
            integration_status["errors"].append(f"Error checking KG integration: {e}")

        return integration_status

    def check_native_task_management(self) -> Dict[str, Any]:
        """
        Verifica integração com native task management (Cursor + Augment)
        Conforme master_rule.mdc: task-management.json e cursor-tasks.json
        """
        native_status = {
            "augment_task_config": False,
            "cursor_task_config": False,
            "task_storage_primary": False,
            "task_storage_fallback": False,
            "native_integration_score": 0,
            "errors": []
        }

        try:
            # Verifica .augment/task-management.json
            augment_task_config = self.augment_dir / "task-management.json"
            if augment_task_config.exists():
                try:
                    with open(augment_task_config, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    native_status["augment_task_config"] = True
                    self.task_metrics['augment_config_valid'] = True
                except json.JSONDecodeError:
                    native_status["errors"].append("task-management.json inválido")
                    self.task_metrics['augment_config_valid'] = False
            else:
                native_status["errors"].append("task-management.json não encontrado em .augment")

            # Verifica .cursor/config/cursor-tasks.json (se existir)
            cursor_config_dir = self.cursor_dir / "config"
            cursor_task_config = cursor_config_dir / "cursor-tasks.json"
            if cursor_task_config.exists():
                try:
                    with open(cursor_task_config, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    native_status["cursor_task_config"] = True
                    self.task_metrics['cursor_config_valid'] = True
                except json.JSONDecodeError:
                    native_status["errors"].append("cursor-tasks.json inválido")
                    self.task_metrics['cursor_config_valid'] = False

            # Verifica task storage files
            primary_storage = self.memory_root / "task-storage.md"
            fallback_storage = self.memory_root / "tasks.md"

            if primary_storage.exists():
                native_status["task_storage_primary"] = True
                self.task_metrics['primary_storage'] = True
            elif fallback_storage.exists():
                native_status["task_storage_fallback"] = True
                self.task_metrics['fallback_storage'] = True
            else:
                native_status["errors"].append("Nenhum arquivo de task storage encontrado")

            # Calcula score de integração
            integration_factors = [
                native_status["augment_task_config"],
                native_status["task_storage_primary"] or native_status["task_storage_fallback"],
                len(native_status["errors"]) == 0
            ]
            native_status["native_integration_score"] = (sum(integration_factors) / len(integration_factors)) * 100

        except Exception as e:
            native_status["errors"].append(f"Erro na verificação native task management: {e}")

        return native_status

    def collect_comprehensive_task_metrics(self) -> Dict[str, Any]:
        """
        Coleta métricas abrangentes do sistema de tasks
        Para integração com Knowledge Graph Manager
        """
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "workspace_root": str(self.workspace_root),
            "task_counts": {},
            "system_health": {},
            "performance_metrics": {},
            "errors": []
        }

        try:
            # Métricas de contagem de tasks
            task_info = self.get_simple_task_info()
            metrics["task_counts"] = {
                "total_tasks": task_info.get("tasks_count", 0),
                "active_file": task_info.get("active_file", "none"),
                "last_modified": task_info.get("last_modified")
            }

            # Métricas de saúde do sistema
            system_status = self.check_task_system_availability()
            kg_integration = self.check_knowledge_graph_integration()
            native_management = self.check_native_task_management()

            metrics["system_health"] = {
                "system_available": system_status.get("system_available", False),
                "kg_manager_available": kg_integration.get("kg_manager_available", False),
                "native_integration_score": native_management.get("native_integration_score", 0),
                "components_status": system_status.get("components", {})
            }

            # Métricas de performance
            metrics["performance_metrics"] = {
                "recent_insights": kg_integration.get("recent_insights", 0),
                "recent_metrics": kg_integration.get("recent_metrics", 0),
                "task_metrics": self.task_metrics
            }

            # Salva métricas se diretório existe
            metrics_dir = self.python_dir / "operational_metrics"
            if metrics_dir.exists():
                metrics_file = metrics_dir / f"task_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(metrics_file, 'w', encoding='utf-8') as f:
                    json.dump(metrics, f, indent=2, ensure_ascii=False)

                logger.info(f"📊 Task metrics salvos: {metrics_file.name}")

        except Exception as e:
            metrics["errors"].append(f"Erro ao coletar métricas: {e}")
            logger.error(f"Erro ao coletar task metrics: {e}")

        return metrics

    def generate_status_report(self) -> str:
        """
        Gera relatório completo de status do sistema
        Inclui: System status, Task info, KG integration, Native management
        """
        system_status = self.check_task_system_availability()
        task_info = self.get_simple_task_info()
        kg_integration = self.check_knowledge_graph_integration()
        native_management = self.check_native_task_management()
        comprehensive_metrics = self.collect_comprehensive_task_metrics()

        report = f"""
🎯 **VIBECODE TASK SYSTEM STATUS REPORT (ENHANCED)**

**System Overview:**
- Workspace: {system_status['workspace_root']}
- System Available: {'✅ YES' if system_status['system_available'] else '❌ NO'}
- Native Integration Score: {native_management['native_integration_score']:.1f}%
- Timestamp: {system_status['timestamp']}

**Components Status:**
"""

        for component, status in system_status["components"].items():
            if isinstance(status, dict):
                # Para python_files
                found = status.get("found", [])
                total = status.get("total", 0)
                report += f"- {component}: {len(found)}/{total} files found\n"
            else:
                status_icon = "✅" if status == "available" else "❌"
                report += f"- {component}: {status_icon} {status}\n"

        report += f"""
**Task Information:**
- Tasks file exists: {'✅ YES' if task_info['tasks_file_exists'] else '❌ NO'}
- Tasks count: {task_info['tasks_count']}
- Last modified: {task_info['last_modified'] or 'N/A'}

**Knowledge Graph Integration:**
- KG Manager available: {'✅ YES' if kg_integration['kg_manager_available'] else '❌ NO'}
- KG Directory exists: {'✅ YES' if kg_integration['kg_directory_exists'] else '❌ NO'}
- Recent insights: {kg_integration['recent_insights']}
- Recent metrics: {kg_integration['recent_metrics']}

**Native Task Management:**
- Augment config: {'✅ YES' if native_management['augment_task_config'] else '❌ NO'}
- Cursor config: {'✅ YES' if native_management['cursor_task_config'] else '❌ NO'}
- Primary storage: {'✅ YES' if native_management['task_storage_primary'] else '❌ NO'}
- Fallback storage: {'✅ YES' if native_management['task_storage_fallback'] else '❌ NO'}

**Comprehensive Metrics:**
- Total tasks: {comprehensive_metrics['task_counts']['total_tasks']}
- System health score: {comprehensive_metrics['system_health'].get('native_integration_score', 0):.1f}%
- Performance metrics collected: {'✅ YES' if len(comprehensive_metrics['performance_metrics']) > 0 else '❌ NO'}

**Errors/Warnings:**
"""

        all_errors = (system_status["errors"] + task_info["errors"] +
                     kg_integration["errors"] + native_management["errors"] +
                     comprehensive_metrics["errors"])
        if all_errors:
            for error in all_errors:
                report += f"⚠️  {error}\n"
        else:
            report += "✅ No errors found\n"

        report += f"""
**Recommendations:**
"""
        if not system_status["system_available"]:
            report += "- Run system setup to initialize missing components\n"
            report += "- Check memory-bank directory structure\n"

        if not task_info["tasks_file_exists"]:
            report += "- Create task-storage.md file in memory-bank/\n"

        # Enhanced quality assessment based on comprehensive system health
        quality_factors = [
            system_status["system_available"],
            task_info["tasks_file_exists"],
            kg_integration["kg_manager_available"],
            kg_integration["kg_directory_exists"],
            native_management["native_integration_score"] >= 70,  # Native integration threshold
            comprehensive_metrics["system_health"]["system_available"],
            len(all_errors) == 0
        ]
        quality_score = (sum(quality_factors) / len(quality_factors)) * 10
        quality_status = "PASS" if quality_score >= 8.0 else "FAIL"

        report += f"""
**Quality Assessment:**
- Quality Score: {quality_score:.1f}/10 ({quality_status})
- Quality Gate: {'✅ PASSED' if quality_status == 'PASS' else '❌ FAILED'} (minimum 8.0/10)
"""

        if system_status["system_available"] and task_info["tasks_file_exists"] and quality_status == "PASS":
            report += "✅ System appears to be ready for operation\n"
        elif quality_status == "FAIL":
            report += f"⚠️ System quality score {quality_score:.1f}/10 below minimum threshold\n"

        return report


def main():
    """Função principal"""
    import argparse

    parser = argparse.ArgumentParser(description='VIBECODE Task System Status Checker')
    parser.add_argument('--status', action='store_true', help='Show system status')
    parser.add_argument('--json', action='store_true', help='Output in JSON format')
    parser.add_argument('--quiet', action='store_true', help='Minimal output')

    args = parser.parse_args()

    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)

    checker = TaskSystemChecker()

    if args.json:
        # Output JSON
        status = checker.check_task_system_availability()
        task_info = checker.get_simple_task_info()
        kg_integration = checker.check_knowledge_graph_integration()
        output = {
            "system_status": status,
            "task_info": task_info,
            "knowledge_graph_integration": kg_integration
        }
        print(json.dumps(output, indent=2, ensure_ascii=False))
    else:
        # Output formatted report
        report = checker.generate_status_report()
        print(report)

    # Return exit code based on system availability
    status = checker.check_task_system_availability()
    return 0 if status["system_available"] else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
