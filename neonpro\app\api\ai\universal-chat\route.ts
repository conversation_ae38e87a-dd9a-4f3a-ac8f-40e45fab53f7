// Universal AI Chat Endpoint for NeonPro (Epic 4 - Story 4.1)
// app/api/ai/universal-chat/route.ts

import { UniversalChatContext } from "@/app/lib/ai/types";
import { createClient } from "@/app/utils/supabase/server";
import { NextRequest, NextResponse } from "next/server";

// Rate limiting and security imports (to be implemented)
// import { rateLimit } from '@/app/lib/rate-limit'
// import { validateAPIKey } from '@/app/lib/auth'

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { query, context, sessionId } = body;

    if (!query || typeof query !== "string") {
      return NextResponse.json(
        { success: false, error: "Query is required and must be a string" },
        { status: 400 }
      );
    }

    // Get user from session
    const supabase = await createClient();
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get user's clinic ID
    const { data: profile } = await supabase
      .from("profiles")
      .select("clinic_id")
      .eq("id", userId)
      .single();

    if (!profile?.clinic_id) {
      return NextResponse.json(
        { success: false, error: "User clinic not found" },
        { status: 403 }
      );
    }

    const clinicId = profile.clinic_id;

    // Rate limiting (to be implemented)
    // const rateLimitResult = await rateLimit(userId)
    // if (!rateLimitResult.success) {
    //   return NextResponse.json(
    //     { success: false, error: 'Rate limit exceeded' },
    //     { status: 429 }
    //   )
    // }

    // Build context if not provided
    let chatContext: UniversalChatContext = context;
    if (!chatContext) {
      chatContext = await buildUniversalContext(supabase, clinicId);
    }

    // Initialize AI chat engine and process query
    const chatEngine = new NeonProAIChatEngine();
    const response = await chatEngine.processUniversalQuery(
      query,
      chatContext,
      userId,
      clinicId
    );

    // Log successful interaction
    console.log(
      `AI Chat - User: ${userId}, Clinic: ${clinicId}, Query length: ${query.length}`
    );

    return NextResponse.json({
      success: true,
      response: response.chatResponse,
      suggestions: response.suggestions,
      predictions: response.predictions,
      automations: response.automations,
      metadata: response.metadata,
      sessionId: sessionId || generateSessionId(),
    });
  } catch (error) {
    console.error("Error in AI chat endpoint:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Internal server error";

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * Build universal context from current clinic data
 */
async function buildUniversalContext(
  supabase: any,
  clinicId: string
): Promise<UniversalChatContext> {
  try {
    const [appointmentsData, financialData, clinicalData, biData] =
      await Promise.all([
        buildAppointmentsContext(supabase, clinicId),
        buildFinancialContext(supabase, clinicId),
        buildClinicalContext(supabase, clinicId),
        buildBusinessIntelligenceContext(supabase, clinicId),
      ]);

    return {
      appointments: appointmentsData,
      financial: financialData,
      clinical: clinicalData,
      businessIntelligence: biData,
    };
  } catch (error) {
    console.error("Error building universal context:", error);
    // Return minimal context on error
    return {
      appointments: {
        upcoming: [],
        conflicts: {
          totalConflicts: 0,
          conflictTypes: {},
          resolutionSuggestions: [],
        },
        utilization: {
          professionalId: "",
          utilizationRate: 0,
          appointmentsCount: 0,
          availableSlots: 0,
          efficiency: 0,
        },
        patientFlow: {
          averageWaitTime: 0,
          appointmentDuration: 0,
          noShowRate: 0,
          cancellationRate: 0,
        },
      },
      financial: {
        cashFlow: {
          currentBalance: 0,
          projectedBalance: 0,
          inflow: 0,
          outflow: 0,
          burnRate: 0,
        },
        receivables: {
          current: 0,
          thirtyDays: 0,
          sixtyDays: 0,
          ninetyDaysPlus: 0,
          totalReceivables: 0,
        },
        payables: {
          currentPayables: 0,
          overduePayables: 0,
          upcomingPayments: 0,
          averagePaymentDays: 0,
        },
        profitability: {
          treatmentType: "",
          revenue: 0,
          costs: 0,
          margin: 0,
          profitabilityRate: 0,
        },
        forecasting: {
          revenue: {
            metric: "",
            currentValue: 0,
            predictedValue: 0,
            confidence: 0,
            trend: "stable" as const,
            factors: [],
          },
          expenses: {
            metric: "",
            currentValue: 0,
            predictedValue: 0,
            confidence: 0,
            trend: "stable" as const,
            factors: [],
          },
          cashFlow: {
            metric: "",
            currentValue: 0,
            predictedValue: 0,
            confidence: 0,
            trend: "stable" as const,
            factors: [],
          },
          profitability: {
            metric: "",
            currentValue: 0,
            predictedValue: 0,
            confidence: 0,
            trend: "stable" as const,
            factors: [],
          },
        },
      },
      clinical: {
        patientRecords: [],
        treatmentProtocols: {
          protocols: [],
          successRates: {},
          averageDuration: {},
          contraindications: {},
        },
        professionalPerformance: {
          professionalId: "",
          performanceScore: 0,
          patientSatisfaction: 0,
          treatmentSuccessRate: 0,
          efficiency: 0,
          specialties: [],
        },
        complianceStatus: {
          cfmCompliance: true,
          anvisaCompliance: true,
          lgpdCompliance: true,
          lastAuditDate: "",
          complianceScore: 0,
          violations: [],
          recommendations: [],
        },
      },
      businessIntelligence: {
        kpis: {
          revenue: 0,
          profitMargin: 0,
          customerSatisfaction: 0,
          appointmentUtilization: 0,
          professionalEfficiency: 0,
          complianceScore: 0,
        },
        trends: {
          revenueGrowth: 0,
          patientRetention: 0,
          servicePopularity: {},
          seasonalPatterns: [],
          competitivePosition: "",
        },
        opportunities: {
          revenueOpportunities: [],
          costReductionOpportunities: [],
          operationalImprovements: [],
          marketExpansion: [],
        },
        alerts: { critical: [], warning: [], info: [] },
      },
    };
  }
}

/**
 * Context builders for each epic
 */
async function buildAppointmentsContext(supabase: any, clinicId: string) {
  // Epic 1 - Appointments data
  const { data: appointments } = await supabase
    .from("appointments")
    .select("*")
    .eq("clinic_id", clinicId)
    .gte("scheduled_at", new Date().toISOString())
    .limit(50);

  return {
    upcoming: appointments || [],
    conflicts: {
      totalConflicts: 0,
      conflictTypes: {},
      resolutionSuggestions: [],
    },
    utilization: {
      professionalId: "",
      utilizationRate: 0,
      appointmentsCount: 0,
      availableSlots: 0,
      efficiency: 0,
    },
    patientFlow: {
      averageWaitTime: 0,
      appointmentDuration: 0,
      noShowRate: 0,
      cancellationRate: 0,
    },
  };
}

async function buildFinancialContext(supabase: any, clinicId: string) {
  // Epic 2 - Financial data
  const { data: transactions } = await supabase
    .from("financial_transactions")
    .select("*")
    .eq("clinic_id", clinicId)
    .gte(
      "created_at",
      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    )
    .limit(100);

  return {
    cashFlow: {
      currentBalance: 0,
      projectedBalance: 0,
      inflow: 0,
      outflow: 0,
      burnRate: 0,
    },
    receivables: {
      current: 0,
      thirtyDays: 0,
      sixtyDays: 0,
      ninetyDaysPlus: 0,
      totalReceivables: 0,
    },
    payables: {
      currentPayables: 0,
      overduePayables: 0,
      upcomingPayments: 0,
      averagePaymentDays: 0,
    },
    profitability: {
      treatmentType: "",
      revenue: 0,
      costs: 0,
      margin: 0,
      profitabilityRate: 0,
    },
    forecasting: {
      revenue: {
        metric: "",
        currentValue: 0,
        predictedValue: 0,
        confidence: 0,
        trend: "stable",
        factors: [],
      },
      expenses: {
        metric: "",
        currentValue: 0,
        predictedValue: 0,
        confidence: 0,
        trend: "stable",
        factors: [],
      },
      cashFlow: {
        metric: "",
        currentValue: 0,
        predictedValue: 0,
        confidence: 0,
        trend: "stable",
        factors: [],
      },
      profitability: {
        metric: "",
        currentValue: 0,
        predictedValue: 0,
        confidence: 0,
        trend: "stable",
        factors: [],
      },
    },
  };
}

async function buildClinicalContext(supabase: any, clinicId: string) {
  // Epic 3 - Clinical data (with LGPD compliance)
  const { data: patients } = await supabase
    .from("patients")
    .select("id, first_name, created_at")
    .eq("clinic_id", clinicId)
    .limit(10);

  return {
    patientRecords: patients || [],
    treatmentProtocols: {
      protocols: [],
      successRates: {},
      averageDuration: {},
      contraindications: {},
    },
    professionalPerformance: {
      professionalId: "",
      performanceScore: 0,
      patientSatisfaction: 0,
      treatmentSuccessRate: 0,
      efficiency: 0,
      specialties: [],
    },
    complianceStatus: {
      cfmCompliance: true,
      anvisaCompliance: true,
      lgpdCompliance: true,
      lastAuditDate: "",
      complianceScore: 0,
      violations: [],
      recommendations: [],
    },
  };
}

async function buildBusinessIntelligenceContext(
  supabase: any,
  clinicId: string
) {
  // Cross-epic analytics
  return {
    kpis: {
      revenue: 0,
      profitMargin: 0,
      customerSatisfaction: 0,
      appointmentUtilization: 0,
      professionalEfficiency: 0,
      complianceScore: 0,
    },
    trends: {
      revenueGrowth: 0,
      patientRetention: 0,
      servicePopularity: {},
      seasonalPatterns: [],
      competitivePosition: "",
    },
    opportunities: {
      revenueOpportunities: [],
      costReductionOpportunities: [],
      operationalImprovements: [],
      marketExpansion: [],
    },
    alerts: { critical: [], warning: [], info: [] },
  };
}

/**
 * Generate unique session ID
 */
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
