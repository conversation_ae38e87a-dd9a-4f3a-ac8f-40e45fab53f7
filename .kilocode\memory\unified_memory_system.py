from typing import Dict, Any, List

class UnifiedMemorySystem:
    """
    A simplified unified memory system to store and retrieve interaction context.
    In a real system, this would connect to a database like Redis or a vector store.
    """
    def __init__(self):
        """
        Initializes the UnifiedMemorySystem with in-memory storage.
        """
        self.short_term_memory: List[Dict[str, Any]] = []
        self.long_term_memory: Dict[str, Any] = {} # Placeholder for more structured memory

    def store_interaction(self, interaction: Dict[str, Any]):
        """
        Stores an interaction in short-term memory.

        Args:
            interaction (Dict[str, Any]): The interaction data to store.
        """
        self.short_term_memory.append(interaction)
        # Simple mechanism to prevent memory from growing indefinitely
        if len(self.short_term_memory) > 100:
            self.short_term_memory.pop(0)

    def get_recent_context(self, n: int = 5) -> List[Dict[str, Any]]:
        """
        Retrieves the 'n' most recent interactions.

        Args:
            n (int): The number of recent interactions to retrieve.

        Returns:
            List[Dict[str, Any]]: A list of recent interactions.
        """
        return self.short_term_memory[-n:]

    def consult_memories(self, query: str) -> Dict[str, Any]:
        """
        Consults both short and long-term memory for relevant information.
        This is a simplified placeholder for a more complex search/retrieval mechanism.
        """
        # Simple keyword search in short-term memory
        relevant_interactions = [
            interaction for interaction in self.short_term_memory
            if query.lower() in interaction.get("request", {}).get("description", "").lower()
        ]

        return {
            "relevant_short_term": relevant_interactions,
            "long_term_insights": {} # Placeholder
        }

if __name__ == '__main__':
    # Example Usage
    memory = UnifiedMemorySystem()

    # Simulate some interactions
    memory.store_interaction({"request": {"description": "Fix the login bug"}, "response": "Fixed."})
    memory.store_interaction({"request": {"description": "Refactor the database module"}, "response": "Refactored."})
    memory.store_interaction({"request": {"description": "Analyze API performance"}, "response": "Analysis complete."})

    print("Recent Context:")
    print(memory.get_recent_context(2))

    print("\nMemory Consultation for 'database':")
    print(memory.consult_memories("database"))
