"use client";

// AGENDATRINTAE3 AI Smart Scheduling Component
// Phase 7 AI Integration - Streaming UI Component for Smart Scheduling
// Generated by VIBECODE SYSTEM V4.0 - AI Integration

import type { GenerateSchedulingInput } from "@/lib/actions/ai-scheduling";
import { generateSchedulingOptimization } from "@/lib/actions/ai-scheduling";
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  Loader2,
  Spa<PERSON>les,
  Stethoscope,
} from "lucide-react";
import React, { useState } from "react";

// Simple UI Components
const Button = ({ children, onClick, disabled, className }: any) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`px-4 py-2 bg-blue-600 text-white rounded disabled:opacity-50 hover:bg-blue-700 transition-colors ${className}`}
  >
    {children}
  </button>
);

const Card = ({ children, className }: any) => (
  <div className={`border rounded-lg shadow-sm bg-white ${className}`}>
    {children}
  </div>
);

const CardHeader = ({ children }: any) => (
  <div className="p-6 pb-4">{children}</div>
);
const CardTitle = ({ children, className }: any) => (
  <h3 className={`text-lg font-semibold ${className}`}>{children}</h3>
);
const CardDescription = ({ children }: any) => (
  <p className="text-sm text-gray-600 mt-1">{children}</p>
);
const CardContent = ({ children, className }: any) => (
  <div className={`p-6 pt-0 ${className}`}>{children}</div>
);

const Select = ({ children, onValueChange, placeholder }: any) => (
  <select
    onChange={(e) => onValueChange(e.target.value)}
    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
  >
    <option value="">{placeholder || "Select an option"}</option>
    {children}
  </select>
);

const SelectItem = ({ value, children }: any) => (
  <option value={value}>{children}</option>
);

const Badge = ({ children, className }: any) => (
  <span className={`px-2 py-1 text-xs rounded-full font-medium ${className}`}>
    {children}
  </span>
);

const Textarea = ({ value, onChange, placeholder, className, id }: any) => (
  <textarea
    id={id}
    value={value}
    onChange={onChange}
    placeholder={placeholder}
    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${className}`}
    rows={3}
  />
);

const Label = ({ children, htmlFor }: any) => (
  <label
    htmlFor={htmlFor}
    className="block text-sm font-medium mb-2 text-gray-700"
  >
    {children}
  </label>
);

const Input = ({
  value,
  onChange,
  placeholder,
  className,
  id,
  type = "text",
}: any) => (
  <input
    id={id}
    type={type}
    value={value}
    onChange={onChange}
    placeholder={placeholder}
    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
  />
);

interface SmartSchedulingProps {
  userId: string;
  onOptimizationGenerated?: (optimization: any) => void;
}

interface StreamingState {
  isStreaming: boolean;
  content: string;
  isComplete: boolean;
  error?: string;
}

interface UrgencyCardProps {
  level: string;
  title: string;
  description: string;
  icon: React.ElementType;
  color: string;
  isSelected: boolean;
  onClick: () => void;
}

function UrgencyCard({
  level,
  title,
  description,
  icon: Icon,
  color,
  isSelected,
  onClick,
}: UrgencyCardProps) {
  return (
    <div
      onClick={onClick}
      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
        isSelected
          ? `border-${color}-500 bg-${color}-50`
          : "border-gray-200 hover:border-gray-300"
      }`}
    >
      <div className="flex items-center gap-3">
        <div
          className={`p-2 rounded-full ${
            isSelected ? `bg-${color}-100` : "bg-gray-100"
          }`}
        >
          <Icon
            className={`h-5 w-5 ${
              isSelected ? `text-${color}-600` : "text-gray-600"
            }`}
          />
        </div>
        <div>
          <h4 className="font-medium">{title}</h4>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
    </div>
  );
}

export default function SmartScheduling({
  userId,
  onOptimizationGenerated,
}: SmartSchedulingProps) {
  const [formData, setFormData] = useState<Partial<GenerateSchedulingInput>>({
    user_id: userId,
    urgency_level: "routine",
    service_id: "",
    medical_notes: "",
    patient_preferences: "",
    doctor_preferences: {
      preferred_doctors: [],
      specialty_requirements: [],
      exclude_doctors: [],
    },
    scheduling_constraints: {
      preferred_dates: [],
      preferred_times: [],
      avoid_dates: [],
      max_wait_days: 30,
    },
  });

  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    content: "",
    isComplete: false,
  });

  const urgencyLevels = [
    {
      level: "emergency",
      title: "Emergency",
      description: "Immediate medical attention required",
      icon: AlertTriangle,
      color: "red",
    },
    {
      level: "urgent",
      title: "Urgent",
      description: "Needs attention within 24-48 hours",
      icon: Clock,
      color: "orange",
    },
    {
      level: "routine",
      title: "Routine",
      description: "Standard appointment scheduling",
      icon: Calendar,
      color: "blue",
    },
    {
      level: "follow_up",
      title: "Follow-up",
      description: "Continuing care appointment",
      icon: Stethoscope,
      color: "green",
    },
  ];

  const mockServices = [
    { id: "service-1", name: "General Consultation", duration: 30 },
    { id: "service-2", name: "Cardiology Consultation", duration: 45 },
    { id: "service-3", name: "Dermatology Consultation", duration: 30 },
    { id: "service-4", name: "Orthopedic Consultation", duration: 60 },
    { id: "service-5", name: "Pediatric Consultation", duration: 30 },
  ];

  const handleInputChange = (
    field: keyof GenerateSchedulingInput,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNestedInputChange = (
    parentField: keyof GenerateSchedulingInput,
    childField: string,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [parentField]: {
        ...(prev[parentField] as any),
        [childField]: value,
      },
    }));
  };

  const handleGenerateOptimization = async () => {
    if (!formData.service_id) {
      alert("Please select a service");
      return;
    }

    setStreamingState({
      isStreaming: true,
      content: "",
      isComplete: false,
    });

    try {
      const result = await generateSchedulingOptimization(
        formData as GenerateSchedulingInput
      );

      if (!result.success) {
        throw new Error(result.error);
      }

      // Simulate streaming for demo
      const mockContent = `
# AI Smart Scheduling Optimization

## OPTIMAL SCHEDULING RECOMMENDATIONS

### 🎯 PRIMARY RECOMMENDATION

**Recommended Doctor**: Dr. Maria Silva (Cardiology)
- **Specialty Match**: 95% - Perfect match for your medical needs
- **Experience**: 15 years of specialized experience
- **Patient Rating**: 4.8/5 stars (127 reviews)
- **Availability**: Excellent - Multiple slots available

**Optimal Date & Time**: **Tuesday, June 18th at 2:30 PM**
- **Scheduling Efficiency**: 9.2/10 - Optimal clinic flow timing
- **Medical Urgency Alignment**: ${
        formData.urgency_level === "emergency"
          ? "IMMEDIATE - Emergency slot secured"
          : formData.urgency_level === "urgent"
          ? "HIGH PRIORITY - Next available urgent slot"
          : formData.urgency_level === "routine"
          ? "ROUTINE - Optimal timing for comprehensive care"
          : "FOLLOW-UP - Continuity of care maintained"
      }
- **Wait Time**: ${
        formData.urgency_level === "emergency"
          ? "Same day"
          : formData.urgency_level === "urgent"
          ? "2 days"
          : "5 days"
      }

### 🔄 ALTERNATIVE OPTIONS

**Option 2**: Dr. João Santos (General Medicine)
- **Date & Time**: Wednesday, June 19th at 10:00 AM
- **Benefits**: Earlier morning slot, shorter wait time
- **Efficiency Score**: 8.7/10

**Option 3**: Dr. Ana Costa (Cardiology)
- **Date & Time**: Thursday, June 20th at 4:00 PM
- **Benefits**: Same specialty, afternoon availability
- **Efficiency Score**: 8.5/10

## CONTINUITY OF CARE ANALYSIS

### 📋 Previous Doctor Relationships
- **Last Appointment**: Dr. Maria Silva (March 15, 2024)
- **Treatment History**: 3 previous consultations with excellent outcomes
- **Doctor Familiarity**: High - Dr. Silva knows your medical history
- **Recommendation**: Continue with Dr. Silva for optimal care continuity

### 🎯 Specialty Matching
- **Required Expertise**: ${
        formData.service_id === "service-2"
          ? "Cardiology specialist"
          : "General medical consultation"
      }
- **Doctor Qualification**: Board-certified specialist with relevant experience
- **Success Rate**: 94% positive outcomes for similar cases
- **Treatment Approach**: Conservative, evidence-based methodology

### ⏰ Treatment Timeline Optimization
- **Optimal Timing**: Mid-week appointment allows for follow-up scheduling
- **Preparation Time**: 24 hours advance notice for any required tests
- **Recovery Planning**: Adequate time for post-appointment care if needed

## CONFLICT RESOLUTION STRATEGIES

### 🔍 Scheduling Conflicts Identified
- **Minor Conflict**: Original preferred time (Monday 9 AM) unavailable
- **Resolution**: Alternative Tuesday 2:30 PM offers better doctor availability
- **Impact**: Minimal - only 1 day delay with significant quality improvement

### ⚡ Wait Time Optimization
- **Current Wait Time**: ${
        formData.urgency_level === "emergency"
          ? "Immediate"
          : formData.urgency_level === "urgent"
          ? "2 days (50% faster than average)"
          : "5 days (30% faster than average)"
      }
- **Optimization Strategy**: Mid-week scheduling reduces clinic congestion
- **Efficiency Gain**: 25% reduction in overall appointment time

### 🏥 Operational Efficiency
- **Clinic Flow**: Appointment scheduled during optimal staffing hours
- **Resource Utilization**: 92% - Excellent use of medical resources
- **Staff Availability**: Full support team available for comprehensive care

## PATIENT EXPERIENCE OPTIMIZATION

### ✅ Preference Alignment
- **Time Preferences**: ${
        formData.scheduling_constraints?.preferred_times?.length
          ? `Matches your preferred times: ${formData.scheduling_constraints.preferred_times.join(
              ", "
            )}`
          : "Optimized for minimal wait and maximum care quality"
      }
- **Doctor Preferences**: ${
        formData.doctor_preferences?.preferred_doctors?.length
          ? "Matches your preferred doctor selection"
          : "Matched with highest-rated available specialist"
      }
- **Scheduling Flexibility**: High - Multiple backup options available

### 🚗 Convenience Factors
- **Location**: Main clinic - easy parking and public transport access
- **Travel Time**: Estimated 15-20 minutes from your location
- **Accessibility**: Full wheelchair access and patient amenities
- **Parking**: Free parking available with 2-hour validation

### 📋 Preparation Time
- **Pre-Appointment**: 24 hours notice for any required fasting or medication adjustments
- **Documentation**: Medical records will be reviewed in advance
- **Special Instructions**: ${
        formData.medical_notes
          ? "Custom preparation based on your medical notes"
          : "Standard preparation guidelines will be provided"
      }

## MEDICAL PRIORITY CONSIDERATIONS

### 🚨 Urgency Assessment
- **Priority Level**: ${formData.urgency_level?.toUpperCase() || "ROUTINE"}
- **Medical Justification**: ${
        formData.urgency_level === "emergency"
          ? "Immediate medical attention required - emergency protocols activated"
          : formData.urgency_level === "urgent"
          ? "Expedited scheduling due to medical urgency"
          : formData.urgency_level === "routine"
          ? "Standard scheduling with optimal care timing"
          : "Follow-up care scheduled for treatment continuity"
      }
- **Risk Assessment**: Low risk with current scheduling approach

### 🛡️ Risk Mitigation
- **Medical Safety**: All appointments include adequate consultation time
- **Emergency Backup**: Emergency protocols available if condition worsens
- **Monitoring Plan**: Clear escalation path if urgent care becomes needed

### 📅 Follow-up Planning
- **Next Appointment**: Tentatively scheduled for 4 weeks post-consultation
- **Treatment Integration**: Seamlessly integrated with ongoing care plan
- **Specialist Coordination**: Automatic referral coordination if needed

## IMPLEMENTATION RECOMMENDATIONS

### ⚡ Immediate Actions
1. **Confirm Appointment**: Accept Tuesday, June 18th at 2:30 PM slot
2. **Preparation**: Review pre-appointment instructions (will be sent 24h prior)
3. **Documentation**: Ensure insurance and ID are ready
4. **Transportation**: Plan arrival 15 minutes early for check-in

### 🔄 Backup Plans
- **Alternative 1**: If primary slot becomes unavailable, automatically book Option 2
- **Alternative 2**: Emergency rescheduling protocol activated if urgent care needed
- **Cancellation Policy**: 24-hour notice required for changes

### 📞 Patient Communication
- **Confirmation**: SMS and email confirmation within 2 hours
- **Reminders**: 24-hour and 2-hour appointment reminders
- **Updates**: Real-time notifications for any schedule changes
- **Support**: 24/7 patient support line for questions or concerns

---

## ⚠️ IMPORTANT MEDICAL DISCLAIMERS

**Medical Priority**: This AI scheduling optimization is based on available data and operational efficiency. Medical urgency should always be verified by healthcare professionals.

**Emergency Care**: If you experience severe symptoms or medical emergency, bypass normal scheduling and seek immediate emergency care by calling 911 or visiting the nearest emergency room.

**Clinical Judgment**: Final scheduling decisions should consider clinical judgment and may be adjusted by medical staff based on current health status.

**Patient Safety**: Patient safety and medical best practices always take priority over operational efficiency and scheduling optimization.

---

**Scheduling Confidence**: 94% | **Efficiency Score**: 9.2/10 | **Patient Satisfaction Prediction**: 96%
      `;

      // Simulate streaming
      let currentIndex = 0;
      const streamInterval = setInterval(() => {
        if (currentIndex < mockContent.length) {
          const chunk = mockContent.slice(0, currentIndex + 100);
          setStreamingState((prev) => ({
            ...prev,
            content: chunk,
          }));
          currentIndex += 100;
        } else {
          clearInterval(streamInterval);
          setStreamingState((prev) => ({
            ...prev,
            isStreaming: false,
            isComplete: true,
          }));

          if (onOptimizationGenerated) {
            onOptimizationGenerated(result.optimization);
          }
        }
      }, 80);
    } catch (error) {
      console.error("Error generating optimization:", error);
      setStreamingState({
        isStreaming: false,
        content: "",
        isComplete: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to generate optimization",
      });
    }
  };

  const formatContent = (content: string) => {
    return content.split("\n").map((line, index) => {
      if (line.startsWith("# ")) {
        return (
          <h1
            key={index}
            className="text-2xl font-bold mt-6 mb-4 text-blue-700"
          >
            {line.replace("# ", "")}
          </h1>
        );
      }
      if (line.startsWith("## ")) {
        return (
          <h2
            key={index}
            className="text-xl font-semibold mt-5 mb-3 text-blue-600"
          >
            {line.replace("## ", "")}
          </h2>
        );
      }
      if (line.startsWith("### ")) {
        return (
          <h3
            key={index}
            className="text-lg font-medium mt-4 mb-2 text-gray-800"
          >
            {line.replace("### ", "")}
          </h3>
        );
      }
      if (line.startsWith("**") && line.endsWith("**")) {
        return (
          <p key={index} className="font-semibold mt-3 mb-1 text-gray-800">
            {line.replace(/\*\*/g, "")}
          </p>
        );
      }
      if (line.startsWith("- ")) {
        return (
          <li key={index} className="ml-4 mb-1 text-gray-700">
            {line.replace("- ", "")}
          </li>
        );
      }
      if (line.startsWith("⚠️") || line.includes("IMPORTANT")) {
        return (
          <div
            key={index}
            className="bg-yellow-50 border-l-4 border-yellow-400 p-3 my-3"
          >
            <p className="text-yellow-800 font-medium">{line}</p>
          </div>
        );
      }
      if (line.includes("🎯") || line.includes("✅") || line.includes("🚨")) {
        return (
          <div key={index} className="bg-blue-50 p-2 rounded my-2">
            <p className="text-blue-800">{line}</p>
          </div>
        );
      }
      if (line.trim()) {
        return (
          <p key={index} className="mb-2 leading-relaxed text-gray-700">
            {line}
          </p>
        );
      }
      return <br key={index} />;
    });
  };

  return (
    <div className="space-y-6 max-w-6xl mx-auto">
      {/* Urgency Level Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            Medical Urgency Level
          </CardTitle>
          <CardDescription>
            Select the urgency level for your medical appointment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {urgencyLevels.map((urgency) => (
              <UrgencyCard
                key={urgency.level}
                level={urgency.level}
                title={urgency.title}
                description={urgency.description}
                icon={urgency.icon}
                color={urgency.color}
                isSelected={formData.urgency_level === urgency.level}
                onClick={() =>
                  handleInputChange("urgency_level", urgency.level)
                }
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Appointment Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Stethoscope className="h-5 w-5 text-blue-600" />
            Appointment Details
          </CardTitle>
          <CardDescription>
            Provide details about your medical appointment needs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="service_id">Medical Service *</Label>
              <Select
                onValueChange={(value: string) =>
                  handleInputChange("service_id", value)
                }
                placeholder="Select medical service"
              >
                {mockServices.map((service) => (
                  <SelectItem key={service.id} value={service.id}>
                    {service.name} ({service.duration} min)
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="max_wait_days">Maximum Wait Time (days)</Label>
              <Input
                id="max_wait_days"
                type="number"
                value={formData.scheduling_constraints?.max_wait_days || 30}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleNestedInputChange(
                    "scheduling_constraints",
                    "max_wait_days",
                    parseInt(e.target.value)
                  )
                }
                placeholder="30"
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="medical_notes">Medical Notes</Label>
              <Textarea
                id="medical_notes"
                placeholder="Describe your symptoms, concerns, or specific medical needs..."
                value={formData.medical_notes || ""}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  handleInputChange("medical_notes", e.target.value)
                }
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="patient_preferences">
                Scheduling Preferences
              </Label>
              <Textarea
                id="patient_preferences"
                placeholder="Preferred times, days, doctor preferences, or other scheduling requirements..."
                value={formData.patient_preferences || ""}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  handleInputChange("patient_preferences", e.target.value)
                }
              />
            </div>
          </div>

          <Button
            onClick={handleGenerateOptimization}
            disabled={streamingState.isStreaming || !formData.service_id}
            className="w-full"
          >
            {streamingState.isStreaming ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Optimizing Schedule...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generate AI Scheduling Optimization
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Streaming Results */}
      {(streamingState.content || streamingState.error) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {streamingState.isComplete ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : streamingState.error ? (
                <AlertTriangle className="h-5 w-5 text-red-600" />
              ) : (
                <Clock className="h-5 w-5 text-blue-600 animate-pulse" />
              )}
              Smart Scheduling Results
              {streamingState.isStreaming && (
                <Badge className="ml-auto bg-blue-100 text-blue-800">
                  <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                  Optimizing...
                </Badge>
              )}
              {streamingState.isComplete && (
                <Badge className="ml-auto bg-green-100 text-green-800">
                  Complete
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {streamingState.error ? (
              <div className="text-red-600 p-4 bg-red-50 rounded-lg">
                <p className="font-semibold">Error generating optimization:</p>
                <p>{streamingState.error}</p>
              </div>
            ) : (
              <div className="prose prose-sm max-w-none">
                {formatContent(streamingState.content)}
                {streamingState.isStreaming && (
                  <div className="inline-block w-2 h-4 bg-blue-600 animate-pulse ml-1"></div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
