---
source_id: master_rule
data_file: C:/Users/<USER>/.kilocode/rules/master_rule.md
priority: 1
enabled: true
tags_func: master_rule_tags
description: Carrega as regras principais do VIBECODE Master Rule
---

# Master Rule Manifest

Este manifesto configura o carregamento dinâmico das regras principais do sistema VIBECODE.

## Configuração

- **Source ID**: `master_rule`
- **Data File**: Caminho absoluto para o arquivo master rule
- **Priority**: 1 (máxima prioridade)
- **Tags Function**: `master_rule_tags` para categorização automática

## Funcionalidade

Este manifesto permite que o sistema de memória unificado carregue dinamicamente as regras do VIBECODE Master Rule, garantindo que todas as diretrizes sejam aplicadas durante a execução de tarefas.
