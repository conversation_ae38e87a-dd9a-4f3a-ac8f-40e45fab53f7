# System Configuration - VIBECODE V1.0

**Data de Migração**: 16 de janeiro de 2025  
**Fonte**: Sistema unificado memory-bank  
**Status**: Configurações ativas do sistema

## 🔗 Augment Integration Config

### Memory Integration
- **Enabled**: ✅ True
- **Auto Sync**: ✅ True  
- **Bridge Module**: cursor_memory_bridge.py
- **Compression**: ❌ Disabled
- **Max Memories**: 10,000

### Bidirectional Sync
- **Enabled**: ✅ True
- **Native Memory Bridge**: ✅ True
- **Real-time Updates**: ✅ True
- **Remember Function Override**: ✅ True

### Logging Configuration
- **Console Logging**: ✅ True
- **File Logging**: ✅ True  
- **Level**: INFO
- **Log File**: E:\CODE-BACKUP\logs\memory_bridge.log

## 📁 File Management Rules

### Temporary File Policy
- **Base Path**: E:\CODE-BACKUP
- **Rule**: Todos os arquivos temporários que serão usados somente uma vez devem ser criados em E:\CODE-BACKUP

### Directory Structure
```
E:\CODE-BACKUP\
├── temp\           # Arquivos temporários
├── backups\        # Backups do sistema  
├── tests\          # Arquivos de teste
├── reports\        # Relatórios gerados
└── cache\          # Cache temporário
```

## 🗂️ Root Directory Rules

### Memory Bank Structure (Unificado)
- **Location**: memory-bank/
- **Purpose**: Sistema único de memória e contexto
- **Components MD**:
  - Project Brief e Context
  - Technical Context  
  - System Patterns
  - Progress Tracking
- **Components Python**:
  - Knowledge Graph Manager
  - Cursor Integration Bridge
  - ML Components
  - Production Tools

## ⚙️ System Monitoring

### Desktop Commander Integration
- **Tool Selection Threshold**: 200 lines
- **Fallback Strategy**: Cursor Editor para arquivos grandes
- **Performance Monitoring**: ✅ Enabled
- **Error Logging**: ✅ Enabled

### Knowledge Graph Settings
- **Node Capacity**: Unlimited
- **Edge Capacity**: Unlimited  
- **Learning Rate**: Adaptive
- **Pattern Recognition**: ✅ Enabled

## 🔧 Development Environment

### Python Environment
- **Version**: Python 3.13
- **Package Manager**: pip/uv
- **Virtual Environment**: Recomendado

### Node.js Environment  
- **Package Manager**: pnpm (preferred) / npm
- **Version Management**: Volta/nvm recomendado

## 🚨 Error Handling

### Automatic Recovery
- **Session Recovery**: ✅ Enabled
- **Memory Persistence**: ✅ Enabled
- **Fallback Mechanisms**: ✅ Enabled

### Logging Strategy
- **Error Level**: WARN and above
- **Success Patterns**: INFO level
- **Debug Mode**: Configurable

## 📊 Performance Optimization

### Caching Strategy
- **Pattern Cache**: ✅ Enabled
- **Memory Bridge Cache**: ✅ Enabled  
- **File Operation Cache**: ✅ Enabled

### Resource Management
- **Memory Limit**: 10,000 entries
- **Cleanup Frequency**: Daily
- **Backup Retention**: 30 days

---

**Nota**: Este arquivo substitui e consolida:
- augment_config.json
- temp_file_rules.json  
- root_directory_rules.json

**Manutenção**: Atualizar quando houver mudanças nas configurações do sistema.