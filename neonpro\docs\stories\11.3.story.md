# Story 11.3: Controle de Saídas e Consumo

## Story Overview

**Como** responsável pelo estoque da clínica  
**Eu quero** sistema automatizado de controle de saídas e consumo de materiais  
**Para que** eu possa ter controle preciso do uso de materiais, baixa automática durante procedimentos e gestão eficiente do consumo por centro de custo

### Story Details

- **Epic**: Epic 11 - Estoque Simplificado
- **Story Points**: 8
- **Priority**: P1 (High)
- **Theme**: Stock Output Control, Automatic Deduction & Consumption Management
- **Dependencies**: Story 11.1 (Product Management), Story 11.2 (Purchase Management)

### Acceptance Criteria

#### AC1: Baixa Automática por Procedimentos Realizados

- [ ] **GIVEN** procedimento sendo realizado na clínica
- [ ] **WHEN** procedimento é finalizado no sistema
- [ ] **THEN** baixa automática de materiais do estoque
- [ ] **AND** seleção automática de materiais por procedimento
- [ ] **AND** quantidade padrão baseada em protocolos da clínica
- [ ] **AND** possibilidade de ajuste manual antes da baixa
- [ ] **AND** verificação de disponibilidade em estoque
- [ ] **AND** alertas quando quantidade insuficiente
- [ ] **AND** sugestão de materiais alternativos
- [ ] **AND** registro de profissional responsável pelo consumo
- [ ] **AND** centro de custo automático por sala/equipamento

#### AC2: Controle Manual de Saídas e Ajustes

- [ ] **GIVEN** necessidade de saída manual de materiais
- [ ] **WHEN** registro saída no sistema
- [ ] **THEN** controle completo de saídas manuais
- [ ] **AND** múltiplos tipos de saída (uso, perda, vencimento, transferência)
- [ ] **AND** seleção de produtos com busca inteligente
- [ ] **AND** quantidade com validação de estoque disponível
- [ ] **AND** motivo da saída obrigatório para alguns tipos
- [ ] **AND** foto/evidência para perdas e vencimentos
- [ ] **AND** aprovação necessária para saídas de alto valor
- [ ] **AND** código de barras para agilizar registro
- [ ] **AND** histórico completo de saídas por responsável

#### AC3: Gestão FIFO e Controle de Lotes

- [ ] **GIVEN** produtos com controle de lote e validade
- [ ] **WHEN** realizo saída de materiais
- [ ] **THEN** gestão automática FIFO (First In, First Out)
- [ ] **AND** seleção automática do lote mais antigo
- [ ] **AND** verificação automática de produtos próximos ao vencimento
- [ ] **AND** alertas para lotes vencidos ou próximos (30, 15, 7 dias)
- [ ] **AND** bloqueio automático de lotes vencidos
- [ ] **AND** relatório de perdas por vencimento
- [ ] **AND** sugestões de uso prioritário para lotes próximos
- [ ] **AND** rastreabilidade completa por lote utilizado

#### AC4: Consumo por Centro de Custo e Profissional

- [ ] **GIVEN** materiais consumidos em diferentes setores
- [ ] **WHEN** analiso consumo por área
- [ ] **THEN** controle detalhado por centro de custo
- [ ] **AND** classificação automática por sala/equipamento/profissional
- [ ] **AND** rateio de custos por procedimento realizado
- [ ] **AND** comparação de consumo entre profissionais
- [ ] **AND** identificação de padrões de uso anômalo
- [ ] **AND** metas de consumo por setor/profissional
- [ ] **AND** relatórios de eficiência operacional
- [ ] **AND** alertas para consumo acima da média
- [ ] **AND** análise de rentabilidade por centro de custo

#### AC5: Movimentações e Transferências Internas

- [ ] **GIVEN** necessidade de transferir materiais entre setores
- [ ] **WHEN** processo transferência interna
- [ ] **THEN** controle completo de movimentações
- [ ] **AND** transferência entre centros de custo/salas
- [ ] **AND** workflow de aprovação para transferências
- [ ] **AND** atualização automática de localização
- [ ] **AND** histórico de movimentações por produto
- [ ] **AND** reconciliação de inventário entre setores
- [ ] **AND** relatórios de giro por localização
- [ ] **AND** otimização de distribuição de estoque
- [ ] **AND** alertas para desbalanceamento entre setores

#### AC6: Relatórios e Analytics de Consumo

- [ ] **GIVEN** histórico de consumo acumulado
- [ ] **WHEN** analiso padrões de uso
- [ ] **THEN** relatórios completos de analytics
- [ ] **AND** consumo por período (diário, semanal, mensal)
- [ ] **AND** ranking de produtos mais consumidos
- [ ] **AND** eficiência de uso por profissional
- [ ] **AND** previsão de demanda baseada em histórico
- [ ] **AND** identificação de desperdícios e perdas
- [ ] **AND** comparação com metas estabelecidas
- [ ] **AND** ROI por tipo de procedimento
- [ ] **AND** sugestões automáticas de otimização

### Technical Requirements

#### Stock Output Management System

```typescript
// Sistema de Controle de Saídas
interface SaidaEstoque {
  id: string
  numero_saida: string
  tipo_saida: TipoSaida
  
  // Origem da Saída
  procedimento_id?: string
  agendamento_id?: string
  centro_custo_id: string
  sala_id?: string
  equipamento_id?: string
  
  // Responsável
  profissional_id: string
  responsavel_registro: string
  
  // Data e Hora
  data_saida: Date
  hora_saida: string
  
  // Itens da Saída
  itens: ItemSaida[]
  
  // Totais
  quantidade_total: number
  valor_total: number
  custo_total: number
  
  // Motivo e Observações
  motivo_saida: string
  observacoes?: string
  evidencias?: string[] // fotos/documentos
  
  // Aprovação
  requer_aprovacao: boolean
  aprovado: boolean
  aprovado_por?: string
  aprovado_em?: Date
  
  // Status e Controle
  status: StatusSaida
  automatico: boolean
  reversivel: boolean
  revertido: boolean
  
  // Auditoria
  criado_em: Date
  criado_por: string
  clinica_id: string
}

// Item de Saída
interface ItemSaida {
  id: string
  saida_id: string
  produto_id: string
  
  // Lote e Rastreabilidade
  lote_id: string
  numero_lote: string
  data_validade: Date
  
  // Quantidade e Custos
  quantidade: number
  custo_unitario: number
  valor_total: number
  
  // Localização
  localizacao_origem: string
  localizacao_destino?: string
  
  // FIFO Control
  ordem_fifo: number
  selecionado_automaticamente: boolean
  
  // Observações
  motivo_item?: string
  observacoes_item?: string
  
  // Auditoria
  baixado_em: Date
  baixado_por: string
}

// Configuração de Procedimentos
interface ProcedimentoMaterial {
  id: string
  procedimento_id: string
  produto_id: string
  
  // Quantidade Padrão
  quantidade_padrao: number
  quantidade_minima: number
  quantidade_maxima: number
  
  // Opções
  obrigatorio: boolean
  permite_ajuste: boolean
  unidade_medida: string
  
  // Custos
  custo_padrao: number
  margem_aplicada: number
  
  // Controle
  ativo: boolean
  observacoes?: string
  
  // Auditoria
  criado_em: Date
  atualizado_em: Date
  clinica_id: string
}

// Centro de Custo
interface CentroCusto {
  id: string
  codigo: string
  nome: string
  descricao?: string
  
  // Hierarquia
  centro_pai_id?: string
  nivel: number
  caminho_completo: string
  
  // Configurações
  controla_estoque: boolean
  requer_aprovacao_saida: boolean
  limite_valor_saida?: number
  
  // Metas e Controle
  meta_consumo_mensal?: number
  meta_eficiencia?: number
  
  // Responsáveis
  responsavel_principal: string
  responsaveis_secundarios: string[]
  
  // Status
  ativo: boolean
  
  // Auditoria
  criado_em: Date
  clinica_id: string
}

// Controle de Lotes
interface LoteEstoque {
  id: string
  produto_id: string
  numero_lote: string
  
  // Datas
  data_fabricacao: Date
  data_validade: Date
  dias_para_vencer: number
  
  // Quantidades
  quantidade_inicial: number
  quantidade_atual: number
  quantidade_reservada: number
  quantidade_disponivel: number
  
  // Localização
  localizacao_principal: string
  localizacoes_secundarias: string[]
  
  // Status do Lote
  status: StatusLote
  bloqueado: boolean
  motivo_bloqueio?: string
  
  // FIFO Control
  prioridade_uso: number
  proximo_a_vencer: boolean
  
  // Fornecedor
  entrada_id: string
  fornecedor_id: string
  nota_fiscal?: string
  
  // Qualidade
  inspecionado: boolean
  aprovado_qualidade: boolean
  observacoes_qualidade?: string
  
  // Auditoria
  criado_em: Date
  clinica_id: string
}

// Análise de Consumo
interface AnaliseConsumo {
  periodo: PeriodoAnalise
  centro_custo_id?: string
  profissional_id?: string
  
  // Métricas Gerais
  total_saidas: number
  valor_total_consumido: number
  numero_produtos_diferentes: number
  numero_procedimentos: number
  
  // Top Produtos
  produtos_mais_consumidos: ProdutoConsumo[]
  produtos_maior_custo: ProdutoConsumo[]
  produtos_maior_desperdicio: ProdutoConsumo[]
  
  // Eficiência
  eficiencia_uso: number
  desperdicio_percentual: number
  economia_fifo: number
  
  // Comparativos
  variacao_periodo_anterior: number
  ranking_profissionais: RankingConsumo[]
  ranking_centros_custo: RankingConsumo[]
  
  // Previsões
  previsao_demanda: PrevisaoDemanda[]
  recomendacoes_otimizacao: RecomendacaoOtimizacao[]
  
  // Alertas
  alertas_consumo_anormal: AlertaConsumo[]
  produtos_desperdicio_alto: string[]
}

// Transferência Interna
interface TransferenciaInterna {
  id: string
  numero_transferencia: string
  
  // Origem e Destino
  centro_custo_origem: string
  centro_custo_destino: string
  localizacao_origem: string
  localizacao_destino: string
  
  // Solicitação
  solicitado_por: string
  motivo_transferencia: string
  urgente: boolean
  
  // Itens
  itens: ItemTransferencia[]
  quantidade_total: number
  valor_total: number
  
  // Workflow
  status: StatusTransferencia
  aprovado: boolean
  aprovado_por?: string
  aprovado_em?: Date
  
  // Execução
  executado: boolean
  executado_por?: string
  executado_em?: Date
  
  // Observações
  observacoes?: string
  
  // Auditoria
  criado_em: Date
  criado_por: string
  clinica_id: string
}

// Item de Transferência
interface ItemTransferencia {
  id: string
  transferencia_id: string
  produto_id: string
  lote_id: string
  
  // Quantidade
  quantidade_solicitada: number
  quantidade_transferida: number
  
  // Status
  status: StatusItemTransferencia
  observacoes?: string
  
  // Auditoria
  transferido_em?: Date
  transferido_por?: string
}

// Previsão de Demanda
interface PrevisaoDemanda {
  produto_id: string
  centro_custo_id: string
  
  // Histórico
  consumo_medio_diario: number
  consumo_ultimo_mes: number
  tendencia: TendenciaConsumo
  
  // Previsão
  demanda_proximos_30_dias: number
  demanda_proximos_60_dias: number
  demanda_proximos_90_dias: number
  
  // Confiabilidade
  precisao_previsao: number
  fatores_sazonais: FatorSazonal[]
  
  // Recomendações
  ponto_pedido_sugerido: number
  quantidade_seguranca_sugerida: number
}

// Tipos Enum
type TipoSaida = 'procedimento' | 'uso_direto' | 'perda' | 'vencimento' | 'transferencia' | 'ajuste' | 'devolucao'
type StatusSaida = 'registrada' | 'aprovada' | 'executada' | 'revertida' | 'cancelada'
type StatusLote = 'disponivel' | 'reservado' | 'bloqueado' | 'vencido' | 'esgotado'
type StatusTransferencia = 'solicitada' | 'aprovada' | 'em_transito' | 'concluida' | 'cancelada'
type StatusItemTransferencia = 'pendente' | 'transferido' | 'cancelado'
type TendenciaConsumo = 'crescente' | 'estavel' | 'decrescente' | 'sazonal'
```

#### Database Schema for Stock Output Control

```sql
-- Saídas de Estoque
CREATE TABLE saidas_estoque (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  numero_saida VARCHAR(50) NOT NULL,
  tipo_saida tipo_saida_type DEFAULT 'uso_direto',
  
  -- Origem da Saída
  procedimento_id UUID REFERENCES procedimentos(id),
  agendamento_id UUID REFERENCES agendamentos(id),
  centro_custo_id UUID NOT NULL REFERENCES centros_custo(id),
  sala_id UUID REFERENCES salas(id),
  equipamento_id UUID REFERENCES equipamentos(id),
  
  -- Responsável
  profissional_id UUID NOT NULL REFERENCES auth.users(id),
  responsavel_registro UUID NOT NULL REFERENCES auth.users(id),
  
  -- Data e Hora
  data_saida DATE NOT NULL DEFAULT CURRENT_DATE,
  hora_saida TIME NOT NULL DEFAULT CURRENT_TIME,
  
  -- Totais
  quantidade_total DECIMAL(15,3) DEFAULT 0,
  valor_total DECIMAL(15,2) DEFAULT 0,
  custo_total DECIMAL(15,2) DEFAULT 0,
  
  -- Motivo e Observações
  motivo_saida VARCHAR(500) NOT NULL,
  observacoes TEXT,
  evidencias TEXT[], -- URLs de fotos/documentos
  
  -- Aprovação
  requer_aprovacao BOOLEAN DEFAULT FALSE,
  aprovado BOOLEAN DEFAULT FALSE,
  aprovado_por UUID REFERENCES auth.users(id),
  aprovado_em TIMESTAMPTZ,
  
  -- Status e Controle
  status status_saida_type DEFAULT 'registrada',
  automatico BOOLEAN DEFAULT FALSE,
  reversivel BOOLEAN DEFAULT TRUE,
  revertido BOOLEAN DEFAULT FALSE,
  saida_reversao_id UUID REFERENCES saidas_estoque(id),
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  ultima_atualizacao TIMESTAMPTZ DEFAULT NOW(),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_numero_saida_clinica UNIQUE (numero_saida, clinica_id),
  CONSTRAINT chk_quantidade_total_positiva CHECK (quantidade_total >= 0)
);

-- Itens das Saídas
CREATE TABLE itens_saida_estoque (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  saida_id UUID NOT NULL REFERENCES saidas_estoque(id) ON DELETE CASCADE,
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id),
  lote_id UUID NOT NULL REFERENCES lotes_estoque(id),
  
  -- Quantidade e Custos
  quantidade DECIMAL(15,3) NOT NULL,
  custo_unitario DECIMAL(15,2) NOT NULL,
  valor_total DECIMAL(15,2) GENERATED ALWAYS AS (quantidade * custo_unitario) STORED,
  
  -- Localização
  localizacao_origem VARCHAR(100) NOT NULL,
  localizacao_destino VARCHAR(100),
  
  -- FIFO Control
  ordem_fifo INTEGER DEFAULT 0,
  selecionado_automaticamente BOOLEAN DEFAULT FALSE,
  
  -- Observações
  motivo_item VARCHAR(255),
  observacoes_item TEXT,
  
  -- Auditoria
  baixado_em TIMESTAMPTZ DEFAULT NOW(),
  baixado_por UUID NOT NULL REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_quantidade_positiva CHECK (quantidade > 0),
  CONSTRAINT chk_custo_positivo CHECK (custo_unitario > 0)
);

-- Configuração de Materiais por Procedimento
CREATE TABLE procedimentos_materiais (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  procedimento_id UUID NOT NULL REFERENCES procedimentos(id),
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id),
  
  -- Quantidade Padrão
  quantidade_padrao DECIMAL(15,3) NOT NULL,
  quantidade_minima DECIMAL(15,3) DEFAULT 0,
  quantidade_maxima DECIMAL(15,3),
  
  -- Opções
  obrigatorio BOOLEAN DEFAULT FALSE,
  permite_ajuste BOOLEAN DEFAULT TRUE,
  unidade_medida VARCHAR(20) DEFAULT 'unidade',
  
  -- Custos
  custo_padrao DECIMAL(15,2) DEFAULT 0,
  margem_aplicada DECIMAL(5,2) DEFAULT 0,
  
  -- Controle
  ativo BOOLEAN DEFAULT TRUE,
  observacoes TEXT,
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  atualizado_em TIMESTAMPTZ DEFAULT NOW(),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_procedimento_produto UNIQUE (procedimento_id, produto_id),
  CONSTRAINT chk_quantidade_padrao_positiva CHECK (quantidade_padrao > 0)
);

-- Centros de Custo
CREATE TABLE centros_custo (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  codigo VARCHAR(20) NOT NULL,
  nome VARCHAR(100) NOT NULL,
  descricao TEXT,
  
  -- Hierarquia
  centro_pai_id UUID REFERENCES centros_custo(id),
  nivel INTEGER DEFAULT 1,
  caminho_completo VARCHAR(500),
  
  -- Configurações
  controla_estoque BOOLEAN DEFAULT TRUE,
  requer_aprovacao_saida BOOLEAN DEFAULT FALSE,
  limite_valor_saida DECIMAL(15,2),
  
  -- Metas e Controle
  meta_consumo_mensal DECIMAL(15,2),
  meta_eficiencia DECIMAL(5,2),
  
  -- Responsáveis
  responsavel_principal UUID NOT NULL REFERENCES auth.users(id),
  responsaveis_secundarios UUID[],
  
  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  ultima_atualizacao TIMESTAMPTZ DEFAULT NOW(),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_codigo_centro_custo_clinica UNIQUE (codigo, clinica_id)
);

-- Controle de Lotes
CREATE TABLE lotes_estoque (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id),
  numero_lote VARCHAR(50) NOT NULL,
  
  -- Datas
  data_fabricacao DATE,
  data_validade DATE,
  dias_para_vencer INTEGER GENERATED ALWAYS AS (data_validade - CURRENT_DATE) STORED,
  
  -- Quantidades
  quantidade_inicial DECIMAL(15,3) NOT NULL,
  quantidade_atual DECIMAL(15,3) NOT NULL,
  quantidade_reservada DECIMAL(15,3) DEFAULT 0,
  quantidade_disponivel DECIMAL(15,3) GENERATED ALWAYS AS (quantidade_atual - quantidade_reservada) STORED,
  
  -- Localização
  localizacao_principal VARCHAR(100) NOT NULL,
  localizacoes_secundarias VARCHAR(100)[],
  
  -- Status do Lote
  status status_lote_type DEFAULT 'disponivel',
  bloqueado BOOLEAN DEFAULT FALSE,
  motivo_bloqueio TEXT,
  
  -- FIFO Control
  prioridade_uso INTEGER DEFAULT 0,
  proximo_a_vencer BOOLEAN GENERATED ALWAYS AS (
    data_validade IS NOT NULL AND data_validade - CURRENT_DATE <= 30
  ) STORED,
  
  -- Fornecedor
  entrada_id UUID NOT NULL REFERENCES entradas_estoque(id),
  fornecedor_id UUID NOT NULL REFERENCES fornecedores(id),
  nota_fiscal VARCHAR(50),
  
  -- Qualidade
  inspecionado BOOLEAN DEFAULT FALSE,
  aprovado_qualidade BOOLEAN DEFAULT TRUE,
  observacoes_qualidade TEXT,
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  ultima_movimentacao TIMESTAMPTZ,
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_produto_lote_clinica UNIQUE (produto_id, numero_lote, clinica_id),
  CONSTRAINT chk_quantidade_atual_valida CHECK (quantidade_atual >= 0),
  CONSTRAINT chk_quantidade_reservada_valida CHECK (quantidade_reservada >= 0 AND quantidade_reservada <= quantidade_atual)
);

-- Transferências Internas
CREATE TABLE transferencias_internas (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  numero_transferencia VARCHAR(50) NOT NULL,
  
  -- Origem e Destino
  centro_custo_origem UUID NOT NULL REFERENCES centros_custo(id),
  centro_custo_destino UUID NOT NULL REFERENCES centros_custo(id),
  localizacao_origem VARCHAR(100) NOT NULL,
  localizacao_destino VARCHAR(100) NOT NULL,
  
  -- Solicitação
  solicitado_por UUID NOT NULL REFERENCES auth.users(id),
  motivo_transferencia VARCHAR(500) NOT NULL,
  urgente BOOLEAN DEFAULT FALSE,
  
  -- Totais
  quantidade_total DECIMAL(15,3) DEFAULT 0,
  valor_total DECIMAL(15,2) DEFAULT 0,
  
  -- Workflow
  status status_transferencia_type DEFAULT 'solicitada',
  aprovado BOOLEAN DEFAULT FALSE,
  aprovado_por UUID REFERENCES auth.users(id),
  aprovado_em TIMESTAMPTZ,
  
  -- Execução
  executado BOOLEAN DEFAULT FALSE,
  executado_por UUID REFERENCES auth.users(id),
  executado_em TIMESTAMPTZ,
  
  -- Observações
  observacoes TEXT,
  
  -- Auditoria
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT uk_numero_transferencia_clinica UNIQUE (numero_transferencia, clinica_id),
  CONSTRAINT chk_origem_destino_diferentes CHECK (centro_custo_origem != centro_custo_destino)
);

-- Itens das Transferências
CREATE TABLE itens_transferencia_interna (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  transferencia_id UUID NOT NULL REFERENCES transferencias_internas(id) ON DELETE CASCADE,
  produto_id UUID NOT NULL REFERENCES produtos_estoque(id),
  lote_id UUID NOT NULL REFERENCES lotes_estoque(id),
  
  -- Quantidade
  quantidade_solicitada DECIMAL(15,3) NOT NULL,
  quantidade_transferida DECIMAL(15,3) DEFAULT 0,
  
  -- Status
  status status_item_transferencia_type DEFAULT 'pendente',
  observacoes TEXT,
  
  -- Auditoria
  transferido_em TIMESTAMPTZ,
  transferido_por UUID REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_quantidade_solicitada_positiva CHECK (quantidade_solicitada > 0),
  CONSTRAINT chk_quantidade_transferida_valida CHECK (quantidade_transferida >= 0 AND quantidade_transferida <= quantidade_solicitada)
);

-- Análises de Consumo (Cache de Relatórios)
CREATE TABLE analises_consumo (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Filtros da Análise
  data_inicio DATE NOT NULL,
  data_fim DATE NOT NULL,
  centro_custo_id UUID REFERENCES centros_custo(id),
  profissional_id UUID REFERENCES auth.users(id),
  
  -- Dados da Análise
  metricas_gerais JSONB NOT NULL,
  produtos_consumo JSONB DEFAULT '[]',
  eficiencia_uso JSONB DEFAULT '{}',
  previsoes_demanda JSONB DEFAULT '[]',
  recomendacoes JSONB DEFAULT '[]',
  
  -- Metadados
  gerada_em TIMESTAMPTZ DEFAULT NOW(),
  gerada_por UUID NOT NULL REFERENCES auth.users(id),
  valida_ate TIMESTAMPTZ,
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_periodo_analise CHECK (data_fim >= data_inicio)
);

-- Tipos Enum para Saídas
CREATE TYPE tipo_saida_type AS ENUM ('procedimento', 'uso_direto', 'perda', 'vencimento', 'transferencia', 'ajuste', 'devolucao');
CREATE TYPE status_saida_type AS ENUM ('registrada', 'aprovada', 'executada', 'revertida', 'cancelada');
CREATE TYPE status_lote_type AS ENUM ('disponivel', 'reservado', 'bloqueado', 'vencido', 'esgotado');
CREATE TYPE status_transferencia_type AS ENUM ('solicitada', 'aprovada', 'em_transito', 'concluida', 'cancelada');
CREATE TYPE status_item_transferencia_type AS ENUM ('pendente', 'transferido', 'cancelado');

-- Índices para Performance de Saídas
CREATE INDEX idx_saidas_data ON saidas_estoque(data_saida);
CREATE INDEX idx_saidas_centro_custo ON saidas_estoque(centro_custo_id);
CREATE INDEX idx_saidas_profissional ON saidas_estoque(profissional_id);
CREATE INDEX idx_saidas_tipo ON saidas_estoque(tipo_saida);
CREATE INDEX idx_saidas_procedimento ON saidas_estoque(procedimento_id) WHERE procedimento_id IS NOT NULL;

-- Índices para Lotes e FIFO
CREATE INDEX idx_lotes_produto ON lotes_estoque(produto_id);
CREATE INDEX idx_lotes_validade ON lotes_estoque(data_validade) WHERE data_validade IS NOT NULL;
CREATE INDEX idx_lotes_disponivel ON lotes_estoque(quantidade_disponivel) WHERE quantidade_disponivel > 0;
CREATE INDEX idx_lotes_fifo ON lotes_estoque(produto_id, data_validade, prioridade_uso);
CREATE INDEX idx_lotes_vencimento ON lotes_estoque(dias_para_vencer) WHERE dias_para_vencer <= 30;

-- Índices para Centros de Custo
CREATE INDEX idx_centros_custo_hierarquia ON centros_custo(centro_pai_id, nivel);
CREATE INDEX idx_centros_custo_ativo ON centros_custo(ativo) WHERE ativo = TRUE;

-- Índices para Transferências
CREATE INDEX idx_transferencias_origem ON transferencias_internas(centro_custo_origem);
CREATE INDEX idx_transferencias_destino ON transferencias_internas(centro_custo_destino);
CREATE INDEX idx_transferencias_status ON transferencias_internas(status);

-- Índices Compostos para Analytics
CREATE INDEX idx_saidas_analytics ON saidas_estoque(clinica_id, data_saida, centro_custo_id, profissional_id);
CREATE INDEX idx_itens_saida_analytics ON itens_saida_estoque(produto_id, baixado_em, centro_custo_id);

-- Full-text search para materiais e procedimentos
CREATE INDEX idx_procedimentos_materiais_search ON procedimentos_materiais USING gin(
  to_tsvector('portuguese', observacoes)
) WHERE observacoes IS NOT NULL;
```

#### Stock Output API Endpoints

```typescript
// Stock Output Management API
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '50')
  const dataInicio = searchParams.get('data_inicio')
  const dataFim = searchParams.get('data_fim')
  const centroCustoId = searchParams.get('centro_custo_id')
  const profissionalId = searchParams.get('profissional_id')
  const tipo = searchParams.get('tipo')
  
  const supabase = createServerClient()
  
  try {
    let query = supabase
      .from('saidas_estoque')
      .select(`
        *,
        centro_custo:centros_custo(nome, codigo),
        profissional:auth.users(nome),
        procedimento:procedimentos(nome),
        itens:itens_saida_estoque(
          *,
          produto:produtos_estoque(nome, codigo_interno),
          lote:lotes_estoque(numero_lote, data_validade)
        )
      `)
    
    // Aplicar filtros
    if (dataInicio) query = query.gte('data_saida', dataInicio)
    if (dataFim) query = query.lte('data_saida', dataFim)
    if (centroCustoId) query = query.eq('centro_custo_id', centroCustoId)
    if (profissionalId) query = query.eq('profissional_id', profissionalId)
    if (tipo) query = query.eq('tipo_saida', tipo)
    
    // Paginação
    const from = (page - 1) * limit
    const to = from + limit - 1
    
    const { data: saidas, count } = await query
      .range(from, to)
      .order('data_saida', { ascending: false })
    
    // Estatísticas das saídas
    const estatisticas = await calcularEstatisticasSaidas(searchParams)
    
    return NextResponse.json({
      saidas,
      estatisticas,
      paginacao: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      },
      message: 'Saídas carregadas com sucesso'
    })
    
  } catch (error) {
    console.error('Error loading stock outputs:', error)
    return NextResponse.json({
      error: 'Erro ao carregar saídas'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  const supabase = createServerClient()
  
  try {
    const {
      tipo_saida,
      centro_custo_id,
      profissional_id,
      procedimento_id,
      itens,
      motivo_saida,
      automatico = false
    } = await request.json()
    
    // Validar dados obrigatórios
    if (!centro_custo_id || !profissional_id || !itens || itens.length === 0) {
      return NextResponse.json({
        error: 'Centro de custo, profissional e itens são obrigatórios'
      }, { status: 400 })
    }
    
    // Verificar disponibilidade de estoque
    const verificacao = await verificarDisponibilidadeEstoque(itens)
    if (!verificacao.disponivel) {
      return NextResponse.json({
        error: 'Estoque insuficiente',
        itens_indisponiveis: verificacao.itens_indisponiveis
      }, { status: 400 })
    }
    
    // Gerar número da saída
    const numeroSaida = await gerarNumeroSaida()
    
    // Aplicar FIFO para seleção de lotes
    const itensComFifo = await aplicarSelecaoFIFO(itens)
    
    // Calcular totais
    const totais = calcularTotaisSaida(itensComFifo)
    
    // Verificar se requer aprovação
    const requerAprovacao = await verificarNecessidadeAprovacao(
      centro_custo_id, 
      totais.valor_total, 
      tipo_saida
    )
    
    // Criar saída
    const { data: saida, error: saidaError } = await supabase
      .from('saidas_estoque')
      .insert({
        numero_saida: numeroSaida,
        tipo_saida,
        centro_custo_id,
        profissional_id,
        procedimento_id,
        quantidade_total: totais.quantidade,
        valor_total: totais.valor,
        custo_total: totais.custo,
        motivo_saida,
        automatico,
        requer_aprovacao: requerAprovacao,
        aprovado: !requerAprovacao,
        status: requerAprovacao ? 'registrada' : 'aprovada'
      })
      .select()
      .single()
    
    if (saidaError) throw saidaError
    
    // Criar itens da saída
    const itensData = itensComFifo.map(item => ({
      saida_id: saida.id,
      produto_id: item.produto_id,
      lote_id: item.lote_id,
      quantidade: item.quantidade,
      custo_unitario: item.custo_unitario,
      localizacao_origem: item.localizacao_origem,
      ordem_fifo: item.ordem_fifo,
      selecionado_automaticamente: item.automatico
    }))
    
    const { data: itensCreated } = await supabase
      .from('itens_saida_estoque')
      .insert(itensData)
      .select()
    
    // Atualizar quantidades nos lotes se aprovado
    if (!requerAprovacao) {
      await atualizarQuantidadesLotes(itensCreated)
    }
    
    return NextResponse.json({
      saida: { ...saida, itens: itensCreated },
      message: requerAprovacao 
        ? 'Saída registrada e aguardando aprovação'
        : 'Saída processada com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating stock output:', error)
    return NextResponse.json({
      error: 'Erro ao processar saída'
    }, { status: 500 })
  }
}

// Automatic Procedure Material Deduction
export async function POST(
  request: NextRequest,
  { params }: { params: { action: string } }
) {
  const supabase = createServerClient()
  
  try {
    if (params.action === 'baixa-automatica-procedimento') {
      const { 
        procedimento_id, 
        agendamento_id, 
        profissional_id,
        ajustes_manuais = []
      } = await request.json()
      
      // Buscar materiais padrão do procedimento
      const { data: materiaisPadrao } = await supabase
        .from('procedimentos_materiais')
        .select(`
          *,
          produto:produtos_estoque(nome, codigo_interno, categoria)
        `)
        .eq('procedimento_id', procedimento_id)
        .eq('ativo', true)
      
      // Aplicar ajustes manuais se fornecidos
      const materiaisParaBaixa = aplicarAjustesManuais(
        materiaisPadrao, 
        ajustes_manuais
      )
      
      // Verificar disponibilidade
      const verificacao = await verificarDisponibilidadeEstoque(materiaisParaBaixa)
      if (!verificacao.disponivel) {
        return NextResponse.json({
          error: 'Estoque insuficiente para materiais do procedimento',
          itens_indisponiveis: verificacao.itens_indisponiveis,
          sugestoes_alternativas: await buscarAlternativas(verificacao.itens_indisponiveis)
        }, { status: 400 })
      }
      
      // Criar saída automática
      const saida = await criarSaidaAutomaticaProcedimento({
        procedimento_id,
        agendamento_id,
        profissional_id,
        materiais: materiaisParaBaixa
      })
      
      return NextResponse.json({
        saida,
        materiais_utilizados: materiaisParaBaixa,
        message: 'Baixa automática realizada com sucesso'
      })
    }
    
    if (params.action === 'sugerir-materiais') {
      const { procedimento_id } = await request.json()
      
      // Buscar sugestões baseadas em histórico
      const sugestoes = await sugerirMateriaisProcedimento(procedimento_id)
      
      return NextResponse.json({
        sugestoes,
        message: 'Sugestões geradas com base no histórico'
      })
    }
    
    if (params.action === 'verificar-disponibilidade') {
      const { itens } = await request.json()
      
      const verificacao = await verificarDisponibilidadeComFIFO(itens)
      
      return NextResponse.json({
        verificacao,
        message: 'Verificação de disponibilidade concluída'
      })
    }
    
  } catch (error) {
    console.error('Error in automatic deduction:', error)
    return NextResponse.json({
      error: 'Erro no processamento automático'
    }, { status: 500 })
  }
}

// FIFO and Batch Management API
export async function GET(
  request: NextRequest,
  { params }: { params: { type: string } }
) {
  const { searchParams } = new URL(request.url)
  const supabase = createServerClient()
  
  try {
    if (params.type === 'lotes-vencimento') {
      const dias = parseInt(searchParams.get('dias') || '30')
      
      // Buscar lotes próximos ao vencimento
      const { data: lotes } = await supabase
        .from('lotes_estoque')
        .select(`
          *,
          produto:produtos_estoque(nome, codigo_interno, categoria)
        `)
        .lte('dias_para_vencer', dias)
        .gt('quantidade_disponivel', 0)
        .eq('status', 'disponivel')
        .order('dias_para_vencer', { ascending: true })
      
      // Agrupar por criticidade
      const agrupados = agruparLotesPorCriticidade(lotes)
      
      return NextResponse.json({
        lotes_agrupados: agrupados,
        total_produtos: lotes?.length || 0,
        valor_total_risco: calcularValorTotalRisco(lotes),
        message: 'Lotes próximos ao vencimento'
      })
    }
    
    if (params.type === 'fifo-disponivel') {
      const produtoId = searchParams.get('produto_id')
      
      if (!produtoId) {
        return NextResponse.json({
          error: 'ID do produto é obrigatório'
        }, { status: 400 })
      }
      
      // Buscar lotes disponíveis ordenados por FIFO
      const lotesFifo = await buscarLotesFIFO(produtoId)
      
      return NextResponse.json({
        lotes_fifo: lotesFifo,
        message: 'Lotes ordenados por FIFO'
      })
    }
    
    if (params.type === 'consumo-centro-custo') {
      const centroCustoId = searchParams.get('centro_custo_id')
      const periodo = parseInt(searchParams.get('periodo') || '30')
      
      const analise = await analisarConsumoCentroCusto(centroCustoId, periodo)
      
      return NextResponse.json({
        analise,
        message: 'Análise de consumo por centro de custo'
      })
    }
    
  } catch (error) {
    console.error('Error in FIFO/batch management:', error)
    return NextResponse.json({
      error: 'Erro na gestão de lotes'
    }, { status: 500 })
  }
}

// Internal Transfer API
export async function POST(request: NextRequest) {
  const supabase = createServerClient()
  
  try {
    const {
      centro_custo_origem,
      centro_custo_destino,
      localizacao_origem,
      localizacao_destino,
      motivo_transferencia,
      itens,
      urgente = false
    } = await request.json()
    
    // Validar transferência
    if (centro_custo_origem === centro_custo_destino) {
      return NextResponse.json({
        error: 'Origem e destino devem ser diferentes'
      }, { status: 400 })
    }
    
    // Verificar disponibilidade na origem
    const verificacao = await verificarDisponibilidadeTransferencia(
      centro_custo_origem, 
      itens
    )
    
    if (!verificacao.disponivel) {
      return NextResponse.json({
        error: 'Itens não disponíveis para transferência',
        itens_indisponiveis: verificacao.itens_indisponiveis
      }, { status: 400 })
    }
    
    // Gerar número da transferência
    const numeroTransferencia = await gerarNumeroTransferencia()
    
    // Calcular totais
    const totais = calcularTotaisTransferencia(itens)
    
    // Verificar se requer aprovação
    const requerAprovacao = await verificarAprovacaoTransferencia(
      centro_custo_origem,
      centro_custo_destino,
      totais.valor_total
    )
    
    // Criar transferência
    const { data: transferencia } = await supabase
      .from('transferencias_internas')
      .insert({
        numero_transferencia: numeroTransferencia,
        centro_custo_origem,
        centro_custo_destino,
        localizacao_origem,
        localizacao_destino,
        motivo_transferencia,
        urgente,
        quantidade_total: totais.quantidade,
        valor_total: totais.valor,
        aprovado: !requerAprovacao,
        status: requerAprovacao ? 'solicitada' : 'aprovada'
      })
      .select()
      .single()
    
    // Criar itens da transferência
    const itensData = itens.map(item => ({
      transferencia_id: transferencia.id,
      produto_id: item.produto_id,
      lote_id: item.lote_id,
      quantidade_solicitada: item.quantidade
    }))
    
    const { data: itensCreated } = await supabase
      .from('itens_transferencia_interna')
      .insert(itensData)
      .select()
    
    // Se não requer aprovação, executar transferência
    if (!requerAprovacao) {
      await executarTransferencia(transferencia.id)
    }
    
    return NextResponse.json({
      transferencia: { ...transferencia, itens: itensCreated },
      message: requerAprovacao 
        ? 'Transferência solicitada e aguardando aprovação'
        : 'Transferência executada com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating internal transfer:', error)
    return NextResponse.json({
      error: 'Erro ao criar transferência'
    }, { status: 500 })
  }
}

// Consumption Analytics API
export async function GET(
  request: NextRequest,
  { params }: { params: { tipo: string } }
) {
  const { searchParams } = new URL(request.url)
  const periodo = parseInt(searchParams.get('periodo') || '30')
  const centroCustoId = searchParams.get('centro_custo_id')
  const profissionalId = searchParams.get('profissional_id')
  
  const supabase = createServerClient()
  
  try {
    const dataInicio = new Date()
    dataInicio.setDate(dataInicio.getDate() - periodo)
    
    if (params.tipo === 'consumo-geral') {
      const analise = await analisarConsumoGeral(dataInicio, {
        centro_custo_id: centroCustoId,
        profissional_id: profissionalId
      })
      
      return NextResponse.json({
        analise,
        message: 'Análise geral de consumo'
      })
    }
    
    if (params.tipo === 'eficiencia-profissionais') {
      const analise = await analisarEficienciaProfissionais(dataInicio)
      
      return NextResponse.json({
        analise,
        message: 'Análise de eficiência por profissional'
      })
    }
    
    if (params.tipo === 'previsao-demanda') {
      const previsoes = await gerarPrevisaoDemanda(periodo)
      
      return NextResponse.json({
        previsoes,
        message: 'Previsão de demanda gerada'
      })
    }
    
    if (params.tipo === 'desperdicio') {
      const analise = await analisarDesperdicio(dataInicio)
      
      return NextResponse.json({
        analise,
        message: 'Análise de desperdício e perdas'
      })
    }
    
  } catch (error) {
    console.error('Error generating consumption analytics:', error)
    return NextResponse.json({
      error: 'Erro ao gerar análise de consumo'
    }, { status: 500 })
  }
}
```

### Integration Points

#### Epic 6 Integration (Agenda Inteligente)

- **Automatic Deduction**: Baixa automática de materiais ao finalizar procedimentos
- **Material Planning**: Planejamento de materiais baseado na agenda
- **Procedure Integration**: Link direto entre agendamentos e consumo
- **Resource Optimization**: Otimização de recursos por sala/profissional

#### Epic 7 Integration (Financeiro Essencial)

- **Cost Centers**: Integração com centros de custo financeiros
- **Cost Allocation**: Rateio de custos por procedimento/profissional
- **Budget Control**: Controle orçamentário por centro de custo
- **Profitability**: Análise de rentabilidade por setor

#### Epic 8 Integration (BI & Dashboards)

- **Consumption KPIs**: Métricas de consumo nos dashboards
- **Efficiency Analytics**: Análise de eficiência operacional
- **Waste Tracking**: Acompanhamento de desperdícios e perdas
- **Demand Forecasting**: Previsões de demanda baseadas em IA

#### Story 11.1-11.2 Integration

- **Product Foundation**: Base de produtos e fornecedores estabelecida
- **Cost Updates**: Atualização automática de custos médios
- **Purchase Integration**: Integração com entradas de compra
- **Supplier Analysis**: Análise de performance baseada no consumo

### Testing Strategy

#### Stock Output Tests

```typescript
describe('Stock Output Management', () => {
  test('processes automatic procedure deduction', async () => {
    const procedimento = await createTestProcedure()
    const materiais = await setupProcedureMaterials(procedimento.id)
    
    const saida = await processAutomaticDeduction({
      procedimento_id: procedimento.id,
      profissional_id: testUserId,
      centro_custo_id: testCenterCostId
    })
    
    expect(saida.automatico).toBe(true)
    expect(saida.status).toBe('aprovada')
    expect(saida.itens).toHaveLength(materiais.length)
  })
  
  test('applies FIFO correctly for batch selection', async () => {
    const produto = await createTestProduct()
    const loteAntigo = await createBatch(produto.id, { data_validade: '2024-06-01' })
    const loteNovo = await createBatch(produto.id, { data_validade: '2024-12-01' })
    
    const fifoSelection = await selectBatchesFIFO(produto.id, 5)
    
    expect(fifoSelection[0].lote_id).toBe(loteAntigo.id)
    expect(fifoSelection[0].ordem_fifo).toBe(1)
  })
  
  test('manages internal transfers with approval', async () => {
    const transferencia = await createInternalTransfer({
      centro_custo_origem: 'SALA01',
      centro_custo_destino: 'SALA02',
      valor_total: 500.00
    })
    
    expect(transferencia.requer_aprovacao).toBe(true)
    expect(transferencia.status).toBe('solicitada')
    
    await approveTransfer(transferencia.id)
    const approved = await getTransfer(transferencia.id)
    
    expect(approved.status).toBe('aprovada')
  })
  
  test('tracks consumption by cost center', async () => {
    await createMultipleOutputs('SALA01', 10)
    
    const analise = await analyzeCostCenterConsumption('SALA01', 30)
    
    expect(analise.total_saidas).toBe(10)
    expect(analise.eficiencia_uso).toBeGreaterThan(0)
    expect(analise.produtos_mais_consumidos).toBeDefined()
  })
  
  test('generates demand forecasting accurately', async () => {
    await createHistoricalConsumption(testProductId, 90)
    
    const previsao = await generateDemandForecast(testProductId, 30)
    
    expect(previsao.demanda_proximos_30_dias).toBeGreaterThan(0)
    expect(previsao.precisao_previsao).toBeGreaterThan(0.7)
  })
})
```

### Dev Notes

#### Advanced FIFO Features

- **Smart Batch Selection**: Seleção inteligente de lotes baseada em FIFO + proximidade do vencimento
- **Automatic Alerts**: Alertas automáticos para lotes próximos ao vencimento
- **Quality Control**: Controle de qualidade integrado à gestão de lotes
- **Traceability**: Rastreabilidade completa dos materiais utilizados

#### Consumption Analytics

- **AI-Powered Forecasting**: Previsão de demanda usando machine learning
- **Anomaly Detection**: Detecção de padrões anômalos de consumo
- **Efficiency Benchmarking**: Comparação de eficiência entre profissionais
- **Cost Optimization**: Sugestões automáticas de otimização de custos

#### Integration Capabilities

- **Real-time Updates**: Atualizações em tempo real do estoque
- **Procedure Integration**: Integração completa com sistema de agendamentos
- **Financial Control**: Controle financeiro por centro de custo
- **BI Analytics**: Analytics avançado para tomada de decisão

---

## Dev Agent Record

### Task Status

- [x] Analyzed stock output and consumption requirements for Epic 11
- [x] Created comprehensive story with 6 acceptance criteria
- [x] Designed TypeScript interfaces for outputs, FIFO control, and consumption analytics
- [x] Specified database schema with FIFO optimization and batch management
- [x] Developed complete API endpoints for stock output management
- [x] Added automatic procedure deduction with material planning
- [x] Implemented FIFO batch selection and expiration control
- [x] Created internal transfer system with approval workflow
- [x] Established consumption analytics and demand forecasting
- [x] Created comprehensive testing strategy for output operations

### File List

- `docs/stories/11.3.story.md` - Stock Output Control and Consumption Management system

### Change Log

- **Story 11.3 Creation**: Complete stock output control with FIFO and consumption analytics
- **Automatic Deduction**: Integration with procedures for automatic material consumption
- **FIFO Management**: Smart batch selection with expiration control
- **Transfer System**: Internal transfers with approval workflow
- **Consumption Analytics**: Advanced analytics with demand forecasting
- **Cost Center Control**: Detailed cost allocation and efficiency tracking

### Completion Notes

Story 11.3 completes the core inventory movement system, building on the product foundation (11.1) and purchase management (11.2) to create comprehensive stock control. The story covers automatic deduction, FIFO management, internal transfers, and advanced consumption analytics.

### Next Steps

Ready to continue with Story 11.4: Alertas e Relatórios de Estoque to complete Epic 11 with comprehensive inventory analytics and automation.
