"""
Logging utilities for the VIBECODE-Kiro sync system.
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Optional


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    log_format: Optional[str] = None
) -> None:
    """Set up logging configuration for the sync system."""
    
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Create logs directory if it doesn't exist
    if log_file:
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
    else:
        # Default log file location
        log_dir = ".kiro/sync-system/logs"
        os.makedirs(log_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = f"{log_dir}/sync_system_{timestamp}.log"
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # Also log to console
        ]
    )
    
    # Set specific loggers to appropriate levels
    logging.getLogger("watchdog").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for the given name."""
    return logging.getLogger(name)


class SyncLogger:
    """Specialized logger for sync operations with structured logging."""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
        self.operation_id: Optional[str] = None
    
    def set_operation_id(self, operation_id: str) -> None:
        """Set operation ID for tracking related log entries."""
        self.operation_id = operation_id
    
    def _format_message(self, message: str) -> str:
        """Format message with operation ID if available."""
        if self.operation_id:
            return f"[{self.operation_id}] {message}"
        return message
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self.logger.info(self._format_message(message), extra=kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(self._format_message(message), extra=kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message."""
        self.logger.error(self._format_message(message), extra=kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(self._format_message(message), extra=kwargs)
    
    def sync_started(self, source: str, target: str) -> None:
        """Log sync operation start."""
        self.info(f"Sync started: {source} -> {target}")
    
    def sync_completed(self, files_processed: int, duration: float) -> None:
        """Log sync operation completion."""
        self.info(f"Sync completed: {files_processed} files processed in {duration:.2f}s")
    
    def sync_failed(self, error: str) -> None:
        """Log sync operation failure."""
        self.error(f"Sync failed: {error}")
    
    def file_processed(self, file_path: str, action: str) -> None:
        """Log file processing."""
        self.debug(f"File processed: {action} - {file_path}")
    
    def conflict_detected(self, file_path: str, conflict_type: str) -> None:
        """Log conflict detection."""
        self.warning(f"Conflict detected: {conflict_type} in {file_path}")
    
    def backup_created(self, backup_path: str, files_count: int) -> None:
        """Log backup creation."""
        self.info(f"Backup created: {backup_path} ({files_count} files)")
    
    def performance_metric(self, metric_name: str, value: float, unit: str = "") -> None:
        """Log performance metric."""
        self.debug(f"Performance: {metric_name} = {value}{unit}")


# Initialize default logging
setup_logging()