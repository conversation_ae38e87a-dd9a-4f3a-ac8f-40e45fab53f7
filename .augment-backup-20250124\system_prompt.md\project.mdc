---
description:
globs:
alwaysApply: true
---
# Project-Specific Rules

**Summary:**
Project-specific stack, preferences, and standards.

# Configuração de Projeto - GRUPO US
Version: 1.0.0

## Visão Geral
- **Empresa**: GRUPO US
- **Repositório**: https://github.com/GrupoUS/
- **Foco**: Desenvolvimento de SaaS
- **Idioma**: Código em inglês, conteúdo em português-BR

## Stack Tecnológica Obrigatória
### Frontend
- Next.js 14 (App Router)
- TypeScript (strict mode)
- Tailwind CSS
- shadcn/ui
- Zustand (estado global)
- React Query (data fetching)
- React Hook Form + Zod

### Backend
- Supabase (PostgreSQL + Auth + Storage)
- Prisma ORM
- tRPC ou API Routes
- Edge Functions quando necessário

### Ferramentas
- VS Code + Cline
- Task Master AI CLI
- Vercel (deploy)
- GitHub Actions (CI/CD)

## Estrutura de Pastas
src/
├── app/ # Next.js App Router
├── components/ # Componentes reutilizáveis
│ ├── ui/ # shadcn/ui
│ └── features/ # Componentes de features
├── lib/ # Utilitários e configurações
├── hooks/ # Custom hooks
├── stores/ # Zustand stores
├── types/ # TypeScript types
└── styles/ # Estilos globais

## Padrões de Código
### Nomenclatura
- Componentes: PascalCase
- Arquivos de componente: PascalCase.tsx
- Hooks: camelCase começando com 'use'
- Utilitários: camelCase
- Constantes: UPPER_SNAKE_CASE
- Variáveis de ambiente: NEXT_PUBLIC_ para cliente

### Componentes
- Sempre usar 'use client' apenas quando necessário
- Server Components por padrão
- Props com interface, não type
- Exportar interface de props

### Exemplo de Componente
```typescript
interface ButtonProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary'
  onClick?: () => void
}

export function Button({ children, variant = 'primary', onClick }: ButtonProps) {
  return (
    <button
      className={cn(
        'px-4 py-2 rounded-md font-medium',
        variant === 'primary' && 'bg-blue-600 text-white',
        variant === 'secondary' && 'bg-gray-200 text-gray-900'
      )}
      onClick={onClick}
    >
      {children}
    </button>
  )
}
```

## Preferências de Desenvolvimento
- Yarn sobre npm
- Commits convencionais (feat:, fix:, docs:)
- Branch naming: feature/nome-da-feature
- PRs com descrição detalhada
- Testes para lógica crítica
- Documentação inline com JSDoc
- Comentários em português quando necessário

## Segurança
- Nunca commitar .env
- Usar variáveis de ambiente do Vercel
- RLS habilitado no Supabase
- Validação com Zod em todas entradas
- Sanitização de dados do usuário

## Performance
- Lazy loading de componentes pesados
- Image optimization com next/image
- Minimizar re-renders (React.memo quando apropriado)
- Prefetch de dados críticos
- Cache adequado com React Query

## Considerações Especiais
- Mobile-first sempre
- Acessibilidade (ARIA labels)
- SEO otimizado
- Internacionalização preparada (mesmo que inicial só pt-BR)
- Monitoramento com Vercel Analytics

## Regras Relacionadas
- @memory.mdc: Regras principais do sistema de memória
- @self.mdc: Erros e correções conhecidas
- @database-schema.mdc: Schema do banco Supabase
- @apis.mdc: Documentação de endpoints da aplicação
