{"taskManagement": {"version": "1.0.0", "type": "native", "status": "active", "lastUpdated": "2025-07-16T00:00:00Z"}, "storage": {"primaryFile": "task-storage.md", "backupFile": "tasks.md", "location": "E:\\VIBECODE\\memory-bank\\", "format": "markdown", "encoding": "utf-8"}, "systems": {"cursor": {"enabled": true, "features": ["built-in task list", "command palette", "workspace tasks"], "syncEnabled": true, "autoSave": true}, "augment": {"enabled": true, "features": ["native task management", "workflow coordination", "memory integration"], "syncEnabled": true, "autoUpdate": true}}, "routing": {"byComplexity": {"simple": {"range": "1-4", "tools": ["cursor_native", "augment_native"], "storage": "memory-bank/task-storage.md"}, "medium": {"range": "5-7", "tools": ["sequential-thinking", "memory-bank"], "coordination": "native_task_managers"}, "complex": {"range": "8-10", "tools": ["sequential-thinking", "memory-bank", "manual_coordination"], "planning": "required"}}, "byType": {"daily": "cursor_augment_native", "quick": "cursor_augment_native", "add": "cursor_augment_native", "document": "memory_bank_storage", "knowledge": "memory_bank_storage", "orchestrate": "sequential_thinking_native", "dependencies": "sequential_thinking_native"}}, "integration": {"syncRule": ".augment follows .cursor changes", "conflictResolution": "cursor_authority", "memoryPersistence": true, "crossSession": true}}