{"source_path": "E:\\VIBECODE\\.cursor", "target_path": ".kiro", "auto_sync_enabled": true, "sync_interval_minutes": 30, "backup_retention_days": 30, "conflict_resolution_strategy": "preserve_kiro_customizations", "file_types": [".mdc", ".json", ".md", ".py"], "excluded_paths": ["temp", "cache", "logs", "__pycache__", ".git"], "debounce_delay_ms": 1000, "preserve_kiro_optimizations": true, "update_core_principles": true, "merge_mcp_configurations": true, "max_concurrent_operations": 5, "sync_timeout_seconds": 300, "enable_performance_monitoring": true, "validate_file_paths": true, "encrypt_backups": false, "log_security_events": true}