"""
🔄 VIBECODE V1.0 - OPERATIONAL CONTEXT MANAGER
Sistema de preservação automática de contexto e aprendizado contínuo

Funcionalidades Operacionais:
- Cross-session context preservation
- Continuous learning integration
- Real-time operational monitoring
- Production-ready memory management
"""

import json
import logging
import pickle
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import hashlib

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class OperationalSession:
    """Estrutura para sessões operacionais"""
    session_id: str
    start_time: str
    end_time: Optional[str]
    context_snapshot: Dict[str, Any]
    learning_data: List[Dict[str, Any]]
    performance_metrics: Dict[str, float]
    tasks_completed: List[str]

@dataclass
class LearningInsight:
    """Estrutura para insights de aprendizado"""
    timestamp: str
    insight_type: str  # 'pattern', 'optimization', 'error_correction'
    description: str
    confidence_score: float
    applied: bool
    impact_metrics: Dict[str, Any]

class OperationalContextManager:
    """Sistema de gestão operacional de contexto"""
    
    def __init__(self, memory_bank_path: str = "memory-bank"):
        self.memory_bank_path = Path(memory_bank_path)
        self.sessions_path = self.memory_bank_path / "python" / "sessions"
        self.learning_path = self.memory_bank_path / "python" / "learning_insights"
        self.context_cache_path = self.memory_bank_path / "python" / "context_cache.pkl"
        
        # Criar diretórios se não existirem
        self.sessions_path.mkdir(exist_ok=True)
        self.learning_path.mkdir(exist_ok=True)
        
        self.current_session = None
        self.context_cache = self._load_context_cache()
        
    def _load_context_cache(self) -> Dict[str, Any]:
        """Carrega cache de contexto da sessão anterior"""
        try:
            if self.context_cache_path.exists():
                with open(self.context_cache_path, 'rb') as f:
                    cache = pickle.load(f)
                logger.info("✅ Context cache loaded from previous session")
                return cache
        except Exception as e:
            logger.warning(f"⚠️ Could not load context cache: {e}")
        
        return {
            "last_session_id": None,
            "preserved_context": {},
            "learning_state": {},
            "performance_baseline": {}
        }
    
    def _save_context_cache(self):
        """Salva cache de contexto para próxima sessão"""
        try:
            with open(self.context_cache_path, 'wb') as f:
                pickle.dump(self.context_cache, f)
            logger.info("✅ Context cache saved for next session")
        except Exception as e:
            logger.error(f"❌ Error saving context cache: {e}")
    
    def start_operational_session(self) -> str:
        """Inicia nova sessão operacional"""
        session_id = hashlib.md5(
            f"{datetime.now(timezone.utc).isoformat()}".encode()
        ).hexdigest()[:12]
        
        # Restaura contexto da sessão anterior se disponível
        preserved_context = self.context_cache.get("preserved_context", {})
        
        self.current_session = OperationalSession(
            session_id=session_id,
            start_time=datetime.now(timezone.utc).isoformat(),
            end_time=None,
            context_snapshot=preserved_context,
            learning_data=[],
            performance_metrics={},
            tasks_completed=[]
        )
        
        logger.info(f"🚀 Operational session started: {session_id}")
        
        # Atualiza cache
        self.context_cache["last_session_id"] = session_id
        self._save_context_cache()
        
        return session_id
    
    def preserve_context(self, context_data: Dict[str, Any]):
        """Preserva contexto para próxima sessão"""
        if not self.current_session:
            logger.warning("⚠️ No active session for context preservation")
            return
        
        # Atualiza contexto da sessão atual
        self.current_session.context_snapshot.update(context_data)
        
        # Preserva no cache para próxima sessão
        self.context_cache["preserved_context"].update(context_data)
        self._save_context_cache()
        
        logger.info("✅ Context preserved for cross-session continuity")
    
    def record_learning_insight(self, 
                              insight_type: str,
                              description: str,
                              confidence_score: float,
                              impact_metrics: Dict[str, Any] = None) -> str:
        """Registra insight de aprendizado contínuo"""
        if not self.current_session:
            logger.warning("⚠️ No active session for learning recording")
            return ""
        
        insight = LearningInsight(
            timestamp=datetime.now(timezone.utc).isoformat(),
            insight_type=insight_type,
            description=description,
            confidence_score=confidence_score,
            applied=False,
            impact_metrics=impact_metrics or {}
        )
        
        # Adiciona à sessão atual
        self.current_session.learning_data.append(asdict(insight))
        
        # Salva insight individual
        insight_file = self.learning_path / f"insight_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(insight_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(insight), f, indent=2, ensure_ascii=False)
        
        logger.info(f"🧠 Learning insight recorded: {insight_type} (confidence: {confidence_score})")
        return insight.timestamp
    
    def update_performance_metrics(self, metrics: Dict[str, float]):
        """Atualiza métricas de performance operacional"""
        if not self.current_session:
            logger.warning("⚠️ No active session for metrics update")
            return
        
        self.current_session.performance_metrics.update(metrics)
        
        # Atualiza baseline no cache
        baseline = self.context_cache.get("performance_baseline", {})
        for key, value in metrics.items():
            if key not in baseline:
                baseline[key] = value
            else:
                # Média móvel simples
                baseline[key] = (baseline[key] + value) / 2
        
        self.context_cache["performance_baseline"] = baseline
        self._save_context_cache()
        
        logger.info(f"📊 Performance metrics updated: {list(metrics.keys())}")
    
    def complete_task(self, task_name: str, success: bool = True):
        """Registra conclusão de tarefa"""
        if not self.current_session:
            logger.warning("⚠️ No active session for task completion")
            return
        
        task_record = f"{task_name} ({'✅' if success else '❌'})"
        self.current_session.tasks_completed.append(task_record)
        
        logger.info(f"📋 Task completed: {task_record}")
    
    def end_operational_session(self) -> Dict[str, Any]:
        """Finaliza sessão operacional"""
        if not self.current_session:
            logger.warning("⚠️ No active session to end")
            return {}
        
        self.current_session.end_time = datetime.now(timezone.utc).isoformat()
        
        # Salva sessão completa
        session_file = self.sessions_path / f"session_{self.current_session.session_id}.json"
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(self.current_session), f, indent=2, ensure_ascii=False)
        
        # Prepara resumo da sessão
        session_summary = {
            "session_id": self.current_session.session_id,
            "duration": self._calculate_session_duration(),
            "tasks_completed": len(self.current_session.tasks_completed),
            "learning_insights": len(self.current_session.learning_data),
            "performance_metrics": self.current_session.performance_metrics,
            "context_preserved": bool(self.current_session.context_snapshot)
        }
        
        logger.info(f"🏁 Operational session ended: {self.current_session.session_id}")
        
        # Reset sessão atual
        self.current_session = None
        
        return session_summary
    
    def _calculate_session_duration(self) -> str:
        """Calcula duração da sessão"""
        if not self.current_session or not self.current_session.end_time:
            return "Unknown"
        
        start = datetime.fromisoformat(self.current_session.start_time.replace('Z', '+00:00'))
        end = datetime.fromisoformat(self.current_session.end_time.replace('Z', '+00:00'))
        duration = end - start
        
        return str(duration).split('.')[0]  # Remove microseconds
    
    def get_operational_status(self) -> Dict[str, Any]:
        """Retorna status operacional atual"""
        return {
            "active_session": self.current_session.session_id if self.current_session else None,
            "context_cache_size": len(self.context_cache.get("preserved_context", {})),
            "learning_insights_count": len(list(self.learning_path.glob("*.json"))),
            "sessions_count": len(list(self.sessions_path.glob("*.json"))),
            "performance_baseline": self.context_cache.get("performance_baseline", {}),
            "last_session": self.context_cache.get("last_session_id"),
            "operational_mode": "ACTIVE" if self.current_session else "STANDBY"
        }

# Instância global para uso operacional
operational_manager = OperationalContextManager()

def start_operational_mode():
    """Inicia modo operacional"""
    session_id = operational_manager.start_operational_session()
    return session_id

def preserve_operational_context(context: Dict[str, Any]):
    """Preserva contexto operacional"""
    operational_manager.preserve_context(context)

def record_operational_learning(insight_type: str, description: str, confidence: float):
    """Registra aprendizado operacional"""
    return operational_manager.record_learning_insight(insight_type, description, confidence)

if __name__ == "__main__":
    # Teste do sistema operacional
    print("🔄 Testing Operational Context Manager...")
    
    # Inicia sessão
    session_id = start_operational_mode()
    print(f"✅ Session started: {session_id}")
    
    # Preserva contexto
    preserve_operational_context({
        "current_phase": "Operational Production Mode",
        "active_features": ["continuous_learning", "context_preservation"],
        "performance_target": "high_efficiency"
    })
    print("✅ Context preserved")
    
    # Registra aprendizado
    learning_id = record_operational_learning(
        "optimization", 
        "Memory Bank operational efficiency improved", 
        0.85
    )
    print(f"✅ Learning recorded: {learning_id}")
    
    # Status
    status = operational_manager.get_operational_status()
    print(f"📊 Operational Status: {status['operational_mode']}")
    
    print("🚀 Operational Context Manager is working!")
