import type { Metada<PERSON> } from "next";
import "./globals.css";
// import { StagewiseProvider } from "@saas-projects/shared/stagewise";

export const metadata: Metadata = {
  title: "AGENDATRINTAE3 - Sistema de Agendamento Médico",
  description:
    "Sistema inteligente de agendamento médico com especialistas qualificados, telemedicina e prescrições digitais.",
  keywords: [
    "agendamento",
    "médico",
    "consulta",
    "telemedicina",
    "saúde",
    "especialistas",
  ],
  authors: [{ name: "GRUPO US VIBECODE SYSTEM V4.0" }],
  openGraph: {
    title: "AGENDATRINTAE3 - Sistema de Agendamento Médico",
    description: "Sistema inteligente de agendamento médico",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <body className="min-h-screen bg-background text-foreground antialiased">
        <div className="relative flex min-h-screen flex-col">
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-14 items-center">
              <div className="mr-4 flex">
                <a className="mr-6 flex items-center space-x-2" href="/">
                  <span className="font-bold text-xl">AGENDATRINTAE3</span>
                </a>
              </div>
              <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
                <nav className="flex items-center space-x-6 text-sm font-medium">
                  <a
                    href="/dashboard"
                    className="transition-colors hover:text-foreground/80"
                  >
                    Dashboard
                  </a>
                  <a
                    href="/appointments"
                    className="transition-colors hover:text-foreground/80"
                  >
                    Consultas
                  </a>
                  <a
                    href="/doctors"
                    className="transition-colors hover:text-foreground/80"
                  >
                    Médicos
                  </a>
                  <a
                    href="/schedule"
                    className="transition-colors hover:text-foreground/80"
                  >
                    Agendar
                  </a>
                  <a
                    href="/profile"
                    className="transition-colors hover:text-foreground/80"
                  >
                    Perfil
                  </a>
                </nav>
              </div>
            </div>
          </header>
          <main className="flex-1">{children}</main>
          <footer className="border-t py-6 md:py-0">
            <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
              <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
                <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
                  Desenvolvido com VIBECODE SYSTEM V4.0. Powered by Next.js 15 +
                  PPR + Drizzle ORM + Supabase.
                </p>
              </div>
            </div>
          </footer>
        </div>
        {/* <StagewiseProvider
          projectName="agendatrintae3"
          debug={process.env.NODE_ENV === "development"}
        /> */}
      </body>
    </html>
  );
}
