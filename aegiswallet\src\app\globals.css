@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Glass Morphism Variables */
@layer base {
  :root {
    /* Glass Morphism Effects */
    --glass-blur: 16px;
    --glass-saturation: 180%;
    --glass-opacity: 0.1;
    --glass-border-opacity: 0.2;

    /* Enhanced Shadows */
    --shadow-glass: 0 8px 32px rgba(17, 32, 49, 0.08);
    --shadow-glass-hover: 0 8px 40px rgba(172, 148, 105, 0.12);
    --shadow-glow: 0 0 60px rgba(172, 148, 105, 0.15);

    /* Animation Timing */
    --transition-fast: 150ms;
    --transition-base: 300ms;
    --transition-slow: 500ms;
    --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply antialiased;
    background-image: radial-gradient(
        circle at 20% 50%,
        rgba(172, 148, 105, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(17, 32, 49, 0.03) 0%,
        transparent 50%
      );
  }

  /* Selection colors */
  ::selection {
    @apply bg-grupous-secondary/20 text-grupous-primary;
  }
}

/* Glass Morphism Components */
@layer components {
  /* Glass Card Base */
  .glass-card {
    @apply relative overflow-hidden rounded-2xl;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-glass);
    transition: all var(--transition-base) var(--ease-smooth);
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-glass-hover);
    border-color: rgba(172, 148, 105, 0.3);
    transform: translateY(-2px);
  }

  /* Glass Button */
  .glass-button {
    @apply relative px-6 py-3 font-medium rounded-lg overflow-hidden;
    background: rgba(172, 148, 105, 0.1);
    backdrop-filter: blur(8px) saturate(180%);
    -webkit-backdrop-filter: blur(8px) saturate(180%);
    border: 1px solid rgba(172, 148, 105, 0.2);
    transition: all var(--transition-fast) var(--ease-smooth);
  }

  .glass-button:hover {
    background: rgba(172, 148, 105, 0.2);
    border-color: rgba(172, 148, 105, 0.4);
    box-shadow: 0 0 20px rgba(172, 148, 105, 0.3);
  }

  .glass-button:active {
    transform: scale(0.98);
  }

  /* Glass Input */
  .glass-input {
    @apply w-full px-4 py-3 rounded-lg;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-fast) var(--ease-smooth);
  }

  .glass-input:focus {
    @apply outline-none;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(172, 148, 105, 0.5);
    box-shadow: 0 0 0 3px rgba(172, 148, 105, 0.1);
  }

  /* Glass Modal Backdrop */
  .glass-backdrop {
    background: rgba(17, 32, 49, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-grupous-secondary to-[#8A6A4E] bg-clip-text text-transparent;
  }

  /* Gradient Button */
  .gradient-button {
    @apply relative px-6 py-3 font-medium rounded-lg text-white overflow-hidden;
    background: linear-gradient(120deg, #AC9469 0%, #8A6A4E 100%);
    transition: all var(--transition-base) var(--ease-smooth);
  }

  .gradient-button::before {
    @apply absolute inset-0 opacity-0 transition-opacity duration-300;
    content: "";
    background: linear-gradient(120deg, #8A6A4E 0%, #AC9469 100%);
  }

  .gradient-button:hover::before {
    @apply opacity-100;
  }

  .gradient-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(172, 148, 105, 0.3);
  }

  /* Neon Glow Effect */
  .neon-glow {
    filter: drop-shadow(0 0 8px rgba(172, 148, 105, 0.7));
    animation: neon-pulse 2s ease-in-out infinite alternate;
  }

  @keyframes neon-pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
}

/* Utilities */
@layer utilities {
  /* Scrollbar Styling */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-grupous-secondary/30 rounded-full;
    transition: background var(--transition-fast);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-grupous-secondary/50;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Gradient Border */
  .gradient-border {
    position: relative;
    background: linear-gradient(#fff, #fff) padding-box,
      linear-gradient(120deg, #AC9469 0%, #8A6A4E 100%) border-box;
    border: 2px solid transparent;
  }

  /* Text Balance */
  .text-balance {
    text-wrap: balance;
  }
}