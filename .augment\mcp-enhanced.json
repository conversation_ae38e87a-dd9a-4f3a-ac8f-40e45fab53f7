{"metadata": {"name": "VIBECODE AUGMENT ENHANCED V2.0 - Intelligent MCP Configuration", "version": "2.0.0-enhanced", "description": "Enhanced MCP configuration with intelligent routing and context optimization", "lastUpdated": "2025-01-24T00:00:00.000Z", "environment": "augment-enhanced-optimized", "research_integration": "2025 Context Engineering Best Practices"}, "mcpServers": {"desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "enabled": true, "name": "Desktop Commander MCP Enhanced", "description": "Enhanced file operations ≤200 lines + system commands with intelligent routing", "priority": 1, "usage_patterns": ["file_operations", "system_commands", "directory_management"], "optimization": {"batch_operations": true, "cache_enabled": true, "context_aware": true}, "tier": 1}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "enabled": true, "name": "Sequential Thinking MCP Enhanced", "description": "Enhanced complex reasoning for complexity ≥7 with context optimization", "priority": 1, "usage_patterns": ["complex_reasoning", "strategic_planning", "problem_solving"], "optimization": {"context_compression": true, "quality_monitoring": true, "adaptive_depth": true}, "tier": 1}, "context7-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "enabled": true, "name": "Context7 MCP Enhanced", "description": "Enhanced library documentation with intelligent caching (ALWAYS first for research)", "priority": 1, "usage_patterns": ["documentation_search", "library_research", "technical_reference"], "optimization": {"cache_aggressive": true, "relevance_scoring": true, "context_filtering": true}, "env": {"UPSTASH_CONTEXT7_API_KEY": "ctx7_fzqcQNgU3AChDBMjNIVYg4zLQp4LgFBjZnbA"}, "tier": 1}, "tavily-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "tavily-mcp@0.2.4"], "enabled": true, "name": "<PERSON><PERSON> MCP Enhanced", "description": "Enhanced web search with advanced synthesis (ALWAYS second for research)", "priority": 1, "usage_patterns": ["web_search", "current_information", "trend_analysis"], "optimization": {"result_synthesis": true, "quality_filtering": true, "context_summarization": true}, "env": {"TAVILY_API_KEY": "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"}, "tier": 1}, "exa-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "exa-mcp-server"], "enabled": true, "name": "Exa MCP Enhanced", "description": "Enhanced alternative search with intelligent content extraction (ALWAYS third for research)", "priority": 2, "usage_patterns": ["alternative_search", "content_extraction", "specialized_research"], "optimization": {"content_optimization": true, "relevance_boosting": true, "context_enrichment": true}, "env": {"EXA_API_KEY": "fae6582d-4562-45be-8ce9-f6c0c3518c66"}, "tier": 2}}, "routing_intelligence": {"research_chain": {"sequence": ["context7-mcp", "tavily-mcp", "exa-mcp", "sequential-thinking"], "triggers": ["research", "investigate", "analyze", "study", "evaluate", "compare"], "optimization": "parallel_search_with_synthesis", "quality_gate": "≥8/10 synthesis required"}, "implementation_chain": {"sequence": ["desktop-commander", "context7-mcp", "sequential-thinking"], "triggers": ["implement", "create", "build", "develop", "code", "write"], "optimization": "sequential_with_validation", "quality_gate": "code_verification_required"}, "architecture_chain": {"sequence": ["sequential-thinking", "context7-mcp", "tavily-mcp", "desktop-commander"], "triggers": ["architecture", "design", "system", "structure", "patterns"], "optimization": "strategic_planning_with_research", "quality_gate": "design_validation_required"}}, "performance_optimization": {"cache_strategy": {"enabled": true, "layers": {"L1_hot": "2h TTL - Frequently used combinations", "L2_warm": "8h TTL - Recently accessed contexts", "L3_cold": "24h TTL - Historical patterns"}, "hit_rate_target": "≥85%", "miss_penalty_limit": "<500ms"}, "batch_operations": {"enabled": true, "consolidation_target": "≥70% API call reduction", "batch_size_optimal": "5-10 operations", "timeout_per_batch": "30s"}, "context_compression": {"enabled": true, "compression_ratio": "21.59×", "quality_preservation": "≥95%", "context_rot_prevention": true}}, "quality_assurance": {"mandatory_thresholds": {"overall_quality": "≥9.5/10", "task_classification_accuracy": "≥98%", "context_relevance": "≥95%", "mcp_efficiency": "≥85%"}, "monitoring": {"real_time_tracking": true, "performance_alerts": true, "quality_degradation_detection": true, "automatic_adjustment": true}}, "integration": {"vibecode_sync": {"enabled": true, "sync_rule": "Augment follows .cursor directory changes", "memory_bank_path": "E:\\VIBECODE\\memory-bank\\", "task_storage_shared": true}, "context_engine": {"intelligent_loading": true, "dynamic_rule_activation": true, "context_rot_prevention": true, "adaptive_optimization": true}}}