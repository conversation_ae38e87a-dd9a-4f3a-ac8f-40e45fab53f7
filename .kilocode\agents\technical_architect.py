import json
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from .base_agent import BaseAgent

class TechnicalArchitect(BaseAgent):
    """
    Technical Architect agent specialized in software architecture, system design,
    and complex technical analysis using Claude Sonnet 4.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Technical Architect agent.

        Args:
            config (Dict[str, Any]): Agent configuration
        """
        super().__init__(config)
        self.model = "anthropic/claude-sonnet-4"
        self.complexity_range = {"min": 6, "max": 10}
        self.optimal_complexity = [8, 9, 10]
        self.quality_threshold = 8.5

        # Specialized capabilities
        self.domain_expertise = {
            "primary": [
                "software_architecture",
                "system_design",
                "code_architecture",
                "design_patterns",
                "technical_specifications"
            ],
            "secondary": [
                "performance_optimization",
                "scalability_analysis",
                "security_architecture",
                "integration_patterns"
            ]
        }

        # Architecture-specific keywords
        self.architecture_keywords = {
            "high_priority": [
                "architecture", "design", "pattern", "structure", "framework",
                "system", "technical", "specification", "blueprint", "model"
            ],
            "medium_priority": [
                "optimize", "refactor", "improve", "enhance", "scale",
                "performance", "security", "integration", "api"
            ],
            "design_patterns": [
                "singleton", "factory", "observer", "strategy", "decorator",
                "mvc", "mvp", "mvvm", "microservices", "monolith"
            ]
        }

    def can_handle(self, request: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Determines if this agent can handle the given request.

        Args:
            request (Dict[str, Any]): The request to evaluate

        Returns:
            Tuple[bool, float]: (can_handle, confidence_score)
        """
        complexity = request.get("complexity", 0)
        message = request.get("message", "").lower()
        request_type = request.get("type", "general")

        # Base confidence from complexity
        if complexity < self.complexity_range["min"]:
            base_confidence = 0.2  # Can handle but not optimal
        elif complexity > self.complexity_range["max"]:
            base_confidence = 0.8  # High complexity is our specialty
        elif complexity in self.optimal_complexity:
            base_confidence = 0.9  # Optimal range
        else:
            base_confidence = 0.7  # Good but not optimal

        # Keyword matching boost
        keyword_boost = 0.0

        # High priority keywords
        high_priority_matches = sum(1 for keyword in self.architecture_keywords["high_priority"] if keyword in message)
        keyword_boost += high_priority_matches * 0.15

        # Medium priority keywords
        medium_priority_matches = sum(1 for keyword in self.architecture_keywords["medium_priority"] if keyword in message)
        keyword_boost += medium_priority_matches * 0.10

        # Design pattern keywords
        pattern_matches = sum(1 for keyword in self.architecture_keywords["design_patterns"] if keyword in message)
        keyword_boost += pattern_matches * 0.20

        # Domain expertise boost
        domain_boost = 0.0
        for domain in self.domain_expertise["primary"]:
            if domain.replace("_", " ") in message:
                domain_boost += 0.25
        for domain in self.domain_expertise["secondary"]:
            if domain.replace("_", " ") in message:
                domain_boost += 0.15

        # Request type boost
        type_boost = 0.0
        if request_type in ["architecture", "design", "technical_analysis"]:
            type_boost = 0.20
        elif request_type in ["optimization", "refactoring", "integration"]:
            type_boost = 0.15

        # Calculate final confidence
        confidence = min(base_confidence + keyword_boost + domain_boost + type_boost, 1.0)

        # Decision threshold
        can_handle = confidence >= 0.6

        return can_handle, confidence

    def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes a request using technical architecture expertise.

        Args:
            request (Dict[str, Any]): The request to process

        Returns:
            Dict[str, Any]: Processed response
        """
        start_time = time.time()

        # Extract request details
        message = request.get("message", "")
        complexity = request.get("complexity", 0)
        context = request.get("context", {})

        # Determine processing strategy based on complexity and content
        processing_strategy = self._determine_processing_strategy(message, complexity)

        # Process based on strategy
        if processing_strategy == "architectural_design":
            response = self._process_architectural_design(message, context)
        elif processing_strategy == "system_analysis":
            response = self._process_system_analysis(message, context)
        elif processing_strategy == "optimization_review":
            response = self._process_optimization_review(message, context)
        elif processing_strategy == "integration_planning":
            response = self._process_integration_planning(message, context)
        else:
            response = self._process_general_technical(message, context)

        # Calculate processing time
        processing_time = (time.time() - start_time) * 1000

        # Enhance response with architecture-specific metadata
        response.update({
            "agent": "TechnicalArchitect",
            "model": self.model,
            "processing_time_ms": processing_time,
            "complexity_handled": complexity,
            "processing_strategy": processing_strategy,
            "quality_score": self._calculate_quality_score(response, complexity),
            "architecture_insights": self._extract_architecture_insights(message),
            "recommendations": self._generate_recommendations(message, complexity),
            "timestamp": datetime.now().isoformat()
        })

        return response

    def _determine_processing_strategy(self, message: str, complexity: int) -> str:
        """
        Determines the best processing strategy based on message content and complexity.
        """
        message_lower = message.lower()

        # Architecture and design keywords
        if any(keyword in message_lower for keyword in ["architecture", "design", "blueprint", "structure"]):
            return "architectural_design"

        # System analysis keywords
        elif any(keyword in message_lower for keyword in ["analyze", "analysis", "evaluate", "assessment"]):
            return "system_analysis"

        # Optimization keywords
        elif any(keyword in message_lower for keyword in ["optimize", "performance", "scale", "improve"]):
            return "optimization_review"

        # Integration keywords
        elif any(keyword in message_lower for keyword in ["integrate", "api", "connection", "interface"]):
            return "integration_planning"

        # Default to general technical
        else:
            return "general_technical"

    def _process_architectural_design(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes architectural design requests.
        """
        return {
            "type": "architectural_design",
            "content": f"Architectural analysis for: {message}",
            "design_principles": [
                "Single Responsibility Principle",
                "Open/Closed Principle",
                "Dependency Inversion",
                "Separation of Concerns"
            ],
            "recommended_patterns": self._suggest_design_patterns(message),
            "architecture_diagram": "Would generate architectural diagram based on requirements",
            "scalability_considerations": self._analyze_scalability(message),
            "technology_stack": self._recommend_technology_stack(message)
        }

    def _process_system_analysis(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes system analysis requests.
        """
        return {
            "type": "system_analysis",
            "content": f"System analysis for: {message}",
            "analysis_dimensions": [
                "Performance characteristics",
                "Scalability potential",
                "Security implications",
                "Maintainability factors"
            ],
            "strengths": self._identify_system_strengths(message),
            "weaknesses": self._identify_system_weaknesses(message),
            "improvement_opportunities": self._suggest_improvements(message),
            "risk_assessment": self._assess_technical_risks(message)
        }

    def _process_optimization_review(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes optimization review requests.
        """
        return {
            "type": "optimization_review",
            "content": f"Optimization review for: {message}",
            "optimization_areas": [
                "Performance bottlenecks",
                "Memory usage patterns",
                "Database query optimization",
                "Caching strategies"
            ],
            "performance_metrics": self._suggest_performance_metrics(message),
            "optimization_strategies": self._recommend_optimization_strategies(message),
            "implementation_priority": self._prioritize_optimizations(message),
            "expected_improvements": self._estimate_performance_gains(message)
        }

    def _process_integration_planning(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes integration planning requests.
        """
        return {
            "type": "integration_planning",
            "content": f"Integration planning for: {message}",
            "integration_patterns": [
                "API Gateway pattern",
                "Event-driven architecture",
                "Message queue integration",
                "Database integration patterns"
            ],
            "api_design": self._design_api_structure(message),
            "data_flow": self._map_data_flow(message),
            "error_handling": self._design_error_handling(message),
            "testing_strategy": self._plan_integration_testing(message)
        }

    def _process_general_technical(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes general technical requests.
        """
        return {
            "type": "general_technical",
            "content": f"Technical analysis for: {message}",
            "technical_considerations": self._analyze_technical_considerations(message),
            "best_practices": self._recommend_best_practices(message),
            "implementation_guidance": self._provide_implementation_guidance(message),
            "quality_checkpoints": self._define_quality_checkpoints(message)
        }

    def _calculate_quality_score(self, response: Dict[str, Any], complexity: int) -> float:
        """
        Calculates quality score for the response.
        """
        base_score = 8.5  # TechnicalArchitect baseline

        # Complexity handling bonus
        if complexity >= 8:
            base_score += 0.5
        elif complexity >= 6:
            base_score += 0.3

        # Response completeness bonus
        if len(response.get("content", "")) > 100:
            base_score += 0.2

        # Architecture-specific content bonus
        if any(key in response for key in ["design_principles", "recommended_patterns", "architecture_diagram"]):
            base_score += 0.3

        return min(base_score, 10.0)

    def _extract_architecture_insights(self, message: str) -> List[str]:
        """
        Extracts architecture-specific insights from the message.
        """
        insights = []

        if "microservices" in message.lower():
            insights.append("Microservices architecture considerations identified")
        if "monolith" in message.lower():
            insights.append("Monolithic architecture patterns detected")
        if "api" in message.lower():
            insights.append("API design requirements present")
        if "database" in message.lower():
            insights.append("Database architecture decisions required")

        return insights

    def _generate_recommendations(self, message: str, complexity: int) -> List[str]:
        """
        Generates architecture-specific recommendations.
        """
        recommendations = []

        if complexity >= 8:
            recommendations.append("Consider implementing comprehensive architectural documentation")
            recommendations.append("Implement robust monitoring and observability patterns")

        if complexity >= 6:
            recommendations.append("Apply SOLID principles throughout the design")
            recommendations.append("Implement proper error handling and logging")

        recommendations.append("Consider future scalability requirements")
        recommendations.append("Implement appropriate testing strategies")

        return recommendations

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Returns the agent's capabilities.
        """
        return {
            "agent_type": "TechnicalArchitect",
            "model": self.model,
            "complexity_range": self.complexity_range,
            "optimal_complexity": self.optimal_complexity,
            "domain_expertise": self.domain_expertise,
            "specializations": [
                "Software Architecture Design",
                "System Design Patterns",
                "Technical Specification Creation",
                "Performance Architecture",
                "Scalability Planning",
                "Integration Architecture",
                "Security Architecture"
            ],
            "quality_threshold": self.quality_threshold,
            "processing_strategies": [
                "architectural_design",
                "system_analysis",
                "optimization_review",
                "integration_planning",
                "general_technical"
            ]
        }

    # Helper methods for specific analysis types
    def _suggest_design_patterns(self, message: str) -> List[str]:
        """Suggests appropriate design patterns."""
        patterns = []
        message_lower = message.lower()

        if "single" in message_lower or "one" in message_lower:
            patterns.append("Singleton Pattern")
        if "create" in message_lower or "build" in message_lower:
            patterns.append("Factory Pattern")
        if "notify" in message_lower or "event" in message_lower:
            patterns.append("Observer Pattern")
        if "strategy" in message_lower or "algorithm" in message_lower:
            patterns.append("Strategy Pattern")

        return patterns or ["Consider MVC/MVP patterns", "Repository pattern for data access"]

    def _analyze_scalability(self, message: str) -> Dict[str, Any]:
        """Analyzes scalability considerations."""
        return {
            "horizontal_scaling": "Consider load balancing and distributed architecture",
            "vertical_scaling": "Optimize resource utilization and performance",
            "data_scaling": "Implement proper database sharding and caching",
            "service_scaling": "Design for microservices if complexity warrants"
        }

    def _recommend_technology_stack(self, message: str) -> Dict[str, Any]:
        """Recommends appropriate technology stack."""
        return {
            "backend": "Python/FastAPI, Node.js/Express, or Java/Spring Boot",
            "database": "PostgreSQL for relational, MongoDB for document-based",
            "caching": "Redis for session management and caching",
            "messaging": "RabbitMQ or Apache Kafka for async processing",
            "monitoring": "Prometheus + Grafana for metrics and monitoring"
        }

    def _identify_system_strengths(self, message: str) -> List[str]:
        """Identifies system strengths."""
        return [
            "Modular architecture approach",
            "Clear separation of concerns",
            "Scalable design patterns",
            "Comprehensive error handling"
        ]

    def _identify_system_weaknesses(self, message: str) -> List[str]:
        """Identifies system weaknesses."""
        return [
            "Potential performance bottlenecks",
            "Complex integration points",
            "Monitoring coverage gaps",
            "Documentation completeness"
        ]

    def _suggest_improvements(self, message: str) -> List[str]:
        """Suggests system improvements."""
        return [
            "Implement comprehensive logging strategy",
            "Add performance monitoring and alerting",
            "Enhance error handling and recovery",
            "Improve documentation and API specifications"
        ]

    def _assess_technical_risks(self, message: str) -> Dict[str, str]:
        """Assesses technical risks."""
        return {
            "performance_risk": "Medium - requires load testing validation",
            "scalability_risk": "Low - architecture supports horizontal scaling",
            "security_risk": "Medium - requires security audit and penetration testing",
            "maintenance_risk": "Low - well-structured and documented code"
        }

    def _suggest_performance_metrics(self, message: str) -> List[str]:
        """Suggests performance metrics to track."""
        return [
            "Response time percentiles (p50, p95, p99)",
            "Throughput (requests per second)",
            "Error rate and types",
            "Resource utilization (CPU, memory, disk I/O)"
        ]

    def _recommend_optimization_strategies(self, message: str) -> List[str]:
        """Recommends optimization strategies."""
        return [
            "Implement caching at multiple levels",
            "Optimize database queries and indexes",
            "Use async processing for heavy operations",
            "Implement connection pooling and resource management"
        ]

    def _prioritize_optimizations(self, message: str) -> Dict[str, str]:
        """Prioritizes optimization efforts."""
        return {
            "high_priority": "Database query optimization and indexing",
            "medium_priority": "Caching strategy implementation",
            "low_priority": "Code refactoring for maintainability"
        }

    def _estimate_performance_gains(self, message: str) -> Dict[str, str]:
        """Estimates expected performance gains."""
        return {
            "database_optimization": "30-50% improvement in query response times",
            "caching_implementation": "60-80% reduction in backend processing load",
            "async_processing": "40-60% improvement in user experience responsiveness"
        }

    def _design_api_structure(self, message: str) -> Dict[str, Any]:
        """Designs API structure."""
        return {
            "rest_endpoints": "RESTful API with proper HTTP methods",
            "authentication": "JWT-based authentication with refresh tokens",
            "versioning": "URL-based versioning strategy",
            "documentation": "OpenAPI/Swagger specification"
        }

    def _map_data_flow(self, message: str) -> List[str]:
        """Maps data flow patterns."""
        return [
            "Request validation and sanitization",
            "Business logic processing",
            "Data persistence operations",
            "Response formatting and delivery"
        ]

    def _design_error_handling(self, message: str) -> Dict[str, Any]:
        """Designs error handling strategy."""
        return {
            "error_types": "Validation, business logic, system errors",
            "error_codes": "Standardized error code system",
            "logging": "Structured logging with correlation IDs",
            "user_feedback": "User-friendly error messages"
        }

    def _plan_integration_testing(self, message: str) -> List[str]:
        """Plans integration testing strategy."""
        return [
            "API contract testing",
            "End-to-end integration tests",
            "Database integration testing",
            "External service mocking and testing"
        ]

    def _analyze_technical_considerations(self, message: str) -> List[str]:
        """Analyzes general technical considerations."""
        return [
            "Performance and scalability requirements",
            "Security and compliance needs",
            "Maintainability and extensibility",
            "Testing and deployment strategies"
        ]

    def _recommend_best_practices(self, message: str) -> List[str]:
        """Recommends technical best practices."""
        return [
            "Follow SOLID principles in design",
            "Implement comprehensive error handling",
            "Use dependency injection for testability",
            "Maintain clean and documented code"
        ]

    def _provide_implementation_guidance(self, message: str) -> List[str]:
        """Provides implementation guidance."""
        return [
            "Start with MVP and iterate based on feedback",
            "Implement monitoring and logging from day one",
            "Use version control and CI/CD pipelines",
            "Plan for gradual rollout and feature flags"
        ]

    def _define_quality_checkpoints(self, message: str) -> List[str]:
        """Defines quality checkpoints."""
        return [
            "Code review and static analysis",
            "Unit and integration testing",
            "Performance and load testing",
            "Security scanning and penetration testing"
        ]
