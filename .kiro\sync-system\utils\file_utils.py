"""
File utility functions for the VIBECODE-Kiro sync system.
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Optional, Dict, Any
from ..utils.logger import get_logger

logger = get_logger(__name__)


class FileUtils:
    """Utility class for file operations."""
    
    @staticmethod
    def calculate_checksum(file_path: str, algorithm: str = "md5") -> Optional[str]:
        """Calculate checksum of a file."""
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating checksum for {file_path}: {e}")
            return None
    
    @staticmethod
    def safe_copy(source: str, destination: str, create_dirs: bool = True) -> bool:
        """Safely copy a file with error handling."""
        try:
            if create_dirs:
                os.makedirs(os.path.dirname(destination), exist_ok=True)
            
            shutil.copy2(source, destination)
            logger.debug(f"File copied: {source} -> {destination}")
            return True
            
        except Exception as e:
            logger.error(f"Error copying file {source} to {destination}: {e}")
            return False
    
    @staticmethod
    def safe_move(source: str, destination: str, create_dirs: bool = True) -> bool:
        """Safely move a file with error handling."""
        try:
            if create_dirs:
                os.makedirs(os.path.dirname(destination), exist_ok=True)
            
            shutil.move(source, destination)
            logger.debug(f"File moved: {source} -> {destination}")
            return True
            
        except Exception as e:
            logger.error(f"Error moving file {source} to {destination}: {e}")
            return False
    
    @staticmethod
    def safe_delete(file_path: str) -> bool:
        """Safely delete a file with error handling."""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.debug(f"File deleted: {file_path}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            return False
    
    @staticmethod
    def read_file_content(file_path: str, encoding: str = 'utf-8') -> Optional[str]:
        """Read file content with error handling."""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return None
    
    @staticmethod
    def write_file_content(file_path: str, content: str, encoding: str = 'utf-8', create_dirs: bool = True) -> bool:
        """Write content to file with error handling."""
        try:
            if create_dirs:
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            logger.debug(f"Content written to file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error writing to file {file_path}: {e}")
            return False
    
    @staticmethod
    def get_file_info(file_path: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive file information."""
        try:
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            return {
                'path': file_path,
                'size': stat.st_size,
                'modified_time': stat.st_mtime,
                'created_time': stat.st_ctime,
                'is_file': os.path.isfile(file_path),
                'is_directory': os.path.isdir(file_path),
                'extension': os.path.splitext(file_path)[1],
                'checksum': FileUtils.calculate_checksum(file_path) if os.path.isfile(file_path) else None
            }
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            return None
    
    @staticmethod
    def find_files(directory: str, patterns: List[str], recursive: bool = True) -> List[str]:
        """Find files matching patterns in directory."""
        found_files = []
        try:
            path = Path(directory)
            if not path.exists():
                return found_files
            
            for pattern in patterns:
                if recursive:
                    found_files.extend([str(p) for p in path.rglob(pattern)])
                else:
                    found_files.extend([str(p) for p in path.glob(pattern)])
            
            return list(set(found_files))  # Remove duplicates
            
        except Exception as e:
            logger.error(f"Error finding files in {directory}: {e}")
            return found_files
    
    @staticmethod
    def ensure_directory(directory: str) -> bool:
        """Ensure directory exists, create if necessary."""
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Error creating directory {directory}: {e}")
            return False
    
    @staticmethod
    def is_text_file(file_path: str) -> bool:
        """Check if file is a text file."""
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                return b'\x00' not in chunk
        except Exception:
            return False
    
    @staticmethod
    def backup_file(file_path: str, backup_dir: str) -> Optional[str]:
        """Create a backup of a file."""
        try:
            if not os.path.exists(file_path):
                return None
            
            # Create backup filename with timestamp
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.basename(file_path)
            backup_filename = f"{timestamp}_{filename}"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            if FileUtils.safe_copy(file_path, backup_path):
                return backup_path
            return None
            
        except Exception as e:
            logger.error(f"Error backing up file {file_path}: {e}")
            return None