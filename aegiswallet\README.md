# AegisWallet - Crypto Wallet SaaS

## 🚀 Sobre o Projeto

AegisWallet é uma aplicação SaaS de carteira crypto completa e independente, com suporte a testes automatizados.

## 📦 Estrutura do Projeto

Este projeto contém todos os arquivos necessários para funcionar independentemente:

```
aegiswallet/
├── app/              # Next.js App Router
├── components/       # Componentes React
│   └── ui/          # Componentes UI
├── lib/             # Utilitários e configurações
├── public/          # Arquivos públicos
├── styles/          # Estilos globais
├── __tests__/       # Testes unitários
├── tests/           # Testes E2E (Playwright)
├── drizzle/         # Schema do banco de dados
├── .env.example     # Template de variáveis de ambiente
├── jest.config.js   # Configuração Jest
├── playwright.config.ts # Configuração Playwright
└── package.json     # Dependências e scripts
```

## 🛠️ Tecnologias

- **Framework**: Next.js 15.4.0-canary
- **UI**: TailwindCSS + componentes customizados
- **Database**: Supabase + Drizzle ORM
- **Testing**: Jest + Playwright
- **Payments**: Stripe
- **Language**: TypeScript

## 🚀 Deploy

### 1. Preparação

```bash
# Clone apenas este projeto
git clone https://github.com/seu-usuario/aegiswallet.git
cd aegiswallet

# Instale as dependências
npm install

# Configure as variáveis de ambiente
cp .env.example .env.local
```

### 2. Configuração do Banco de Dados

```bash
# Push do schema para o Supabase
npm run db:push

# Abrir Drizzle Studio (opcional)
npm run db:studio
```

### 3. Testes

```bash
# Testes unitários
npm test

# Testes E2E
npm run test:e2e
```

### 4. Deploy

```bash
# Build
npm run build

# Deploy no Vercel
vercel

# Ou qualquer serviço que suporte Next.js
```

## 📝 Scripts Disponíveis

- `npm run dev` - Desenvolvimento
- `npm run build` - Build para produção
- `npm test` - Rodar testes unitários
- `npm run test:e2e` - Rodar testes E2E
- `npm run db:push` - Push do schema para o banco
- `npm run db:studio` - Abrir Drizzle Studio

## 🔐 Segurança

- Implementa autenticação segura
- Criptografia de dados sensíveis
- RLS (Row Level Security) no Supabase
- Validação de entrada com Zod

---

**Projeto independente e pronto para produção.**