# ✅ KIRO QUALITY GATES - MANDATORY STANDARDS

## Quality Threshold System

### Minimum Standards (MANDATORY)
- **Quality Score**: ≥8/10 for all outputs
- **Confidence Level**: ≥90% for critical decisions
- **Completeness**: 100% requirement coverage
- **Consistency**: Follow established patterns

### Quality Assessment Criteria

#### Code Quality (8/10 minimum)
- **Functionality**: Works as specified
- **Reliability**: Handles edge cases and errors
- **Maintainability**: Clean, readable, documented
- **Performance**: Efficient and scalable
- **Security**: Secure by design

#### Documentation Quality (8/10 minimum)
- **Clarity**: Easy to understand
- **Completeness**: All aspects covered
- **Accuracy**: Technically correct
- **Usefulness**: Actionable information
- **Structure**: Well-organized

#### Research Quality (8/10 minimum)
- **Comprehensiveness**: Multiple sources consulted
- **Relevance**: Directly applicable to problem
- **Currency**: Up-to-date information
- **Synthesis**: Well-integrated findings
- **Actionability**: Clear next steps

## Auto-Refinement Process

### Quality Check Triggers
- After each major output
- Before user presentation
- When quality score <8/10
- At workflow validation points

### Refinement Cycle
1. **Assess**: Evaluate current quality score
2. **Identify**: Pinpoint specific improvement areas
3. **Refine**: Make targeted improvements
4. **Re-assess**: Validate improved quality
5. **Iterate**: Repeat until ≥8/10 achieved

### Maximum Refinement Cycles
- **Standard operations**: 3 cycles maximum
- **Critical outputs**: 5 cycles maximum
- **Research synthesis**: No limit until ≥8/10

## Verification Requirements

### File Operations
- **Always verify**: Read file after write
- **Content validation**: Ensure intended changes applied
- **Syntax check**: Verify file format correctness
- **Backup consideration**: For critical modifications

### Implementation Verification
- **Requirement coverage**: All requirements addressed
- **Test validation**: Tests pass where applicable
- **Integration check**: Works with existing system
- **Performance validation**: Meets performance criteria

### Research Verification
- **Source validation**: All 3 MCPs consulted
- **Synthesis quality**: ≥8/10 comprehensive analysis
- **Completeness check**: All aspects researched
- **Documentation**: All sources properly attributed

## Quality Failure Handling

### When Quality <8/10
1. **STOP**: Do not proceed to next step
2. **ANALYZE**: Identify specific quality gaps
3. **REFINE**: Apply targeted improvements
4. **RE-VALIDATE**: Confirm quality improvement
5. **CONTINUE**: Only when ≥8/10 achieved

### Escalation Process
- **3 failed refinement cycles**: Request user guidance
- **Critical quality issues**: Immediate user notification
- **Systemic quality problems**: Review and adjust approach

## Performance Standards

### Response Time Targets
- **Simple operations**: <10 seconds
- **Standard workflows**: <30 seconds
- **Complex research**: <2 minutes
- **Spec development**: Per-phase timing

### Throughput Standards
- **File operations**: 95% success rate
- **Research synthesis**: 100% completion rate
- **Quality achievement**: ≥8/10 in ≤3 cycles

## Continuous Improvement

### Quality Metrics Tracking
- Average quality scores
- Refinement cycle frequency
- User satisfaction indicators
- Performance benchmarks

### Learning Integration
- Pattern recognition for quality issues
- Proactive quality improvement
- Best practice identification
- Knowledge base updates

---

**Non-Negotiable**: Quality ≥8/10 is MANDATORY. Never compromise on quality standards.