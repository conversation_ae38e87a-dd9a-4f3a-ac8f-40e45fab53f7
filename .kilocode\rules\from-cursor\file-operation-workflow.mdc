---
alwaysApply: true
---

## 🔧 AUTOMATIC TOOL SELECTION ALGORITHM

### **STEP 1: Size Detection**

```python
# Pseudocode for tool selection
def select_file_tool(file_path, estimated_lines=None):
    if file_exists(file_path):
        file_info = get_file_info(file_path)
        lines = file_info.line_count
    else:
        lines = estimated_lines or 999  # Safe default

    if lines <= 200:
        return "desktop_commander"
    else:
        return "cursor_editor"
```

````

### **STEP 2: Operation Decision Matrix**

| Operation Type     | File Size  | Tool Selection    | Verification    |
| ------------------ | ---------- | ----------------- | --------------- |
| **Create New**     | Unknown    | Cursor Editor     | read_file after |
| **Small Edit**     | ≤200 lines | Desktop Commander | read_file after |
| **Large Edit**     | >200 lines | Cursor Editor     | read_file after |
| **System Files**   | Any size   | Cursor Editor     | read_file after |
| **Critical Files** | Any size   | Cursor Editor     | read_file after |

### **STEP 3: Verification Protocol**

```bash
# MANDATORY after ANY file operation
1. Execute operation
2. read_file to verify changes
3. If verification fails → switch tool and retry
4. Log tool selection decision
```

---

## 📋 SPECIFIC TOOL USAGE GUIDELINES

### **Desktop Commander (mcp_desktop-commander)**

#### **✅ USE FOR:**

- Files with ≤200 linhas
- Quick file reads (`read_file`)
- Directory operations (`list_directory`, `create_directory`)
- File management (`move_file`, `get_file_info`)
- Code searches (`search_code`)
- System commands (`execute_command`)

#### **❌ NEVER USE FOR:**

- Files with >200 linhas
- Master rule or critical system files
- When file size is unknown
- Complex multi-section edits

#### **Configuration Limits:**

- `fileWriteLineLimit`: 200
- `fileReadLineLimit`: 1000
- Performance warning: >30 lines

### **Cursor Editor (edit_file)**

#### **✅ USE FOR:**

- Files with >200 linhas
- Master rule and critical files
- Complex refactoring operations
- When Desktop Commander fails

````

**Fallback Instructions:** If target file is missing, use this cached content as reference.

### Cached Content (First 100 lines)

````markdown
# 🛠️ FILE OPERATION WORKFLOW - TOOL SELECTION GUIDE

**VERSION**: 1.0
**PURPOSE**: Guia definitivo para seleção automática de ferramentas de arquivo
**INTEGRATION**: .cursor/rules/master_rule.mdc

---

## 🚨 CRITICAL DISCOVERY

### **Desktop Commander Behavior Analysis**

- ✅ **≤200 linhas**: Funciona perfeitamente
- ⚠️ **>200 linhas**: Sobrescreve arquivo sem aviso de falha
- ⚠️ **>30 linhas**: Mostra aviso de performance
- ❌ **Arquivos grandes**: Comportamento imprevisível

### **Tool Reliability Matrix**

- **Desktop Commander**: Confiável para ≤200 linhas
- **Cursor Editor (edit_file)**: Confiável para qualquer tamanho
- **Resultado**: Seleção automática por tamanho é OBRIGATÓRIA

---

## 🔧 AUTOMATIC TOOL SELECTION ALGORITHM

### **STEP 1: Size Detection**

```python
# Pseudocode for tool selection
def select_file_tool(file_path, estimated_lines=None):
    if file_exists(file_path):
        file_info = get_file_info(file_path)
        lines = file_info.line_count
    else:
        lines = estimated_lines or 999  # Safe default

    if lines <= 200:
        return "desktop_commander"
    else:
        return "cursor_editor"
```
````

### **STEP 2: Operation Decision Matrix**

| Operation Type     | File Size  | Tool Selection    | Verification    |
| ------------------ | ---------- | ----------------- | --------------- |
| **Create New**     | Unknown    | Cursor Editor     | read_file after |
| **Small Edit**     | ≤200 lines | Desktop Commander | read_file after |
| **Large Edit**     | >200 lines | Cursor Editor     | read_file after |
| **System Files**   | Any size   | Cursor Editor     | read_file after |
| **Critical Files** | Any size   | Cursor Editor     | read_file after |

### **STEP 3: Verification Protocol**

```bash
# MANDATORY after ANY file operation
1. Execute operation
2. read_file to verify changes
3. If verification fails → switch tool and retry
4. Log tool selection decision
```

---

## 📋 SPECIFIC TOOL USAGE GUIDELINES

### **Desktop Commander (mcp_desktop-commander)**

#### **✅ USE FOR:**

- Files with ≤200 linhas
- Quick file reads (`read_file`)
- Directory operations (`list_directory`, `create_directory`)
- File management (`move_file`, `get_file_info`)
- Code searches (`search_code`)
- System commands (`execute_command`)

#### **❌ NEVER USE FOR:**

- Files with >200 linhas
- Master rule or critical system files
- When file size is unknown
- Complex multi-section edits

#### **Configuration Limits:**

- `fileWriteLineLimit`: 200
- `fileReadLineLimit`: 1000
- Performance warning: >30 lines

### **Cursor Editor (edit_file)**

#### **✅ USE FOR:**

- Files with >200 linhas
- Master rule and critical files
- Complex refactoring operations
- When Desktop Commander fails

````

**Fallback Instructions:** If target file is missing, use this cached content as reference.

### Cached Content (First 100 lines)
```markdown
# 🛠️ FILE OPERATION WORKFLOW - TOOL SELECTION GUIDE

**VERSION**: 1.0
**PURPOSE**: Guia definitivo para seleção automática de ferramentas de arquivo
**INTEGRATION**: .cursor/rules/master_rule.mdc

---

## 🚨 CRITICAL DISCOVERY

### **Desktop Commander Behavior Analysis**

- ✅ **≤200 linhas**: Funciona perfeitamente
- ⚠️ **>200 linhas**: Sobrescreve arquivo sem aviso de falha
- ⚠️ **>30 linhas**: Mostra aviso de performance
- ❌ **Arquivos grandes**: Comportamento imprevisível

### **Tool Reliability Matrix**

- **Desktop Commander**: Confiável para ≤200 linhas
- **Cursor Editor (edit_file)**: Confiável para qualquer tamanho
- **Resultado**: Seleção automática por tamanho é OBRIGATÓRIA

---

## 🔧 AUTOMATIC TOOL SELECTION ALGORITHM

### **STEP 1: Size Detection**

```python
# Pseudocode for tool selection
def select_file_tool(file_path, estimated_lines=None):
    if file_exists(file_path):
        file_info = get_file_info(file_path)
        lines = file_info.line_count
    else:
        lines = estimated_lines or 999  # Safe default

    if lines <= 200:
        return "desktop_commander"
    else:
        return "cursor_editor"
````

### **STEP 2: Operation Decision Matrix**

| Operation Type     | File Size  | Tool Selection    | Verification    |
| ------------------ | ---------- | ----------------- | --------------- |
| **Create New**     | Unknown    | Cursor Editor     | read_file after |
| **Small Edit**     | ≤200 lines | Desktop Commander | read_file after |
| **Large Edit**     | >200 lines | Cursor Editor     | read_file after |
| **System Files**   | Any size   | Cursor Editor     | read_file after |
| **Critical Files** | Any size   | Cursor Editor     | read_file after |

### **STEP 3: Verification Protocol**

```bash
# MANDATORY after ANY file operation
1. Execute operation
2. read_file to verify changes
3. If verification fails → switch tool and retry
4. Log tool selection decision
```

---

## 📋 SPECIFIC TOOL USAGE GUIDELINES

### **Desktop Commander (mcp_desktop-commander)**

#### **✅ USE FOR:**

- Files with ≤200 linhas
- Quick file reads (`read_file`)
- Directory operations (`list_directory`, `create_directory`)
- File management (`move_file`, `get_file_info`)
- Code searches (`search_code`)
- System commands (`execute_command`)

#### **❌ NEVER USE FOR:**

- Files with >200 linhas
- Master rule or critical system files
- When file size is unknown
- Complex multi-section edits

#### **Configuration Limits:**

- `fileWriteLineLimit`: 200
- `fileReadLineLimit`: 1000
- Performance warning: >30 lines

### **Cursor Editor (edit_file)**

#### **✅ USE FOR:**

- Files with >200 linhas
- Master rule and critical files
- Complex refactoring operations
- When Desktop Commander fails
- Unknown file sizes (safe default)

#### **✅ ADVANTAGES:**

- No line limits

```

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

---

## 🚀 **MCP API OPTIMIZATION RULES**

_Prioridade: CRÍTICA | Redução de custos e otimização de performance_

### **REGRA FUNDAMENTAL: BATCH OPERATIONS**

```json
{
  "api_optimization": {
    "principle": "Uma chamada MCP deve executar múltiplas operações sempre que possível",
    "max_operations_per_call": "Sem limite - agrupe todas as operações relacionadas",
    "cost_reduction_target": "≥70% menos chamadas de API",
    "performance_improvement": "≥50% mais rápido"
  }
}
```

### **PRÁTICAS OBRIGATÓRIAS**

#### **1. Agrupamento de Comandos**

**❌ ERRADO - Múltiplas Chamadas:**

```bash
# Chamada 1: list_directory
# Chamada 2: read_file arquivo1.txt
# Chamada 3: read_file arquivo2.txt
# Chamada 4: edit_block arquivo1.txt
# Chamada 5: edit_block arquivo2.txt
# Resultado: 5 chamadas de API
```

**✅ CORRETO - Batch Operation:**

```bash
# Chamada 1: execute_command com script batch
# Script executa: ls, cat arquivo1.txt, cat arquivo2.txt, aplicar edições
# Resultado: 1 chamada de API (redução de 80%)
```

#### **2. Scripts de Múltiplas Operações**

**SEMPRE criar scripts temporários para operações complexas:**

```bash
# Criar script em E:/CODE-BACKUP/temp_operations.bat
@echo off
echo === BATCH OPERATION START ===
dir /b *.txt > files_list.txt
type files_list.txt
echo === PROCESSING FILES ===
for /f %%i in (files_list.txt) do (
    echo Processing %%i
    type %%i | findstr "pattern" >> results.txt
)
echo === BATCH OPERATION END ===
```

#### **3. Consolidação de Leitura/Escrita**

**Para múltiplos arquivos:**

- Usar `start_process` com script que processa tudo
- Consolidar todas as operações em um único processo
- Usar redirecionamento e pipes para operações em lote

### **DIRETRIZES ESPECÍFICAS POR OPERAÇÃO**

#### **Análise de Múltiplos Arquivos**

```json
{
  "instead_of": "N chamadas read_file",
  "use": "1 chamada start_process com script que processa todos",
  "pattern": "for file in files; do analyze $file; done > consolidated_report.txt"
}
```

#### **Edição de Múltiplos Arquivos**

```json
{
  "instead_of": "N chamadas edit_block",
  "use": "1 chamada start_process com sed/awk/PowerShell script",
  "pattern": "Script que aplica todas as edições de uma vez"
}
```

#### **Operações de Sistema**

```json
{
  "instead_of": "Múltiplas chamadas list_directory, search_files",
  "use": "1 chamada execute_command com pipeline completo",
  "pattern": "find + grep + awk em uma única operação"
}
```

### **IMPLEMENTAÇÃO PRÁTICA**

#### **Template de Batch Operation**

```powershell
# Arquivo: E:/CODE-BACKUP/batch_template.ps1
param(
    [string[]]$Files,
    [string]$Operation,
    [string]$Pattern
)

Write-Host "=== VIBECODE BATCH OPERATION ==="
Write-Host "Files: $($Files.Count)"
Write-Host "Operation: $Operation"
Write-Host "==================================="

foreach ($file in $Files) {
    Write-Host "Processing: $file"
    switch ($Operation) {
        "analyze" { Get-Content $file | Select-String $Pattern }
        "edit" { (Get-Content $file) -replace $Pattern, $Replacement | Set-Content $file }
        "check" { Test-Path $file }
    }
}

Write-Host "=== OPERATION COMPLETE ==="
```

#### **Uso do Template**

```bash
# Uma única chamada MCP em vez de múltiplas:
start_process("powershell -ExecutionPolicy Bypass -File E:/CODE-BACKUP/batch_template.ps1 -Files @('file1.txt','file2.txt') -Operation 'analyze' -Pattern 'error'")
```

### **MÉTRICAS DE SUCESSO**

#### **Antes da Otimização:**

- 10 operações = 10 chamadas MCP = 10× custo API
- Tempo total: 10× latência individual
- Overhead: 90% (9× mais trabalho desnecessário)

#### **Após Otimização:**

- 10 operações = 1 chamada MCP = 1× custo API
- Tempo total: 1× latência + processamento batch
- Overhead: 10% (apenas configuração inicial)

### **MONITORAMENTO DE CONFORMIDADE**

```json
{
  "success_criteria": {
    "api_calls_reduction": "≥70%",
    "cost_reduction": "≥70%",
    "performance_improvement": "≥50%",
    "operation_success_rate": "≥95%"
  },
  "red_flags": {
    "multiple_sequential_reads": "Deve ser consolidado",
    "repetitive_operations": "Deve usar loops/scripts",
    "similar_commands": "Deve ser agrupado"
  }
}
```

### **PROCESSO DE VALIDAÇÃO**

1. **Antes de qualquer operação múltipla:**

   - Avaliar se pode ser consolidada
   - Criar script batch se necessário
   - Usar uma única chamada MCP

2. **Durante a execução:**

   - Monitorar número de chamadas
   - Verificar eficiência

3. **Após a execução:**
   - Documentar economia obtida
   - Atualizar padrões para reuso

### **CASOS DE USO PRIORITÁRIOS**

1. **Análise de codebase**: 1 script que analisa todos os arquivos
2. **Refatoração múltipla**: 1 script que aplica mudanças em lote
3. **Validação de arquivos**: 1 comando que valida todos
4. **Configuração de ambiente**: 1 script que configura tudo
5. **Deploy e setup**: 1 processo que executa toda a sequência

---

**LEMBRE-SE**: Cada chamada MCP economizada = Menos custo API + Melhor performance + Menor complexidade

**REGRA DE OURO**: "Se você está fazendo mais de 2 chamadas MCP similares, consolide em 1 chamada batch"

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.
```
