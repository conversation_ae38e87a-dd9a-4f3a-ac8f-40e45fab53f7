// AGENDATRINTAE3 Server Actions - Appointment Management
// Type-safe Server Actions for Medical Appointment System
// Generated by VIBECODE SYSTEM V4.0 - Phase 6 Migration

'use server'

import { db } from '@/lib/db'
import { 
  appointments, 
  appointment_history, 
  doctors, 
  services, 
  users,
  type NewAppointment, 
  type NewAppointmentHistory 
} from '@/lib/schema'
import { createServerSupabaseClient } from '@/lib/supabase'
import { eq, and, desc, gte, lte, sql } from 'drizzle-orm'
import { revalidatePath } from 'next/cache'
import { z } from 'zod'

// Validation schemas
const createAppointmentSchema = z.object({
  doctor_id: z.string().uuid(),
  service_id: z.string().uuid(),
  scheduled_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  scheduled_time: z.string().regex(/^\d{2}:\d{2}$/),
  type: z.enum(['in_person', 'telemedicine']).default('in_person'),
  notes: z.string().optional(),
})

const updateAppointmentSchema = z.object({
  id: z.string().uuid(),
  status: z.enum(['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show']).optional(),
  scheduled_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  scheduled_time: z.string().regex(/^\d{2}:\d{2}$/).optional(),
  notes: z.string().optional(),
  doctor_notes: z.string().optional(),
  prescription: z.string().optional(),
  diagnosis: z.string().optional(),
})

// Server Actions
export async function createAppointment(formData: FormData) {
  try {
    // Validate user authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    // Parse and validate form data
    const rawData = {
      doctor_id: formData.get('doctor_id') as string,
      service_id: formData.get('service_id') as string,
      scheduled_date: formData.get('scheduled_date') as string,
      scheduled_time: formData.get('scheduled_time') as string,
      type: formData.get('type') as string || 'in_person',
      notes: formData.get('notes') as string || undefined,
    }

    const validatedData = createAppointmentSchema.parse(rawData)

    // Check if time slot is available
    const existingAppointment = await db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.doctor_id, validatedData.doctor_id),
          eq(appointments.scheduled_date, validatedData.scheduled_date),
          eq(appointments.scheduled_time, validatedData.scheduled_time),
          sql`${appointments.status} NOT IN ('cancelled', 'no_show')`
        )
      )
      .limit(1)

    if (existingAppointment.length > 0) {
      throw new Error('Time slot is not available')
    }

    // Get service details for pricing
    const service = await db
      .select()
      .from(services)
      .where(eq(services.id, validatedData.service_id))
      .limit(1)

    if (service.length === 0) {
      throw new Error('Service not found')
    }

    // Create appointment in database
    const [newAppointment] = await db
      .insert(appointments)
      .values({
        ...validatedData,
        patient_id: user.id,
        duration_minutes: service[0].duration_minutes,
        total_amount: service[0].price,
      })
      .returning()

    // Log appointment creation
    await logAppointmentHistory(newAppointment.id, 'created', {}, newAppointment, user.id)

    // Revalidate relevant pages
    revalidatePath('/dashboard/appointments')
    revalidatePath('/dashboard/calendar')

    return { success: true, appointment: newAppointment }
  } catch (error) {
    console.error('Error creating appointment:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to create appointment' 
    }
  }
}

export async function updateAppointment(formData: FormData) {
  try {
    // Validate user authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    // Parse and validate form data
    const rawData = {
      id: formData.get('id') as string,
      status: formData.get('status') as string || undefined,
      scheduled_date: formData.get('scheduled_date') as string || undefined,
      scheduled_time: formData.get('scheduled_time') as string || undefined,
      notes: formData.get('notes') as string || undefined,
      doctor_notes: formData.get('doctor_notes') as string || undefined,
      prescription: formData.get('prescription') as string || undefined,
      diagnosis: formData.get('diagnosis') as string || undefined,
    }

    const validatedData = updateAppointmentSchema.parse(rawData)

    // Get current appointment for history
    const currentAppointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, validatedData.id))
      .limit(1)

    if (currentAppointment.length === 0) {
      throw new Error('Appointment not found')
    }

    // Check authorization (patient can only update their own appointments)
    const userRole = await getUserRole(user.id)
    if (userRole === 'patient' && currentAppointment[0].patient_id !== user.id) {
      throw new Error('Unauthorized to update this appointment')
    }

    // Update appointment in database
    const updateData: any = { ...validatedData }
    delete updateData.id
    updateData.updated_at = new Date()

    const [updatedAppointment] = await db
      .update(appointments)
      .set(updateData)
      .where(eq(appointments.id, validatedData.id))
      .returning()

    // Log appointment update
    await logAppointmentHistory(
      validatedData.id, 
      'updated', 
      currentAppointment[0], 
      updatedAppointment, 
      user.id
    )

    // Revalidate relevant pages
    revalidatePath('/dashboard/appointments')
    revalidatePath('/dashboard/calendar')

    return { success: true, appointment: updatedAppointment }
  } catch (error) {
    console.error('Error updating appointment:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update appointment' 
    }
  }
}

export async function cancelAppointment(appointmentId: string, reason?: string) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    // Get current appointment
    const currentAppointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1)

    if (currentAppointment.length === 0) {
      throw new Error('Appointment not found')
    }

    // Check authorization
    const userRole = await getUserRole(user.id)
    if (userRole === 'patient' && currentAppointment[0].patient_id !== user.id) {
      throw new Error('Unauthorized to cancel this appointment')
    }

    // Update appointment status to cancelled
    const [cancelledAppointment] = await db
      .update(appointments)
      .set({ 
        status: 'cancelled',
        notes: reason ? `Cancelled: ${reason}` : 'Cancelled',
        updated_at: new Date()
      })
      .where(eq(appointments.id, appointmentId))
      .returning()

    // Log cancellation
    await logAppointmentHistory(
      appointmentId, 
      'cancelled', 
      currentAppointment[0], 
      cancelledAppointment, 
      user.id,
      reason
    )

    // Revalidate relevant pages
    revalidatePath('/dashboard/appointments')
    revalidatePath('/dashboard/calendar')

    return { success: true, appointment: cancelledAppointment }
  } catch (error) {
    console.error('Error cancelling appointment:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to cancel appointment' 
    }
  }
}

export async function getAppointmentsByUser() {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    const userRole = await getUserRole(user.id)
    
    let userAppointments
    
    if (userRole === 'patient') {
      // Get appointments for patient
      userAppointments = await db
        .select({
          appointment: appointments,
          doctor: {
            id: doctors.id,
            crm: doctors.crm,
            specialty: doctors.specialty,
          },
          doctor_user: {
            name: users.name,
          },
          service: {
            name: services.name,
            duration_minutes: services.duration_minutes,
          }
        })
        .from(appointments)
        .leftJoin(doctors, eq(appointments.doctor_id, doctors.id))
        .leftJoin(users, eq(doctors.user_id, users.id))
        .leftJoin(services, eq(appointments.service_id, services.id))
        .where(eq(appointments.patient_id, user.id))
        .orderBy(desc(appointments.scheduled_date), desc(appointments.scheduled_time))
    } else if (userRole === 'doctor') {
      // Get appointments for doctor
      const doctorProfile = await db
        .select()
        .from(doctors)
        .where(eq(doctors.user_id, user.id))
        .limit(1)

      if (doctorProfile.length === 0) {
        throw new Error('Doctor profile not found')
      }

      userAppointments = await db
        .select({
          appointment: appointments,
          patient: {
            name: users.name,
            phone: users.phone,
          },
          service: {
            name: services.name,
            duration_minutes: services.duration_minutes,
          }
        })
        .from(appointments)
        .leftJoin(users, eq(appointments.patient_id, users.id))
        .leftJoin(services, eq(appointments.service_id, services.id))
        .where(eq(appointments.doctor_id, doctorProfile[0].id))
        .orderBy(desc(appointments.scheduled_date), desc(appointments.scheduled_time))
    } else {
      throw new Error('Invalid user role')
    }

    return { success: true, appointments: userAppointments }
  } catch (error) {
    console.error('Error fetching appointments:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to fetch appointments' 
    }
  }
}

export async function getAvailableTimeSlots(doctorId: string, date: string) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    // Get day of week (0=Sunday, 1=Monday, etc.)
    const dayOfWeek = new Date(date).getDay()

    // Get doctor's availability for this day
    const doctorAvailability = await db
      .select()
      .from(availability)
      .where(
        and(
          eq(availability.doctor_id, doctorId),
          eq(availability.day_of_week, dayOfWeek),
          eq(availability.is_active, true)
        )
      )

    if (doctorAvailability.length === 0) {
      return { success: true, timeSlots: [] }
    }

    // Get existing appointments for this date
    const existingAppointments = await db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.doctor_id, doctorId),
          eq(appointments.scheduled_date, date),
          sql`${appointments.status} NOT IN ('cancelled', 'no_show')`
        )
      )

    // Generate available time slots
    const timeSlots = []
    for (const avail of doctorAvailability) {
      const startTime = avail.start_time
      const endTime = avail.end_time
      
      // Generate 30-minute slots between start and end time
      let currentTime = startTime
      while (currentTime < endTime) {
        const isBooked = existingAppointments.some(apt => apt.scheduled_time === currentTime)
        
        if (!isBooked) {
          timeSlots.push({
            time: currentTime,
            available: true
          })
        }
        
        // Add 30 minutes
        const [hours, minutes] = currentTime.split(':').map(Number)
        const totalMinutes = hours * 60 + minutes + 30
        const newHours = Math.floor(totalMinutes / 60)
        const newMinutes = totalMinutes % 60
        currentTime = `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`
      }
    }

    return { success: true, timeSlots }
  } catch (error) {
    console.error('Error fetching available time slots:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to fetch available time slots' 
    }
  }
}

// Helper functions
async function getUserRole(userId: string): Promise<string> {
  const user = await db
    .select({ role: users.role })
    .from(users)
    .where(eq(users.id, userId))
    .limit(1)

  return user[0]?.role || 'patient'
}

async function logAppointmentHistory(
  appointmentId: string,
  action: string,
  oldValues: any,
  newValues: any,
  performedBy: string,
  notes?: string
) {
  try {
    await db.insert(appointment_history).values({
      appointment_id: appointmentId,
      action,
      old_values: oldValues,
      new_values: newValues,
      performed_by: performedBy,
      notes,
    })
  } catch (error) {
    console.error('Error logging appointment history:', error)
  }
}
