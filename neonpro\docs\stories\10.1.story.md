# Story 10.1: Segmentação Inteligente de Pacientes

## Story Overview

**Como** gerente de marketing da clínica  
**Eu quero** sistema de segmentação inteligente de pacientes baseado em múltiplos critérios  
**Para que** eu possa criar campanhas direcionadas e personalizadas com maior efetividade

### Story Details

- **Epic**: Epic 10 - CRM & Campanhas
- **Story Points**: 9
- **Priority**: P1 (High)
- **Theme**: Smart Customer Segmentation
- **Dependencies**: Epic 9 (Complete Patient Database)

### Acceptance Criteria

#### AC1: Segmentação por Critérios Demográficos

- [ ] **GIVEN** base de pacientes cadastrados no sistema
- [ ] **WHEN** acesso o sistema de segmentação
- [ ] **THEN** posso filtrar por idade, gênero, localização
- [ ] **AND** filtros por estado civil, profissão, renda estimada
- [ ] **AND** visualizo preview do tamanho do segmento em tempo real
- [ ] **AND** posso salvar combinações de filtros como segmentos nomeados

#### AC2: Segmentação por Comportamento e Histórico

- [ ] **GIVEN** histórico de consultas e procedimentos dos pacientes
- [ ] **WHEN** configuro filtros comportamentais
- [ ] **THEN** posso segmentar por frequência de consultas (Epic 6)
- [ ] **AND** filtros por tipos de procedimentos realizados
- [ ] **AND** segmentação por valor médio de ticket (Epic 7)
- [ ] **AND** filtros por última consulta (30, 60, 90, 180 dias)
- [ ] **AND** identificação de pacientes VIP e alto valor

#### AC3: Segmentação por Status Financeiro

- [ ] **GIVEN** dados financeiros dos pacientes no sistema
- [ ] **WHEN** aplico filtros financeiros
- [ ] **THEN** posso segmentar por status de pagamento (Epic 7)
- [ ] **AND** filtros por inadimplência (atual, histórica)
- [ ] **AND** segmentação por forma de pagamento preferida
- [ ] **AND** filtros por planos de saúde e convênios
- [ ] **AND** identificação de pacientes com potencial de cobrança

#### AC4: Segmentos Predefinidos e Inteligentes

- [ ] **GIVEN** necessidade de segmentação rápida
- [ ] **WHEN** acesso segmentos predefinidos
- [ ] **THEN** tenho acesso a "Pacientes VIP" (alto valor e frequência)
- [ ] **AND** segmento "Pacientes Inativos" (sem consulta >90 dias)
- [ ] **AND** segmento "Inadimplentes" (faturas em atraso)
- [ ] **AND** segmento "Potencial Alto" (perfil similar aos VIPs)
- [ ] **AND** segmento "Aniversariantes do Mês"
- [ ] **AND** todos os segmentos atualizam automaticamente

#### AC5: Sistema de Tags e Categorização Manual

- [ ] **GIVEN** necessidade de categorização personalizada
- [ ] **WHEN** gerencio tags de pacientes
- [ ] **THEN** posso criar tags personalizadas por clínica
- [ ] **AND** aplicar tags manualmente a pacientes específicos
- [ ] **AND** usar tags como critério de segmentação
- [ ] **AND** tags com cores e categorias organizadas
- [ ] **AND** relatório de uso e distribuição de tags

#### AC6: Exportação e Integração com Campanhas

- [ ] **GIVEN** segmento criado e validado
- [ ] **WHEN** exporto lista de pacientes
- [ ] **THEN** posso exportar em formato CSV com campos selecionáveis
- [ ] **AND** exportação direct para sistema de campanhas
- [ ] **AND** agendamento de exportação automática para segmentos dinâmicos
- [ ] **AND** histórico de exportações com auditoria
- [ ] **AND** integração com ferramentas externas de marketing

### Technical Requirements

#### Patient Segmentation System

```typescript
// Sistema de Segmentação de Pacientes
interface SegmentacaoPacientes {
  id: string
  nome: string
  descricao?: string
  
  // Critérios de Segmentação
  criteriosDemograficos: CriteriosDemograficos
  criteriosComportamentais: CriteriosComportamentais
  criteriosFinanceiros: CriteriosFinanceiros
  tagsIncluidas: string[]
  tagsExcluidas: string[]
  
  // Configurações do Segmento
  atualizacaoAutomatica: boolean
  frequenciaAtualizacao: FrequenciaAtualizacao
  condicaoLogica: 'AND' | 'OR' // Entre diferentes tipos de critério
  
  // Resultados e Métricas
  quantidadePacientes: number
  ultimaAtualizacao: Date
  crescimentoUltimos30Dias: number
  valorEstimadoSegmento: number
  
  // Metadados
  criadoPor: string
  criadoEm: Date
  ultimaExportacao?: Date
  compartilhadoCom: string[]
  
  // Status
  ativo: boolean
  tipo: TipoSegmento
}

// Critérios Demográficos
interface CriteriosDemograficos {
  idadeMin?: number
  idadeMax?: number
  generos?: Genero[]
  estadoCivil?: EstadoCivil[]
  
  // Localização
  estados?: string[]
  cidades?: string[]
  bairros?: string[]
  cepRange?: { inicio: string; fim: string }
  
  // Profissional
  profissoes?: string[]
  rendaEstimadaMin?: number
  rendaEstimadaMax?: number
  
  // Outros
  temFilhos?: boolean
  numeroFilhosMin?: number
  numeroFilhosMax?: number
}

// Critérios Comportamentais
interface CriteriosComportamentais {
  // Frequência de Consultas
  consultasUltimos12Meses?: { min: number; max: number }
  consultasUltimos6Meses?: { min: number; max: number }
  consultasUltimos3Meses?: { min: number; max: number }
  
  // Última Consulta
  ultimaConsultaDias?: number // Quantos dias atrás
  temConsultaAgendada?: boolean
  
  // Procedimentos
  procedimentosRealizados?: string[]
  procedimentosNaoRealizados?: string[]
  especialidadesAtendidas?: EspecialidadeMedica[]
  
  // Engajamento
  abreCampanhas?: boolean // Histório de abertura de emails
  clicaCampanhas?: boolean
  respondeWhatsApp?: boolean
  usaPortalPaciente?: boolean
  
  // Padrões
  diaSemanaPreferido?: DiaSemana[]
  periodoPreferido?: PeriodoAtendimento[]
  profissionalPreferido?: string[]
}

// Critérios Financeiros
interface CriteriosFinanceiros {
  // Valor de Ticket
  ticketMedioMin?: number
  ticketMedioMax?: number
  valorTotalGastoMin?: number
  valorTotalGastoMax?: number
  
  // Status de Pagamento
  statusPagamento?: StatusPagamento[]
  temInadimplencia?: boolean
  inadimplenciaValorMin?: number
  diasAtraso?: { min: number; max: number }
  
  // Formas de Pagamento
  formasPagamentoPreferidas?: FormaPagamento[]
  parcelamentoMax?: number
  
  // Planos e Convênios
  temPlanoSaude?: boolean
  planosSaude?: string[]
  tipoConvenio?: TipoConvenio[]
  
  // Histórico Financeiro
  temChargeback?: boolean
  temProtesto?: boolean
  scoreCreditoMin?: number
  scoreCreditoMax?: number
}

// Segmento Predefinido
interface SegmentoPredefinido {
  id: string
  nome: string
  descricao: string
  icone: string
  cor: string
  
  // Lógica do Segmento
  criterios: CriteriosSegmentacao
  querySQL: string // Query otimizada para performance
  
  // Configurações
  atualizacaoAutomatica: boolean
  frequenciaAtualizacao: FrequenciaAtualizacao
  
  // Métricas
  quantidadeMedia: number
  tendenciaCrescimento: 'crescendo' | 'estavel' | 'diminuindo'
  
  // Recomendações
  campanhasSugeridas: string[]
  acoesSugeridas: string[]
}

// Tag de Paciente
interface TagPaciente {
  id: string
  nome: string
  cor: string
  categoria: CategoriaTag
  descricao?: string
  
  // Configurações
  aplicacaoManual: boolean
  aplicacaoAutomatica?: RegraAutomaticaTag
  
  // Métricas
  quantidadePacientes: number
  ultimaUtilizacao: Date
  
  // Metadados
  criadaPor: string
  criadaEm: Date
  ativa: boolean
  privada: boolean // Visível apenas para quem criou
}

// Regra Automática para Tags
interface RegraAutomaticaTag {
  condicoes: CriteriosSegmentacao
  frequenciaVerificacao: FrequenciaAtualizacao
  removerSeNaoAtender: boolean
  notificarQuandoAplicada: boolean
}

// Resultado de Segmentação
interface ResultadoSegmentacao {
  segmentoId: string
  pacientes: PacienteSegmentado[]
  
  // Estatísticas
  totalPacientes: number
  distribuicaoIdade: DistribuicaoIdade
  distribuicaoGenero: DistribuicaoGenero
  distribuicaoGeografica: DistribuicaoGeografica
  
  // Métricas Financeiras
  valorTotalSegmento: number
  ticketMedioSegmento: number
  potencialFaturamento: number
  
  // Métricas Comportamentais
  frequenciaMediaConsultas: number
  ultimaConsultaMedia: number
  engajamentoCampanhas: number
  
  // Insights
  caracteristicasPrincipais: string[]
  campanhasSugeridas: TipoCampanha[]
  melhorHorarioContato: HorarioContato
}

// Paciente no Segmento
interface PacienteSegmentado {
  id: string
  nome: string
  email?: string
  telefone?: string
  
  // Dados Relevantes para Campanhas
  dataUltimaConsulta?: Date
  valorTotalGasto: number
  frequenciaConsultas: number
  statusFinanceiro: StatusPagamento
  
  // Preferências de Contato
  aceitaEmail: boolean
  aceitaSMS: boolean
  aceitaWhatsApp: boolean
  melhorHorarioContato: HorarioContato
  
  // Engajamento
  taxaAberturaEmail: number
  taxaCliqueEmail: number
  respondeWhatsApp: boolean
  
  // Segmentação
  tags: string[]
  pontuacaoEngajamento: number
  probabilidadeRetorno: number
}

// Tipos de Enum
type TipoSegmento = 'manual' | 'automatico' | 'predefinido' | 'inteligente'
type FrequenciaAtualizacao = 'tempo_real' | 'diario' | 'semanal' | 'mensal'
type CategoriaTag = 'comportamento' | 'demografico' | 'medico' | 'financeiro' | 'marketing' | 'personalizado'
type TipoCampanha = 'retencao' | 'aquisicao' | 'upsell' | 'cobranca' | 'promocional' | 'educativa'
type HorarioContato = 'manha' | 'tarde' | 'noite' | 'qualquer'
```

#### Database Schema for Patient Segmentation

```sql
-- Segmentos de Pacientes
CREATE TABLE segmentos_pacientes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  
  -- Critérios de Segmentação (JSON para flexibilidade)
  criterios_demograficos JSONB DEFAULT '{}',
  criterios_comportamentais JSONB DEFAULT '{}',
  criterios_financeiros JSONB DEFAULT '{}',
  tags_incluidas TEXT[],
  tags_excluidas TEXT[],
  
  -- Lógica e Configuração
  condicao_logica condicao_type DEFAULT 'AND',
  atualizacao_automatica BOOLEAN DEFAULT TRUE,
  frequencia_atualizacao frequencia_type DEFAULT 'diario',
  
  -- Query SQL Otimizada (gerada automaticamente)
  query_sql TEXT,
  query_hash VARCHAR(64), -- Hash para cache
  
  -- Resultados Atuais
  quantidade_pacientes INTEGER DEFAULT 0,
  valor_estimado_segmento DECIMAL(15,2),
  ultima_atualizacao TIMESTAMPTZ,
  
  -- Histórico e Tendências
  crescimento_30_dias INTEGER DEFAULT 0,
  crescimento_7_dias INTEGER DEFAULT 0,
  tendencia_crescimento tendencia_type,
  
  -- Metadados
  tipo tipo_segmento_type DEFAULT 'manual',
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  ultima_exportacao TIMESTAMPTZ,
  compartilhado_com UUID[],
  
  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  privado BOOLEAN DEFAULT FALSE,
  
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT chk_nome_nao_vazio CHECK (LENGTH(TRIM(nome)) > 0),
  CONSTRAINT chk_quantidade_positiva CHECK (quantidade_pacientes >= 0)
);

-- Segmentos Predefinidos (Templates)
CREATE TABLE segmentos_predefinidos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(255) NOT NULL UNIQUE,
  descricao TEXT NOT NULL,
  icone VARCHAR(50),
  cor VARCHAR(7), -- Hex color
  
  -- Configuração do Segmento
  criterios JSONB NOT NULL,
  query_sql TEXT NOT NULL,
  
  -- Configurações de Atualização
  atualizacao_automatica BOOLEAN DEFAULT TRUE,
  frequencia_atualizacao frequencia_type DEFAULT 'diario',
  
  -- Métricas Históricas
  quantidade_media INTEGER,
  tendencia_crescimento tendencia_type,
  
  -- Recomendações
  campanhas_sugeridas TEXT[],
  acoes_sugeridas TEXT[],
  
  -- Metadados
  categoria categoria_segmento_type,
  ordem_exibicao INTEGER DEFAULT 0,
  ativo BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tags de Pacientes
CREATE TABLE tags_pacientes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(100) NOT NULL,
  cor VARCHAR(7) NOT NULL, -- Hex color
  categoria categoria_tag_type NOT NULL,
  descricao TEXT,
  
  -- Configurações de Aplicação
  aplicacao_manual BOOLEAN DEFAULT TRUE,
  aplicacao_automatica JSONB, -- Regras para aplicação automática
  
  -- Configurações de Uso
  frequencia_verificacao frequencia_type DEFAULT 'diario',
  remover_se_nao_atender BOOLEAN DEFAULT FALSE,
  notificar_quando_aplicada BOOLEAN DEFAULT FALSE,
  
  -- Métricas
  quantidade_pacientes INTEGER DEFAULT 0,
  ultima_utilizacao TIMESTAMPTZ,
  
  -- Metadados
  criada_por UUID NOT NULL REFERENCES auth.users(id),
  criada_em TIMESTAMPTZ DEFAULT NOW(),
  ativa BOOLEAN DEFAULT TRUE,
  privada BOOLEAN DEFAULT FALSE,
  
  -- Clínica (Multi-tenant)
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT uk_tag_nome_clinica UNIQUE (nome, clinica_id),
  CONSTRAINT chk_tag_nome_valido CHECK (LENGTH(TRIM(nome)) > 0),
  CONSTRAINT chk_cor_hex_valida CHECK (cor ~ '^#[0-9A-Fa-f]{6}$')
);

-- Associação de Tags com Pacientes
CREATE TABLE pacientes_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  paciente_id UUID NOT NULL REFERENCES pacientes(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES tags_pacientes(id) ON DELETE CASCADE,
  
  -- Metadados da Aplicação
  aplicada_automaticamente BOOLEAN DEFAULT FALSE,
  aplicada_por UUID REFERENCES auth.users(id),
  aplicada_em TIMESTAMPTZ DEFAULT NOW(),
  
  -- Informações Adicionais
  observacoes TEXT,
  valida_ate DATE, -- Tag temporária
  
  CONSTRAINT uk_paciente_tag UNIQUE (paciente_id, tag_id)
);

-- Histórico de Segmentações (para auditoria e analytics)
CREATE TABLE historico_segmentacoes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  segmento_id UUID NOT NULL REFERENCES segmentos_pacientes(id) ON DELETE CASCADE,
  
  -- Snapshot dos Resultados
  quantidade_pacientes INTEGER NOT NULL,
  lista_pacientes_ids UUID[], -- Para auditoria
  criterios_utilizados JSONB NOT NULL,
  
  -- Métricas Calculadas
  valor_total_segmento DECIMAL(15,2),
  ticket_medio DECIMAL(10,2),
  distribuicao_demografica JSONB,
  distribuicao_comportamental JSONB,
  
  -- Metadados
  executado_por UUID REFERENCES auth.users(id),
  executado_em TIMESTAMPTZ DEFAULT NOW(),
  tempo_processamento_ms INTEGER,
  
  -- Contexto
  tipo_execucao execucao_type DEFAULT 'manual', -- manual, automatica, exportacao
  
  CONSTRAINT chk_quantidade_historico_positiva CHECK (quantidade_pacientes >= 0),
  CONSTRAINT chk_tempo_processamento_positivo CHECK (tempo_processamento_ms > 0)
);

-- Resultados de Segmentação em Cache
CREATE TABLE cache_segmentacao (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  segmento_id UUID NOT NULL REFERENCES segmentos_pacientes(id) ON DELETE CASCADE,
  hash_criterios VARCHAR(64) NOT NULL,
  
  -- Resultados em Cache
  pacientes_ids UUID[] NOT NULL,
  quantidade_total INTEGER NOT NULL,
  metricas_calculadas JSONB,
  
  -- Controle de Cache
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  expira_em TIMESTAMPTZ NOT NULL,
  acessado_em TIMESTAMPTZ DEFAULT NOW(),
  numero_acessos INTEGER DEFAULT 1,
  
  CONSTRAINT uk_cache_segmento_hash UNIQUE (segmento_id, hash_criterios),
  CONSTRAINT chk_cache_expiracao CHECK (expira_em > criado_em)
);

-- Tipos Enum para Segmentação
CREATE TYPE condicao_type AS ENUM ('AND', 'OR');
CREATE TYPE frequencia_type AS ENUM ('tempo_real', 'diario', 'semanal', 'mensal');
CREATE TYPE tendencia_type AS ENUM ('crescendo', 'estavel', 'diminuindo');
CREATE TYPE tipo_segmento_type AS ENUM ('manual', 'automatico', 'predefinido', 'inteligente');
CREATE TYPE categoria_segmento_type AS ENUM ('marketing', 'financeiro', 'medico', 'operacional');
CREATE TYPE categoria_tag_type AS ENUM ('comportamento', 'demografico', 'medico', 'financeiro', 'marketing', 'personalizado');
CREATE TYPE execucao_type AS ENUM ('manual', 'automatica', 'exportacao', 'teste');

-- Índices para Performance de Segmentação
CREATE INDEX idx_segmentos_ativo ON segmentos_pacientes(ativo) WHERE ativo = true;
CREATE INDEX idx_segmentos_atualizacao ON segmentos_pacientes(atualizacao_automatica, frequencia_atualizacao) WHERE atualizacao_automatica = true;
CREATE INDEX idx_segmentos_criador ON segmentos_pacientes(criado_por);
CREATE INDEX idx_segmentos_hash ON segmentos_pacientes(query_hash);

-- Índices para Tags
CREATE INDEX idx_tags_categoria ON tags_pacientes(categoria);
CREATE INDEX idx_tags_ativa ON tags_pacientes(ativa) WHERE ativa = true;
CREATE INDEX idx_tags_clinica ON tags_pacientes(clinica_id);
CREATE INDEX idx_pacientes_tags_paciente ON pacientes_tags(paciente_id);
CREATE INDEX idx_pacientes_tags_tag ON pacientes_tags(tag_id);

-- Índices para Cache
CREATE INDEX idx_cache_expiracao ON cache_segmentacao(expira_em);
CREATE INDEX idx_cache_segmento ON cache_segmentacao(segmento_id);

-- Índices para Histórico
CREATE INDEX idx_historico_segmento ON historico_segmentacoes(segmento_id);
CREATE INDEX idx_historico_data ON historico_segmentacoes(executado_em);

-- Full-text search para nomes de segmentos e tags
CREATE INDEX idx_segmentos_search ON segmentos_pacientes USING gin(
  to_tsvector('portuguese', coalesce(nome, '') || ' ' || coalesce(descricao, ''))
);

CREATE INDEX idx_tags_search ON tags_pacientes USING gin(
  to_tsvector('portuguese', coalesce(nome, '') || ' ' || coalesce(descricao, ''))
);

-- Índices específicos para critérios de segmentação frequentes
CREATE INDEX idx_pacientes_idade ON pacientes(data_nascimento);
CREATE INDEX idx_pacientes_genero ON pacientes(genero);
CREATE INDEX idx_pacientes_cidade ON pacientes(cidade);
CREATE INDEX idx_pacientes_ultima_consulta ON pacientes(ultima_consulta);
```

#### Segmentation API Endpoints

```typescript
// Create Patient Segment API
export async function POST(request: NextRequest) {
  const { 
    nome, 
    descricao, 
    criteriosDemograficos,
    criteriosComportamentais,
    criteriosFinanceiros,
    tagsIncluidas,
    tagsExcluidas,
    atualizacaoAutomatica,
    frequenciaAtualizacao
  } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    // Validar permissões
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Gerar query SQL otimizada
    const querySQL = await generateSegmentationQuery({
      criteriosDemograficos,
      criteriosComportamentais,
      criteriosFinanceiros,
      tagsIncluidas,
      tagsExcluidas
    })
    
    // Calcular hash para cache
    const queryHash = generateQueryHash(querySQL)
    
    // Executar query para preview
    const previewResult = await executeSegmentationQuery(querySQL, true)
    
    // Criar segmento
    const { data: segmento, error } = await supabase
      .from('segmentos_pacientes')
      .insert({
        nome,
        descricao,
        criterios_demograficos: criteriosDemograficos,
        criterios_comportamentais: criteriosComportamentais,
        criterios_financeiros: criteriosFinanceiros,
        tags_incluidas: tagsIncluidas,
        tags_excluidas: tagsExcluidas,
        atualizacao_automatica: atualizacaoAutomatica,
        frequencia_atualizacao: frequenciaAtualizacao,
        query_sql: querySQL,
        query_hash: queryHash,
        quantidade_pacientes: previewResult.total,
        valor_estimado_segmento: previewResult.valorTotal,
        criado_por: user.id
      })
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    // Agendar primeira atualização
    if (atualizacaoAutomatica) {
      await scheduleSegmentUpdate(segmento.id, frequenciaAtualizacao)
    }
    
    // Salvar no cache
    await cacheSegmentationResult(segmento.id, queryHash, previewResult)
    
    return NextResponse.json({
      segmento,
      preview: previewResult,
      message: 'Segmento criado com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating segment:', error)
    return NextResponse.json({
      error: 'Erro ao criar segmento'
    }, { status: 500 })
  }
}

// Execute Segmentation API
export async function POST(
  request: NextRequest,
  { params }: { params: { segmentId: string } }
) {
  const { forceRefresh = false } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    // Buscar segmento
    const { data: segmento } = await supabase
      .from('segmentos_pacientes')
      .select('*')
      .eq('id', params.segmentId)
      .single()
    
    if (!segmento) {
      return NextResponse.json({
        error: 'Segmento não encontrado'
      }, { status: 404 })
    }
    
    // Verificar cache se não for refresh forçado
    if (!forceRefresh) {
      const cachedResult = await getCachedSegmentationResult(
        params.segmentId, 
        segmento.query_hash
      )
      
      if (cachedResult && !isCacheExpired(cachedResult)) {
        return NextResponse.json({
          resultado: cachedResult,
          fromCache: true,
          message: 'Resultado obtido do cache'
        })
      }
    }
    
    // Executar segmentação
    const startTime = Date.now()
    const resultado = await executeSegmentationQuery(segmento.query_sql, false)
    const processingTime = Date.now() - startTime
    
    // Calcular métricas adicionais
    const metricas = await calculateSegmentMetrics(resultado.pacientes)
    
    // Gerar insights automáticos
    const insights = await generateSegmentInsights(resultado, segmento)
    
    // Atualizar segmento com novos dados
    await supabase
      .from('segmentos_pacientes')
      .update({
        quantidade_pacientes: resultado.total,
        valor_estimado_segmento: resultado.valorTotal,
        ultima_atualizacao: new Date(),
        crescimento_7_dias: await calculateGrowth(params.segmentId, 7),
        crescimento_30_dias: await calculateGrowth(params.segmentId, 30)
      })
      .eq('id', params.segmentId)
    
    // Salvar histórico
    await supabase
      .from('historico_segmentacoes')
      .insert({
        segmento_id: params.segmentId,
        quantidade_pacientes: resultado.total,
        lista_pacientes_ids: resultado.pacientes.map(p => p.id),
        criterios_utilizados: {
          demograficos: segmento.criterios_demograficos,
          comportamentais: segmento.criterios_comportamentais,
          financeiros: segmento.criterios_financeiros
        },
        valor_total_segmento: resultado.valorTotal,
        ticket_medio: metricas.ticketMedio,
        distribuicao_demografica: metricas.distribuicaoDemografica,
        distribuicao_comportamental: metricas.distribuicaoComportamental,
        tempo_processamento_ms: processingTime,
        tipo_execucao: forceRefresh ? 'manual' : 'automatica'
      })
    
    // Atualizar cache
    await cacheSegmentationResult(
      params.segmentId, 
      segmento.query_hash, 
      { ...resultado, metricas, insights }
    )
    
    return NextResponse.json({
      resultado: {
        ...resultado,
        metricas,
        insights,
        processingTime
      },
      fromCache: false,
      message: 'Segmentação executada com sucesso'
    })
    
  } catch (error) {
    console.error('Error executing segmentation:', error)
    return NextResponse.json({
      error: 'Erro ao executar segmentação'
    }, { status: 500 })
  }
}

// Predefined Segments API
export async function GET() {
  const supabase = createServerClient()
  
  try {
    // Buscar segmentos predefinidos ativos
    const { data: segmentosPredefinidos } = await supabase
      .from('segmentos_predefinidos')
      .select('*')
      .eq('ativo', true)
      .order('ordem_exibicao', { ascending: true })
    
    // Executar cada segmento para obter contagem atual
    const segmentosComContagem = await Promise.all(
      segmentosPredefinidos.map(async (segmento) => {
        try {
          const resultado = await executeSegmentationQuery(segmento.query_sql, true)
          
          return {
            ...segmento,
            quantidadeAtual: resultado.total,
            valorEstimado: resultado.valorTotal,
            ultimaAtualizacao: new Date()
          }
        } catch (error) {
          console.error(`Error executing predefined segment ${segmento.id}:`, error)
          return {
            ...segmento,
            quantidadeAtual: 0,
            valorEstimado: 0,
            erro: true
          }
        }
      })
    )
    
    // Agrupar por categoria
    const segmentosPorCategoria = segmentosComContagem.reduce((acc, segmento) => {
      const categoria = segmento.categoria || 'geral'
      if (!acc[categoria]) {
        acc[categoria] = []
      }
      acc[categoria].push(segmento)
      return acc
    }, {} as Record<string, any[]>)
    
    return NextResponse.json({
      segmentosPredefinidos: segmentosComContagem,
      segmentosPorCategoria,
      totalSegmentos: segmentosComContagem.length,
      message: 'Segmentos predefinidos carregados com sucesso'
    })
    
  } catch (error) {
    console.error('Error loading predefined segments:', error)
    return NextResponse.json({
      error: 'Erro ao carregar segmentos predefinidos'
    }, { status: 500 })
  }
}

// Tag Management API
export async function POST(
  request: NextRequest,
  { params }: { params: { action: string } }
) {
  const { nome, cor, categoria, descricao, aplicacaoAutomatica } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    if (params.action === 'create') {
      // Criar nova tag
      const { data: tag, error } = await supabase
        .from('tags_pacientes')
        .insert({
          nome,
          cor,
          categoria,
          descricao,
          aplicacao_automatica: aplicacaoAutomatica,
          criada_por: user.id,
          clinica_id: await getClinicaId(user.id)
        })
        .select()
        .single()
      
      if (error) {
        throw error
      }
      
      // Configurar regra automática se especificada
      if (aplicacaoAutomatica) {
        await setupAutomaticTagRule(tag.id, aplicacaoAutomatica)
      }
      
      return NextResponse.json({
        tag,
        message: 'Tag criada com sucesso'
      })
    }
    
    if (params.action === 'apply') {
      const { tagId, pacienteIds, observacoes } = await request.json()
      
      // Aplicar tag a múltiplos pacientes
      const aplicacoes = pacienteIds.map((pacienteId: string) => ({
        paciente_id: pacienteId,
        tag_id: tagId,
        aplicada_por: user.id,
        observacoes
      }))
      
      const { error } = await supabase
        .from('pacientes_tags')
        .upsert(aplicacoes)
      
      if (error) {
        throw error
      }
      
      // Atualizar contador da tag
      await updateTagCount(tagId)
      
      return NextResponse.json({
        aplicadas: pacienteIds.length,
        message: 'Tags aplicadas com sucesso'
      })
    }
    
  } catch (error) {
    console.error('Error managing tags:', error)
    return NextResponse.json({
      error: 'Erro ao gerenciar tags'
    }, { status: 500 })
  }
}

// Segment Export API
export async function POST(
  request: NextRequest,
  { params }: { params: { segmentId: string } }
) {
  const { formato, campos, incluirMetricas } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    // Buscar dados do segmento
    const { data: segmento } = await supabase
      .from('segmentos_pacientes')
      .select('*')
      .eq('id', params.segmentId)
      .single()
    
    if (!segmento) {
      return NextResponse.json({
        error: 'Segmento não encontrado'
      }, { status: 404 })
    }
    
    // Executar segmentação
    const resultado = await executeSegmentationQuery(segmento.query_sql, false)
    
    // Preparar dados para exportação
    const dadosExportacao = await prepareExportData(
      resultado.pacientes,
      campos,
      incluirMetricas
    )
    
    // Gerar arquivo baseado no formato
    let arquivoExportacao: Buffer
    let contentType: string
    let nomeArquivo: string
    
    if (formato === 'csv') {
      arquivoExportacao = await generateCSV(dadosExportacao)
      contentType = 'text/csv'
      nomeArquivo = `segmento_${segmento.nome}_${Date.now()}.csv`
    } else if (formato === 'excel') {
      arquivoExportacao = await generateExcel(dadosExportacao)
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      nomeArquivo = `segmento_${segmento.nome}_${Date.now()}.xlsx`
    } else {
      throw new Error('Formato de exportação não suportado')
    }
    
    // Salvar arquivo no storage
    const { data: upload } = await supabase.storage
      .from('exports')
      .upload(`segmentos/${nomeArquivo}`, arquivoExportacao, {
        contentType,
        upsert: false
      })
    
    if (!upload) {
      throw new Error('Erro ao salvar arquivo de exportação')
    }
    
    // Registrar exportação
    await supabase
      .from('segmentos_pacientes')
      .update({ ultima_exportacao: new Date() })
      .eq('id', params.segmentId)
    
    // Gerar URL de download temporária
    const { data: downloadUrl } = await supabase.storage
      .from('exports')
      .createSignedUrl(upload.path, 3600) // 1 hora
    
    return NextResponse.json({
      downloadUrl: downloadUrl?.signedUrl,
      nomeArquivo,
      totalRegistros: dadosExportacao.length,
      formato,
      message: 'Exportação concluída com sucesso'
    })
    
  } catch (error) {
    console.error('Error exporting segment:', error)
    return NextResponse.json({
      error: 'Erro ao exportar segmento'
    }, { status: 500 })
  }
}
```

### Integration Points

#### Epic 5 Integration (Portal Paciente)
- **Preferências de Comunicação**: Dados para segmentação por canal preferido
- **Engajamento Digital**: Métricas de uso do portal para segmentação comportamental
- **Self-Service Data**: Informações atualizadas pelos pacientes
- **Consent Management**: Respeito às preferências de marketing

#### Epic 6 Integration (Agenda Inteligente)
- **Padrões de Agendamento**: Segmentação por frequência e horários preferenciais
- **Histórico de Consultas**: Critérios baseados em última consulta e procedimentos
- **No-Show Patterns**: Identificação de pacientes com histórico de faltas
- **Professional Preferences**: Segmentação por profissional de preferência

#### Epic 7 Integration (Financeiro Essencial)
- **Payment Behavior**: Segmentação por formas de pagamento e pontualidade
- **Financial Status**: Identificação de inadimplentes e clientes VIP
- **Insurance Data**: Segmentação por planos de saúde e convênios
- **Spending Patterns**: Análise de ticket médio e sazonalidade de gastos

#### Epic 8 Integration (BI & Dashboards)
- **Segment Performance**: KPIs de engajamento por segmento
- **Campaign Analytics**: Métricas de performance integradas aos dashboards
- **Predictive Models**: ML para identificação de segmentos de alto valor
- **Real-time Updates**: Dashboards com segmentação em tempo real

#### Epic 9 Integration (Cadastro & Prontuário)
- **Medical History**: Segmentação por condições médicas e tratamentos
- **Health Tags**: Tags automáticas baseadas em dados médicos
- **Treatment Compliance**: Identificação de pacientes com boa aderência
- **Risk Segmentation**: Grupos de risco baseados em histórico médico

### Testing Strategy

#### Segmentation Tests

```typescript
describe('Patient Segmentation System', () => {
  test('creates demographic segment correctly', async () => {
    const segment = await createSegment({
      nome: 'Mulheres 25-35 anos',
      criteriosDemograficos: {
        generos: ['feminino'],
        idadeMin: 25,
        idadeMax: 35
      }
    })
    
    expect(segment.nome).toBe('Mulheres 25-35 anos')
    expect(segment.quantidade_pacientes).toBeGreaterThan(0)
  })
  
  test('applies behavioral filters correctly', async () => {
    const segment = await createSegment({
      nome: 'Pacientes Inativos',
      criteriosComportamentais: {
        ultimaConsultaDias: 90
      }
    })
    
    const result = await executeSegmentation(segment.id)
    expect(result.pacientes.every(p => 
      daysBetween(p.ultimaConsulta, new Date()) > 90
    )).toBe(true)
  })
  
  test('combines multiple criteria with AND logic', async () => {
    const segment = await createSegment({
      nome: 'VIP Inativos',
      criteriosComportamentais: { ultimaConsultaDias: 60 },
      criteriosFinanceiros: { ticketMedioMin: 500 },
      condicaoLogica: 'AND'
    })
    
    const result = await executeSegmentation(segment.id)
    expect(result.pacientes.length).toBeGreaterThan(0)
    expect(result.pacientes.every(p => 
      p.ticketMedio >= 500 && daysBetween(p.ultimaConsulta, new Date()) > 60
    )).toBe(true)
  })
  
  test('manages tags correctly', async () => {
    const tag = await createTag({
      nome: 'VIP',
      categoria: 'financeiro',
      cor: '#FFD700'
    })
    
    await applyTagToPatients(tag.id, ['patient-1', 'patient-2'])
    
    const updatedTag = await getTag(tag.id)
    expect(updatedTag.quantidade_pacientes).toBe(2)
  })
})
```

### Dev Notes

#### Performance Optimization
- **Query Optimization**: Índices específicos para critérios de segmentação frequentes
- **Caching Strategy**: Cache inteligente para segmentos com atualizações periódicas
- **Async Processing**: Processamento de segmentos grandes em background
- **Preview Mode**: Execução otimizada para preview rápido

#### Advanced Segmentation Features
- **Smart Suggestions**: Sugestões de segmentos baseadas em padrões históricos
- **Similarity Detection**: Identificação de pacientes similares aos melhores clientes
- **Churn Prediction**: Segmentos baseados em probabilidade de abandono
- **Dynamic Updates**: Segmentos que se atualizam automaticamente

#### Integration Architecture
- **Real-time Sync**: Sincronização em tempo real com dados de outros épicos
- **Event-Driven**: Updates automáticos baseados em eventos do sistema
- **Cross-Epic Queries**: Queries otimizadas que cruzam dados de múltiplos épicos
- **Unified Data Model**: Modelo de dados unificado para segmentação 360°

---

## Dev Agent Record

### Task Status
- [x] Analyzed segmentation requirements for Epic 10
- [x] Created comprehensive story with 6 acceptance criteria
- [x] Designed advanced TypeScript interfaces for segmentation
- [x] Specified database schema with optimization for large datasets
- [x] Developed complete API endpoints for segment management
- [x] Integrated with Epic 5-9 for 360° customer view
- [x] Added predefined segments and intelligent tagging
- [x] Included performance optimization and caching strategies
- [x] Created comprehensive testing strategy

### File List
- `docs/stories/10.1.story.md` - Smart Patient Segmentation implementation story

### Change Log
- **Story 10.1 Creation**: Advanced patient segmentation system with demographic, behavioral, and financial criteria
- **Multi-Criteria Segmentation**: Support for complex queries with AND/OR logic
- **Predefined Segments**: Smart segments for common use cases (VIP, inactive, delinquent)
- **Intelligent Tagging**: Manual and automatic tag application with rules
- **Performance Optimization**: Caching, indexing, and query optimization for large datasets
- **Epic Integration**: Full integration with Epic 5-9 for comprehensive customer data

### Completion Notes
Story 10.1 delivers a powerful patient segmentation system that enables precise targeting for marketing campaigns. The system combines demographic, behavioral, and financial data from across the entire NeonPro platform to create actionable customer segments.

### Next Story
Ready to create **Story 10.2: Automação de Campanhas e Marketing** - the campaign creation and execution system.
