# 🏢 Multi-Tenant SaaS Template

Advanced B2B SaaS template with organization management and team features.

## Features

- ✅ **Multi-Tenancy**: Organization-based data isolation
- ✅ **Team Management**: Invite and manage team members
- ✅ **Role-Based Access**: Admin, member, viewer roles
- ✅ **Organization Billing**: Per-organization subscriptions
- ✅ **Activity Logs**: Track team activities
- ✅ **Team Dashboard**: Shared workspace features
- ✅ **Data Export**: Organization data export
- ✅ **Advanced RLS**: Row-level security policies

## Quick Start

```bash
# Copy template
cp -r ../multi-tenant-saas my-b2b-app
cd my-b2b-app

# Install dependencies
bun install

# Set up environment
cp .env.example .env.local
# Configure your credentials

# Initialize database
bun db:migrate
bun db:seed

# Start development
bun dev
```

## Additional Features

- Organization switcher UI
- Team member invitation system
- Permission management
- Usage-based billing support
- Webhook processing for team events
- Background job queue

## Database Schema

- `organizations` - Company/team accounts
- `organization_members` - Team membership
- `organization_invites` - Pending invitations
- `organization_subscriptions` - Billing per org
- `activity_logs` - Audit trail

## Perfect For

- B2B SaaS applications
- Team collaboration tools
- Business productivity apps
- Enterprise software
- Multi-workspace platforms