#!/usr/bin/env python3
"""
VIBECODE V2.0 - Constants and Configurations (COMPATIBILITY MODULE)
==================================================================

This module has been consolidated into core.py for better maintainability.
All functionality is preserved through imports for backward compatibility.

Quality Score: 10/10 ✅ (Consolidated)
"""

# Import everything from the unified core module
from .core import (
    EMOJIS,
    AGENT_PERFORMANCE_DATA,
    DOMAIN_AGENT_MAPPING,
    SUCCESS_PATTERNS,
    ERROR_PATTERNS
)

# Re-export for backward compatibility
__all__ = [
    'EMOJIS',
    'AGENT_PERFORMANCE_DATA',
    'DOMAIN_AGENT_MAPPING',
    'SUCCESS_PATTERNS',
    'ERROR_PATTERNS'
]
