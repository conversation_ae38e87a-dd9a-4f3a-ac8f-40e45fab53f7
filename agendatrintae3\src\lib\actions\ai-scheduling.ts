// AGENDATRINTAE3 AI Smart Scheduling Server Actions
// Phase 7 AI Integration - Streaming Server Actions for Smart Scheduling
// Generated by VIBECODE SYSTEM V4.0 - AI Integration

"use server";

import { db } from "@/lib/db";
import {
  ai_scheduling_history,
  ai_scheduling_optimization,
  appointments,
  availability,
  doctors,
  services,
  users,
} from "@/lib/schema";
import { createServerSupabaseClient } from "@/lib/supabase";
import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";
import { and, desc, eq, gte, lte, ne, sql } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { z } from "zod";

// Validation Schemas
const generateSchedulingSchema = z.object({
  user_id: z.string().uuid(),
  doctor_preferences: z
    .object({
      preferred_doctors: z.array(z.string().uuid()).optional(),
      specialty_requirements: z.array(z.string()).optional(),
      exclude_doctors: z.array(z.string().uuid()).optional(),
    })
    .optional(),
  scheduling_constraints: z
    .object({
      preferred_dates: z.array(z.string()).optional(),
      preferred_times: z.array(z.string()).optional(),
      avoid_dates: z.array(z.string()).optional(),
      max_wait_days: z.number().min(1).max(90).optional(),
    })
    .optional(),
  urgency_level: z.enum(["emergency", "urgent", "routine", "follow_up"]),
  service_id: z.string().uuid(),
  medical_notes: z.string().optional(),
  patient_preferences: z.string().optional(),
});

const reviewSchedulingSchema = z.object({
  optimization_id: z.string().uuid(),
  status: z.enum(["reviewed", "implemented", "archived"]),
  feedback: z.string().optional(),
  accuracy_rating: z.number().min(1).max(5).optional(),
  selected_slot: z
    .object({
      doctor_id: z.string().uuid(),
      date: z.string(),
      time: z.string(),
    })
    .optional(),
});

// Types
export type GenerateSchedulingInput = z.infer<typeof generateSchedulingSchema>;
export type ReviewSchedulingInput = z.infer<typeof reviewSchedulingSchema>;

// Helper Functions
async function getDoctorAvailability(
  doctorId: string,
  startDate: string,
  endDate: string
) {
  const doctorAvailability = await db
    .select()
    .from(availability)
    .where(
      and(
        eq(availability.doctor_id, doctorId),
        eq(availability.is_active, true)
      )
    );

  return doctorAvailability;
}

async function getExistingAppointments(
  doctorId: string,
  startDate: string,
  endDate: string
) {
  const existingAppointments = await db
    .select()
    .from(appointments)
    .where(
      and(
        eq(appointments.doctor_id, doctorId),
        gte(appointments.scheduled_date, startDate),
        lte(appointments.scheduled_date, endDate),
        ne(appointments.status, "cancelled"),
        ne(appointments.status, "no_show")
      )
    )
    .orderBy(appointments.scheduled_date, appointments.scheduled_time);

  return existingAppointments;
}

async function getPatientHistory(userId: string) {
  const patientHistory = await db
    .select({
      appointment: appointments,
      doctor: doctors,
      service: services,
    })
    .from(appointments)
    .leftJoin(doctors, eq(appointments.doctor_id, doctors.id))
    .leftJoin(services, eq(appointments.service_id, services.id))
    .where(eq(appointments.patient_id, userId))
    .orderBy(desc(appointments.scheduled_date))
    .limit(10);

  return patientHistory;
}

async function getAvailableDoctors(
  serviceId: string,
  specialtyRequirements?: string[]
) {
  let query = db
    .select({
      doctor: doctors,
      user: users,
    })
    .from(doctors)
    .leftJoin(users, eq(doctors.user_id, users.id))
    .where(and(eq(doctors.is_available, true), eq(users.is_active, true)));

  // Add specialty filtering if provided
  if (specialtyRequirements && specialtyRequirements.length > 0) {
    // Note: In real implementation, would use proper SQL IN operator
    // For now, simplified filtering
  }

  const availableDoctors = await query.orderBy(doctors.rating);
  return availableDoctors;
}

function calculateUrgencyPriority(urgencyLevel: string): number {
  switch (urgencyLevel) {
    case "emergency":
      return 10;
    case "urgent":
      return 8;
    case "routine":
      return 5;
    case "follow_up":
      return 3;
    default:
      return 5;
  }
}

function generateTimeSlots(
  availability: any[],
  existingAppointments: any[],
  serviceDuration: number
) {
  const slots = [];
  const now = new Date();

  // Generate slots for next 30 days
  for (let day = 0; day < 30; day++) {
    const date = new Date(now);
    date.setDate(date.getDate() + day);
    const dayOfWeek = date.getDay();

    // Find availability for this day
    const dayAvailability = availability.filter(
      (av) => av.day_of_week === dayOfWeek
    );

    for (const avail of dayAvailability) {
      const startTime = new Date(
        `${date.toISOString().split("T")[0]}T${avail.start_time}`
      );
      const endTime = new Date(
        `${date.toISOString().split("T")[0]}T${avail.end_time}`
      );

      // Generate slots every 30 minutes (or service duration)
      const slotDuration = Math.max(serviceDuration, 30);
      let currentTime = new Date(startTime);

      while (currentTime < endTime) {
        const slotEnd = new Date(currentTime.getTime() + slotDuration * 60000);

        // Check if slot conflicts with existing appointments
        const hasConflict = existingAppointments.some((apt) => {
          const aptStart = new Date(
            `${apt.scheduled_date}T${apt.scheduled_time}`
          );
          const aptEnd = new Date(
            aptStart.getTime() + (apt.duration_minutes || 30) * 60000
          );

          return currentTime < aptEnd && slotEnd > aptStart;
        });

        if (!hasConflict && currentTime > now) {
          slots.push({
            date: date.toISOString().split("T")[0],
            time:
              currentTime.toTimeString().split(" ")[0]?.substring(0, 5) ||
              "00:00",
            datetime: currentTime.toISOString(),
            available: true,
          });
        }

        currentTime = new Date(currentTime.getTime() + slotDuration * 60000);
      }
    }
  }

  return slots.slice(0, 20); // Return top 20 slots
}

function buildSchedulingPrompt(
  patientHistory: any[],
  availableDoctors: any[],
  availableSlots: any[],
  serviceDetails: any,
  input: GenerateSchedulingInput
): string {
  const urgencyPriority = calculateUrgencyPriority(input.urgency_level);

  const prompt = `
You are an intelligent medical scheduling assistant for AGENDATRINTAE3. Optimize appointment scheduling based on medical urgency, doctor availability, patient preferences, and operational efficiency.

PATIENT CONTEXT:
- Patient ID: ${input.user_id}
- Urgency Level: ${input.urgency_level} (Priority: ${urgencyPriority}/10)
- Medical Notes: ${input.medical_notes || "None provided"}
- Patient Preferences: ${input.patient_preferences || "None specified"}

REQUESTED SERVICE:
- Service: ${serviceDetails.name}
- Duration: ${serviceDetails.duration_minutes} minutes
- Category: ${serviceDetails.category}
- Preparation Required: ${serviceDetails.requires_preparation ? "Yes" : "No"}
${
  serviceDetails.preparation_instructions
    ? `- Preparation Instructions: ${serviceDetails.preparation_instructions}`
    : ""
}

PATIENT MEDICAL HISTORY:
${
  patientHistory.length > 0
    ? patientHistory
        .map(
          (h) =>
            `- ${h.service?.name || "Unknown Service"} with Dr. ${
              h.doctor?.user?.name || "Unknown"
            } (${h.appointment.scheduled_date}): ${h.appointment.status}`
        )
        .join("\n")
    : "No previous appointments recorded"
}

DOCTOR PREFERENCES:
${
  input.doctor_preferences?.preferred_doctors?.length
    ? `- Preferred Doctors: ${input.doctor_preferences.preferred_doctors.join(
        ", "
      )}`
    : "- No specific doctor preferences"
}
${
  input.doctor_preferences?.specialty_requirements?.length
    ? `- Required Specialties: ${input.doctor_preferences.specialty_requirements.join(
        ", "
      )}`
    : ""
}

SCHEDULING CONSTRAINTS:
- Max Wait Time: ${input.scheduling_constraints?.max_wait_days || 30} days
${
  input.scheduling_constraints?.preferred_dates?.length
    ? `- Preferred Dates: ${input.scheduling_constraints.preferred_dates.join(
        ", "
      )}`
    : "- No date preferences"
}
${
  input.scheduling_constraints?.preferred_times?.length
    ? `- Preferred Times: ${input.scheduling_constraints.preferred_times.join(
        ", "
      )}`
    : "- No time preferences"
}

AVAILABLE DOCTORS:
${availableDoctors
  .map(
    (d) =>
      `- Dr. ${d.user?.name || "Unknown"} (${d.doctor.specialty}) - Rating: ${
        d.doctor.rating
      }/5, Experience: ${d.doctor.experience_years} years`
  )
  .join("\n")}

AVAILABLE TIME SLOTS (Next 20 optimal slots):
${availableSlots
  .map(
    (slot) =>
      `- ${slot.date} at ${slot.time} (${new Date(
        slot.datetime
      ).toLocaleDateString("en-US", { weekday: "long" })})`
  )
  .join("\n")}

Please provide comprehensive scheduling optimization including:

## OPTIMAL SCHEDULING RECOMMENDATIONS

### PRIMARY RECOMMENDATION
- **Recommended Doctor**: [Doctor name and rationale]
- **Optimal Date & Time**: [Specific date and time with reasoning]
- **Scheduling Efficiency**: [Why this slot optimizes patient care and clinic operations]
- **Medical Urgency Alignment**: [How recommendation addresses urgency level]

### ALTERNATIVE OPTIONS (2-3 alternatives)
- **Option 2**: [Alternative doctor/time with rationale]
- **Option 3**: [Another alternative with different benefits]

## CONTINUITY OF CARE ANALYSIS
- **Previous Doctor Relationships**: [Analysis of patient-doctor history]
- **Specialty Matching**: [How doctor expertise aligns with patient needs]
- **Treatment Timeline Optimization**: [Optimal timing for medical care]

## CONFLICT RESOLUTION STRATEGIES
- **Scheduling Conflicts Identified**: [Any conflicts found and solutions]
- **Wait Time Optimization**: [Strategies to minimize patient wait time]
- **Operational Efficiency**: [How to optimize clinic resource utilization]

## PATIENT EXPERIENCE OPTIMIZATION
- **Preference Alignment**: [How recommendations match patient preferences]
- **Convenience Factors**: [Travel time, parking, accessibility considerations]
- **Preparation Time**: [Adequate time for any required preparation]

## MEDICAL PRIORITY CONSIDERATIONS
- **Urgency Assessment**: [Medical urgency evaluation and scheduling priority]
- **Risk Mitigation**: [Ensuring appropriate medical care timing]
- **Follow-up Planning**: [Integration with ongoing treatment plans]

## IMPLEMENTATION RECOMMENDATIONS
- **Immediate Actions**: [Steps to secure optimal appointment]
- **Backup Plans**: [Alternative strategies if primary option unavailable]
- **Patient Communication**: [How to communicate options to patient]

**IMPORTANT MEDICAL DISCLAIMERS:**
- Scheduling recommendations based on available data and operational efficiency
- Medical urgency should always be verified by healthcare professionals
- Emergency cases should bypass normal scheduling and seek immediate care
- Patient safety and medical best practices take priority over operational efficiency

Always prioritize patient safety and medical best practices in scheduling decisions.
`;

  return prompt.trim();
}

// Server Actions
export async function generateSchedulingOptimization(
  input: GenerateSchedulingInput
) {
  try {
    // Validate user authentication
    const supabase = await createServerSupabaseClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new Error("Unauthorized");
    }

    // Validate input
    const validatedInput = generateSchedulingSchema.parse(input);

    // Get service details
    const [serviceDetails] = await db
      .select()
      .from(services)
      .where(eq(services.id, validatedInput.service_id))
      .limit(1);

    if (!serviceDetails) {
      throw new Error("Service not found");
    }

    // Get patient history and context
    const patientHistory = await getPatientHistory(validatedInput.user_id);
    const availableDoctors = await getAvailableDoctors(
      validatedInput.service_id,
      validatedInput.doctor_preferences?.specialty_requirements
    );

    // Generate available time slots for each doctor
    const allAvailableSlots: any[] = [];
    const startDate = new Date().toISOString().split("T")[0];
    const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0];

    for (const doctorData of availableDoctors.slice(0, 5)) {
      // Limit to top 5 doctors
      const doctorAvailability = await getDoctorAvailability(
        doctorData.doctor.id,
        startDate,
        endDate
      );
      const existingAppointments = await getExistingAppointments(
        doctorData.doctor.id,
        startDate,
        endDate
      );

      const doctorSlots = generateTimeSlots(
        doctorAvailability,
        existingAppointments,
        serviceDetails.duration_minutes
      );

      doctorSlots.forEach((slot) => {
        allAvailableSlots.push({
          ...slot,
          doctor_id: doctorData.doctor.id,
          doctor_name: doctorData.user?.name,
          doctor_specialty: doctorData.doctor.specialty,
          doctor_rating: doctorData.doctor.rating,
        });
      });
    }

    // Sort slots by urgency priority and availability
    const prioritizedSlots = allAvailableSlots
      .sort((a, b) => {
        const aDate = new Date(a.datetime);
        const bDate = new Date(b.datetime);

        // For emergency/urgent, prioritize sooner dates
        if (
          validatedInput.urgency_level === "emergency" ||
          validatedInput.urgency_level === "urgent"
        ) {
          return aDate.getTime() - bDate.getTime();
        }

        // For routine, balance availability and doctor rating
        const aScore = parseFloat(a.doctor_rating || "0");
        const bScore = parseFloat(b.doctor_rating || "0");
        return bScore - aScore;
      })
      .slice(0, 10);

    // Build AI prompt
    const prompt = buildSchedulingPrompt(
      patientHistory,
      availableDoctors,
      prioritizedSlots,
      serviceDetails,
      validatedInput
    );

    // Generate streaming AI response
    const result = await streamText({
      model: openai("gpt-4o"),
      prompt,
      maxTokens: 2500,
      temperature: 0.5,
    });

    // Store optimization in database
    const [newOptimization] = await db
      .insert(ai_scheduling_optimization)
      .values({
        user_id: validatedInput.user_id,
        request_data: {
          service_id: validatedInput.service_id,
          urgency_level: validatedInput.urgency_level,
          medical_notes: validatedInput.medical_notes,
          patient_preferences: validatedInput.patient_preferences,
        },
        doctor_preferences: validatedInput.doctor_preferences || {},
        scheduling_constraints: validatedInput.scheduling_constraints || {},
        urgency_level: validatedInput.urgency_level,
        optimization_results: {}, // Will be updated after streaming
        recommended_slots: prioritizedSlots,
        alternative_options: prioritizedSlots.slice(3, 6),
        efficiency_score: "8.5", // Default efficiency score
        medical_priority:
          validatedInput.urgency_level === "emergency"
            ? "high"
            : validatedInput.urgency_level === "urgent"
            ? "medium"
            : "low",
        ai_model: "gpt-4o",
        ai_version: "2024-11-20",
        confidence_score: "0.85",
      })
      .returning();

    // Log optimization generation
    if (newOptimization) {
      await db.insert(ai_scheduling_history).values({
        optimization_id: newOptimization.id,
        action: "generated",
        performed_by: user.id,
        notes: "AI scheduling optimization generated",
        metadata: {
          prompt_length: prompt.length,
          available_doctors_count: availableDoctors.length,
          available_slots_count: prioritizedSlots.length,
          urgency_level: validatedInput.urgency_level,
        },
      });
    }

    // Revalidate relevant pages
    revalidatePath("/dashboard/appointments");
    revalidatePath("/dashboard/scheduling");
    revalidatePath("/dashboard/ai");

    return {
      success: true,
      optimization: newOptimization,
      stream: result.textStream,
    };
  } catch (error) {
    console.error("Error generating scheduling optimization:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to generate optimization",
    };
  }
}

export async function updateOptimizationWithResult(
  optimizationId: string,
  fullText: string,
  parsedOptimization: any
) {
  try {
    const supabase = await createServerSupabaseClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new Error("Unauthorized");
    }

    // Update optimization with full AI response
    const [updatedOptimization] = await db
      .update(ai_scheduling_optimization)
      .set({
        optimization_results: {
          content: fullText,
          generated_at: new Date().toISOString(),
          ...parsedOptimization,
        },
        updated_at: new Date(),
      })
      .where(eq(ai_scheduling_optimization.id, optimizationId))
      .returning();

    return { success: true, optimization: updatedOptimization };
  } catch (error) {
    console.error("Error updating optimization:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update optimization",
    };
  }
}

export async function reviewSchedulingOptimization(
  input: ReviewSchedulingInput
) {
  try {
    // Validate user authentication
    const supabase = await createServerSupabaseClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new Error("Unauthorized");
    }

    // Validate input
    const validatedInput = reviewSchedulingSchema.parse(input);

    // If implementing the scheduling, create the actual appointment
    let implementedAppointmentId = null;
    if (
      validatedInput.status === "implemented" &&
      validatedInput.selected_slot
    ) {
      const slot = validatedInput.selected_slot;

      // Get optimization details for appointment creation
      const [optimization] = await db
        .select()
        .from(ai_scheduling_optimization)
        .where(
          eq(ai_scheduling_optimization.id, validatedInput.optimization_id)
        )
        .limit(1);

      if (optimization) {
        const [newAppointment] = await db
          .insert(appointments)
          .values({
            patient_id: optimization.user_id,
            doctor_id: slot.doctor_id,
            service_id: (optimization.request_data as any).service_id,
            scheduled_date: slot.date,
            scheduled_time: slot.time,
            status: "scheduled",
            notes: `AI-optimized appointment - ${optimization.urgency_level} priority`,
          })
          .returning();

        implementedAppointmentId = newAppointment?.id;
      }
    }

    // Update optimization status
    const [updatedOptimization] = await db
      .update(ai_scheduling_optimization)
      .set({
        status: validatedInput.status,
        feedback: validatedInput.feedback,
        accuracy_rating: validatedInput.accuracy_rating,
        implemented_slot: implementedAppointmentId,
        updated_at: new Date(),
      })
      .where(eq(ai_scheduling_optimization.id, validatedInput.optimization_id))
      .returning();

    // Log review action
    await db.insert(ai_scheduling_history).values({
      optimization_id: validatedInput.optimization_id,
      action: validatedInput.status,
      performed_by: user.id,
      notes: validatedInput.feedback || `Optimization ${validatedInput.status}`,
      metadata: {
        accuracy_rating: validatedInput.accuracy_rating,
        implemented_appointment_id: implementedAppointmentId,
      },
    });

    // Revalidate relevant pages
    revalidatePath("/dashboard/ai");
    revalidatePath("/dashboard/appointments");
    revalidatePath("/dashboard/scheduling");

    return { success: true, optimization: updatedOptimization };
  } catch (error) {
    console.error("Error reviewing optimization:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to review optimization",
    };
  }
}

export async function getUserOptimizations(userId: string) {
  try {
    // Validate user authentication
    const supabase = await createServerSupabaseClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new Error("Unauthorized");
    }

    // Get user optimizations
    const optimizations = await db
      .select()
      .from(ai_scheduling_optimization)
      .where(eq(ai_scheduling_optimization.user_id, userId))
      .orderBy(desc(ai_scheduling_optimization.created_at));

    return { success: true, optimizations };
  } catch (error) {
    console.error("Error fetching optimizations:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch optimizations",
    };
  }
}

export async function getOptimizationHistory(optimizationId: string) {
  try {
    // Validate user authentication
    const supabase = await createServerSupabaseClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new Error("Unauthorized");
    }

    // Get optimization history
    const history = await db
      .select()
      .from(ai_scheduling_history)
      .where(eq(ai_scheduling_history.optimization_id, optimizationId))
      .orderBy(desc(ai_scheduling_history.created_at));

    return { success: true, history };
  } catch (error) {
    console.error("Error fetching optimization history:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch history",
    };
  }
}

export async function getSchedulingInsights(userId?: string) {
  try {
    // Validate user authentication
    const supabase = await createServerSupabaseClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new Error("Unauthorized");
    }

    // Get scheduling insights
    const insights = {
      totalOptimizations: 0,
      implementedOptimizations: 0,
      averageEfficiencyScore: 0,
      urgencyDistribution: {},
    };

    // Get total optimizations
    const totalOptimizations = await db
      .select({ count: sql<number>`count(*)` })
      .from(ai_scheduling_optimization)
      .where(
        userId ? eq(ai_scheduling_optimization.user_id, userId) : undefined
      );

    insights.totalOptimizations = totalOptimizations[0]?.count || 0;

    // Get implemented optimizations
    const implementedOptimizations = await db
      .select({ count: sql<number>`count(*)` })
      .from(ai_scheduling_optimization)
      .where(
        and(
          eq(ai_scheduling_optimization.status, "implemented"),
          userId ? eq(ai_scheduling_optimization.user_id, userId) : undefined
        )
      );

    insights.implementedOptimizations = implementedOptimizations[0]?.count || 0;

    return { success: true, insights };
  } catch (error) {
    console.error("Error fetching scheduling insights:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch insights",
    };
  }
}
