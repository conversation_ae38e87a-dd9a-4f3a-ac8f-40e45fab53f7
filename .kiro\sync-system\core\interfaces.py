"""
Core interfaces for the VIBECODE-Kiro sync system components.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Callable
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sync_models import (
    SyncAction, Conflict, Resolution, BackupInfo, 
    FileChange, SyncResult, SyncStatus
)


class IFileSystemMonitor(ABC):
    """Interface for file system monitoring."""
    
    @abstractmethod
    def start_monitoring(self) -> None:
        """Start monitoring the file system for changes."""
        pass
    
    @abstractmethod
    def stop_monitoring(self) -> None:
        """Stop monitoring the file system."""
        pass
    
    @abstractmethod
    def is_monitoring(self) -> bool:
        """Check if monitoring is active."""
        pass
    
    @abstractmethod
    def get_monitored_paths(self) -> List[str]:
        """Get list of currently monitored paths."""
        pass
    
    @abstractmethod
    def set_change_callback(self, callback: Callable[[List[FileChange]], None]) -> None:
        """Set callback function for file changes."""
        pass


class IChangeDetectionEngine(ABC):
    """Interface for change detection and analysis."""
    
    @abstractmethod
    def analyze_changes(self, changes: List[FileChange]) -> List[SyncAction]:
        """Analyze file changes and determine required sync actions."""
        pass
    
    @abstractmethod
    def detect_conflicts(self, actions: List[SyncAction]) -> List[Conflict]:
        """Detect potential conflicts in sync actions."""
        pass
    
    @abstractmethod
    def prioritize_actions(self, actions: List[SyncAction]) -> List[SyncAction]:
        """Prioritize sync actions based on importance and dependencies."""
        pass


class IRuleAdaptationEngine(ABC):
    """Interface for rule adaptation from VIBECODE to Kiro format."""
    
    @abstractmethod
    def adapt_mdc_to_md(self, content: str) -> str:
        """Convert .mdc file content to .md format."""
        pass
    
    @abstractmethod
    def adapt_paths(self, content: str) -> str:
        """Adapt VIBECODE paths to Kiro paths in content."""
        pass
    
    @abstractmethod
    def adapt_mcp_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt MCP configuration from VIBECODE to Kiro format."""
        pass
    
    @abstractmethod
    def preserve_kiro_optimizations(self, original: str, adapted: str) -> str:
        """Preserve Kiro-specific optimizations during adaptation."""
        pass


class IConflictResolver(ABC):
    """Interface for conflict resolution."""
    
    @abstractmethod
    def resolve_conflicts(self, conflicts: List[Conflict]) -> List[Resolution]:
        """Resolve a list of conflicts."""
        pass
    
    @abstractmethod
    def create_merge_proposal(self, conflict: Conflict) -> Optional[Dict[str, Any]]:
        """Create a merge proposal for manual review."""
        pass
    
    @abstractmethod
    def apply_resolution(self, resolution: Resolution) -> bool:
        """Apply a conflict resolution."""
        pass


class IBackupManager(ABC):
    """Interface for backup management."""
    
    @abstractmethod
    def create_backup(self, files: List[str]) -> Optional[BackupInfo]:
        """Create backup of specified files."""
        pass
    
    @abstractmethod
    def restore_backup(self, backup_id: str) -> bool:
        """Restore files from backup."""
        pass
    
    @abstractmethod
    def cleanup_old_backups(self, days: int = 30) -> None:
        """Clean up backups older than specified days."""
        pass
    
    @abstractmethod
    def list_backups(self) -> List[BackupInfo]:
        """List available backups."""
        pass
    
    @abstractmethod
    def verify_backup_integrity(self, backup_id: str) -> bool:
        """Verify backup integrity."""
        pass


class ISyncEngine(ABC):
    """Interface for the main sync engine."""
    
    @abstractmethod
    def start_sync(self, force: bool = False) -> SyncResult:
        """Start synchronization process."""
        pass
    
    @abstractmethod
    def stop_sync(self) -> None:
        """Stop ongoing synchronization."""
        pass
    
    @abstractmethod
    def get_sync_status(self) -> SyncStatus:
        """Get current sync status."""
        pass
    
    @abstractmethod
    def preview_changes(self) -> List[SyncAction]:
        """Preview changes without applying them."""
        pass
    
    @abstractmethod
    def sync_specific_files(self, files: List[str]) -> SyncResult:
        """Sync only specific files."""
        pass


class IStatusTracker(ABC):
    """Interface for status tracking and reporting."""
    
    @abstractmethod
    def update_status(self, status: SyncStatus) -> None:
        """Update sync status."""
        pass
    
    @abstractmethod
    def log_operation(self, operation: str, details: Dict[str, Any]) -> None:
        """Log sync operation."""
        pass
    
    @abstractmethod
    def get_operation_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get operation history."""
        pass
    
    @abstractmethod
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive status report."""
        pass


class ISyncScheduler(ABC):
    """Interface for sync scheduling."""
    
    @abstractmethod
    def start_scheduler(self) -> None:
        """Start the sync scheduler."""
        pass
    
    @abstractmethod
    def stop_scheduler(self) -> None:
        """Stop the sync scheduler."""
        pass
    
    @abstractmethod
    def schedule_sync(self, interval_minutes: int) -> None:
        """Schedule periodic sync."""
        pass
    
    @abstractmethod
    def trigger_immediate_sync(self) -> None:
        """Trigger immediate sync."""
        pass
    
    @abstractmethod
    def is_scheduler_running(self) -> bool:
        """Check if scheduler is running."""
        pass