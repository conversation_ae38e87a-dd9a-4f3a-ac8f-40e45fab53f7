/**
 * 🌍 Environment Configuration for Stagewise Integration
 * VIBECODE V1.0 - Centralized Environment Detection
 * 
 * Provides intelligent environment detection for development-only
 * Stagewise toolbar activation across all Next.js projects.
 */

export interface EnvironmentConfig {
  isDevelopment: boolean;
  isProduction: boolean;
  isTest: boolean;
  nodeEnv: string;
  enableStagewise: boolean;
}

/**
 * Detects current environment with multiple fallback strategies
 * Ensures Stagewise only runs in development mode
 */
export function detectEnvironment(): EnvironmentConfig {
  // Primary detection: NODE_ENV
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  // Secondary detection: Next.js specific
  const isDev = process.env.NODE_ENV === 'development';
  const isProd = process.env.NODE_ENV === 'production';
  const isTest = process.env.NODE_ENV === 'test';
  
  // Tertiary detection: Development indicators
  const hasDevIndicators = Boolean(
    process.env.NEXT_DEV ||
    process.env.DEV ||
    process.env.DEVELOPMENT ||
    (typeof window !== 'undefined' && window.location?.hostname === 'localhost')
  );
  
  // Final determination
  const isDevelopment = isDev || (!isProd && !isTest && hasDevIndicators);
  const isProduction = isProd && !isDevelopment;
  
  return {
    isDevelopment,
    isProduction,
    isTest,
    nodeEnv,
    enableStagewise: isDevelopment && !isTest
  };
}

/**
 * Environment validation for debugging
 */
export function validateEnvironment(): void {
  const env = detectEnvironment();
  
  if (typeof window !== 'undefined' && env.isDevelopment) {
    console.log('🎯 Stagewise Environment:', {
      environment: env.nodeEnv,
      stagewise_enabled: env.enableStagewise,
      development_mode: env.isDevelopment
    });
  }
}

/**
 * Safe environment check for SSR compatibility
 */
export function isStagewiseEnabled(): boolean {
  try {
    const env = detectEnvironment();
    return env.enableStagewise;
  } catch (error) {
    // Fallback to false in case of any environment detection issues
    console.warn('⚠️ Stagewise environment detection failed, disabling toolbar');
    return false;
  }
}
