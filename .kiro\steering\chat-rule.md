# 📋 KIRO SMART WORKFLOW - OPTIMIZED

**Authority**: E:\VIBECODE\.kiro\steering\chat-rule.md  
**Purpose**: Efficient task routing with minimal API calls

## 🎯 CORE PRINCIPLES

- **Smart Routing**: Auto-detect task type and use appropriate workflow
- **API Efficiency**: Minimize calls while maintaining quality ≥8/10
- **Conditional Execution**: Only use complex tools when necessary
- **Batch Operations**: Consolidate similar operations automatically

## 🚀 SMART TASK ROUTER

### **Auto-Detection & Routing**

```json
{
  "task_classification": {
    "SIMPLE_TASKS": {
      "triggers": ["read", "show", "list", "edit [file]", "create [simple]"],
      "workflow": "direct_execution",
      "tools": ["native_kiro", "desktop-commander"],
      "validations": "minimal",
      "api_calls": "1-3"
    },
    "COMPLEX_TASKS": {
      "triggers": ["design", "architecture", "refactor", "multiple files"],
      "workflow": "structured_approach",
      "tools": ["desktop-commander", "sequential-thinking"],
      "validations": "quality_gates",
      "api_calls": "4-8"
    },
    "RESEARCH_TASKS": {
      "triggers": ["how to", "documentation", "tutorial", "como fazer"],
      "workflow": "research_protocol",
      "tools": ["context7", "tavily", "exa"],
      "validations": "synthesis_quality",
      "api_calls": "6-10"
    }
  }
}
```

### **Conditional Workflows**

**SIMPLE WORKFLOW (90% of tasks):**

1. Execute directly with native tools
2. Verify only if critical operation
3. No mandatory quality gates

**COMPLEX WORKFLOW (8% of tasks):**

1. Use sequential-thinking if logic complexity detected
2. Apply quality gates for critical outputs
3. Batch operations when possible

**RESEARCH WORKFLOW (2% of tasks):**

1. Context7 → Tavily → Exa (only when research keywords detected)
2. Synthesize findings ≥8/10 quality
3. Document sources

## 🔧 TOOL SELECTION MATRIX

### **File Operations**

```json
{
  "file_size_routing": {
    "≤200_lines": {
      "tool": "desktop-commander",
      "mcp": "@mcp_desktop-commander",
      "functions": ["read_file", "write_file", "edit_block"]
    },
    ">200_lines": {
      "tool": "kiro-editor",
      "functions": ["edit_file", "search_replace"]
    }
  },
  "always_verify": "Read file after write to verify changes",
  "backup_location": "E:/CODE-BACKUP"
}
```

## ⚡ EFFICIENCY RULES

### **API Call Optimization**

```json
{
  "smart_execution": {
    "default_behavior": "Use native tools first",
    "escalation_only": "When native tools insufficient",
    "batch_operations": "Consolidate similar tasks automatically",
    "avoid_redundancy": "Skip unnecessary validations"
  },
  "conditional_protocols": {
    "research_protocol": "ONLY when research keywords detected",
    "quality_gates": "ONLY for critical outputs",
    "sequential_thinking": "ONLY for complex logic",
    "file_verification": "ONLY after writes, not reads"
  }
}
```

### **Essential Quality Gates**

- **Quality**: ≥8/10 for critical outputs only
- **Completeness**: 100% requirements met
- **Efficiency**: Minimize API calls while maintaining quality

## 📋 QUICK REFERENCE

### **Smart Routing Decision Tree**

```
USER REQUEST → AUTO-DETECT → ROUTE TO WORKFLOW

SIMPLE (90%): read, show, edit → Native Tools → 1-3 API calls
COMPLEX (8%): design, refactor → + Sequential Thinking → 4-8 API calls
RESEARCH (2%): how to, docs → + Research Protocol → 6-10 API calls
```

### **Tool Priority Matrix**

| Task Type      | Primary Tool        | Fallback          | API Calls |
| -------------- | ------------------- | ----------------- | --------- |
| File Read/Edit | Native Kiro         | Desktop Commander | 1-2       |
| Complex Logic  | Sequential Thinking | Native + Desktop  | 4-6       |
| Research       | Context7→Tavily→Exa | Native Search     | 6-8       |

### **Efficiency Targets**

- **API Reduction**: ≥70% fewer calls vs old workflow
- **Quality Maintained**: ≥8/10 for critical outputs
- **Response Time**: <10s simple, <30s complex
- **Default Behavior**: Use simplest tool that works

---

**OPTIMIZED WORKFLOW ACTIVE** - Smart routing with minimal API usage while maintaining quality standards.
