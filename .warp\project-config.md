# 🚀 WARP CLI - PROJECT CONFIGURATION

_Configuração de Projeto - GRUPO US_

**Version**: 1.0.0 | **Empresa**: GRUPO US | **Foco**: Desenvolvimento SaaS

## 📋 VISÃO GERAL

- **Repositório**: https://github.com/GrupoUS/
- **Idioma**: Có<PERSON> em inglês, conteúdo em português-BR
- **Ambiente**: E:/VIBECODE
- **Platform**: Windows + PowerShell + Warp CLI

## 🛠️ STACK TECNOLÓGICA OBRIGATÓRIA

### **Frontend**
- Next.js 14 (App Router)
- TypeScript (strict mode)
- Tailwind CSS
- shadcn/ui
- Zustand (estado global)
- React Query (data fetching)
- React Hook Form + Zod

### **Backend**
- Supabase (PostgreSQL + Auth + Storage)
- Prisma ORM
- tRPC ou API Routes
- Edge Functions quando necessário

### **Ferramentas**
- Warp CLI (ambiente principal)
- Cursor IDE (desenvolvimento)
- Vercel (deploy)
- GitHub Actions (CI/CD)

## 📁 ESTRUTURA DE PASTAS

```
src/
├── app/              # Next.js App Router
├── components/       # Componentes reutilizáveis
│   ├── ui/          # shadcn/ui
│   └── features/    # Componentes de features
├── lib/             # Utilitários e configurações
├── hooks/           # Custom hooks
├── stores/          # Zustand stores
├── types/           # TypeScript types
└── styles/          # Estilos globais
```

## 🎯 PADRÕES DE CÓDIGO

### **Nomenclatura**
- **Componentes**: PascalCase
- **Arquivos de componente**: PascalCase.tsx
- **Hooks**: camelCase começando com 'use'
- **Utilitários**: camelCase
- **Constantes**: UPPER_SNAKE_CASE
- **Variáveis de ambiente**: NEXT_PUBLIC_ para cliente

### **Componentes**

```typescript
// ✅ CORRECT: Interface + Named Export
interface ButtonProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary'
  onClick?: () => void
}

export function Button({ children, variant = 'primary', onClick }: ButtonProps) {
  return (
    <button
      className={cn(
        'px-4 py-2 rounded-md font-medium',
        variant === 'primary' && 'bg-blue-600 text-white',
        variant === 'secondary' && 'bg-gray-200 text-gray-900'
      )}
      onClick={onClick}
    >
      {children}
    </button>
  )
}
```

## 🔧 PREFERÊNCIAS DE DESENVOLVIMENTO

### **Package Manager & Tools**
- **npm/yarn**: npm preferido
- **Commits**: Convencionais (feat:, fix:, docs:)
- **Branches**: feature/nome-da-feature
- **PRs**: Descrição detalhada obrigatória
- **Testes**: Para lógica crítica (≥80% coverage)
- **Documentação**: JSDoc inline + comentários em português

### **Development Workflow**

```bash
# Setup inicial
npm install
npm run dev

# Commits convencionais
git add .
git commit -m "feat: add user authentication"
git push origin feature/user-auth

# Build e deploy
npm run build
npm run start
```

## 🔒 SEGURANÇA

### **Environment Variables**
- ✅ Nunca commitar .env
- ✅ Usar variáveis do Vercel/Supabase
- ✅ Prefixar client vars com NEXT_PUBLIC_

### **Security Measures**
- ✅ RLS habilitado no Supabase
- ✅ Validação com Zod em todas entradas
- ✅ Sanitização de dados do usuário
- ✅ Authentication middleware
- ✅ Security headers configurados

```typescript
// ✅ CORRECT: Input validation
const userSchema = z.object({
  email: z.string().email(),
  name: z.string().min(2).max(50)
})

function validateUserInput(data: unknown) {
  const result = userSchema.safeParse(data)
  if (!result.success) {
    throw new ValidationError('Invalid user data', result.error)
  }
  return result.data
}
```

## ⚡ PERFORMANCE

### **Optimization Standards**
- ✅ Lazy loading de componentes pesados
- ✅ Image optimization com next/image
- ✅ Minimizar re-renders (React.memo quando apropriado)
- ✅ Prefetch de dados críticos
- ✅ Cache adequado com React Query

### **Performance Targets**
- **LCP**: <2.5s
- **FID**: <100ms
- **CLS**: <0.1
- **TTI**: <3.8s
- **Bundle**: <200KB gzipped

```typescript
// ✅ CORRECT: Performance optimizations
import dynamic from 'next/dynamic'
import { memo, useMemo } from 'react'

const HeavyChart = dynamic(() => import('./heavy-chart'), {
  loading: () => <ChartSkeleton />
})

const OptimizedList = memo(function OptimizedList({ items }) {
  const sortedItems = useMemo(
    () => items.sort((a, b) => a.name.localeCompare(b.name)),
    [items]
  )
  
  return <ul>{/* render sorted items */}</ul>
})
```

## 📱 CONSIDERAÇÕES ESPECIAIS

### **Design Principles**
- ✅ **Mobile-first** sempre
- ✅ **Accessibility** (ARIA labels)
- ✅ **SEO** otimizado
- ✅ **Internacionalização** preparada (pt-BR inicial)
- ✅ **Dark mode** support
- ✅ **Responsive** em todos os breakpoints

### **Monitoring & Analytics**
- ✅ Vercel Analytics configurado
- ✅ Error tracking (Sentry)
- ✅ Performance monitoring
- ✅ User behavior analytics

## 🔄 INTEGRATION WORKFLOW

### **Com Warp CLI**

```bash
# Análise de complexidade automática
# Seleção de ferramentas baseada em contexto
# Workflow de 7 etapas aplicado
# Quality gates ≥8/10 enforced

# Comandos Warp específicos
warp analyze complexity    # Avaliar task complexity
warp select tools         # Auto-select based on context
warp execute workflow     # Run 7-step workflow
warp validate quality     # Ensure ≥8/10 quality
```

### **File Operations**
- **≤200 linhas**: Ferramentas rápidas (fast tools)
- **>200 linhas**: Editores robustos (robust editors)
- **Sempre**: Verificar após write operations

## 📊 QUALITY ASSURANCE

### **Mandatory Checks**
- [ ] TypeScript strict mode passes
- [ ] All tests passing (≥80% coverage)
- [ ] ESLint/Prettier formatting
- [ ] Accessibility audit (lighthouse)
- [ ] Performance budget met
- [ ] Security headers verified
- [ ] Error handling comprehensive

### **Review Process**
1. **Self review**: Code quality ≥8/10
2. **Automated tests**: All passing
3. **Manual testing**: Feature validation
4. **Performance check**: Metrics within targets
5. **Security scan**: No vulnerabilities

## 🎯 REGRAS RELACIONADAS

- **Master Rules**: `.warp/master-rules.md`
- **Coding Standards**: `.warp/coding-standards.md`
- **Quality Gates**: Quality threshold ≥8/10
- **Memory System**: Knowledge base integration
- **Task Management**: Automatic detection ≥3 complexity

---

**Princípios Fundamentais**:
- "Aprimore, Não Prolifere" (≥85% reuse)
- Context First, Quality Always
- Mobile-first, Accessible Design
- Performance & Security by Default

**Status**: ✅ ATIVO - Configurado para Warp CLI
**Ambiente**: GRUPO US Development Environment
