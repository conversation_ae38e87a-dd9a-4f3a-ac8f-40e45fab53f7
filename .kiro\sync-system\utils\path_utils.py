"""
Path utility functions for the VIBECODE-Kiro sync system.
"""

import os
import re
from pathlib import Path
from typing import List, Optional, Tuple
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger

logger = get_logger(__name__)


class PathUtils:
    """Utility class for path operations and transformations."""
    
    # Path mapping patterns for VIBECODE to Kiro conversion
    PATH_MAPPINGS = {
        r'E:\\VIBECODE\\\.cursor\\rules\\': '.kiro/steering/',
        r'E:\\VIBECODE\\\.cursor\\config\\': '.kiro/settings/',
        r'E:\\VIBECODE\\\.cursor\\': '.kiro/',
        r'\.cursor\\rules\\': '.kiro/steering/',
        r'\.cursor\\config\\': '.kiro/settings/',
        r'\.cursor\\': '.kiro/',
        r'memory-bank\\': '.kiro/memory/',
        r'@saas-projects\\': '.kiro/projects/'
    }
    
    @staticmethod
    def normalize_path(path: str) -> str:
        """Normalize path separators for current OS."""
        return os.path.normpath(path)
    
    @staticmethod
    def is_safe_path(path: str, base_path: str) -> bool:
        """Check if path is safe (no directory traversal)."""
        try:
            # Resolve both paths
            resolved_path = os.path.realpath(path)
            resolved_base = os.path.realpath(base_path)
            
            # Check if the resolved path is within the base path
            return resolved_path.startswith(resolved_base)
            
        except Exception as e:
            logger.error(f"Error checking path safety: {e}")
            return False
    
    @staticmethod
    def convert_vibecode_to_kiro_path(vibecode_path: str) -> str:
        """Convert VIBECODE path to equivalent Kiro path."""
        kiro_path = vibecode_path
        
        # Apply path mappings
        for pattern, replacement in PathUtils.PATH_MAPPINGS.items():
            kiro_path = re.sub(pattern, replacement, kiro_path, flags=re.IGNORECASE)
        
        # Convert file extensions
        if kiro_path.endswith('.mdc'):
            kiro_path = kiro_path[:-4] + '.md'
        
        # Normalize path separators
        kiro_path = PathUtils.normalize_path(kiro_path)
        
        logger.debug(f"Path converted: {vibecode_path} -> {kiro_path}")
        return kiro_path
    
    @staticmethod
    def get_relative_path(file_path: str, base_path: str) -> str:
        """Get relative path from base path."""
        try:
            return os.path.relpath(file_path, base_path)
        except Exception as e:
            logger.error(f"Error getting relative path: {e}")
            return file_path
    
    @staticmethod
    def split_path_components(path: str) -> Tuple[str, str, str]:
        """Split path into directory, filename, and extension."""
        directory = os.path.dirname(path)
        filename_with_ext = os.path.basename(path)
        filename, extension = os.path.splitext(filename_with_ext)
        return directory, filename, extension
    
    @staticmethod
    def ensure_extension(path: str, extension: str) -> str:
        """Ensure path has the specified extension."""
        if not extension.startswith('.'):
            extension = '.' + extension
        
        current_ext = os.path.splitext(path)[1]
        if current_ext.lower() != extension.lower():
            return path + extension
        return path
    
    @staticmethod
    def find_common_path(paths: List[str]) -> str:
        """Find common base path for a list of paths."""
        if not paths:
            return ""
        
        if len(paths) == 1:
            return os.path.dirname(paths[0])
        
        try:
            return os.path.commonpath(paths)
        except Exception as e:
            logger.error(f"Error finding common path: {e}")
            return ""
    
    @staticmethod
    def is_excluded_path(path: str, excluded_patterns: List[str]) -> bool:
        """Check if path matches any excluded patterns."""
        normalized_path = PathUtils.normalize_path(path).lower()
        
        for pattern in excluded_patterns:
            pattern_lower = pattern.lower()
            
            # Check if pattern is in the path
            if pattern_lower in normalized_path:
                return True
            
            # Check if path ends with pattern (for file extensions)
            if normalized_path.endswith(pattern_lower):
                return True
            
            # Check if any path component matches pattern
            path_parts = normalized_path.split(os.sep)
            if pattern_lower in path_parts:
                return True
        
        return False
    
    @staticmethod
    def get_backup_path(original_path: str, backup_base: str) -> str:
        """Generate backup path for original file."""
        from datetime import datetime
        
        # Get relative path from current directory
        try:
            rel_path = os.path.relpath(original_path)
        except Exception:
            rel_path = os.path.basename(original_path)
        
        # Create timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create backup path
        backup_dir = os.path.join(backup_base, "backups", timestamp)
        backup_path = os.path.join(backup_dir, rel_path)
        
        return PathUtils.normalize_path(backup_path)
    
    @staticmethod
    def validate_paths(source_path: str, target_path: str) -> List[str]:
        """Validate source and target paths."""
        errors = []
        
        # Check source path
        if not os.path.exists(source_path):
            errors.append(f"Source path does not exist: {source_path}")
        elif not os.path.isdir(source_path):
            errors.append(f"Source path is not a directory: {source_path}")
        
        # Check target path
        try:
            os.makedirs(target_path, exist_ok=True)
        except Exception as e:
            errors.append(f"Cannot create target path {target_path}: {e}")
        
        # Check if paths are the same
        try:
            if os.path.samefile(source_path, target_path):
                errors.append("Source and target paths cannot be the same")
        except Exception:
            pass  # Paths might not exist yet
        
        return errors
    
    @staticmethod
    def get_file_type_from_path(path: str) -> str:
        """Get file type category from path."""
        extension = os.path.splitext(path)[1].lower()
        
        type_mappings = {
            '.mdc': 'rule',
            '.md': 'documentation',
            '.json': 'configuration',
            '.py': 'script',
            '.js': 'script',
            '.txt': 'text',
            '.log': 'log'
        }
        
        return type_mappings.get(extension, 'unknown')
    
    @staticmethod
    def create_directory_structure(base_path: str, structure: dict) -> bool:
        """Create directory structure from nested dictionary."""
        try:
            for name, content in structure.items():
                current_path = os.path.join(base_path, name)
                
                if isinstance(content, dict):
                    # It's a directory
                    os.makedirs(current_path, exist_ok=True)
                    PathUtils.create_directory_structure(current_path, content)
                else:
                    # It's a file (content is file content or None)
                    os.makedirs(os.path.dirname(current_path), exist_ok=True)
                    if content is not None:
                        with open(current_path, 'w', encoding='utf-8') as f:
                            f.write(content)
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating directory structure: {e}")
            return False