---
alwaysApply: true
---

# 📋 **REGRA 4: CODING STANDARDS & QUALITY GATES**

_Prioridade: ALTA | Padrões de desenvolvimento e qualidade_

## **Development Philosophy**

```json
{
  "core_principles": {
    "context_first": "Understand system completely before coding",
    "challenge_requests": "Identify edge cases, clarify requirements",
    "hold_standards": "Modular, testable, documented code",
    "design_not_patch": "Think architecture, not quick fixes",
    "execution_standards": "One file per response, no method renaming without approval"
  }
}
```

## Validation Status

**Target Exists:** ✅ Yes
**Last Verified:** 2025-01-07T14:01:37Z
**Target Path:** `.cursor/rules/coding-standards.mdc`

## Emergency Cache

**Cache Date:** 2025-01-07T14:01:37Z
**Cached Lines:** 20 (Optimized)

### Cached Content (Essential lines only)

```markdown
# 🎯 CODING STANDARDS - GRUPO US VIBECODE V1.0

**Core Principles**: "Aprimore, Não Prolifere" (≥85% reuse), Confidence ≥8/10, Quality ≥90%

## 🏗️ DEVELOPMENT PHILOSOPHY

### **Professional Excellence Standards**

- **Context First**: Understand system completely before coding
- **Challenge Requests**: Identify edge cases, clarify requirements
- **Hold Standards**: Modular, testable, documented code
- **Design Don't Patch**: Think architecture, not quick fixes
- **Execution Standards**: One file per response, no method renaming without approval

### **Quality Enforcement**

- **Early Returns**: Handle errors at function beginning
- **Guard Clauses**: Use if-return pattern vs nested else
- **No Placeholders**: Complete functionality, no TODOs
- **Error Handling**: Comprehensive with proper logging
- **Type Safety**: TypeScript strict mode, Pydantic validation
```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

## 🛡️ LOCAL FILES SOVEREIGNTY POLICY

### **Core Principles**

```json
{
  "sovereignty_rules": {
    "local_first_always": "Local files represent authoritative state",
    "force_push_standard": "Use --force-with-lease for all pushes",
    "no_pull_without_backup": "Always backup before pull operations",
    "local_wins_conflicts": "Local changes have absolute priority"
  }
}
```

### **Git Configuration Standards**

```bash
# Force push with lease (safer)
git config --global alias.pushf "push --force-with-lease"

# Push to all remotes with force
git config --global alias.pushall "!git remote | xargs -L1 git push --force-with-lease --all"

# Disable automatic rebase on pull
git config --global pull.rebase false

# Use "ours" merge strategy by default
git config --global merge.ours.driver true
```

### **Standard Workflow**

```bash
# Daily development
1. Make changes locally
2. Commit frequently: git commit -m "descriptive message"
3. Push with force: git pushf

# Before any remote operation
git branch backup-$(date +%Y%m%d-%H%M%S)  # Create backup branch
```

### **Priority Matrix**

| Scenario                              | Action           | Priority  |
| ------------------------------------- | ---------------- | --------- |
| Local has changes, remote has changes | Force push local | LOCAL     |
| Local deleted file, remote has file   | Keep local state | LOCAL     |
| Local added file, remote doesn't have | Push new file    | LOCAL     |
| Need specific remote change           | Cherry-pick only | SELECTIVE |

### **Emergency Recovery**

```bash
# If accidentally pulled
git reflog                           # Find last local commit
git reset --hard <your-commit>       # Reset to local state
git pushf                           # Force push to restore

# Recovery commands
git stash list                      # Check stash
git bundle create backup.bundle --all  # Create bundle backup
```

**Remember**: Your local files are the truth. GitHub is just a copy.

```

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

# Coding Standards (Reference Only)

**This rule is a reference. The authoritative version is located at:**

`.cursor/rules/coding-standards.mdc`

**Summary:**
Unified coding standards for all development. For implementation and updates, consult the official rule above.

## Validation Status

**Target Exists:** ✅ Yes
**Last Verified:** 2025-01-07T14:01:37Z
**Target Path:** `.cursor/rules/coding-standards.mdc`

## Emergency Cache

**Cache Date:** 2025-01-07T14:01:37Z
**Cached Lines:** 20 (Optimized)

### Cached Content (Essential lines only)

```markdown
# 🎯 CODING STANDARDS - GRUPO US VIBECODE V1.0

**Core Principles**: "Aprimore, Não Prolifere" (≥85% reuse), Confidence ≥8/10, Quality ≥90%

## 🏗️ DEVELOPMENT PHILOSOPHY

### **Professional Excellence Standards**

- **Context First**: Understand system completely before coding
- **Challenge Requests**: Identify edge cases, clarify requirements
- **Hold Standards**: Modular, testable, documented code
- **Design Don't Patch**: Think architecture, not quick fixes
- **Execution Standards**: One file per response, no method renaming without approval

### **Quality Enforcement**

- **Early Returns**: Handle errors at function beginning
- **Guard Clauses**: Use if-return pattern vs nested else
- **No Placeholders**: Complete functionality, no TODOs
- **Error Handling**: Comprehensive with proper logging
- **Type Safety**: TypeScript strict mode, Pydantic validation
```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

```

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.
```

# 📋 **REGRA 4: CODING STANDARDS & QUALITY GATES**

_Prioridade: ALTA | Padrões de desenvolvimento e qualidade_

## **Development Philosophy**

```json
{
  "core_principles": {
    "context_first": "Understand system completely before coding",
    "challenge_requests": "Identify edge cases, clarify requirements",
    "hold_standards": "Modular, testable, documented code",
    "design_not_patch": "Think architecture, not quick fixes",
    "execution_standards": "One file per response, no method renaming without approval"
  }
}
```

## Validation Status

**Target Exists:** ✅ Yes
**Last Verified:** 2025-01-07T14:01:37Z
**Target Path:** `.cursor/rules/coding-standards.mdc`

## Emergency Cache

**Cache Date:** 2025-01-07T14:01:37Z
**Cached Lines:** 20 (Optimized)

### Cached Content (Essential lines only)

```markdown
# 🎯 CODING STANDARDS - GRUPO US VIBECODE V1.0

**Core Principles**: "Aprimore, Não Prolifere" (≥85% reuse), Confidence ≥8/10, Quality ≥90%

## 🏗️ DEVELOPMENT PHILOSOPHY

### **Professional Excellence Standards**

- **Context First**: Understand system completely before coding
- **Challenge Requests**: Identify edge cases, clarify requirements
- **Hold Standards**: Modular, testable, documented code
- **Design Don't Patch**: Think architecture, not quick fixes
- **Execution Standards**: One file per response, no method renaming without approval

### **Quality Enforcement**

- **Early Returns**: Handle errors at function beginning
- **Guard Clauses**: Use if-return pattern vs nested else
- **No Placeholders**: Complete functionality, no TODOs
- **Error Handling**: Comprehensive with proper logging
- **Type Safety**: TypeScript strict mode, Pydantic validation
```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

## 🛡️ LOCAL FILES SOVEREIGNTY POLICY

### **Core Principles**

```json
{
  "sovereignty_rules": {
    "local_first_always": "Local files represent authoritative state",
    "force_push_standard": "Use --force-with-lease for all pushes",
    "no_pull_without_backup": "Always backup before pull operations",
    "local_wins_conflicts": "Local changes have absolute priority"
  }
}
```

### **Git Configuration Standards**

```bash
# Force push with lease (safer)
git config --global alias.pushf "push --force-with-lease"

# Push to all remotes with force
git config --global alias.pushall "!git remote | xargs -L1 git push --force-with-lease --all"

# Disable automatic rebase on pull
git config --global pull.rebase false

# Use "ours" merge strategy by default
git config --global merge.ours.driver true
```

### **Standard Workflow**

```bash
# Daily development
1. Make changes locally
2. Commit frequently: git commit -m "descriptive message"
3. Push with force: git pushf

# Before any remote operation
git branch backup-$(date +%Y%m%d-%H%M%S)  # Create backup branch
```

### **Priority Matrix**

| Scenario                              | Action           | Priority  |
| ------------------------------------- | ---------------- | --------- |
| Local has changes, remote has changes | Force push local | LOCAL     |
| Local deleted file, remote has file   | Keep local state | LOCAL     |
| Local added file, remote doesn't have | Push new file    | LOCAL     |
| Need specific remote change           | Cherry-pick only | SELECTIVE |

### **Emergency Recovery**

```bash
# If accidentally pulled
git reflog                           # Find last local commit
git reset --hard <your-commit>       # Reset to local state
git pushf                           # Force push to restore

# Recovery commands
git stash list                      # Check stash
git bundle create backup.bundle --all  # Create bundle backup
```

**Remember**: Your local files are the truth. GitHub is just a copy.

```

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

# Coding Standards (Reference Only)

**This rule is a reference. The authoritative version is located at:**

`.cursor/rules/coding-standards.mdc`

**Summary:**
Unified coding standards for all development. For implementation and updates, consult the official rule above.

## Validation Status

**Target Exists:** ✅ Yes
**Last Verified:** 2025-01-07T14:01:37Z
**Target Path:** `.cursor/rules/coding-standards.mdc`

## Emergency Cache

**Cache Date:** 2025-01-07T14:01:37Z
**Cached Lines:** 20 (Optimized)

### Cached Content (Essential lines only)

```markdown
# 🎯 CODING STANDARDS - GRUPO US VIBECODE V1.0

**Core Principles**: "Aprimore, Não Prolifere" (≥85% reuse), Confidence ≥8/10, Quality ≥90%

## 🏗️ DEVELOPMENT PHILOSOPHY

### **Professional Excellence Standards**

- **Context First**: Understand system completely before coding
- **Challenge Requests**: Identify edge cases, clarify requirements
- **Hold Standards**: Modular, testable, documented code
- **Design Don't Patch**: Think architecture, not quick fixes
- **Execution Standards**: One file per response, no method renaming without approval

### **Quality Enforcement**

- **Early Returns**: Handle errors at function beginning
- **Guard Clauses**: Use if-return pattern vs nested else
- **No Placeholders**: Complete functionality, no TODOs
- **Error Handling**: Comprehensive with proper logging
- **Type Safety**: TypeScript strict mode, Pydantic validation
```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

```

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.
```
