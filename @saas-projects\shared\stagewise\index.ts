/**
 * 🎯 Stagewise Centralized Solution - Main Export
 * VIBECODE V1.0 - Unified Stagewise Integration for Next.js Projects
 * 
 * Main entry point for Stagewise integration across all projects
 */

// Primary exports for easy integration
export { StagewiseProvider, useStagewise } from './components/StagewiseProvider';
export type { StagewiseProviderProps } from './components/StagewiseProvider';

// Configuration exports
export { getStagewiseConfig, defaultStagewiseConfig } from './config/stagewise.config';
export type { StagewiseConfig } from './config/stagewise.config';

// Environment utilities
export { isStagewiseEnabled, detectEnvironment } from './config/environment.config';

// Validation utilities
export { runFullValidation, logValidationResults } from './utils/validation';

// Default export for convenience
export { default } from './components/StagewiseProvider';
