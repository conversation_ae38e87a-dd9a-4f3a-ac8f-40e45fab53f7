"""
🧠 VIBECODE V2.0 - KNOWLEDGE GRAPH MANAGER (Enhanced Coordinator)
Sistema de aprendizado avançado com knowledge graph para padrões e insights

Enhanced modular architecture with self-correction integration and V2.0 components.
Maintains 100% backward compatibility while providing advanced capabilities.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import time
import hashlib
import networkx as nx
from collections import defaultdict, Counter
import pickle
from pathlib import Path
from datetime import datetime, timezone, timedelta

# V2.0 modular components - simplified import with minimal stubs
MODULAR_COMPONENTS_AVAILABLE = False

# Minimal stubs for V1.0 compatibility - these are not actually used
class SemanticSimilarityEngine:
    async def get_similar_tasks(self, task_description: str, agent_type: str):
        return []

class PredictiveAnalytics:
    async def predict_success_factors(self, agent_type: str, task_description: str, context: Dict):
        return []

# Placeholder classes for unused V2.0 features
class AdaptiveLearningSystem: pass
class IntelligentCache: pass
class ProductionOptimizer: pass
class ErrorHandler: pass
class ObservabilityManager: pass
class ConfigurationManager: pass

logger = logging.getLogger(__name__)

class NodeType(Enum):
    CONCEPT = "concept"
    PATTERN = "pattern"
    AGENT = "agent"
    TASK = "task"
    SOLUTION = "solution"
    ERROR = "error"
    DEPENDENCY = "dependency"

class RelationType(Enum):
    RELATED_TO = "related_to"
    DEPENDS_ON = "depends_on"
    IMPLEMENTS = "implements"
    CAUSES = "causes"
    SOLVES = "solves"
    SIMILAR_TO = "similar_to"
    PART_OF = "part_of"

@dataclass
class KnowledgeNode:
    """Nó no grafo de conhecimento"""
    id: str
    node_type: NodeType
    name: str
    description: str
    attributes: Dict[str, Any]
    confidence: float
    created_at: float
    updated_at: float
    access_count: int = 0
    success_rate: float = 1.0

@dataclass
class KnowledgeRelation:
    """Relação entre nós"""
    source_id: str
    target_id: str
    relation_type: RelationType
    strength: float
    metadata: Dict[str, Any]
    created_at: float

@dataclass
class LearningInsight:
    """Insight extraído do grafo"""
    insight_type: str
    description: str
    confidence: float
    supporting_nodes: List[str]
    actionable_recommendations: List[str]
    impact_score: float

class SelfCorrectionIntegrator:
    """
    🔄 SELF-CORRECTION INTEGRATION MODULE

    Bidirectional integration between Knowledge Graph and self_correction_log.md
    """

    def __init__(self, kg_manager, log_path: str = "memory-bank/python/self_correction_log.md"):
        self.kg_manager = kg_manager
        self.log_path = Path(log_path)
        self.correction_patterns = {}
        self.success_patterns = {}

    async def log_error_pattern(self, error_type: str, context: Dict[str, Any], correction: str):
        """Log error pattern to self_correction_log.md and Knowledge Graph"""
        timestamp = datetime.now().strftime("%Y-%m-%d")

        # Format log entry
        log_entry = f"""
## {timestamp} - ERRO DETECTADO: {error_type}

**PROBLEMA IDENTIFICADO**:
- ❌ {context.get('description', 'Error detected')}
- ❌ Contexto: {context}

**CORREÇÃO APLICADA**:
- ✅ {correction}

**LIÇÃO APRENDIDA**:
- Monitorar contexto similar: {list(context.keys())}
"""

        # Append to self_correction_log.md
        try:
            with open(self.log_path, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            logger.error(f"Failed to write to self_correction_log.md: {e}")

        # Add to Knowledge Graph
        if hasattr(self.kg_manager, 'add_knowledge'):
            await self.kg_manager.add_knowledge(
                node_type=NodeType.ERROR,
                name=f"Error: {error_type}",
                description=f"Error pattern: {error_type}",
                attributes={
                    "error_type": error_type,
                    "context": context,
                    "correction": correction,
                    "timestamp": timestamp,
                    "source": "self_correction_integration"
                }
            )

    async def log_success_pattern(self, success_type: str, context: Dict[str, Any], lessons: List[str]):
        """Log success pattern to self_correction_log.md and Knowledge Graph"""
        timestamp = datetime.now().strftime("%Y-%m-%d")

        log_entry = f"""
## {timestamp} - PADRÃO DE SUCESSO: {success_type}

**SUCESSO IDENTIFICADO**:
- ✅ {context.get('description', 'Success pattern identified')}
- ✅ Contexto: {context}

**LIÇÕES APRENDIDAS**:
{chr(10).join(f'- ✅ {lesson}' for lesson in lessons)}

**APLICAÇÃO FUTURA**:
- Aplicar este padrão em contextos similares
- Monitorar eficácia e ajustar conforme necessário
"""

        # Append to self_correction_log.md
        try:
            with open(self.log_path, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            logger.error(f"Failed to write to self_correction_log.md: {e}")

        # Add to Knowledge Graph
        if hasattr(self.kg_manager, 'add_knowledge'):
            await self.kg_manager.add_knowledge(
                node_type=NodeType.SOLUTION,
                name=f"Success: {success_type}",
                description=f"Success pattern: {success_type}",
                attributes={
                    "success_type": success_type,
                    "context": context,
                    "lessons": lessons,
                    "timestamp": timestamp,
                    "source": "self_correction_integration"
                }
            )

class KnowledgeGraphManager:
    """
    🧠 KNOWLEDGE GRAPH MANAGER V2.0 (Enhanced Coordinator)

    Enhanced Funcionalidades:
    - Construção automática de knowledge graph
    - Pattern recognition e insight extraction
    - Success pattern identification
    - Error pattern analysis
    - Agent performance correlation
    - Recommendation engine
    - Auto-learning from interactions
    - Self-correction integration (NEW)
    - Modular V2.0 component coordination (NEW)
    """

    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self._default_config()

        # Knowledge Graph usando NetworkX
        self.graph = nx.MultiDiGraph()
        self.nodes = {}  # id -> KnowledgeNode
        self.relations = {}  # relation_id -> KnowledgeRelation

        # Learning state
        self.learning_patterns = {}
        self.success_patterns = {}
        self.error_patterns = {}
        self.agent_correlations = {}

        # Storage
        self.storage_path = Path(self.config["storage_path"])
        self.storage_path.mkdir(parents=True, exist_ok=True)

        # V2.0 Enhanced Components
        self.self_correction = SelfCorrectionIntegrator(self)
        self.self_correction_logger = self.self_correction  # Alias for compatibility

        # V2.0 Enhancement: Temporary File Management Integration
        try:
            from .temp_file_manager import temp_manager, create_kg_temp_file
            self.temp_manager = temp_manager
            self.create_temp_file = create_kg_temp_file
            logger.info("🗂️ Temporary file management integrated")
        except ImportError:
            logger.warning("⚠️ Temporary file manager not available")
            self.temp_manager = None
            self.create_temp_file = None

        # Initialize V2.0 modular components if available
        if MODULAR_COMPONENTS_AVAILABLE:
            self.semantic_engine = SemanticSimilarityEngine()
            self.adaptive_learning = AdaptiveLearningSystem()
            self.intelligent_cache = IntelligentCache()
            self.predictive_analytics = PredictiveAnalytics()
            self.production_optimizer = ProductionOptimizer()
            self.error_handler = ErrorHandler()
            self.observability = ObservabilityManager()
            self.config_manager = ConfigurationManager()
            logger.info("🚀 V2.0 modular components initialized")
        else:
            logger.warning("⚠️ Running in V1.0 compatibility mode")

        # Estatísticas
        self.stats = {
            "total_nodes": 0,
            "total_relations": 0,
            "patterns_learned": 0,
            "insights_generated": 0,
            "recommendations_made": 0,
            "success_rate_improvements": 0.0,
            "self_corrections_logged": 0,
            "v2_components_active": MODULAR_COMPONENTS_AVAILABLE
        }

        logger.info("🧠 Knowledge Graph Manager V2.0 initialized")

    def _default_config(self) -> Dict:
        """Configuração padrão"""
        return {
            "storage_path": "memory-bank/python/knowledge_graph",
            "auto_save_interval": 300,  # 5 minutes
            "pattern_detection_threshold": 3,
            "confidence_threshold": 0.7,
            "max_graph_size": 10000,
            "learning_rate": 0.1,
            "insight_generation_interval": 600  # 10 minutes
        }

    async def initialize(self):
        """Inicializa o sistema com V2.0 enhancements"""
        try:
            # Carrega grafo existente
            await self._load_graph()

            # V2.0 Enhancement: Learn from historical self-correction data
            await self.learn_from_self_correction_log()

            # Inicia loops de processamento
            asyncio.create_task(self._auto_save_loop())
            asyncio.create_task(self._pattern_detection_loop())
            asyncio.create_task(self._insight_generation_loop())

            # V2.0 Enhancement: Initialize modular components
            if MODULAR_COMPONENTS_AVAILABLE:
                asyncio.create_task(self._v2_component_sync_loop())

            logger.info("✅ Knowledge Graph Manager V2.0 fully initialized")

        except Exception as e:
            logger.error(f"❌ Failed to initialize Knowledge Graph Manager: {e}")

    async def _v2_component_sync_loop(self):
        """V2.0 Enhancement: Sync loop for modular components"""
        while True:
            try:
                await asyncio.sleep(300)  # Every 5 minutes

                # Sync with intelligent cache
                if hasattr(self, 'intelligent_cache'):
                    await self.intelligent_cache.sync_with_kg(self.nodes, self.relations)

                # Update predictive models
                if hasattr(self, 'adaptive_learning'):
                    await self.adaptive_learning.update_models(self.success_patterns, self.error_patterns)

                logger.debug("🔄 V2.0 components synchronized")

            except Exception as e:
                logger.error(f"❌ V2.0 component sync failed: {e}")
                await asyncio.sleep(60)  # Wait before retry

    async def add_knowledge(
        self,
        node_type: NodeType,
        name: str,
        description: str,
        attributes: Optional[Dict] = None,
        related_nodes: Optional[List[str]] = None
    ) -> str:
        """Adiciona conhecimento ao grafo"""

        node_id = self._generate_node_id(node_type, name)

        # Cria nó
        node = KnowledgeNode(
            id=node_id,
            node_type=node_type,
            name=name,
            description=description,
            attributes=attributes or {},
            confidence=1.0,
            created_at=time.time(),
            updated_at=time.time()
        )

        # Adiciona ao grafo
        self.nodes[node_id] = node
        self.graph.add_node(node_id, **asdict(node))

        # Adiciona relações
        if related_nodes:
            for related_id in related_nodes:
                if related_id in self.nodes:
                    await self._add_relation(
                        node_id, related_id,
                        RelationType.RELATED_TO,
                        strength=0.8
                    )

        self.stats["total_nodes"] += 1

        logger.info(f"🧠 Added knowledge node: {name} (ID: {node_id[:8]}...)")

        # Detecta padrões automaticamente
        await self._detect_patterns_for_node(node_id)

        return node_id

    async def record_agent_action(
        self,
        agent_type: str,
        task_description: str,
        result: Dict[str, Any],
        success: bool,
        execution_time: float
    ):
        """Registra ação de agente para aprendizado com self-correction integration"""

        # Cria nó para agente se não existir
        agent_node_id = await self._ensure_agent_node(agent_type)

        # Cria nó para tarefa
        task_node_id = await self.add_knowledge(
            node_type=NodeType.TASK,
            name=f"Task: {task_description}",
            description=task_description,
            attributes={
                "execution_time": execution_time,
                "success": success,
                "agent_type": agent_type,
                "result_data": result
            }
        )

        # Conecta agente à tarefa
        await self._add_relation(
            agent_node_id, task_node_id,
            RelationType.IMPLEMENTS,
            strength=1.0 if success else 0.3,
            metadata={"success": success, "execution_time": execution_time}
        )

        # Atualiza estatísticas do agente
        await self._update_agent_stats(agent_node_id, success, execution_time)

        # Aprende padrões
        await self._learn_from_action(agent_type, task_description, result, success)

        # V2.0 Enhancement: Self-correction integration
        if success:
            # Log success pattern for future reference
            context = {
                "agent": agent_type,
                "task": task_description,
                "execution_time": execution_time,
                "result_summary": str(result)[:100] + "..." if len(str(result)) > 100 else str(result)
            }
            lessons = [
                f"Successful {agent_type} execution in {execution_time:.2f}s",
                f"Task pattern: {task_description[:50]}..." if len(task_description) > 50 else task_description,
                "Pattern can be reapplied in similar situations"
            ]
            await self.self_correction.log_success_pattern(
                f"{agent_type}_success", context, lessons
            )
            self.stats["self_corrections_logged"] += 1
        else:
            # Log error pattern for learning
            context = {
                "agent": agent_type,
                "task": task_description,
                "execution_time": execution_time,
                "error_details": result.get("error_message", "Unknown error")
            }
            correction = f"Analyze {agent_type} failure pattern and apply appropriate fixes"
            await self.self_correction.log_error_pattern(
                f"{agent_type}_failure", context, correction
            )
            self.stats["self_corrections_logged"] += 1

        logger.info(f"📊 Recorded action: {agent_type} -> {task_description} (success: {success})")

    async def _ensure_agent_node(self, agent_type: str) -> str:
        """Garante que nó do agente existe"""
        agent_id = f"agent_{agent_type}"

        # Check if agent already exists by searching through nodes
        for node_id, node in self.nodes.items():
            if (node.node_type == NodeType.AGENT and
                node.attributes.get("agent_type") == agent_type):
                return node_id

        # Create new agent node
        node_id = await self.add_knowledge(
            node_type=NodeType.AGENT,
            name=f"Agent: {agent_type}",
            description=f"AI Agent specialized in {agent_type} tasks",
            attributes={
                "agent_type": agent_type,
                "total_tasks": 0,
                "success_count": 0,
                "avg_execution_time": 0.0
            }
        )

        return node_id

    async def _update_agent_stats(self, agent_id: str, success: bool, execution_time: float):
        """Atualiza estatísticas do agente"""
        node = self.nodes[agent_id]
        attrs = node.attributes

        # Atualiza contadores
        attrs["total_tasks"] = attrs.get("total_tasks", 0) + 1
        if success:
            attrs["success_count"] = attrs.get("success_count", 0) + 1

        # Atualiza taxa de sucesso
        node.success_rate = attrs["success_count"] / attrs["total_tasks"]

        # Atualiza tempo médio
        current_avg = attrs.get("avg_execution_time", 0.0)
        total_tasks = attrs["total_tasks"]
        attrs["avg_execution_time"] = (
            (current_avg * (total_tasks - 1) + execution_time) / total_tasks
        )

        node.updated_at = time.time()

    async def _learn_from_action(
        self,
        agent_type: str,
        task_description: str,
        result: Dict[str, Any],
        success: bool
    ):
        """Aprende padrões da ação executada"""

        # Extrai características da tarefa
        task_features = self._extract_task_features(task_description, result)

        # Categoriza por sucesso/falha
        pattern_key = f"{agent_type}_{task_features['complexity']}_{'success' if success else 'failure'}"

        if success:
            # Aprende padrão de sucesso
            if pattern_key not in self.success_patterns:
                self.success_patterns[pattern_key] = {
                    "count": 0,
                    "features": task_features,
                    "successful_approaches": [],
                    "avg_execution_time": 0.0
                }

            pattern = self.success_patterns[pattern_key]
            pattern["count"] += 1
            pattern["successful_approaches"].append(result)

            # Atualiza tempo médio
            if "execution_time" in result:
                current_avg = pattern["avg_execution_time"]
                count = pattern["count"]
                pattern["avg_execution_time"] = (
                    (current_avg * (count - 1) + result["execution_time"]) / count
                )

        else:
            # Aprende padrão de erro
            if pattern_key not in self.error_patterns:
                self.error_patterns[pattern_key] = {
                    "count": 0,
                    "features": task_features,
                    "common_failures": [],
                    "failure_reasons": Counter()
                }

            pattern = self.error_patterns[pattern_key]
            pattern["count"] += 1
            pattern["common_failures"].append(result)

            # Conta razões de falha
            if "error_message" in result:
                pattern["failure_reasons"][result["error_message"]] += 1

        self.stats["patterns_learned"] += 1

    def _extract_task_features(self, description: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai características da tarefa"""

        # Análise simples de complexidade baseada em descrição
        word_count = len(description.split())
        complexity = "low" if word_count < 5 else "medium" if word_count < 10 else "high"

        # Extrai domínio
        domain_keywords = {
            "api": ["api", "endpoint", "rest", "http"],
            "database": ["database", "sql", "query", "table"],
            "ui": ["ui", "component", "interface", "frontend"],
            "backend": ["backend", "server", "service", "microservice"],
            "ai": ["ai", "machine learning", "model", "training"]
        }

        description_lower = description.lower()
        detected_domains = []

        for domain, keywords in domain_keywords.items():
            if any(keyword in description_lower for keyword in keywords):
                detected_domains.append(domain)

        return {
            "complexity": complexity,
            "word_count": word_count,
            "domains": detected_domains,
            "description_length": len(description),
            "has_technical_terms": len(detected_domains) > 0
        }

    async def get_recommendations(
        self,
        agent_type: str,
        task_description: str,
        context: Optional[Dict] = None
    ) -> List[Dict[str, Any]]:
        """Gera recomendações baseadas no knowledge graph com V2.0 enhancements"""

        recommendations = []

        # Extrai características da tarefa atual
        task_features = self._extract_task_features(task_description, context or {})

        # V2.0 Enhancement: Use semantic similarity if available
        if MODULAR_COMPONENTS_AVAILABLE and hasattr(self, 'semantic_engine'):
            try:
                semantic_recommendations = await self.semantic_engine.get_similar_tasks(
                    task_description, agent_type
                )
                recommendations.extend(semantic_recommendations)
            except Exception as e:
                logger.warning(f"Semantic engine failed: {e}")

        # Busca padrões similares de sucesso
        similar_success_patterns = self._find_similar_patterns(
            agent_type, task_features, self.success_patterns
        )

        for pattern_key, pattern_data in similar_success_patterns[:3]:
            recommendations.append({
                "type": "success_pattern",
                "confidence": self._calculate_pattern_confidence(pattern_data),
                "description": f"Similar successful tasks suggest this approach",
                "suggested_approach": self._extract_approach_summary(pattern_data),
                "expected_execution_time": pattern_data["avg_execution_time"],
                "success_probability": pattern_data["count"] / (pattern_data["count"] + 1)
            })

        # Verifica padrões de erro para evitar
        similar_error_patterns = self._find_similar_patterns(
            agent_type, task_features, self.error_patterns
        )

        for pattern_key, pattern_data in similar_error_patterns[:2]:
            most_common_failure = pattern_data["failure_reasons"].most_common(1)
            if most_common_failure:
                recommendations.append({
                    "type": "error_avoidance",
                    "confidence": 0.8,
                    "description": f"Avoid common failure pattern",
                    "warning": most_common_failure[0][0],
                    "occurrence_rate": most_common_failure[0][1] / pattern_data["count"]
                })

        # Recomendações baseadas em performance do agente
        agent_id = f"agent_{agent_type}"
        if agent_id in self.nodes:
            agent_node = self.nodes[agent_id]
            if agent_node.success_rate < 0.8:
                recommendations.append({
                    "type": "agent_improvement",
                    "confidence": 0.9,
                    "description": f"Agent {agent_type} has {agent_node.success_rate:.2%} success rate",
                    "suggestion": "Consider task breakdown or additional validation",
                    "current_success_rate": agent_node.success_rate
                })

        # V2.0 Enhancement: Predictive analytics
        if MODULAR_COMPONENTS_AVAILABLE and hasattr(self, 'predictive_analytics'):
            try:
                predictive_recommendations = await self.predictive_analytics.predict_success_factors(
                    agent_type, task_description, context or {}
                )
                recommendations.extend(predictive_recommendations)
            except Exception as e:
                logger.warning(f"Predictive analytics failed: {e}")

        self.stats["recommendations_made"] += len(recommendations)

        logger.info(f"💡 Generated {len(recommendations)} recommendations for {agent_type}")

        return recommendations

    async def learn_from_self_correction_log(self):
        """V2.0 Enhancement: Learn patterns from self_correction_log.md"""
        try:
            if not self.self_correction.log_path.exists():
                logger.warning("Self correction log not found")
                return

            with open(self.self_correction.log_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract error patterns
            error_sections = content.split("## ")[1:]  # Skip first empty split

            for section in error_sections:
                if "ERRO DETECTADO:" in section:
                    lines = section.split('\n')
                    error_type = lines[0].split("ERRO DETECTADO: ")[1] if "ERRO DETECTADO: " in lines[0] else "unknown"

                    # Extract context if available
                    context = {}
                    for line in lines:
                        if "Contexto:" in line:
                            try:
                                context_str = line.split("Contexto: ")[1]
                                # Simple parsing - could be enhanced
                                context = {"raw_context": context_str}
                            except:
                                pass

                    # Add to knowledge graph
                    await self.add_knowledge(
                        node_type=NodeType.ERROR,
                        name=f"Historical Error: {error_type}",
                        description=f"Error pattern from self-correction log: {error_type}",
                        attributes={
                            "error_type": error_type,
                            "context": context,
                            "source": "self_correction_log_learning",
                            "historical": True
                        }
                    )

                elif "PADRÃO DE SUCESSO:" in section:
                    lines = section.split('\n')
                    success_type = lines[0].split("PADRÃO DE SUCESSO: ")[1] if "PADRÃO DE SUCESSO: " in lines[0] else "unknown"

                    # Extract lessons
                    lessons = []
                    for line in lines:
                        if line.strip().startswith("- ✅"):
                            lessons.append(line.strip()[4:])  # Remove "- ✅ "

                    # Add to knowledge graph
                    await self.add_knowledge(
                        node_type=NodeType.SOLUTION,
                        name=f"Historical Success: {success_type}",
                        description=f"Success pattern from self-correction log: {success_type}",
                        attributes={
                            "success_type": success_type,
                            "lessons": lessons,
                            "source": "self_correction_log_learning",
                            "historical": True
                        }
                    )

            logger.info("🔄 Successfully learned from self_correction_log.md")

        except Exception as e:
            logger.error(f"Failed to learn from self_correction_log.md: {e}")

    async def export_graph_data(self, format: str = 'json', use_temp: bool = True) -> Path:
        """V2.0 Enhancement: Export graph data using temporary file system"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"kg_export_{timestamp}.{format}"

            if use_temp and self.temp_manager:
                # Use temporary file system
                if format == 'json':
                    export_data = {
                        'nodes': {node_id: asdict(node) for node_id, node in self.nodes.items()},
                        'relations': {rel_id: asdict(rel) for rel_id, rel in self.relations.items()},
                        'stats': self.stats,
                        'export_timestamp': timestamp
                    }
                    content = json.dumps(export_data, indent=2, default=str)
                else:
                    content = f"# Knowledge Graph Export - {timestamp}\n\nFormat: {format}\nNodes: {len(self.nodes)}\nRelations: {len(self.relations)}\n"

                if self.create_temp_file:
                    file_path = self.create_temp_file(filename, content, preserve=True)
                else:
                    file_path = self.temp_manager.create_temp_file('kg_temp', filename, content, preserve=True)
                logger.info(f"🗂️ Graph data exported to temp file: {filename}")
                return file_path
            else:
                # Fallback to storage path
                file_path = self.storage_path / filename
                # Implementation for non-temp export would go here
                logger.info(f"📁 Graph data exported to storage: {filename}")
                return file_path

        except Exception as e:
            logger.error(f"❌ Failed to export graph data: {e}")
            raise

    async def create_temp_backup(self) -> Optional[Path]:
        """V2.0 Enhancement: Create temporary backup of graph data"""
        try:
            if not self.temp_manager:
                logger.warning("⚠️ Temporary file manager not available for backup")
                return None

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"kg_backup_{timestamp}.json"

            backup_data = {
                'nodes': {node_id: asdict(node) for node_id, node in self.nodes.items()},
                'relations': {rel_id: asdict(rel) for rel_id, rel in self.relations.items()},
                'learning_patterns': self.learning_patterns,
                'success_patterns': self.success_patterns,
                'error_patterns': self.error_patterns,
                'stats': self.stats,
                'backup_timestamp': timestamp
            }

            backup_content = json.dumps(backup_data, indent=2, default=str)
            if self.create_temp_file:
                backup_path = self.create_temp_file(backup_filename, backup_content, preserve=True)
            else:
                backup_path = self.temp_manager.create_temp_file('kg_temp', backup_filename, backup_content, preserve=True)

            logger.info(f"🗂️ Temporary backup created: {backup_filename}")
            return backup_path

        except Exception as e:
            logger.error(f"❌ Failed to create temporary backup: {e}")
            return None

    def cleanup_temp_files(self, max_age_hours: int = 24) -> Dict[str, Any]:
        """V2.0 Enhancement: Clean up Knowledge Graph temporary files"""
        if not self.temp_manager:
            logger.warning("⚠️ Temporary file manager not available")
            return {'error': 'Temporary file manager not available'}

        try:
            # Get KG-specific temp directory
            kg_temp_dir = self.temp_manager.get_temp_path('kg_temp')

            cleanup_results = {
                'files_removed': 0,
                'bytes_freed': 0,
                'files_preserved': 0,
                'errors': []
            }

            if not kg_temp_dir.exists():
                return cleanup_results

            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

            for file_path in kg_temp_dir.rglob('*'):
                if not file_path.is_file():
                    continue

                try:
                    # Preserve important files
                    if file_path.name.endswith(('.important', '.preserve')):
                        cleanup_results['files_preserved'] += 1
                        continue

                    # Check file age
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_time:
                        file_size = file_path.stat().st_size
                        file_path.unlink()

                        cleanup_results['files_removed'] += 1
                        cleanup_results['bytes_freed'] += file_size

                except Exception as e:
                    cleanup_results['errors'].append(f"Failed to process {file_path}: {e}")

            logger.info(f"🗂️ KG temp cleanup: {cleanup_results['files_removed']} files removed")
            return cleanup_results

        except Exception as e:
            logger.error(f"❌ KG temp cleanup failed: {e}")
            return {'error': str(e)}

    def _find_similar_patterns(
        self,
        agent_type: str,
        task_features: Dict[str, Any],
        pattern_store: Dict[str, Dict]
    ) -> List[Tuple[str, Dict]]:
        """Encontra padrões similares"""

        similar_patterns = []

        for pattern_key, pattern_data in pattern_store.items():
            if not pattern_key.startswith(agent_type):
                continue

            # Calcula similaridade
            similarity = self._calculate_feature_similarity(
                task_features, pattern_data["features"]
            )

            if similarity > 0.6:  # Threshold de similaridade
                similar_patterns.append((pattern_key, pattern_data))

        # Ordena por contagem (mais frequentes primeiro)
        similar_patterns.sort(key=lambda x: x[1]["count"], reverse=True)

        return similar_patterns

    def _calculate_feature_similarity(self, features1: Dict, features2: Dict) -> float:
        """Calcula similaridade entre características"""

        similarity_score = 0.0
        total_weight = 0.0

        # Complexidade (peso alto)
        if features1["complexity"] == features2["complexity"]:
            similarity_score += 0.4
        total_weight += 0.4

        # Domínios (peso médio)
        common_domains = set(features1["domains"]) & set(features2["domains"])
        if features1["domains"] and features2["domains"]:
            domain_similarity = len(common_domains) / max(len(features1["domains"]), len(features2["domains"]))
            similarity_score += domain_similarity * 0.3
        total_weight += 0.3

        # Tamanho da descrição (peso baixo)
        length_diff = abs(features1["description_length"] - features2["description_length"])
        max_length = max(features1["description_length"], features2["description_length"])
        if max_length > 0:
            length_similarity = 1.0 - (length_diff / max_length)
            similarity_score += length_similarity * 0.2
        total_weight += 0.2

        # Termos técnicos (peso baixo)
        if features1["has_technical_terms"] == features2["has_technical_terms"]:
            similarity_score += 0.1
        total_weight += 0.1

        return similarity_score / total_weight if total_weight > 0 else 0.0

    def _calculate_pattern_confidence(self, pattern_data: Dict) -> float:
        """Calcula confiança do padrão"""

        # Baseado na frequência e consistência
        frequency_score = min(1.0, pattern_data["count"] / 10.0)  # Max aos 10 ocorrências

        # Bônus para padrões consistentes
        consistency_bonus = 0.2 if pattern_data["count"] >= 3 else 0.0

        return min(1.0, frequency_score + consistency_bonus)

    def _extract_approach_summary(self, pattern_data: Dict) -> str:
        """Extrai resumo da abordagem bem-sucedida"""

        if not pattern_data["successful_approaches"]:
            return "No specific approach data available"

        # Analisa abordagens bem-sucedidas
        common_elements = []

        # Procura por elementos comuns
        first_approach = pattern_data["successful_approaches"][0]
        if isinstance(first_approach, dict):
            if "methodology" in first_approach:
                common_elements.append(f"Methodology: {first_approach['methodology']}")
            if "tools_used" in first_approach:
                common_elements.append(f"Tools: {first_approach['tools_used']}")

        if common_elements:
            return "; ".join(common_elements)
        else:
            return f"Based on {pattern_data['count']} successful executions"

    async def generate_insights(self) -> List[LearningInsight]:
        """Gera insights do knowledge graph"""

        insights = []

        # Insight: Agentes mais/menos eficazes
        agent_performance = await self._analyze_agent_performance()
        if agent_performance:
            insights.append(LearningInsight(
                insight_type="agent_performance",
                description=f"Agent performance analysis reveals efficiency patterns",
                confidence=0.9,
                supporting_nodes=list(agent_performance.keys()),
                actionable_recommendations=self._generate_performance_recommendations(agent_performance),
                impact_score=0.8
            ))

        # Insight: Padrões de erro comuns
        error_insights = await self._analyze_error_patterns()
        if error_insights:
            insights.extend(error_insights)

        # Insight: Dependências e gargalos
        dependency_insights = await self._analyze_dependencies()
        if dependency_insights:
            insights.extend(dependency_insights)

        # Insight: Oportunidades de otimização
        optimization_insights = await self._identify_optimization_opportunities()
        if optimization_insights:
            insights.extend(optimization_insights)

        self.stats["insights_generated"] += len(insights)

        logger.info(f"🔍 Generated {len(insights)} insights from knowledge graph")

        return insights

    async def _analyze_agent_performance(self) -> Dict[str, Dict]:
        """Analisa performance dos agentes"""

        agent_performance = {}

        for node_id, node in self.nodes.items():
            if node.node_type == NodeType.AGENT:
                agent_performance[node_id] = {
                    "success_rate": node.success_rate,
                    "total_tasks": node.attributes.get("total_tasks", 0),
                    "avg_execution_time": node.attributes.get("avg_execution_time", 0.0),
                    "agent_type": node.attributes.get("agent_type", "unknown")
                }

        return agent_performance

    def _generate_performance_recommendations(self, performance_data: Dict[str, Dict]) -> List[str]:
        """Gera recomendações de performance"""

        recommendations = []

        # Identifica agentes com baixa performance
        for agent_id, data in performance_data.items():
            if data["success_rate"] < 0.7 and data["total_tasks"] >= 3:
                recommendations.append(
                    f"Agent {data['agent_type']} needs improvement - "
                    f"success rate: {data['success_rate']:.2%}"
                )

        # Identifica agentes lentos
        avg_times = [data["avg_execution_time"] for data in performance_data.values() if data["avg_execution_time"] > 0]
        if avg_times:
            global_avg = sum(avg_times) / len(avg_times)

            for agent_id, data in performance_data.items():
                if data["avg_execution_time"] > global_avg * 1.5:
                    recommendations.append(
                        f"Agent {data['agent_type']} is slow - "
                        f"avg time: {data['avg_execution_time']:.2f}s vs global {global_avg:.2f}s"
                    )

        return recommendations

    async def _analyze_error_patterns(self) -> List[LearningInsight]:
        """Analisa padrões de erro"""

        insights = []

        # Agrupa erros por tipo de agente
        agent_errors = defaultdict(list)

        for pattern_key, pattern_data in self.error_patterns.items():
            if pattern_data["count"] >= self.config["pattern_detection_threshold"]:
                agent_type = pattern_key.split("_")[0]
                agent_errors[agent_type].append(pattern_data)

        # Gera insights por agente
        for agent_type, error_list in agent_errors.items():
            if len(error_list) >= 2:  # Múltiplos padrões de erro

                # Encontra razões mais comuns
                all_reasons = Counter()
                for error_pattern in error_list:
                    all_reasons.update(error_pattern["failure_reasons"])

                top_reasons = all_reasons.most_common(3)

                insights.append(LearningInsight(
                    insight_type="error_pattern",
                    description=f"Agent {agent_type} shows recurring error patterns",
                    confidence=0.8,
                    supporting_nodes=[f"agent_{agent_type}"],
                    actionable_recommendations=[
                        f"Address common failure: {reason[0]}"
                        for reason in top_reasons
                    ],
                    impact_score=0.7
                ))

        return insights

    async def _analyze_dependencies(self) -> List[LearningInsight]:
        """Analisa dependências e gargalos"""

        insights = []

        # Analisa grau de entrada (dependências)
        in_degrees = dict(self.graph.in_degree())

        # Identifica nós com muitas dependências (possíveis gargalos)
        bottlenecks = [
            node_id for node_id, degree in in_degrees.items()
            if degree >= 5  # Threshold para gargalo
        ]

        if bottlenecks:
            insights.append(LearningInsight(
                insight_type="dependency_analysis",
                description=f"Identified {len(bottlenecks)} potential bottlenecks",
                confidence=0.7,
                supporting_nodes=bottlenecks,
                actionable_recommendations=[
                    f"Review dependencies for node {node_id[:8]}..."
                    for node_id in bottlenecks[:3]
                ],
                impact_score=0.6
            ))

        return insights

    async def _identify_optimization_opportunities(self) -> List[LearningInsight]:
        """Identifica oportunidades de otimização"""

        insights = []

        # Analisa padrões de sucesso subutilizados
        underutilized_patterns = []

        for pattern_key, pattern_data in self.success_patterns.items():
            if (pattern_data["count"] >= 3 and
                pattern_data["avg_execution_time"] < 5.0 and  # Rápido
                pattern_key not in self._get_recently_used_patterns()):

                underutilized_patterns.append(pattern_key)

        if underutilized_patterns:
            insights.append(LearningInsight(
                insight_type="optimization_opportunity",
                description=f"Found {len(underutilized_patterns)} underutilized efficient patterns",
                confidence=0.8,
                supporting_nodes=[],
                actionable_recommendations=[
                    f"Consider reusing pattern: {pattern}"
                    for pattern in underutilized_patterns[:3]
                ],
                impact_score=0.5
            ))

        return insights

    def _get_recently_used_patterns(self) -> Set[str]:
        """Obtém padrões usados recentemente"""
        # Implementação simplificada - em produção seria baseado em timestamp
        return set()

    async def _add_relation(
        self,
        source_id: str,
        target_id: str,
        relation_type: RelationType,
        strength: float = 1.0,
        metadata: Optional[Dict] = None
    ):
        """Adiciona relação entre nós"""

        relation_id = f"{source_id}_{target_id}_{relation_type.value}"

        relation = KnowledgeRelation(
            source_id=source_id,
            target_id=target_id,
            relation_type=relation_type,
            strength=strength,
            metadata=metadata or {},
            created_at=time.time()
        )

        self.relations[relation_id] = relation
        self.graph.add_edge(
            source_id, target_id,
            relation_type=relation_type.value,
            strength=strength,
            metadata=metadata or {}
        )

        self.stats["total_relations"] += 1

    async def _detect_patterns_for_node(self, node_id: str):
        """Detecta padrões para novo nó"""

        # Implementação simplificada - em produção seria mais sofisticada
        node = self.nodes[node_id]

        # Busca nós similares
        similar_nodes = []
        for other_id, other_node in self.nodes.items():
            if (other_id != node_id and
                other_node.node_type == node.node_type and
                self._calculate_node_similarity(node, other_node) > 0.7):
                similar_nodes.append(other_id)

        # Cria relações com nós similares
        for similar_id in similar_nodes[:3]:  # Limita a 3 conexões
            await self._add_relation(
                node_id, similar_id,
                RelationType.SIMILAR_TO,
                strength=0.8
            )

    def _calculate_node_similarity(self, node1: KnowledgeNode, node2: KnowledgeNode) -> float:
        """Calcula similaridade entre nós"""

        # Similaridade simples baseada em atributos
        if node1.node_type != node2.node_type:
            return 0.0

        # Compara atributos comuns
        common_attrs = set(node1.attributes.keys()) & set(node2.attributes.keys())
        if not common_attrs:
            return 0.0

        similarity_score = 0.0
        for attr in common_attrs:
            if node1.attributes[attr] == node2.attributes[attr]:
                similarity_score += 1.0

        return similarity_score / len(common_attrs)

    def _generate_node_id(self, node_type: NodeType, name: str) -> str:
        """Gera ID único para nó"""
        data = f"{node_type.value}_{name}_{time.time()}"
        return hashlib.sha256(data.encode()).hexdigest()[:16]

    async def _auto_save_loop(self):
        """Loop automático de salvamento"""
        while True:
            try:
                await asyncio.sleep(self.config["auto_save_interval"])
                await self._save_graph()
            except Exception as e:
                logger.error(f"❌ Auto save failed: {e}")

    async def _pattern_detection_loop(self):
        """Loop de detecção de padrões"""
        while True:
            try:
                await asyncio.sleep(60)  # A cada minuto
                await self._detect_new_patterns()
            except Exception as e:
                logger.error(f"❌ Pattern detection failed: {e}")

    async def _insight_generation_loop(self):
        """Loop de geração de insights"""
        while True:
            try:
                await asyncio.sleep(self.config["insight_generation_interval"])
                insights = await self.generate_insights()
                if insights:
                    logger.info(f"🔍 Generated {len(insights)} new insights")
            except Exception as e:
                logger.error(f"❌ Insight generation failed: {e}")

    async def _detect_new_patterns(self):
        """Detecta novos padrões no grafo"""
        # Implementação simplificada
        pass

    async def _save_graph(self):
        """Salva o grafo no storage"""
        try:
            # Salva nós
            nodes_file = self.storage_path / "nodes.pkl"
            with open(nodes_file, 'wb') as f:
                pickle.dump(self.nodes, f)

            # Salva relações
            relations_file = self.storage_path / "relations.pkl"
            with open(relations_file, 'wb') as f:
                pickle.dump(self.relations, f)

            # Salva padrões
            patterns_file = self.storage_path / "patterns.pkl"
            with open(patterns_file, 'wb') as f:
                pickle.dump({
                    "success_patterns": self.success_patterns,
                    "error_patterns": self.error_patterns,
                    "learning_patterns": self.learning_patterns
                }, f)

            logger.debug("💾 Knowledge graph saved successfully")

        except Exception as e:
            logger.error(f"❌ Failed to save knowledge graph: {e}")

    async def _load_graph(self):
        """Carrega o grafo do storage"""
        try:
            # Carrega nós
            nodes_file = self.storage_path / "nodes.pkl"
            if nodes_file.exists():
                with open(nodes_file, 'rb') as f:
                    self.nodes = pickle.load(f)

            # Carrega relações
            relations_file = self.storage_path / "relations.pkl"
            if relations_file.exists():
                with open(relations_file, 'rb') as f:
                    self.relations = pickle.load(f)

            # Carrega padrões
            patterns_file = self.storage_path / "patterns.pkl"
            if patterns_file.exists():
                with open(patterns_file, 'rb') as f:
                    patterns_data = pickle.load(f)
                    self.success_patterns = patterns_data.get("success_patterns", {})
                    self.error_patterns = patterns_data.get("error_patterns", {})
                    self.learning_patterns = patterns_data.get("learning_patterns", {})

            # Reconstrói o grafo NetworkX
            self.graph = nx.MultiDiGraph()

            for node_id, node in self.nodes.items():
                self.graph.add_node(node_id, **asdict(node))

            for relation in self.relations.values():
                self.graph.add_edge(
                    relation.source_id, relation.target_id,
                    relation_type=relation.relation_type.value,
                    strength=relation.strength,
                    metadata=relation.metadata
                )

            logger.info(f"📂 Loaded knowledge graph: {len(self.nodes)} nodes, {len(self.relations)} relations")

        except Exception as e:
            logger.error(f"❌ Failed to load knowledge graph: {e}")

    def get_graph_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas do grafo"""
        return {
            "nodes_count": len(self.nodes),
            "relations_count": len(self.relations),
            "success_patterns": len(self.success_patterns),
            "error_patterns": len(self.error_patterns),
            "graph_density": nx.density(self.graph) if self.graph.number_of_nodes() > 1 else 0.0,
            "connected_components": nx.number_weakly_connected_components(self.graph),
            "avg_degree": sum(dict(self.graph.degree()).values()) / max(1, len(self.nodes)),
            "insights_generated": self.stats["insights_generated"],
            "recommendations_made": self.stats["recommendations_made"]
        }

    async def health_check(self) -> Dict[str, Any]:
        """Verifica saúde do sistema"""
        return {
            "status": "healthy",
            "graph_size": len(self.nodes),
            "patterns_learned": len(self.success_patterns) + len(self.error_patterns),
            "storage_available": self.storage_path.exists(),
            "auto_save_enabled": True,
            "pattern_detection_active": True
        }

# V2.0 Enhanced Convenience Functions
async def record_agent_success(agent_type: str, task: str, result: Dict, execution_time: float):
    """Registra sucesso de agente no knowledge graph"""
    kg = KnowledgeGraphManager()
    await kg.initialize()

    await kg.record_agent_action(
        agent_type=agent_type,
        task_description=task,
        result=result,
        success=True,
        execution_time=execution_time
    )

async def record_agent_failure(agent_type: str, task: str, error: str, execution_time: float):
    """Registra falha de agente no knowledge graph"""
    kg = KnowledgeGraphManager()
    await kg.initialize()

    await kg.record_agent_action(
        agent_type=agent_type,
        task_description=task,
        result={"error_message": error},
        success=False,
        execution_time=execution_time
    )

async def get_task_recommendations(agent_type: str, task: str, context: Optional[Dict] = None) -> List[Dict]:
    """Obtém recomendações para tarefa"""
    kg = KnowledgeGraphManager()
    await kg.initialize()

    return await kg.get_recommendations(agent_type, task, context)

async def record_task_management_usage(user_request: str, complexity_score: int, triggers: List[str], tasks_created: int, success: bool):
    """Registra uso de task management no knowledge graph"""
    kg = KnowledgeGraphManager()
    await kg.initialize()

    await kg.record_agent_action(
        agent_type="task_management_system",
        task_description=f"Task management for: {user_request}",
        result={
            "complexity_score": complexity_score,
            "triggers": triggers,
            "tasks_created": tasks_created,
            "auto_activated": True
        },
        success=success,
        execution_time=0.1  # Task management é rápido
    )

async def learn_task_management_patterns(user_request: str, complexity_score: int, effectiveness_score: float):
    """Aprende padrões de efetividade do task management"""
    kg = KnowledgeGraphManager()
    await kg.initialize()

    # Criar padrão de aprendizado
    pattern = {
        "request_type": user_request[:50],  # Primeiras 50 chars
        "complexity": complexity_score,
        "effectiveness": effectiveness_score,
        "timestamp": time.time()
    }

    await kg._learn_from_action(
        "task_management_system",
        user_request,
        pattern,
        effectiveness_score >= 8.0
    )

async def get_task_management_insights(user_request: str) -> Dict[str, Any]:
    """Obtém insights sobre task management para uma solicitação"""
    kg = KnowledgeGraphManager()
    await kg.initialize()

    recommendations = await kg.get_recommendations("task_management_system", user_request)

    return {
        "should_use_task_management": len(recommendations) > 0,
        "recommended_complexity": recommendations[0].get("complexity", 3) if recommendations else 3,
        "similar_patterns": recommendations[:3],
        "confidence": recommendations[0].get("confidence", 0.5) if recommendations else 0.5
    }

async def log_self_correction(error_type: str, context: Dict[str, Any], correction: str):
    """Log self-correction pattern"""
    kg = KnowledgeGraphManager()
    await kg.initialize()

    await kg.self_correction.log_error_pattern(error_type, context, correction)

async def log_success_pattern(success_type: str, context: Dict[str, Any], lessons: List[str]):
    """Log success pattern"""
    kg = KnowledgeGraphManager()
    await kg.initialize()

    await kg.self_correction.log_success_pattern(success_type, context, lessons)

# Backward Compatibility Aliases
KnowledgeGraphManagerHybrid = KnowledgeGraphManager  # For validation framework compatibility

# Exemplo de uso
if __name__ == "__main__":
    async def demo():
        # Use UTF-8 encoding for emoji support
        import sys
        if sys.platform == "win32":
            # Windows console encoding fix
            sys.stdout.reconfigure(encoding='utf-8')

        print("Knowledge Graph Manager Demo")
        print("=" * 50)

        kg = KnowledgeGraphManager()
        await kg.initialize()

        # Simula algumas ações de agentes
        test_actions = [
            ("technical_architect", "Design API architecture", {"methodology": "REST", "success": True}, True, 5.2),
            ("technical_architect", "Implement user auth", {"framework": "FastAPI", "tests": True}, True, 12.3),
            ("operations_coordinator", "Deploy to staging", {"platform": "Docker", "environment": "staging"}, True, 3.1),
            ("technical_architect", "Design database schema", {"database": "PostgreSQL", "tables": 5}, False, 8.7),
            ("quality_guardian", "Fix authentication bug", {"issue": "token validation", "fixed": True}, True, 2.1)
        ]

        for agent_type, task, result, success, exec_time in test_actions:
            await kg.record_agent_action(agent_type, task, result, success, exec_time)
            print(f"📊 Recorded: {agent_type} -> {task} (success: {success})")

        # Testa recomendações
        print(f"\n💡 Getting recommendations...")

        test_queries = [
            ("technical_architect", "Design new microservice architecture"),
            ("operations_coordinator", "Implement OAuth authentication"),
            ("research_strategist", "Deploy new feature to production")
        ]

        for agent_type, task in test_queries:
            recommendations = await kg.get_recommendations(agent_type, task)
            print(f"\n🎯 Recommendations for {agent_type} - {task}:")

            for i, rec in enumerate(recommendations[:2], 1):
                print(f"   {i}. {rec['type']}: {rec['description']} (confidence: {rec['confidence']:.2f})")

        # Gera insights
        print(f"\n🔍 Generating insights...")
        insights = await kg.generate_insights()

        for insight in insights[:3]:
            print(f"\n💡 Insight ({insight.insight_type}):")
            print(f"   {insight.description}")
            print(f"   Confidence: {insight.confidence:.2f}")
            print(f"   Impact: {insight.impact_score:.2f}")
            if insight.actionable_recommendations:
                print(f"   Recommendation: {insight.actionable_recommendations[0]}")

        # Estatísticas
        stats = kg.get_graph_statistics()
        print(f"\n📊 Knowledge Graph Statistics:")
        print(json.dumps(stats, indent=2))

        # Health check
        health = await kg.health_check()
        print(f"\n❤️ Health Status:")
        print(json.dumps(health, indent=2))

    asyncio.run(demo())
