{"metadata": {"name": "GRUPO US VIBECODE SYSTEM V1.0 - Complete MCP Configuration", "version": "1.0.0", "description": "Configuração completa com todos os MCPs do sistema VIBECODE", "lastUpdated": "2025-01-27T21:30:00.000Z", "environment": "cursor-windows-optimized"}, "mcpServers": {"desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "enabled": true, "name": "Desktop Commander MCP", "description": "Terminal & Filesystem Access for practical implementation", "tier": 1}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "enabled": true, "name": "Sequential Thinking MCP", "description": "Advanced reasoning and problem-solving", "tier": 1}, "mcp-shrimp-task-manager": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "mcp-shrimp-task-manager"], "enabled": true, "name": "MCP Shrimp Task Manager", "description": "Task coordination and workflow management", "env": {"DATA_DIR": "C:\\Users\\<USER>\\OneDrive\\GRUPOUS\\VSCODE\\@project-core\\data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}, "tier": 2}, "context7-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "enabled": true, "name": "Context7 MCP", "description": "Documentation and library context", "env": {"UPSTASH_CONTEXT7_API_KEY": "ctx7_fzqcQNgU3AChDBMjNIVYg4zLQp4LgFBjZnbA"}, "tier": 3}, "tavily-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "tavily-mcp@0.2.4"], "enabled": true, "name": "<PERSON><PERSON>", "description": "Web search and research", "env": {"TAVILY_API_KEY": "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"}, "tier": 3}, "exa-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "exa-mcp-server"], "enabled": true, "name": "Exa MCP", "description": "Semantic search and content analysis", "env": {"EXA_API_KEY": "fae6582d-4562-45be-8ce9-f6c0c3518c66"}, "tier": 3}, "playwright-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@playwright/mcp@latest"], "enabled": true, "name": "Playwright <PERSON><PERSON>", "description": "Browser automation and testing", "tier": 4}, "figma-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "figma-mcp"], "enabled": true, "name": "Figma MCP", "description": "Design integration and context", "env": {"FIGMA_ACCESS_TOKEN": ""}, "tier": 4}, "supabase-mcp": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase"], "enabled": true, "name": "Supabase MCP", "description": "Database operations and backend integration", "env": {"SUPABASE_URL": "https://gfkskrkbnawkuppazkpt.supabase.co", "SUPABASE_ANON_KEY": "", "SUPABASE_SERVICE_ROLE_KEY": ""}, "tier": 4}}}