# 📋 Augment Modular Rule Loading Configuration (2025 ENHANCED)

## 🎯 Core Rule Loading Matrix (Updated with 2025 Research Improvements)

```yaml
AUGMENT_CORE_RULES_2025_ENHANCED:
  always_loaded:
    # Augment Core Foundation (SUPREME AUTHORITY - 2025 ENHANCED)
    - path: "system_prompt.md/master_rule.mdc"
      priority: 1
      cache_key: "augment_core_foundation_2025"
      size_estimate: "12.8KB"
      description: "Master rule with 2025 context engineering principles"
      
    - path: "system_prompt.md/quality.mdc"
      priority: 1
      cache_key: "augment_quality_2025"
      size_estimate: "9.2KB"
      description: "Production-grade quality monitoring"
      
    - path: "system_prompt.md/mcp-protocols.mdc"
      priority: 1
      cache_key: "augment_mcp_2025"
      size_estimate: "8.5KB"
      description: "MCP enforcement with intelligent routing"

WORKFLOW_RULES:
  research_workflows:
    path: "system_prompt.md/research-mandatory-summary.mdc"
    trigger_patterns: ["research", "analyze", "investigate", "study", "evaluate", "compare"]
    complexity_range: [4, 10]
    cache_key: "workflow_research"
    size_estimate: "4.2KB"
    
  implementation_workflows:
    path: "system_prompt.md/coding-standards-enhanced-v2.0.mdc"
    trigger_patterns: ["implement", "create", "build", "develop", "code", "write"]
    complexity_range: [3, 8]
    cache_key: "workflow_implementation"
    size_estimate: "3.5KB"
    
  task_workflows:
    path: "system_prompt.md/task-automation.mdc"
    trigger_patterns: ["task", "manage", "organize", "plan", "schedule"]
    complexity_range: [2, 7]
    cache_key: "workflow_task"
    size_estimate: "2.8KB"

MCP_ROUTING_RULES:
  research_chain:
    mcps: ["context7", "tavily", "exa", "sequential-thinking"]
    trigger_patterns: ["research", "investigate", "analyze", "study"]
    optimization: "parallel_search_with_synthesis"
    cache_key: "mcp_research_chain"
    
  implementation_chain:
    mcps: ["desktop-commander", "context7", "sequential-thinking"]
    trigger_patterns: ["implement", "create", "build", "develop"]
    optimization: "sequential_with_validation"
    cache_key: "mcp_implementation_chain"
```