#!/usr/bin/env node
/**
 * 🚀 Augment Code Initialization Script
 * Carrega automaticamente as configurações do VIBECODE V1.0
 */

const fs = require("fs");
const path = require("path");

// Configurações
const PROJECT_ROOT = "E:\\VIBECODE";
const ENV_FILE = path.join(
  PROJECT_ROOT,
  ".cursor",
  "config",
  "environment-complete.env"
);
const AUGMENT_CONFIG = path.join(PROJECT_ROOT, ".augment", "environment.json");

/**
 * Carrega variáveis de ambiente do environment-complete.env
 */
function loadEnvironmentVariables() {
  try {
    if (!fs.existsSync(ENV_FILE)) {
      console.error(`❌ Arquivo não encontrado: ${ENV_FILE}`);
      return false;
    }

    const envContent = fs.readFileSync(ENV_FILE, "utf8");
    const lines = envContent.split("\n");

    let loadedVars = 0;
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (
        trimmedLine &&
        !trimmedLine.startsWith("#") &&
        trimmedLine.includes("=")
      ) {
        const [key, ...valueParts] = trimmedLine.split("=");
        const value = valueParts.join("=").trim();

        if (key && value) {
          process.env[key.trim()] = value;
          loadedVars++;
        }
      }
    }

    console.log(`✅ Carregadas ${loadedVars} variáveis de ambiente`);
    return true;
  } catch (error) {
    console.error(`❌ Erro ao carregar environment: ${error.message}`);
    return false;
  }
}

/**
 * Verifica se as API keys dos MCPs estão configuradas
 */
function verifyMCPKeys() {
  const requiredKeys = [
    "TAVILY_API_KEY",
    "EXA_API_KEY",
    "UPSTASH_CONTEXT7_API_KEY",
    "SENTRY_ACCESS_TOKEN",
  ];

  const missingKeys = [];
  const configuredKeys = [];

  for (const key of requiredKeys) {
    const value = process.env[key];
    if (!value || value.includes("your_") || value.includes("_here")) {
      missingKeys.push(key);
    } else {
      configuredKeys.push(key);
    }
  }

  console.log("\n📊 Status das API Keys MCP:");
  configuredKeys.forEach((key) => {
    console.log(`  ✅ ${key}: CONFIGURADA`);
  });

  missingKeys.forEach((key) => {
    console.log(`  ⚠️ ${key}: PRECISA CONFIGURAR`);
  });

  return missingKeys.length === 0;
}

/**
 * Inicializa configuração do Augment
 */
function initializeAugment() {
  try {
    console.log("🚀 Inicializando Augment Code...");

    // Carregar variáveis de ambiente
    if (!loadEnvironmentVariables()) {
      return false;
    }

    // Verificar API keys
    const allKeysConfigured = verifyMCPKeys();

    // Verificar arquivo de configuração MCP
    const mcpConfigPath = path.join(PROJECT_ROOT, ".augment", "mcp.json");
    const mcpConfigExists = fs.existsSync(mcpConfigPath);

    console.log(
      `\n🔧 Configuração MCP: ${
        mcpConfigExists ? "✅ ENCONTRADA" : "❌ NÃO ENCONTRADA"
      }`
    );

    if (mcpConfigExists && allKeysConfigured) {
      console.log("\n🎯 AUGMENT CODE CONFIGURADO COM SUCESSO!");
      console.log("📋 MCPs Disponíveis:");
      console.log("  • desktop-commander");
      console.log("  • sequential-thinking");
      console.log("  • native-task-management");
      console.log("  • context7-mcp");
      console.log("  • tavily-mcp");
      console.log("  • exa-mcp");
      console.log("  • sentry-mcp");
      return true;
    } else {
      console.log("\n⚠️ CONFIGURAÇÃO INCOMPLETA");
      if (!allKeysConfigured) {
        console.log("  - Configure as API keys em environment-complete.env");
      }
      if (!mcpConfigExists) {
        console.log("  - Arquivo .augment/mcp.json não encontrado");
      }
      return false;
    }
  } catch (error) {
    console.error(`❌ Erro na inicialização: ${error.message}`);
    return false;
  }
}

// Executar inicialização se chamado diretamente
if (require.main === module) {
  initializeAugment();
}

module.exports = {
  loadEnvironmentVariables,
  verifyMCPKeys,
  initializeAugment,
};
