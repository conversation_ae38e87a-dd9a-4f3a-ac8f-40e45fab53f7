# Story 9.4: Anamnese Digital e Integração

## Story Overview

**Como** profissional de saúde da clínica  
**Eu quero** sistema de anamnese digital interativa com formulários personalizáveis  
**Para que** eu possa coletar histórico médico completo de forma estruturada e integrada com todo o sistema

### Story Details

- **Epic**: Epic 9 - Cadastro Pacientes & Prontuário
- **Story Points**: 11
- **Priority**: P0 (Critical)
- **Theme**: Digital Anamnesis & System Integration
- **Dependencies**: Story 9.1, 9.2, 9.3 (Complete Patient System)

### Acceptance Criteria

#### AC1: Anamnese Digital com Formulários Personalizáveis

- [ ] **GIVEN** profissional iniciando consulta com paciente
- [ ] **WHEN** acessa sistema de anamnese digital
- [ ] **THEN** apresenta formulário estruturado por especialidade
- [ ] **AND** permite personalização de campos por profissional
- [ ] **AND** salva respostas automaticamente (auto-save)
- [ ] **AND** valida campos obrigatórios antes de finalizar

#### AC2: Sistema de Questionários Dinâmicos por Especialidade

- [ ] **GIVEN** diferentes especialidades médicas na clínica
- [ ] **WHEN** seleciona especialidade para anamnese
- [ ] **THEN** carrega questionário específico da especialidade
- [ ] **AND** questões condicionais aparecem baseadas em respostas anteriores
- [ ] **AND** permite templates predefinidos e customização
- [ ] **AND** suporte a diferentes tipos de campo (texto, múltipla escolha, escala)

#### AC3: Integração Completa com Histórico de Consultas

- [ ] **GIVEN** paciente com histórico de consultas no sistema
- [ ] **WHEN** inicia nova anamnese
- [ ] **THEN** pré-popula campos com informações do histórico (Epic 6)
- [ ] **AND** mostra evolução de sintomas/condições anteriores
- [ ] **AND** destaca mudanças significativas desde última consulta
- [ ] **AND** permite comparação com anamneses anteriores

#### AC4: Integração com Dados Financeiros e Planos

- [ ] **GIVEN** paciente com plano de saúde cadastrado
- [ ] **WHEN** realiza anamnese
- [ ] **THEN** mostra informações de cobertura do plano (Epic 7)
- [ ] **AND** indica procedimentos cobertos vs particulares
- [ ] **AND** sugere tratamentos baseados em cobertura
- [ ] **AND** registra dados para faturamento posterior

#### AC5: Analytics Médicos e Relatórios de Saúde Populacional

- [ ] **GIVEN** dados de anamneses coletados ao longo do tempo
- [ ] **WHEN** acessa analytics médicos
- [ ] **THEN** gera insights de saúde populacional da clínica
- [ ] **AND** identifica padrões epidemiológicos e tendências
- [ ] **AND** produz relatórios de qualidade do atendimento
- [ ] **AND** suporte a pesquisas médicas anonimizadas

#### AC6: Assinatura Digital e Compliance Médico

- [ ] **GIVEN** anamnese finalizada
- [ ] **WHEN** profissional confirma dados coletados
- [ ] **THEN** aplica assinatura digital na anamnese
- [ ] **AND** gera documento PDF com anamnese completa
- [ ] **AND** integra automaticamente ao prontuário eletrônico
- [ ] **AND** mantém compliance com regulamentações CFM

### Technical Requirements

#### Digital Anamnesis System

```typescript
// Sistema de Anamnese Digital
interface AnamneseDigital {
  id: string
  pacienteId: string
  profissionalId: string
  consultaId?: string
  
  // Dados da Anamnese
  especialidade: EspecialidadeMedica
  templateId: string
  templateVersao: string
  
  // Respostas Estruturadas
  respostas: AnamneseResposta[]
  resumoExecutivo: string
  
  // Análise e Insights
  alertasMedicos: string[]
  recomendacoes: string[]
  mudancasSignificativas: MudancaSignificativa[]
  
  // Comparação com Anamneses Anteriores
  anamneseAnteriorId?: string
  diferencasIdentificadas: DiferencaAnamnese[]
  
  // Status e Workflow
  status: AnamneseStatus
  percentualCompleto: number
  tempoPreenchimento: number // minutos
  
  // Integração com Outros Sistemas
  agendamentoContext: AgendamentoContext
  financeiroContext: FinanceiroContext
  
  // Assinatura e Compliance
  assinaturaDigital?: AssinaturaDigital
  documentoPDF?: string
  compliance: ComplianceFlags
  
  // Auditoria
  iniciadaEm: Date
  finalizadaEm?: Date
  ultimaAtualizacao: Date
  versaoFormulario: string
}

// Template de Anamnese por Especialidade
interface TemplateAnamnese {
  id: string
  nome: string
  especialidade: EspecialidadeMedica
  versao: string
  
  // Estrutura do Formulário
  secoes: SecaoFormulario[]
  camposObrigatorios: string[]
  
  // Lógica Condicional
  regrasCondicionais: RegraCondicional[]
  validacoes: ValidacaoCustomizada[]
  
  // Configurações
  tempoEstimado: number
  nivelComplexidade: 'basico' | 'intermediario' | 'avancado'
  
  // Versionamento
  ativo: boolean
  criadoPor: string
  criadoEm: Date
  aprovadoPor?: string
  aprovadoEm?: Date
}

// Seção do Formulário
interface SecaoFormulario {
  id: string
  titulo: string
  descricao?: string
  ordem: number
  obrigatoria: boolean
  
  // Campos da Seção
  campos: CampoFormulario[]
  
  // Configurações de Exibição
  expansivel: boolean
  visibilidadePadrao: boolean
  icone?: string
  cor?: string
  
  // Condições de Exibição
  condicaoExibicao?: CondicaoExibicao
}

// Campo do Formulário
interface CampoFormulario {
  id: string
  nome: string
  label: string
  descricao?: string
  placeholder?: string
  
  // Tipo e Configuração
  tipo: TipoCampo
  opcoes?: OpcaoCampo[]
  configuracao: ConfiguracaoCampo
  
  // Validação
  obrigatorio: boolean
  validacoes: ValidacaoCampo[]
  
  // Lógica Condicional
  condicoes: CondicaoCampo[]
  
  // Ordem e Layout
  ordem: number
  largura: 'full' | 'half' | 'third' | 'quarter'
  grupoId?: string
}

// Tipos de Campo Suportados
type TipoCampo = 
  | 'texto'
  | 'textarea'
  | 'numero'
  | 'data'
  | 'hora'
  | 'select'
  | 'multiselect'
  | 'radio'
  | 'checkbox'
  | 'escala_likert'
  | 'escala_dor'
  | 'upload_imagem'
  | 'assinatura'
  | 'cid_10'
  | 'medicamento'

// Resposta da Anamnese
interface AnamneseResposta {
  campoId: string
  valor: any
  valorAnterior?: any
  alterado: boolean
  
  // Contexto da Resposta
  respondidoEm: Date
  confianca?: number
  observacoes?: string
  
  // Análise Automática
  categoriaRisco?: 'baixo' | 'medio' | 'alto'
  alertaGerado?: boolean
  requerSegmento?: boolean
}

// Analytics de Saúde Populacional
interface AnalyticsSaudePopulacional {
  // Período de Análise
  periodoInicio: Date
  periodoFim: Date
  totalPacientes: number
  totalAnamneses: number
  
  // Distribuições Demográficas
  distribuicaoIdade: DistribuicaoIdade[]
  distribuicaoGenero: DistribuicaoGenero
  distribuicaoPlanoSaude: DistribuicaoPlano[]
  
  // Indicadores de Saúde
  prevalenciaCondicoes: PrevalenciaCondicao[]
  tendenciasTemporais: TendenciaTemporal[]
  fatoresRisco: FatorRisco[]
  
  // Qualidade do Atendimento
  tempoMedioAnamnese: number
  percentualCompletude: number
  satisfacaoPacientes: number
  
  // Insights Específicos por Especialidade
  insightsPorEspecialidade: Map<EspecialidadeMedica, InsightEspecialidade>
}
```

#### Database Schema for Digital Anamnesis

```sql
-- Templates de Anamnese
CREATE TABLE anamnese_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(255) NOT NULL,
  especialidade especialidade_medica_type NOT NULL,
  versao VARCHAR(10) NOT NULL,
  
  -- Estrutura do Template
  secoes JSONB NOT NULL,
  campos_obrigatorios TEXT[],
  regras_condicionais JSONB DEFAULT '[]',
  validacoes_customizadas JSONB DEFAULT '[]',
  
  -- Configurações
  tempo_estimado INTEGER, -- minutos
  nivel_complexidade complexidade_type DEFAULT 'intermediario',
  
  -- Metadados
  descricao TEXT,
  tags TEXT[],
  categoria VARCHAR(100),
  
  -- Status e Versionamento
  ativo BOOLEAN DEFAULT TRUE,
  aprovado BOOLEAN DEFAULT FALSE,
  
  -- Auditoria
  criado_por UUID NOT NULL REFERENCES auth.users(id),
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  aprovado_por UUID REFERENCES auth.users(id),
  aprovado_em TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT uk_template_nome_versao UNIQUE (nome, versao),
  CONSTRAINT chk_tempo_estimado_positivo CHECK (tempo_estimado > 0)
);

-- Anamneses Digitais
CREATE TABLE anamneses_digitais (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  paciente_id UUID NOT NULL REFERENCES pacientes(id) ON DELETE CASCADE,
  profissional_id UUID NOT NULL REFERENCES profissionais(id),
  consulta_id UUID REFERENCES agendamentos(id),
  prontuario_id UUID NOT NULL REFERENCES prontuarios_eletronicos(id),
  
  -- Template Utilizado
  template_id UUID NOT NULL REFERENCES anamnese_templates(id),
  template_versao VARCHAR(10) NOT NULL,
  especialidade especialidade_medica_type NOT NULL,
  
  -- Dados da Anamnese
  respostas JSONB NOT NULL DEFAULT '[]',
  resumo_executivo TEXT,
  
  -- Análise e Insights
  alertas_medicos TEXT[],
  recomendacoes TEXT[],
  mudancas_significativas JSONB DEFAULT '[]',
  
  -- Comparação
  anamnese_anterior_id UUID REFERENCES anamneses_digitais(id),
  diferencas_identificadas JSONB DEFAULT '[]',
  
  -- Status e Progresso
  status anamnese_status_type DEFAULT 'em_andamento',
  percentual_completo INTEGER DEFAULT 0,
  tempo_preenchimento INTEGER, -- minutos
  
  -- Contexto de Integração
  agendamento_context JSONB,
  financeiro_context JSONB,
  
  -- Assinatura e Compliance
  assinatura_digital JSONB,
  documento_pdf_path TEXT,
  compliance_flags JSONB DEFAULT '{}',
  
  -- Timestamps
  iniciada_em TIMESTAMPTZ DEFAULT NOW(),
  finalizada_em TIMESTAMPTZ,
  ultima_atualizacao TIMESTAMPTZ DEFAULT NOW(),
  
  -- Auditoria
  ip_address INET,
  user_agent TEXT,
  
  CONSTRAINT chk_percentual_valido CHECK (percentual_completo >= 0 AND percentual_completo <= 100),
  CONSTRAINT chk_tempo_positivo CHECK (tempo_preenchimento IS NULL OR tempo_preenchimento >= 0),
  CONSTRAINT chk_finalizada_depois_iniciada CHECK (finalizada_em IS NULL OR finalizada_em >= iniciada_em)
);

-- Respostas Detalhadas da Anamnese
CREATE TABLE anamnese_respostas (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  anamnese_id UUID NOT NULL REFERENCES anamneses_digitais(id) ON DELETE CASCADE,
  
  -- Identificação da Resposta
  campo_id VARCHAR(100) NOT NULL,
  secao_id VARCHAR(100) NOT NULL,
  
  -- Dados da Resposta
  valor JSONB NOT NULL,
  valor_anterior JSONB,
  alterado BOOLEAN DEFAULT FALSE,
  
  -- Contexto
  respondido_em TIMESTAMPTZ DEFAULT NOW(),
  confianca NUMERIC(3,2), -- 0.00 a 1.00
  observacoes TEXT,
  
  -- Análise de Risco
  categoria_risco risco_categoria_type,
  alerta_gerado BOOLEAN DEFAULT FALSE,
  requer_seguimento BOOLEAN DEFAULT FALSE,
  
  -- Versionamento
  versao_resposta INTEGER DEFAULT 1,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT uk_anamnese_campo UNIQUE (anamnese_id, campo_id),
  CONSTRAINT chk_confianca_valida CHECK (confianca IS NULL OR (confianca >= 0 AND confianca <= 1))
);

-- Analytics de Anamnese
CREATE TABLE anamnese_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Período de Análise
  periodo_inicio DATE NOT NULL,
  periodo_fim DATE NOT NULL,
  especialidade especialidade_medica_type,
  
  -- Métricas Gerais
  total_anamneses INTEGER NOT NULL,
  total_pacientes INTEGER NOT NULL,
  tempo_medio_preenchimento NUMERIC(5,2),
  percentual_completude_medio NUMERIC(5,2),
  
  -- Distribuições
  distribuicao_demografica JSONB,
  distribuicao_condicoes JSONB,
  distribuicao_riscos JSONB,
  
  -- Insights e Tendências
  insights_gerados JSONB DEFAULT '[]',
  tendencias_identificadas JSONB DEFAULT '[]',
  alertas_frequentes JSONB DEFAULT '[]',
  
  -- Qualidade
  satisfacao_profissionais NUMERIC(3,2),
  feedback_sistema JSONB DEFAULT '{}',
  
  -- Processamento
  processado_em TIMESTAMPTZ DEFAULT NOW(),
  processado_por UUID REFERENCES auth.users(id),
  versao_algoritmo VARCHAR(20),
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT chk_periodo_valido CHECK (periodo_fim >= periodo_inicio),
  CONSTRAINT chk_metricas_positivas CHECK (total_anamneses >= 0 AND total_pacientes >= 0)
);

-- Configurações Personalizadas por Profissional
CREATE TABLE anamnese_configuracoes_profissional (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profissional_id UUID NOT NULL REFERENCES profissionais(id) ON DELETE CASCADE,
  template_id UUID NOT NULL REFERENCES anamnese_templates(id),
  
  -- Personalizações
  campos_adicionais JSONB DEFAULT '[]',
  campos_ocultos TEXT[],
  ordem_secoes INTEGER[],
  
  -- Configurações de Interface
  auto_save_interval INTEGER DEFAULT 30, -- segundos
  mostrar_comparacao_anterior BOOLEAN DEFAULT TRUE,
  destacar_mudancas BOOLEAN DEFAULT TRUE,
  
  -- Preferências de Análise
  categorias_risco_ativas TEXT[],
  alertas_automaticos_habilitados BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT uk_profissional_template UNIQUE (profissional_id, template_id)
);

-- Tipos Enum
CREATE TYPE especialidade_medica_type AS ENUM (
  'dermatologia', 'cirurgia_plastica', 'medicina_estetica', 
  'fisioterapia_estetica', 'nutricao', 'psicologia', 'odontologia_estetica'
);

CREATE TYPE complexidade_type AS ENUM ('basico', 'intermediario', 'avancado');

CREATE TYPE anamnese_status_type AS ENUM (
  'em_andamento', 'pausada', 'finalizada', 'aprovada', 'cancelada'
);

CREATE TYPE risco_categoria_type AS ENUM ('baixo', 'medio', 'alto', 'critico');

-- Índices para Performance
CREATE INDEX idx_anamneses_paciente ON anamneses_digitais(paciente_id);
CREATE INDEX idx_anamneses_profissional ON anamneses_digitais(profissional_id);
CREATE INDEX idx_anamneses_data_iniciada ON anamneses_digitais(iniciada_em);
CREATE INDEX idx_anamneses_status ON anamneses_digitais(status);
CREATE INDEX idx_anamneses_especialidade ON anamneses_digitais(especialidade);

-- Índices para busca de conteúdo
CREATE INDEX idx_anamnese_respostas_campo ON anamnese_respostas(campo_id);
CREATE INDEX idx_anamnese_respostas_risco ON anamnese_respostas(categoria_risco) WHERE categoria_risco IS NOT NULL;

-- Full-text search em resumos e observações
CREATE INDEX idx_anamnese_resumo_search ON anamneses_digitais USING gin(
  to_tsvector('portuguese', coalesce(resumo_executivo, ''))
);

CREATE INDEX idx_anamnese_observacoes_search ON anamnese_respostas USING gin(
  to_tsvector('portuguese', coalesce(observacoes, ''))
);

-- Índices para analytics
CREATE INDEX idx_analytics_periodo ON anamnese_analytics(periodo_inicio, periodo_fim);
CREATE INDEX idx_analytics_especialidade ON anamnese_analytics(especialidade);
```

#### Digital Anamnesis API

```typescript
// Start Digital Anamnesis API
export async function POST(request: NextRequest) {
  const { pacienteId, profissionalId, consultaId, especialidade, templateId } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    // Verificar permissões
    const { data: permission } = await supabase
      .rpc('check_anamnesis_permission', {
        paciente_id: pacienteId,
        profissional_id: profissionalId,
        user_id: (await supabase.auth.getUser()).data.user?.id
      })
    
    if (!permission) {
      return NextResponse.json({
        error: 'Permissão insuficiente para iniciar anamnese'
      }, { status: 403 })
    }
    
    // Buscar template da especialidade
    const { data: template } = await supabase
      .from('anamnese_templates')
      .select('*')
      .eq('id', templateId)
      .eq('ativo', true)
      .single()
    
    if (!template) {
      return NextResponse.json({
        error: 'Template de anamnese não encontrado'
      }, { status: 404 })
    }
    
    // Buscar anamnese anterior para comparação
    const { data: anamneseAnterior } = await supabase
      .from('anamneses_digitais')
      .select('id, respostas, especialidade')
      .eq('paciente_id', pacienteId)
      .eq('especialidade', especialidade)
      .eq('status', 'finalizada')
      .order('finalizada_em', { ascending: false })
      .limit(1)
    
    // Buscar contexto de integração
    const [agendamentoContext, financeiroContext] = await Promise.all([
      getAgendamentoContext(consultaId),
      getFinanceiroContext(pacienteId)
    ])
    
    // Pré-popular campos com dados existentes
    const respostasIniciais = await prePopulateFromHistory(
      pacienteId, 
      template.secoes, 
      anamneseAnterior[0]?.respostas
    )
    
    // Criar nova anamnese
    const { data: anamnese, error } = await supabase
      .from('anamneses_digitais')
      .insert({
        paciente_id: pacienteId,
        profissional_id: profissionalId,
        consulta_id: consultaId,
        prontuario_id: await getProntuarioId(pacienteId),
        template_id: templateId,
        template_versao: template.versao,
        especialidade: especialidade,
        respostas: respostasIniciais,
        anamnese_anterior_id: anamneseAnterior[0]?.id,
        agendamento_context: agendamentoContext,
        financeiro_context: financeiroContext,
        percentual_completo: calculateInitialCompleteness(respostasIniciais),
        ip_address: getClientIP(request),
        user_agent: request.headers.get('user-agent')
      })
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    // Inserir respostas iniciais
    if (respostasIniciais.length > 0) {
      await insertInitialResponses(anamnese.id, respostasIniciais)
    }
    
    // Configurar auto-save
    await scheduleAutoSave(anamnese.id)
    
    return NextResponse.json({
      anamnese: {
        id: anamnese.id,
        template,
        respostasIniciais,
        anamneseAnterior: anamneseAnterior[0] || null,
        agendamentoContext,
        financeiroContext,
        percentualCompleto: anamnese.percentual_completo
      },
      message: 'Anamnese iniciada com sucesso'
    })
    
  } catch (error) {
    console.error('Error starting anamnesis:', error)
    return NextResponse.json({
      error: 'Erro ao iniciar anamnese digital'
    }, { status: 500 })
  }
}

// Save Anamnesis Response API
export async function PUT(
  request: NextRequest,
  { params }: { params: { anamneseId: string } }
) {
  const { campoId, valor, observacoes } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    // Buscar anamnese atual
    const { data: anamnese } = await supabase
      .from('anamneses_digitais')
      .select('*')
      .eq('id', params.anamneseId)
      .single()
    
    if (!anamnese || anamnese.status === 'finalizada') {
      return NextResponse.json({
        error: 'Anamnese não encontrada ou já finalizada'
      }, { status: 400 })
    }
    
    // Buscar resposta anterior se existir
    const { data: respostaAnterior } = await supabase
      .from('anamnese_respostas')
      .select('valor')
      .eq('anamnese_id', params.anamneseId)
      .eq('campo_id', campoId)
      .single()
    
    const valorAnterior = respostaAnterior?.valor
    const alterado = !isEqual(valor, valorAnterior)
    
    // Analisar resposta para categorização de risco
    const analiseRisco = await analyzeResponseRisk(campoId, valor, anamnese.especialidade)
    
    // Salvar/atualizar resposta
    const { error } = await supabase
      .from('anamnese_respostas')
      .upsert({
        anamnese_id: params.anamneseId,
        campo_id: campoId,
        secao_id: getCampoSecaoId(campoId, anamnese.template_id),
        valor,
        valor_anterior: valorAnterior,
        alterado,
        observacoes,
        categoria_risco: analiseRisco.categoria,
        alerta_gerado: analiseRisco.alertaGerado,
        requer_seguimento: analiseRisco.requerSeguimento,
        confianca: analiseRisco.confianca
      })
    
    if (error) {
      throw error
    }
    
    // Atualizar percentual de completude
    const novoPercentual = await calculateCompleteness(params.anamneseId)
    
    // Atualizar anamnese principal
    await supabase
      .from('anamneses_digitais')
      .update({
        percentual_completo: novoPercentual,
        ultima_atualizacao: new Date(),
        alertas_medicos: analiseRisco.alertaGerado ? 
          [...(anamnese.alertas_medicos || []), analiseRisco.alertaDescricao] : 
          anamnese.alertas_medicos
      })
      .eq('id', params.anamneseId)
    
    // Verificar se deve gerar alertas
    if (analiseRisco.alertaGerado) {
      await generateMedicalAlert(params.anamneseId, analiseRisco)
    }
    
    // Verificar regras condicionais
    const camposCondicionais = await evaluateConditionalFields(
      params.anamneseId, 
      campoId, 
      valor
    )
    
    return NextResponse.json({
      success: true,
      percentualCompleto: novoPercentual,
      analiseRisco: {
        categoria: analiseRisco.categoria,
        alertaGerado: analiseRisco.alertaGerado,
        requerSeguimento: analiseRisco.requerSeguimento
      },
      camposCondicionais,
      message: 'Resposta salva com sucesso'
    })
    
  } catch (error) {
    console.error('Error saving anamnesis response:', error)
    return NextResponse.json({
      error: 'Erro ao salvar resposta da anamnese'
    }, { status: 500 })
  }
}

// Finalize Digital Anamnesis
export async function finalizeAnamnesis(anamneseId: string) {
  const supabase = createServerClient()
  
  try {
    // Buscar anamnese completa
    const { data: anamnese } = await supabase
      .from('anamneses_digitais')
      .select(`
        *,
        respostas:anamnese_respostas(*),
        template:anamnese_templates(*),
        paciente:pacientes(*),
        profissional:profissionais(*)
      `)
      .eq('id', anamneseId)
      .single()
    
    if (!anamnese) {
      throw new Error('Anamnese não encontrada')
    }
    
    // Validar campos obrigatórios
    const validacao = await validateRequiredFields(anamnese)
    if (!validacao.valid) {
      return {
        error: 'Campos obrigatórios não preenchidos',
        camposFaltantes: validacao.camposFaltantes
      }
    }
    
    // Gerar resumo executivo
    const resumoExecutivo = await generateExecutiveSummary(anamnese)
    
    // Identificar mudanças significativas
    const mudancasSignificativas = await identifySignificantChanges(anamnese)
    
    // Gerar recomendações baseadas em IA
    const recomendacoes = await generateAIRecommendations(anamnese)
    
    // Gerar assinatura digital
    const assinaturaDigital = await generateDigitalSignature({
      anamneseId,
      profissionalId: anamnese.profissional_id,
      content: { resumoExecutivo, respostas: anamnese.respostas },
      timestamp: new Date()
    })
    
    // Gerar documento PDF
    const documentoPDF = await generateAnamnesePDF(anamnese, resumoExecutivo)
    
    // Finalizar anamnese
    const { error } = await supabase
      .from('anamneses_digitais')
      .update({
        status: 'finalizada',
        percentual_completo: 100,
        resumo_executivo: resumoExecutivo,
        mudancas_significativas: mudancasSignificativas,
        recomendacoes: recomendacoes,
        assinatura_digital: assinaturaDigital,
        documento_pdf_path: documentoPDF.path,
        finalizada_em: new Date(),
        tempo_preenchimento: calculateTotalTime(anamnese.iniciada_em)
      })
      .eq('id', anamneseId)
    
    if (error) {
      throw error
    }
    
    // Integrar com prontuário eletrônico
    await integrateToProntuario(anamneseId, anamnese.prontuario_id)
    
    // Atualizar contexto em outros sistemas
    await Promise.all([
      // Epic 6: Atualizar contexto médico na agenda
      updateSchedulingMedicalContext(anamnese.paciente_id, resumoExecutivo),
      // Epic 7: Sincronizar dados para faturamento
      syncAnamnesisForBilling(anamneseId, anamnese.consulta_id),
      // Epic 5: Notificar paciente sobre finalização
      notifyPatientAnamnesisComplete(anamnese.paciente_id)
    ])
    
    // Gerar insights para analytics
    await generateAnamnesisInsights(anamneseId)
    
    return {
      success: true,
      anamneseId,
      resumoExecutivo,
      documentoPDFUrl: await getSecureDocumentUrl(documentoPDF.path),
      mudancasSignificativas,
      recomendacoes,
      tempoTotal: calculateTotalTime(anamnese.iniciada_em)
    }
    
  } catch (error) {
    console.error('Error finalizing anamnesis:', error)
    throw new Error('Erro ao finalizar anamnese digital')
  }
}

// Population Health Analytics
export async function generatePopulationHealthAnalytics(
  periodoInicio: Date,
  periodoFim: Date,
  especialidade?: EspecialidadeMedica
) {
  const supabase = createServerClient()
  
  try {
    // Query base para anamneses no período
    let query = supabase
      .from('anamneses_digitais')
      .select(`
        *,
        respostas:anamnese_respostas(*),
        paciente:pacientes(data_nascimento, genero, plano_saude_nome)
      `)
      .gte('finalizada_em', periodoInicio.toISOString())
      .lte('finalizada_em', periodoFim.toISOString())
      .eq('status', 'finalizada')
    
    if (especialidade) {
      query = query.eq('especialidade', especialidade)
    }
    
    const { data: anamneses } = await query
    
    if (!anamneses.length) {
      return {
        error: 'Nenhuma anamnese encontrada no período especificado'
      }
    }
    
    // Processamento de analytics
    const analytics = {
      // Métricas Gerais
      totalAnamneses: anamneses.length,
      totalPacientes: new Set(anamneses.map(a => a.paciente_id)).size,
      tempoMedioPreenchimento: calculateAverageTime(anamneses),
      percentualCompletudeMedia: calculateAverageCompleteness(anamneses),
      
      // Distribuições Demográficas
      distribuicaoIdade: calculateAgeDistribution(anamneses),
      distribuicaoGenero: calculateGenderDistribution(anamneses),
      distribuicaoPlanoSaude: calculateInsuranceDistribution(anamneses),
      
      // Indicadores de Saúde
      prevalenciaCondicoes: calculateConditionPrevalence(anamneses),
      tendenciasTemporais: calculateTemporalTrends(anamneses),
      fatoresRisco: calculateRiskFactors(anamneses),
      
      // Insights por Especialidade
      insightsPorEspecialidade: especialidade ? 
        calculateSpecialtyInsights(anamneses, especialidade) : 
        calculateAllSpecialtyInsights(anamneses),
      
      // Qualidade do Atendimento
      indicadoresQualidade: calculateQualityIndicators(anamneses),
      
      // Alertas e Padrões
      alertasFrequentes: identifyFrequentAlerts(anamneses),
      padroesSazonais: identifySeasonalPatterns(anamneses)
    }
    
    // Salvar analytics no banco
    await supabase
      .from('anamnese_analytics')
      .insert({
        periodo_inicio: periodoInicio,
        periodo_fim: periodoFim,
        especialidade,
        total_anamneses: analytics.totalAnamneses,
        total_pacientes: analytics.totalPacientes,
        tempo_medio_preenchimento: analytics.tempoMedioPreenchimento,
        percentual_completude_medio: analytics.percentualCompletudeMedia,
        distribuicao_demografica: {
          idade: analytics.distribuicaoIdade,
          genero: analytics.distribuicaoGenero,
          planoSaude: analytics.distribuicaoPlanoSaude
        },
        distribuicao_condicoes: analytics.prevalenciaCondicoes,
        distribuicao_riscos: analytics.fatoresRisco,
        insights_gerados: analytics.insightsPorEspecialidade,
        tendencias_identificadas: analytics.tendenciasTemporais,
        alertas_frequentes: analytics.alertasFrequentes,
        versao_algoritmo: 'v1.0'
      })
    
    return analytics
    
  } catch (error) {
    console.error('Error generating population health analytics:', error)
    throw new Error('Erro ao gerar analytics de saúde populacional')
  }
}
```

### Integration Points

#### Complete Epic Integration (1-8)

- **Epic 5 (Portal)**: Pacientes podem iniciar anamnese pelo portal
- **Epic 6 (Agenda)**: Contexto médico integrado no agendamento  
- **Epic 7 (Financeiro)**: Dados para faturamento e cobertura de planos
- **Epic 8 (BI)**: Analytics médicos integrados aos dashboards executivos

#### External Medical Standards

- **CID-10**: Classificação automática de condições médicas
- **CBHPM**: Códigos para procedimentos e faturamento
- **TISS**: Padrão para comunicação com planos de saúde
- **HL7 FHIR**: Interoperabilidade com sistemas externos

### Testing Strategy

#### Digital Anamnesis Tests

```typescript
describe('Digital Anamnesis System', () => {
  test('creates dynamic form based on specialty template', async () => {
    const template = await createDermatologyTemplate()
    
    const anamnesis = await startAnamnesis({
      pacienteId: 'test-patient',
      especialidade: 'dermatologia',
      templateId: template.id
    })
    
    expect(anamnesis.template.especialidade).toBe('dermatologia')
    expect(anamnesis.template.secoes).toHaveLength(6) // Expected sections
  })
  
  test('pre-populates fields from previous anamnesis', async () => {
    const previousAnamnesis = await createPreviousAnamnesis()
    
    const newAnamnesis = await startAnamnesis({
      pacienteId: previousAnamnesis.paciente_id,
      especialidade: previousAnamnesis.especialidade
    })
    
    expect(newAnamnesis.respostasIniciais.length).toBeGreaterThan(0)
    expect(newAnamnesis.anamneseAnterior).toBeDefined()
  })
  
  test('triggers medical alerts based on responses', async () => {
    const anamnesis = await createTestAnamnesis()
    
    await saveAnamnesisResponse(anamnesis.id, {
      campoId: 'diabetes',
      valor: { tipo: 'tipo_1', controlado: false }
    })
    
    const updatedAnamnesis = await getAnamnesis(anamnesis.id)
    expect(updatedAnamnesis.alertas_medicos).toContain('Diabetes não controlado')
  })
  
  test('generates executive summary on completion', async () => {
    const anamnesis = await createCompleteAnamnesis()
    
    const result = await finalizeAnamnesis(anamnesis.id)
    
    expect(result.success).toBe(true)
    expect(result.resumoExecutivo).toBeDefined()
    expect(result.documentoPDFUrl).toBeDefined()
  })
})
```

#### Population Health Analytics Tests

```typescript
describe('Population Health Analytics', () => {
  test('generates meaningful health insights', async () => {
    await createMultipleAnamneses(50) // Create test data
    
    const analytics = await generatePopulationHealthAnalytics(
      subMonths(new Date(), 3),
      new Date(),
      'dermatologia'
    )
    
    expect(analytics.totalAnamneses).toBe(50)
    expect(analytics.prevalenciaCondicoes).toBeDefined()
    expect(analytics.fatoresRisco.length).toBeGreaterThan(0)
  })
  
  test('identifies temporal trends correctly', async () => {
    const analytics = await generatePopulationHealthAnalytics(
      subMonths(new Date(), 12),
      new Date()
    )
    
    expect(analytics.tendenciasTemporais).toBeDefined()
    expect(analytics.padroesSazonais).toBeDefined()
  })
})
```

### Dev Notes

#### AI and ML Integration

- **Natural Language Processing**: Análise de texto para extração de insights
- **Risk Assessment**: Algoritmos para categorização automática de risco
- **Pattern Recognition**: Identificação de padrões epidemiológicos
- **Recommendation Engine**: Sugestões baseadas em evidências médicas

#### Medical Compliance

- **CFM Standards**: Conformidade total com regulamentações médicas
- **Data Privacy**: Proteção especial para dados sensíveis de saúde
- **Digital Signatures**: Assinatura digital para validade legal
- **Audit Trail**: Rastro completo para auditoria médica

#### Performance and Scalability

- **Auto-Save**: Salvamento automático para prevenir perda de dados
- **Progressive Loading**: Carregamento progressivo de seções grandes
- **Caching**: Cache inteligente para templates e configurações
- **Analytics Processing**: Background jobs para analytics pesados

---

## Dev Agent Record

### Task Status
- [x] Analyzed Epic 9 requirements for digital anamnesis system
- [x] Created comprehensive story with 6 acceptance criteria
- [x] Designed TypeScript interfaces for anamnesis system
- [x] Specified database schema with full medical compliance
- [x] Developed complete API endpoints for anamnesis workflow
- [x] Integrated with Epic 5-8 systems for seamless workflow
- [x] Added population health analytics capabilities
- [x] Included medical compliance (CFM, LGPD, digital signatures)
- [x] Created comprehensive testing strategy
- [x] Added AI/ML integration for medical insights

### File List
- `docs/stories/9.4.story.md` - Digital Anamnesis implementation story

### Change Log
- **Story 9.4 Creation**: Complete digital anamnesis system with dynamic forms, medical compliance, population health analytics, and integration with all Epic systems
- **Medical Standards**: Full CFM compliance, digital signatures, LGPD protection
- **Epic Integration**: Seamless integration with Portal (Epic 5), Scheduling (Epic 6), Financial (Epic 7), and BI (Epic 8)
- **AI/ML Features**: Risk assessment, pattern recognition, recommendation engine
- **Testing Strategy**: Comprehensive tests for all anamnesis workflows

### Completion Notes
Epic 9 is now **COMPLETE** with all 4 stories:
- 9.1: Patient Registration System ✅
- 9.2: Electronic Medical Records ✅ 
- 9.3: Document Management ✅
- 9.4: Digital Anamnesis & Integration ✅

Epic 9 delivers a complete medical records system with LGPD compliance, digital signatures, population health analytics, and seamless integration with the entire NeonPro platform.

### Epic Status
**Epic 9 - Cadastro Pacientes & Prontuário**: ✅ **COMPLETE** - Medical management system ready for implementation
