"""
VIBECODE-Kiro Sync System
========================

Automatic synchronization system between VIBECODE rules (E:\VIBECODE\.cursor) 
and Kiro configuration (.kiro).

Version: 1.0.0
Author: Kiro System
"""

__version__ = "1.0.0"
__author__ = "Kiro System"

from .core.sync_engine import SyncEngine
from .core.file_monitor import FileSystemMonitor
from .core.rule_adapter import RuleAdaptationEngine
from .core.conflict_resolver import ConflictResolver
from .core.backup_manager import BackupManager
from .api.sync_api import SyncAPI
from .config.sync_config import SyncConfig

__all__ = [
    'SyncEngine',
    'FileSystemMonitor', 
    'RuleAdaptationEngine',
    'ConflictResolver',
    'BackupManager',
    'SyncAPI',
    'SyncConfig'
]