// NeonPro Service Worker - VIBECODE V1.0 Performance Standards
// PWA Implementation with offline-first strategies
// Target: API p95 ≤ 800ms, Page Load p95 ≤ 300ms

const CACHE_VERSION = '1.0.0'
const STATIC_CACHE = `neonpro-static-v${CACHE_VERSION}`
const DYNAMIC_CACHE = `neonpro-dynamic-v${CACHE_VERSION}`
const API_CACHE = `neonpro-api-v${CACHE_VERSION}`

// Critical resources for offline functionality
const STATIC_CACHE_URLS = [
  '/',
  '/dashboard',
  '/login',
  '/signup',
  '/offline',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
]

// API endpoints to cache for offline access
const CACHEABLE_API_PATTERNS = [
  /\/api\/professionals$/,
  /\/api\/service-types$/,
  /\/api\/settings$/,
]

// Network-first patterns (real-time data)
const NETWORK_FIRST_PATTERNS = [
  /\/api\/appointments/,
  /\/api\/patients\/[^/]+$/,
  /\/api\/auth/,
]

// Install Event - Cache static assets
self.addEventListener('install', (event) => {
  console.log('🚀 NeonPro SW: Installing v' + CACHE_VERSION)
  
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('📦 Caching static resources...')
        return cache.addAll(STATIC_CACHE_URLS)
      }),
      self.skipWaiting()
    ])
  )
})

// Activate Event - Clean old caches and take control
self.addEventListener('activate', (event) => {
  console.log('✅ NeonPro SW: Activating v' + CACHE_VERSION)
  
  event.waitUntil(
    Promise.all([
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (!cacheName.includes(CACHE_VERSION)) {
              console.log('🗑️ Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      }),
      self.clients.claim()
    ])
  )
})

// Fetch Event - Intelligent caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests and external requests
  if (request.method !== 'GET' || url.origin !== self.location.origin) {
    return
  }

  event.respondWith(handleRequest(request))
})

// Main request handler with strategy selection
async function handleRequest(request) {
  const url = new URL(request.url)
  
  try {
    // Static assets - Cache First
    if (isStaticAsset(url.pathname)) {
      return await cacheFirst(request, STATIC_CACHE)
    }
    
    // API requests - Specific strategies
    if (url.pathname.startsWith('/api/')) {
      return await handleApiRequest(request)
    }
    
    // Pages - Network First with cache fallback
    return await networkFirst(request, DYNAMIC_CACHE)
    
  } catch (error) {
    console.error('🚨 Request failed:', error)
    return await handleOfflineRequest(request)
  }
}

// Cache First Strategy (static assets)
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    console.log('📦 Cache hit:', request.url)
    
    // Update cache in background for fresh content
    fetch(request).then((response) => {
      if (response.ok) cache.put(request, response.clone())
    }).catch(() => {})
    
    return cachedResponse
  }
  
  console.log('🌐 Cache miss:', request.url)
  const response = await fetch(request)
  
  if (response.ok) {
    cache.put(request, response.clone())
  }
  
  return response
}

// Network First Strategy (dynamic content)
async function networkFirst(request, cacheName) {
  const cache = await caches.open(cacheName)
  
  try {
    const response = await fetch(request)
    
    if (response.ok) {
      cache.put(request, response.clone())
    }
    
    return response
    
  } catch (error) {
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      console.log('📦 Offline fallback:', request.url)
      return cachedResponse
    }
    
    throw error
  }
}

// Handle API requests with specific strategies
async function handleApiRequest(request) {
  const url = new URL(request.url)
  
  // Authentication - Always network
  if (url.pathname.startsWith('/api/auth/')) {
    return await fetch(request)
  }
  
  // Cacheable APIs - Cache first
  if (isCacheableApi(url.pathname)) {
    return await cacheFirst(request, API_CACHE)
  }
  
  // Real-time APIs - Network first
  return await networkFirst(request, API_CACHE)
}

// Handle offline requests
async function handleOfflineRequest(request) {
  const url = new URL(request.url)
  
  // Navigation requests - Return offline page
  if (request.mode === 'navigate') {
    const cache = await caches.open(STATIC_CACHE)
    const offlinePage = await cache.match('/offline')
    return offlinePage || new Response('Offline', { status: 503 })
  }
  
  // Try to find cached version
  const cacheNames = [STATIC_CACHE, DYNAMIC_CACHE, API_CACHE]
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
  }
  
  return new Response('Offline', { status: 503 })
}

// Utility functions
function isStaticAsset(pathname) {
  return /\.(js|css|png|jpg|jpeg|svg|ico|woff|woff2)$/.test(pathname) ||
         STATIC_CACHE_URLS.includes(pathname)
}

function isCacheableApi(pathname) {
  return CACHEABLE_API_PATTERNS.some(pattern => pattern.test(pathname))
}

// Push notification event handler
self.addEventListener('push', (event) => {
  console.log('[SW] Push received:', event)

  let notificationData = {
    title: 'NeonPro',
    body: 'Você tem uma nova notificação',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'default',
    data: {
      url: '/dashboard'
    },
    actions: [
      {
        action: 'open',
        title: 'Abrir',
        icon: '/icons/open-action.png'
      },
      {
        action: 'close',
        title: 'Fechar'
      }
    ]
  }

  if (event.data) {
    try {
      const pushData = event.data.json()
      notificationData = {
        ...notificationData,
        ...pushData
      }
    } catch (error) {
      console.error('[SW] Error parsing push data:', error)
    }
  }

  const notificationPromise = self.registration.showNotification(
    notificationData.title,
    {
      body: notificationData.body,
      icon: notificationData.icon,
      badge: notificationData.badge,
      tag: notificationData.tag,
      data: notificationData.data,
      actions: notificationData.actions,
      requireInteraction: notificationData.requireInteraction || false,
      silent: notificationData.silent || false,
      vibrate: notificationData.vibrate || [200, 100, 200],
      timestamp: Date.now()
    }
  )

  event.waitUntil(notificationPromise)
})

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event)

  const notification = event.notification
  const action = event.action
  const data = notification.data || {}

  notification.close()

  if (action === 'close') {
    return
  }

  // Default action or 'open' action
  let targetUrl = data.url || '/dashboard'

  // Handle different notification types
  if (data.type) {
    switch (data.type) {
      case 'appointment_reminder':
      case 'appointment_confirmation':
      case 'appointment_cancellation':
        targetUrl = `/dashboard/appointments${data.appointmentId ? `/${data.appointmentId}` : ''}`
        break
      case 'payment_due':
      case 'payment_received':
        targetUrl = `/dashboard/billing${data.invoiceId ? `/${data.invoiceId}` : ''}`
        break
      case 'system_notification':
        targetUrl = data.url || '/dashboard/notifications'
        break
      default:
        targetUrl = data.url || '/dashboard'
    }
  }

  const urlPromise = clients.matchAll({
    type: 'window',
    includeUncontrolled: true
  }).then((clientList) => {
    // Check if the app is already open
    for (let i = 0; i < clientList.length; i++) {
      const client = clientList[i]
      const clientUrl = new URL(client.url)
      
      if (clientUrl.origin === self.location.origin) {
        // Focus existing window and navigate to target URL
        return client.focus().then(() => {
          return client.navigate(targetUrl)
        })
      }
    }

    // Open new window if app is not open
    return clients.openWindow(targetUrl)
  })

  event.waitUntil(urlPromise)
})

// Notification close handler
self.addEventListener('notificationclose', (event) => {
  console.log('[SW] Notification closed:', event)

  const notification = event.notification
  const data = notification.data || {}

  // Track notification closure analytics if needed
  if (data.trackClose) {
    // Could send analytics data here
    console.log('[SW] Tracking notification close for:', data.type)
  }
})

// Background sync handler (for offline actions)
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync:', event.tag)

  if (event.tag === 'sync-notifications') {
    const syncPromise = syncPendingNotifications()
    event.waitUntil(syncPromise)
  }
})

// Helper function to sync pending notifications when back online
async function syncPendingNotifications() {
  try {
    console.log('[SW] Syncing pending notifications...')

    // Get pending notifications from IndexedDB or local storage
    const pendingNotifications = await getPendingNotifications()

    for (const notification of pendingNotifications) {
      try {
        // Attempt to sync with server
        const response = await fetch('/api/notifications/sync', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(notification)
        })

        if (response.ok) {
          // Remove from pending list
          await removePendingNotification(notification.id)
        }
      } catch (error) {
        console.error('[SW] Error syncing notification:', error)
      }
    }

    console.log('[SW] Notification sync completed')
  } catch (error) {
    console.error('[SW] Error during notification sync:', error)
  }
}

// Helper functions for managing pending notifications
async function getPendingNotifications() {
  // This would typically use IndexedDB
  // For now, returning empty array
  return []
}

async function removePendingNotification(notificationId) {
  // This would typically remove from IndexedDB
  console.log('[SW] Removing pending notification:', notificationId)
}

// Handle messages from main thread
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data)

  const { type, data } = event.data

  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
    
    case 'GET_VERSION':
      event.ports[0].postMessage({
        type: 'VERSION',
        version: CACHE_NAME
      })
      break

    case 'CLEAR_CACHE':
      caches.delete(CACHE_NAME).then(() => {
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED',
          success: true
        })
      })
      break

    case 'UPDATE_NOTIFICATION_PREFERENCES':
      // Handle notification preference updates
      updateNotificationPreferences(data)
      break

    default:
      console.log('[SW] Unknown message type:', type)
  }
})

async function updateNotificationPreferences(preferences) {
  console.log('[SW] Updating notification preferences:', preferences)
  // Store preferences for offline use
  try {
    const cache = await caches.open('preferences')
    await cache.put(
      '/preferences/notifications',
      new Response(JSON.stringify(preferences))
    )
  } catch (error) {
    console.error('[SW] Error storing preferences:', error)
  }
}

console.log('[SW] Service Worker loaded successfully')