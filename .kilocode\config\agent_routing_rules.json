{"agent_routing_rules": {"version": "1.0.0", "last_updated": "2025-01-07", "routing_strategy": "hybrid_intelligent", "agents": {"TechnicalArchitect": {"model": "anthropic/claude-sonnet-4", "complexity_range": {"min": 6, "max": 10, "optimal": [8, 9, 10]}, "domain_expertise": {"primary": ["software_architecture", "system_design", "code_architecture", "design_patterns", "technical_specifications"], "secondary": ["performance_optimization", "scalability_analysis", "security_architecture", "integration_patterns"]}, "routing_keywords": {"high_priority": ["architecture", "design", "pattern", "structure", "framework", "system", "technical", "specification", "blueprint", "model"], "medium_priority": ["optimize", "refactor", "improve", "enhance", "scale", "performance", "security", "integration", "api"], "low_priority": ["analyze", "review", "evaluate", "assess", "plan"]}, "exclusion_patterns": ["simple file operations", "basic research queries", "routine maintenance tasks", "low-complexity implementations"], "performance_targets": {"response_time_ms": 3000, "quality_threshold": 8.5, "complexity_handling": "advanced"}}, "OperationsCoordinator": {"model": "google/gemini-2.5-pro", "complexity_range": {"min": 1, "max": 7, "optimal": [3, 4, 5, 6]}, "domain_expertise": {"primary": ["task_coordination", "workflow_management", "file_operations", "system_administration", "process_execution"], "secondary": ["deployment_coordination", "resource_management", "monitoring_setup", "automation_scripts"]}, "routing_keywords": {"high_priority": ["execute", "run", "deploy", "coordinate", "manage", "organize", "schedule", "automate", "monitor", "control"], "medium_priority": ["file", "directory", "folder", "create", "copy", "move", "delete", "backup", "sync", "upload"], "low_priority": ["list", "check", "status", "info", "details"]}, "exclusion_patterns": ["complex architectural decisions", "deep technical analysis", "research-heavy tasks", "quality assessment tasks"], "performance_targets": {"response_time_ms": 1500, "quality_threshold": 7.5, "complexity_handling": "standard"}}, "ResearchStrategist": {"model": "google/gemini-flash", "complexity_range": {"min": 3, "max": 8, "optimal": [4, 5, 6, 7]}, "domain_expertise": {"primary": ["information_research", "web_search", "data_analysis", "competitive_analysis", "market_research"], "secondary": ["documentation_review", "trend_analysis", "technology_research", "best_practices_research"]}, "routing_keywords": {"high_priority": ["research", "search", "find", "investigate", "analyze", "study", "explore", "discover", "gather", "collect"], "medium_priority": ["information", "data", "documentation", "resources", "sources", "references", "examples", "samples", "tutorials", "guides"], "low_priority": ["web", "internet", "online", "website", "url", "link"]}, "exclusion_patterns": ["complex system design", "file manipulation tasks", "deployment operations", "quality gate enforcement"], "performance_targets": {"response_time_ms": 2000, "quality_threshold": 7.0, "complexity_handling": "research_optimized"}}, "QualityGuardian": {"model": "anthropic/claude-sonnet-4", "complexity_range": {"min": 2, "max": 9, "optimal": [6, 7, 8, 9]}, "domain_expertise": {"primary": ["quality_assurance", "code_review", "testing_strategy", "validation_protocols", "quality_metrics"], "secondary": ["performance_testing", "security_auditing", "compliance_checking", "best_practices_enforcement"]}, "routing_keywords": {"high_priority": ["quality", "test", "verify", "validate", "check", "review", "audit", "assess", "evaluate", "ensure"], "medium_priority": ["bug", "error", "issue", "problem", "fix", "debug", "troubleshoot", "diagnose", "resolve"], "low_priority": ["standard", "compliance", "guideline", "rule", "policy"]}, "exclusion_patterns": ["simple research tasks", "basic file operations", "routine deployment tasks", "low-stakes implementations"], "performance_targets": {"response_time_ms": 2500, "quality_threshold": 9.0, "complexity_handling": "quality_focused"}}}, "routing_algorithms": {"complexity_based": {"enabled": true, "weight": 0.4, "algorithm": "threshold_matching", "parameters": {"strict_boundaries": false, "overlap_tolerance": 1, "fallback_strategy": "nearest_capable"}}, "domain_expertise": {"enabled": true, "weight": 0.3, "algorithm": "keyword_matching", "parameters": {"primary_domain_boost": 2.0, "secondary_domain_boost": 1.5, "keyword_density_threshold": 0.1}}, "load_balancing": {"enabled": true, "weight": 0.2, "algorithm": "least_loaded", "parameters": {"max_concurrent_tasks": 3, "queue_timeout_seconds": 30, "priority_override": true}}, "agent_affinity": {"enabled": true, "weight": 0.1, "algorithm": "preference_scoring", "parameters": {"history_weight": 0.3, "success_rate_boost": 1.2, "failure_penalty": 0.8}}}, "routing_decisions": {"confidence_threshold": 0.7, "multi_agent_threshold": 0.5, "fallback_rules": {"no_suitable_agent": {"action": "route_to_technical_architect", "reason": "TechnicalArchitect has broadest capability range"}, "tie_breaking": {"action": "route_to_highest_quality", "reason": "Quality is priority in uncertain situations"}, "overloaded_agents": {"action": "queue_or_redistribute", "parameters": {"queue_max_size": 5, "redistribution_preference": "similar_capability"}}}}, "collaboration_patterns": {"multi_agent_tasks": {"enabled": true, "coordination_agent": "OperationsCoordinator", "patterns": {"research_then_architect": {"sequence": ["ResearchStrategist", "TechnicalArchitect"], "handoff_criteria": "research_complete", "shared_context": true}, "architect_then_execute": {"sequence": ["TechnicalArchitect", "OperationsCoordinator"], "handoff_criteria": "design_approved", "shared_context": true}, "execute_then_validate": {"sequence": ["OperationsCoordinator", "<PERSON><PERSON><PERSON><PERSON>"], "handoff_criteria": "execution_complete", "shared_context": true}, "full_pipeline": {"sequence": ["ResearchStrategist", "TechnicalArchitect", "OperationsCoordinator", "<PERSON><PERSON><PERSON><PERSON>"], "handoff_criteria": "stage_complete", "shared_context": true}}}, "parallel_execution": {"enabled": true, "max_parallel_agents": 2, "coordination_required": true, "conflict_resolution": "priority_based"}}, "performance_optimization": {"caching": {"agent_selection_cache": {"enabled": true, "cache_duration_seconds": 300, "cache_key_factors": ["complexity", "keywords", "domain"]}, "routing_decision_cache": {"enabled": true, "cache_duration_seconds": 600, "invalidation_triggers": ["agent_load_change", "performance_degradation"]}}, "pre_computation": {"keyword_analysis": {"enabled": true, "analysis_depth": "medium", "caching_enabled": true}, "complexity_calculation": {"enabled": true, "algorithm": "enhanced_keyword_weighted", "caching_enabled": true}}}, "monitoring_and_feedback": {"routing_metrics": {"success_rate_tracking": true, "response_time_tracking": true, "quality_score_tracking": true, "agent_utilization_tracking": true}, "feedback_loops": {"success_feedback": {"enabled": true, "boost_factor": 1.1, "decay_rate": 0.95}, "failure_feedback": {"enabled": true, "penalty_factor": 0.9, "recovery_rate": 1.05}}, "adaptive_learning": {"enabled": true, "learning_rate": 0.1, "adaptation_frequency": "daily", "parameters_to_adapt": ["complexity_thresholds", "keyword_weights", "domain_priorities"]}}, "debugging_and_testing": {"routing_simulation": {"enabled": true, "test_scenarios": ["simple_task_routing", "complex_architectural_task", "research_heavy_task", "quality_assurance_task", "multi_agent_coordination"]}, "routing_explanation": {"enabled": true, "detail_level": "comprehensive", "include_alternatives": true, "include_confidence_scores": true}}}}