{"augmentTaskManagement": {"version": "1.0.0", "type": "native", "status": "active", "lastUpdated": "2025-07-16T00:00:00Z"}, "integration": {"cursorSync": {"enabled": true, "authority": "cursor", "syncInterval": "realtime", "conflictResolution": "cursor_wins"}, "memoryBank": {"enabled": true, "storageFile": "memory-bank/task-storage.md", "configFile": "memory-bank/task-config.json", "autoUpdate": true, "persistAcrossSessions": true}, "knowledgeGraph": {"enabled": true, "updateOnTaskComplete": true, "learningEnabled": true, "patternRecognition": true}}, "features": {"nativeTaskManagement": {"enabled": true, "autoDetection": true, "complexityRouting": true, "batchUpdates": true, "workflowCoordination": true}, "intelligentRouting": {"enabled": true, "complexityThreshold": 3, "useSequentialThinking": true, "memoryBankFallback": true}, "crossPlatformSync": {"enabled": true, "cursorIntegration": true, "memoryPersistence": true, "sessionContinuity": true}}, "routing": {"byComplexity": {"simple": {"range": "1-4", "handler": "augment_native", "tools": ["native_task_management", "memory_bank"], "autoComplete": true}, "medium": {"range": "5-7", "handler": "augment_sequential", "tools": ["sequential_thinking", "native_task_management", "memory_bank"], "planningRequired": false}, "complex": {"range": "8-10", "handler": "augment_full", "tools": ["sequential_thinking", "native_task_management", "memory_bank", "knowledge_graph"], "planningRequired": true}}, "byType": {"planning": "augment_sequential", "implementation": "augment_native", "research": "augment_full", "coordination": "augment_sequential", "documentation": "augment_native"}}, "automation": {"autoTaskDetection": {"enabled": true, "keywords": ["planejar", "organizar", "implementar", "coordenar"], "complexityIndicators": ["arquitetura", "sistema", "integração"], "multiStepIndicators": ["primeiro", "segundo", "depois", "então"]}, "batchOperations": {"enabled": true, "workflowTransitions": true, "bulkStateUpdates": true, "optimizedPayloads": true}, "memoryUpdates": {"enabled": true, "autoSyncOnComplete": true, "knowledgeGraphUpdates": true, "sessionPersistence": true}}, "quality": {"minimumThreshold": 8, "autoRefinement": true, "completenessCheck": true, "complianceValidation": true}}