"""
Change detection engine for VIBECODE-Kiro sync system.
Analyzes file changes and determines required sync actions.
"""

import os
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional, Set
from pathlib import Path

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .interfaces import IChangeDetectionEngine
from models.sync_models import (
    FileChange, SyncAction, Conflict, ChangeType, ActionType, 
    Priority, ConflictType
)
from utils.logger import SyncLogger
from utils.path_utils import PathUtils

logger = SyncLogger(__name__)


class ChangeDetectionEngine(IChangeDetectionEngine):
    """
    Engine for detecting and analyzing file changes to determine sync actions.
    
    This engine analyzes FileChange objects and converts them into SyncAction objects
    that specify what operations need to be performed during synchronization.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the change detection engine.
        
        Args:
            config: Configuration dictionary containing sync settings
        """
        self.config = config
        self.source_path = config.get('source_path', 'E:\\VIBECODE\\.cursor')
        self.target_path = config.get('target_path', '.kiro')
        self.monitored_file_types = config.get('file_types', ['.mdc', '.json', '.md'])
        self.excluded_paths = config.get('excluded_paths', ['temp', 'cache', 'logs'])
        
        # Priority mapping for different file types and locations
        self.priority_rules = {
            'mcp.json': Priority.CRITICAL,
            'master-config.json': Priority.CRITICAL,
            'unified-config.json': Priority.HIGH,
            '.mdc': Priority.HIGH,
            '.md': Priority.MEDIUM,
            'default': Priority.LOW
        }
        
        # Files that require adaptation from VIBECODE to Kiro format
        self.adaptation_required_patterns = [
            '.mdc',  # VIBECODE rule files
            'mcp.json',  # MCP configuration
            'config/',  # Configuration files
        ]
        
        logger.info(f"ChangeDetectionEngine initialized for {self.source_path} -> {self.target_path}")
    
    def analyze_changes(self, changes: List[FileChange]) -> List[SyncAction]:
        """
        Analyze file changes and determine required sync actions.
        
        Args:
            changes: List of detected file changes
            
        Returns:
            List of sync actions to be performed
        """
        logger.info(f"Analyzing {len(changes)} file changes")
        
        actions = []
        
        for change in changes:
            try:
                # Skip if file should be excluded
                if self._should_exclude_file(change.file_path):
                    logger.debug(f"Excluding file from sync: {change.file_path}")
                    continue
                
                # Skip if not a monitored file type
                if not self._is_monitored_file_type(change.file_path):
                    logger.debug(f"File type not monitored: {change.file_path}")
                    continue
                
                # Convert change to sync action
                sync_action = self._convert_change_to_action(change)
                if sync_action:
                    actions.append(sync_action)
                    logger.debug(f"Created sync action: {sync_action.action_type} for {sync_action.source_file}")
                
            except Exception as e:
                logger.error(f"Error analyzing change for {change.file_path}: {e}")
                continue
        
        logger.info(f"Generated {len(actions)} sync actions from {len(changes)} changes")
        return actions
    
    def detect_conflicts(self, actions: List[SyncAction]) -> List[Conflict]:
        """
        Detect potential conflicts in sync actions.
        
        Args:
            actions: List of sync actions to analyze
            
        Returns:
            List of detected conflicts
        """
        logger.info(f"Detecting conflicts in {len(actions)} sync actions")
        
        conflicts = []
        
        for action in actions:
            try:
                # Check if target file exists and has local modifications
                if os.path.exists(action.target_file):
                    conflict = self._check_for_conflict(action)
                    if conflict:
                        conflicts.append(conflict)
                        logger.debug(f"Detected conflict: {conflict.conflict_type} for {action.target_file}")
                
            except Exception as e:
                logger.error(f"Error detecting conflicts for {action.target_file}: {e}")
                continue
        
        logger.info(f"Detected {len(conflicts)} conflicts")
        return conflicts
    
    def prioritize_actions(self, actions: List[SyncAction]) -> List[SyncAction]:
        """
        Prioritize sync actions based on importance and dependencies.
        
        Args:
            actions: List of sync actions to prioritize
            
        Returns:
            List of sync actions sorted by priority (highest first)
        """
        logger.info(f"Prioritizing {len(actions)} sync actions")
        
        # Sort by priority (highest first), then by file type importance
        prioritized_actions = sorted(
            actions,
            key=lambda action: (
                -action.priority.value,  # Higher priority first (negative for reverse)
                self._get_file_type_order(action.source_file),  # File type importance
                action.source_file  # Alphabetical for consistency
            )
        )
        
        logger.info("Actions prioritized successfully")
        return prioritized_actions
    
    def _should_exclude_file(self, file_path: str) -> bool:
        """Check if file should be excluded from sync."""
        return PathUtils.is_excluded_path(file_path, self.excluded_paths)
    
    def _is_monitored_file_type(self, file_path: str) -> bool:
        """Check if file type should be monitored."""
        if not self.monitored_file_types:
            return True
        
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in [ft.lower() for ft in self.monitored_file_types]
    
    def _convert_change_to_action(self, change: FileChange) -> Optional[SyncAction]:
        """
        Convert a file change to a sync action.
        
        Args:
            change: The file change to convert
            
        Returns:
            SyncAction or None if no action needed
        """
        try:
            # Determine target file path
            target_file = self._get_target_file_path(change.file_path)
            
            # Determine action type based on change type
            action_type = self._get_action_type(change.change_type, target_file)
            
            # Determine priority
            priority = self._get_file_priority(change.file_path)
            
            # Check if adaptation is required
            requires_adaptation = self._requires_adaptation(change.file_path)
            
            # Check if backup is required
            backup_required = self._requires_backup(change.file_path, action_type)
            
            # Create metadata
            metadata = {
                'original_change_type': change.change_type.value,
                'timestamp': change.timestamp.isoformat(),
                'file_size': change.file_size,
                'old_path': change.old_path
            }
            
            return SyncAction(
                action_type=action_type,
                source_file=change.file_path,
                target_file=target_file,
                priority=priority,
                requires_adaptation=requires_adaptation,
                backup_required=backup_required,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Error converting change to action: {e}")
            return None
    
    def _get_target_file_path(self, source_file: str) -> str:
        """
        Get the target file path in Kiro system for a VIBECODE source file.
        
        Args:
            source_file: Path to source file in VIBECODE
            
        Returns:
            Corresponding path in Kiro system
        """
        # Convert absolute VIBECODE path to relative path
        try:
            source_path = Path(self.source_path)
            file_path = Path(source_file)
            
            # Get relative path from VIBECODE root
            relative_path = file_path.relative_to(source_path)
            
            # Map VIBECODE structure to Kiro structure
            path_parts = list(relative_path.parts)
            
            # Handle specific directory mappings
            if path_parts[0] == 'rules':
                # VIBECODE rules -> Kiro steering
                target_parts = ['steering'] + path_parts[1:]
                # Convert .mdc to .md
                if target_parts[-1].endswith('.mdc'):
                    target_parts[-1] = target_parts[-1][:-4] + '.md'
            elif path_parts[0] == 'config':
                # VIBECODE config -> Kiro settings
                target_parts = ['settings'] + path_parts[1:]
            elif 'mcp.json' in path_parts[-1]:
                # MCP configuration
                target_parts = ['settings', 'mcp.json']
            else:
                # Default mapping
                target_parts = path_parts
            
            # Construct target path
            target_path = Path(self.target_path) / Path(*target_parts)
            return str(target_path)
            
        except Exception as e:
            logger.error(f"Error mapping target path for {source_file}: {e}")
            # Fallback: use relative path as-is
            return os.path.join(self.target_path, os.path.basename(source_file))
    
    def _get_action_type(self, change_type: ChangeType, target_file: str) -> ActionType:
        """
        Determine the sync action type based on change type and target file state.
        
        Args:
            change_type: Type of file system change
            target_file: Path to target file
            
        Returns:
            Appropriate sync action type
        """
        if change_type == ChangeType.DELETED:
            return ActionType.DELETE
        elif change_type == ChangeType.CREATED:
            return ActionType.CREATE
        elif change_type == ChangeType.MODIFIED:
            if os.path.exists(target_file):
                return ActionType.UPDATE
            else:
                return ActionType.CREATE
        elif change_type == ChangeType.MOVED:
            # Handle as update to new location
            return ActionType.UPDATE
        else:
            return ActionType.UPDATE
    
    def _get_file_priority(self, file_path: str) -> Priority:
        """
        Determine priority for a file based on its type and location.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Priority level for the file
        """
        file_name = os.path.basename(file_path).lower()
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # Check specific file names first
        for pattern, priority in self.priority_rules.items():
            if pattern == 'default':
                continue
            elif pattern.startswith('.'):
                # File extension pattern
                if file_ext == pattern:
                    return priority
            else:
                # File name pattern
                if pattern in file_name:
                    return priority
        
        return self.priority_rules['default']
    
    def _requires_adaptation(self, file_path: str) -> bool:
        """
        Check if file requires adaptation from VIBECODE to Kiro format.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if adaptation is required
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        file_name = os.path.basename(file_path).lower()
        
        for pattern in self.adaptation_required_patterns:
            if pattern.startswith('.'):
                # File extension pattern
                if file_ext == pattern:
                    return True
            elif pattern.endswith('/'):
                # Directory pattern
                if pattern.rstrip('/') in file_path.lower():
                    return True
            else:
                # File name pattern
                if pattern in file_name:
                    return True
        
        return False
    
    def _requires_backup(self, file_path: str, action_type: ActionType) -> bool:
        """
        Check if operation requires backup of target file.
        
        Args:
            file_path: Path to the file
            action_type: Type of sync action
            
        Returns:
            True if backup is required
        """
        # Always backup for updates and deletes
        if action_type in [ActionType.UPDATE, ActionType.DELETE]:
            return True
        
        # Backup critical files even for creation
        priority = self._get_file_priority(file_path)
        if priority in [Priority.CRITICAL, Priority.HIGH]:
            return True
        
        return False
    
    def _get_file_type_order(self, file_path: str) -> int:
        """
        Get ordering value for file type (lower = higher priority).
        
        Args:
            file_path: Path to the file
            
        Returns:
            Ordering value
        """
        file_name = os.path.basename(file_path).lower()
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # Order by importance
        if 'mcp.json' in file_name:
            return 0
        elif 'master-config.json' in file_name:
            return 1
        elif 'unified-config.json' in file_name:
            return 2
        elif file_ext == '.mdc':
            return 3
        elif file_ext == '.md':
            return 4
        else:
            return 5
    
    def _check_for_conflict(self, action: SyncAction) -> Optional[Conflict]:
        """
        Check if a sync action would create a conflict.
        
        Args:
            action: Sync action to check
            
        Returns:
            Conflict object if conflict detected, None otherwise
        """
        try:
            if not os.path.exists(action.target_file):
                return None
            
            # Read target file content
            with open(action.target_file, 'r', encoding='utf-8') as f:
                target_content = f.read()
            
            # Read source file content if it exists
            source_content = ""
            if os.path.exists(action.source_file):
                with open(action.source_file, 'r', encoding='utf-8') as f:
                    source_content = f.read()
            
            # Check for different types of conflicts
            conflict_type = self._determine_conflict_type(
                action.source_file, 
                action.target_file, 
                source_content, 
                target_content
            )
            
            if conflict_type:
                return Conflict(
                    conflict_type=conflict_type,
                    source_file=action.source_file,
                    target_file=action.target_file,
                    source_content=source_content,
                    target_content=target_content,
                    resolution_strategy="preserve_kiro_customizations",
                    manual_review_required=self._requires_manual_review(conflict_type),
                    conflict_details={
                        'action_type': action.action_type.value,
                        'priority': action.priority.value,
                        'requires_adaptation': action.requires_adaptation
                    }
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking for conflict: {e}")
            return None
    
    def _determine_conflict_type(self, source_file: str, target_file: str, 
                               source_content: str, target_content: str) -> Optional[ConflictType]:
        """
        Determine the type of conflict between source and target files.
        
        Args:
            source_file: Path to source file
            target_file: Path to target file
            source_content: Content of source file
            target_content: Content of target file
            
        Returns:
            ConflictType if conflict detected, None otherwise
        """
        # If contents are identical, no conflict
        if source_content.strip() == target_content.strip():
            return None
        
        # Check for format conflicts (.mdc vs .md)
        if source_file.endswith('.mdc') and target_file.endswith('.md'):
            return ConflictType.FORMAT_CONFLICT
        
        # Check for customization conflicts (Kiro-specific content)
        if self._has_kiro_customizations(target_content):
            return ConflictType.CUSTOMIZATION_CONFLICT
        
        # Check for path conflicts
        if self._has_path_differences(source_content, target_content):
            return ConflictType.PATH_CONFLICT
        
        # Default to content conflict
        return ConflictType.CONTENT_CONFLICT
    
    def _has_kiro_customizations(self, content: str) -> bool:
        """
        Check if content has Kiro-specific customizations.
        
        Args:
            content: File content to check
            
        Returns:
            True if Kiro customizations detected
        """
        kiro_markers = [
            '# KIRO-SPECIFIC',
            '# Kiro optimization',
            '.kiro/',
            'kiro-specific',
            'KIRO_'
        ]
        
        content_lower = content.lower()
        return any(marker.lower() in content_lower for marker in kiro_markers)
    
    def _has_path_differences(self, source_content: str, target_content: str) -> bool:
        """
        Check if there are significant path differences between contents.
        
        Args:
            source_content: Source file content
            target_content: Target file content
            
        Returns:
            True if path differences detected
        """
        # Look for path patterns
        import re
        
        # Extract paths from both contents
        path_pattern = r'["\']([^"\']*[/\\][^"\']*)["\']'
        
        source_paths = set(re.findall(path_pattern, source_content))
        target_paths = set(re.findall(path_pattern, target_content))
        
        # Check if there are significant differences
        return len(source_paths.symmetric_difference(target_paths)) > 0
    
    def _requires_manual_review(self, conflict_type: ConflictType) -> bool:
        """
        Check if conflict type requires manual review.
        
        Args:
            conflict_type: Type of conflict
            
        Returns:
            True if manual review is required
        """
        manual_review_types = [
            ConflictType.CUSTOMIZATION_CONFLICT,
            ConflictType.PERMISSION_CONFLICT
        ]
        
        return conflict_type in manual_review_types
    
    def get_change_statistics(self, changes: List[FileChange]) -> Dict[str, Any]:
        """
        Get statistics about detected changes.
        
        Args:
            changes: List of file changes
            
        Returns:
            Dictionary with change statistics
        """
        stats = {
            'total_changes': len(changes),
            'by_type': {},
            'by_file_type': {},
            'by_priority': {},
            'requires_adaptation': 0,
            'requires_backup': 0
        }
        
        for change in changes:
            # Count by change type
            change_type = change.change_type.value
            stats['by_type'][change_type] = stats['by_type'].get(change_type, 0) + 1
            
            # Count by file type
            file_ext = os.path.splitext(change.file_path)[1].lower()
            stats['by_file_type'][file_ext] = stats['by_file_type'].get(file_ext, 0) + 1
            
            # Count by priority
            priority = self._get_file_priority(change.file_path).value
            stats['by_priority'][priority] = stats['by_priority'].get(priority, 0) + 1
            
            # Count adaptation and backup requirements
            if self._requires_adaptation(change.file_path):
                stats['requires_adaptation'] += 1
            
            action_type = self._get_action_type(change.change_type, 
                                             self._get_target_file_path(change.file_path))
            if self._requires_backup(change.file_path, action_type):
                stats['requires_backup'] += 1
        
        return stats