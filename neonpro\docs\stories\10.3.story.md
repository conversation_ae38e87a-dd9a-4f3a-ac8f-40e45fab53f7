# Story 10.3: Lembretes de Cobrança e Financeiro

## Story Overview

**Como** gerente financeiro da clínica  
**Eu quero** sistema automatizado de lembretes de cobrança com escalação inteligente  
**Para que** eu possa reduzir inadimplência e melhorar o fluxo de caixa com comunicação efetiva

### Story Details

- **Epic**: Epic 10 - CRM & Campanhas
- **Story Points**: 8
- **Priority**: P1 (High)
- **Theme**: Automated Billing Recovery & Financial Communication
- **Dependencies**: Story 10.1 (Segmentation), Story 10.2 (Campaign Automation), Epic 7 (Financial System)

### Acceptance Criteria

#### AC1: Configuração de Regras de Cobrança Automatizada

- [ ] **GIVEN** sistema financeiro integrado (Epic 7)
- [ ] **WHEN** configuro regras de cobrança
- [ ] **THEN** posso definir diferentes tipos de lembretes por cenário
- [ ] **AND** configurar escalação por dias de atraso (5, 15, 30, 60 dias)
- [ ] **AND** definir canais por etapa (email → SMS → WhatsApp → ligação)
- [ ] **AND** personalizar templates por tipo de débito e urgência
- [ ] **AND** configurar horários de envio respeitando legislação
- [ ] **AND** definir limites de tentativas por canal

#### AC2: Identificação Inteligente de Inadimplência

- [ ] **GIVEN** faturas vencidas no sistema
- [ ] **WHEN** processo de identificação executa
- [ ] **THEN** classifica automaticamente por tipo de inadimplência
- [ ] **AND** considera histórico de pagamento do paciente (Epic 7)
- [ ] **AND** analisa valor e urgência da cobrança
- [ ] **AND** identifica padrões de comportamento de pagamento
- [ ] **AND** segmenta por propensão a pagamento (alto, médio, baixo)
- [ ] **AND** exclui casos em disputa ou negociação

#### AC3: Comunicação Personalizada e Progressiva

- [ ] **GIVEN** paciente identificado como inadimplente
- [ ] **WHEN** sistema inicia cobrança
- [ ] **THEN** envia primeiro lembrete educativo e cordial
- [ ] **AND** personaliza conteúdo com dados específicos (valor, vencimento)
- [ ] **AND** inclui opções de pagamento facilitadas (PIX, cartão, parcelamento)
- [ ] **AND** oferece canais de negociação (WhatsApp, portal)
- [ ] **AND** escalação automática conforme não resposta
- [ ] **AND** tom progressivamente mais formal mas respeitoso

#### AC4: Facilitação de Pagamento e Negociação

- [ ] **GIVEN** paciente recebe cobrança
- [ ] **WHEN** acessa link de pagamento
- [ ] **THEN** encontra múltiplas opções de pagamento
- [ ] **AND** PIX com QR code para pagamento imediato
- [ ] **AND** cartão de crédito com parcelamento automático
- [ ] **AND** boleto com nova data de vencimento
- [ ] **AND** opção de renegociação com desconto automático
- [ ] **AND** agendamento de pagamento futuro
- [ ] **AND** chat direto com setor financeiro

#### AC5: Monitoramento e Gestão de Resultados

- [ ] **GIVEN** campanhas de cobrança ativas
- [ ] **WHEN** monitoro resultados
- [ ] **THEN** visualizo dashboard com métricas de recuperação
- [ ] **AND** taxa de conversão por canal e etapa de cobrança
- [ ] **AND** valor recuperado vs. valor em aberto
- [ ] **AND** tempo médio para pagamento após lembrete
- [ ] **AND** análise de efetividade por tipo de paciente
- [ ] **AND** ROI da automação de cobrança
- [ ] **AND** alertas para casos que necessitam intervenção manual

#### AC6: Compliance e Gestão de Relacionamento

- [ ] **GIVEN** regulamentações de cobrança (CDC, LGPD)
- [ ] **WHEN** sistema executa cobrança
- [ ] **THEN** respeita horários comerciais e feriados
- [ ] **AND** limita frequência de contato conforme lei
- [ ] **AND** mantém tom respeitoso e não abusivo
- [ ] **AND** oferece sempre canais de negociação
- [ ] **AND** registra todas as interações para auditoria
- [ ] **AND** permite opt-out de comunicações não essenciais
- [ ] **AND** escala para atendimento humano quando necessário

### Technical Requirements

#### Billing Recovery System

```typescript
// Sistema de Recuperação de Cobrança
interface SistemaCobranca {
  id: string
  nome: string
  descricao?: string
  
  // Configuração Geral
  ativo: boolean
  configuracao: ConfiguracaoCobranca
  
  // Regras de Escalação
  regrasEscalacao: RegraEscalacao[]
  
  // Templates por Etapa
  templates: TemplatesCobranca
  
  // Métricas e Performance
  metricas: MetricasCobranca
  
  // Compliance
  configuracaoCompliance: ComplianceCobranca
  
  // Metadados
  criadoPor: string
  criadoEm: Date
  ultimaExecucao: Date
  proximaExecucao: Date
}

// Configuração do Sistema de Cobrança
interface ConfiguracaoCobranca {
  // Critérios de Ativação
  diasVencimentoMinimo: number
  valorMinimoCobranca: number
  excluirNegociacaoAndamento: boolean
  excluirDisputaJuridica: boolean
  
  // Horários e Frequência
  horarioComercialInicio: string
  horarioComercialFim: string
  diasSemanaAtivos: DiaSemana[]
  respeitarFeriados: boolean
  
  // Limites de Comunicação
  limiteDiario: number
  limiteSemanal: number
  limiteMensal: number
  
  // Canais Ativos
  canaisAtivos: CanalCobranca[]
  ordemPreferencia: CanalCobranca[]
  
  // Segmentação
  segmentacaoPorPerfil: boolean
  usarHistoricoPagamento: boolean
  considerarValorDivida: boolean
  
  // Automação
  automacaoCompleta: boolean
  intervencaoManualApenas: string[] // Lista de cenários
}

// Regra de Escalação
interface RegraEscalacao {
  id: string
  nome: string
  ordem: number
  
  // Critérios de Ativação
  diasAtraso: number
  valorMinimo?: number
  valorMaximo?: number
  
  // Configuração da Etapa
  canais: CanalCobranca[]
  template: string
  delayProximaEtapa: number // em dias
  
  // Ações Especiais
  ofereceDesconto: boolean
  percentualDesconto?: number
  ofereceParcelamento: boolean
  maxParcelas?: number
  
  // Condições de Parada
  condicoesParada: CondicaoParada[]
  
  // Métricas
  taxaSucesso: number
  valorRecuperado: number
  tempoMedioResposta: number
}

// Processo de Cobrança
interface ProcessoCobranca {
  id: string
  faturaId: string
  pacienteId: string
  
  // Dados da Dívida
  valorOriginal: number
  valorAtual: number
  diasAtraso: number
  dataVencimento: Date
  
  // Classificação
  categoria: CategoriaInadimplencia
  prioridadeCobranca: PrioridadeCobranca
  propensaoPagamento: PropensaoPagamento
  
  // Status do Processo
  status: StatusProcessoCobranca
  etapaAtual: number
  regrasAplicadas: string[]
  
  // Histórico de Comunicação
  tentativasRealizadas: TentativaCobranca[]
  totalTentativas: number
  ultimaTentativa: Date
  proximaTentativa: Date
  
  // Resultados
  respondeuComunicacao: boolean
  dataResposta?: Date
  tipoResposta?: TipoRespostaCobranca
  valorPago?: number
  dataPagamento?: Date
  
  // Negociação
  negociacaoAndamento: boolean
  propostaNegociacao?: PropostaNegociacao
  
  // Metadados
  iniciadoEm: Date
  finalizadoEm?: Date
  motivo_finalizacao?: string
  responsavelProcesso?: string
}

// Tentativa de Cobrança
interface TentativaCobranca {
  id: string
  processoCobrancaId: string
  
  // Configuração da Tentativa
  etapa: number
  canal: CanalCobranca
  template: string
  
  // Conteúdo Enviado
  conteudoEnviado: ConteudoCobranca
  
  // Resultado da Tentativa
  status: StatusTentativa
  dataEnvio: Date
  dataEntrega?: Date
  dataLeitura?: Date
  dataResposta?: Date
  
  // Resposta do Paciente
  tipoResposta?: TipoRespostaCobranca
  conteudoResposta?: string
  acaoTomada?: AcaoPos
  
  // Métricas
  custoTentativa: number
  efetivaTentativa: boolean
  
  // Erros
  erroEnvio?: string
  motivoFalha?: string
}

// Conteúdo de Cobrança
interface ConteudoCobranca {
  // Dados Básicos
  assunto: string
  mensagem: string
  
  // Personalização
  dadosPaciente: DadosPacienteCobranca
  dadosFinanceiros: DadosFinanceirosCobranca
  
  // Opções de Pagamento
  opcoesPagamento: OpcaoPagamento[]
  linksPagamento: LinkPagamento[]
  
  // Facilidades
  ofertaDesconto?: OfertaDesconto
  opcoesParcelamento?: OpcaoParcelamento[]
  
  // Call-to-Action
  acoesPrincipais: AcaoCobranca[]
  canaisNegociacao: CanalNegociacao[]
  
  // Compliance
  informacoesLegais: string[]
  linkDescadastro?: string
  politicaPrivacidade: string
}

// Dados do Paciente para Cobrança
interface DadosPacienteCobranca {
  nome: string
  email?: string
  telefone?: string
  
  // Preferências
  canalPreferido: CanalCobranca
  melhorHorarioContato: string
  
  // Histórico
  historicoPagamento: HistoricoPagamento
  relacionamentoClinica: RelacionamentoClinica
  
  // Contexto
  ultimaConsulta?: Date
  proximaConsulta?: Date
  procedimentosRealizados: string[]
}

// Dados Financeiros para Cobrança
interface DadosFinanceirosCobranca {
  valorOriginal: number
  valorAtual: number
  diasAtraso: number
  dataVencimento: Date
  
  // Detalhes da Fatura
  numeroFatura: string
  descricaoServicos: string[]
  
  // Juros e Multas
  valorJuros: number
  valorMulta: number
  percentualJurosDia: number
  
  // Negociação
  valorComDesconto?: number
  percentualDesconto?: number
  validadeDesconto?: Date
  
  // Parcelamento
  parcelamentoDisponivel: boolean
  maxParcelas: number
  valorMinimoParcela: number
}

// Opção de Pagamento
interface OpcaoPagamento {
  id: string
  tipo: TipoPagamento
  nome: string
  icone: string
  
  // Configuração
  ativo: boolean
  destaque: boolean
  descricao: string
  
  // Facilidades
  temDesconto: boolean
  percentualDesconto?: number
  permiteParcelamento: boolean
  maxParcelas?: number
  
  // Processamento
  link: string
  qrCode?: string
  codigoBarras?: string
  
  // Métricas
  taxaConversao: number
  preferenciaPacientes: number
}

// Proposta de Negociação
interface PropostaNegociacao {
  id: string
  processoCobrancaId: string
  
  // Dados da Proposta
  valorOriginal: number
  valorProposto: number
  percentualDesconto: number
  
  // Parcelamento
  numeroParcelas: number
  valorParcela: number
  dataVencimentoPrimeira: Date
  
  // Condições
  condicoes: string[]
  validadeProposta: Date
  
  // Status
  status: StatusNegociacao
  dataEnvio: Date
  dataResposta?: Date
  respostaPaciente?: RespostaNegociacao
  
  // Aprovação
  aprovadaPor?: string
  dataAprovacao?: Date
  motivoRecusa?: string
}

// Métricas de Cobrança
interface MetricasCobranca {
  // Período
  dataInicio: Date
  dataFim: Date
  
  // Volume
  totalProcessosIniciados: number
  totalProcessosFinalizados: number
  totalTentativasRealizadas: number
  
  // Efetividade
  taxaRecuperacao: number
  taxaResposta: number
  taxaNegociacao: number
  
  // Financeiro
  valorTotalRecuperado: number
  valorAindaEmAberto: number
  custoTotalCobranca: number
  roiCobranca: number
  
  // Performance por Canal
  performanceCanais: {
    [canal: string]: MetricasCanal
  }
  
  // Performance por Etapa
  performanceEtapas: MetricasEtapa[]
  
  // Tempo
  tempoMedioRecuperacao: number
  tempoMedioResposta: number
  
  // Qualidade
  reclamacoes: number
  avaliacaoAtendimento: number
  satisfacaoPacientes: number
}

// Configuração de Compliance
interface ComplianceCobranca {
  // Regulamentações
  segueCDC: boolean
  segueLGPD: boolean
  segueRegulamentacaoLocal: boolean
  
  // Limitações de Comunicação
  respeitaHorarioComercial: boolean
  limitaFrequenciaContato: boolean
  evitaCanaisBloquados: boolean
  
  // Conteúdo
  tomRespeitoso: boolean
  semAmeacas: boolean
  ofereceNegociacao: boolean
  informacoesLegaisCompletas: boolean
  
  // Auditoria
  registraTodasInteracoes: boolean
  mantemHistoricoCompleto: boolean
  permiteAuditoria: boolean
  
  // Direitos do Devedor
  informaDireitosDevedor: boolean
  ofereceCanalReclamacao: boolean
  respeitaOptOut: boolean
  
  // Validação
  validadoPorJuridico: boolean
  dataValidacao?: Date
  proximaRevisao?: Date
}

// Tipos Enum para Cobrança
type CategoriaInadimplencia = 'consulta' | 'procedimento' | 'produto' | 'parcelamento' | 'taxa'
type PrioridadeCobranca = 'baixa' | 'media' | 'alta' | 'critica'
type PropensaoPagamento = 'alta' | 'media' | 'baixa' | 'critica'
type StatusProcessoCobranca = 'ativo' | 'pausado' | 'negociacao' | 'pago' | 'cancelado' | 'juridico'
type CanalCobranca = 'email' | 'sms' | 'whatsapp' | 'ligacao' | 'carta' | 'visita'
type StatusTentativa = 'enviando' | 'enviado' | 'entregue' | 'lido' | 'respondido' | 'falhado'
type TipoRespostaCobranca = 'pagou' | 'agendou' | 'negociou' | 'contestou' | 'bloqueou' | 'ignorou'
type TipoPagamento = 'pix' | 'cartao' | 'boleto' | 'dinheiro' | 'parcelamento'
type StatusNegociacao = 'pendente' | 'aceita' | 'recusada' | 'expirada' | 'cancelada'
type CondicaoParada = 'pagamento_completo' | 'acordo_fechado' | 'contestacao_juridica' | 'limite_tentativas'
```

#### Database Schema for Billing Recovery

```sql
-- Configurações de Cobrança
CREATE TABLE configuracoes_cobranca (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome VARCHAR(255) NOT NULL,
  descricao TEXT,
  
  -- Configuração Geral
  ativo BOOLEAN DEFAULT TRUE,
  padrao BOOLEAN DEFAULT FALSE,
  
  -- Critérios de Ativação
  dias_vencimento_minimo INTEGER DEFAULT 1,
  valor_minimo_cobranca DECIMAL(10,2) DEFAULT 0,
  excluir_negociacao_andamento BOOLEAN DEFAULT TRUE,
  excluir_disputa_juridica BOOLEAN DEFAULT TRUE,
  
  -- Horários e Frequência
  horario_comercial_inicio TIME DEFAULT '08:00',
  horario_comercial_fim TIME DEFAULT '18:00',
  dias_semana_ativos dia_semana_type[] DEFAULT '{segunda,terca,quarta,quinta,sexta}',
  respeitar_feriados BOOLEAN DEFAULT TRUE,
  
  -- Limites de Comunicação
  limite_diario INTEGER DEFAULT 2,
  limite_semanal INTEGER DEFAULT 5,
  limite_mensal INTEGER DEFAULT 15,
  
  -- Canais
  canais_ativos canal_cobranca_type[] NOT NULL,
  ordem_preferencia canal_cobranca_type[] NOT NULL,
  
  -- Segmentação
  segmentacao_por_perfil BOOLEAN DEFAULT TRUE,
  usar_historico_pagamento BOOLEAN DEFAULT TRUE,
  considerar_valor_divida BOOLEAN DEFAULT TRUE,
  
  -- Automação
  automacao_completa BOOLEAN DEFAULT FALSE,
  intervencao_manual_apenas TEXT[],
  
  -- Compliance
  config_compliance JSONB NOT NULL,
  
  -- Metadados
  criada_por UUID NOT NULL REFERENCES auth.users(id),
  criada_em TIMESTAMPTZ DEFAULT NOW(),
  ultima_edicao TIMESTAMPTZ DEFAULT NOW(),
  editada_por UUID REFERENCES auth.users(id),
  
  -- Multi-tenant
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_config_nome_valido CHECK (LENGTH(TRIM(nome)) > 0),
  CONSTRAINT chk_valor_minimo_positivo CHECK (valor_minimo_cobranca >= 0),
  CONSTRAINT chk_horario_comercial CHECK (horario_comercial_fim > horario_comercial_inicio)
);

-- Regras de Escalação
CREATE TABLE regras_escalacao_cobranca (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  configuracao_id UUID NOT NULL REFERENCES configuracoes_cobranca(id) ON DELETE CASCADE,
  
  -- Identificação
  nome VARCHAR(255) NOT NULL,
  ordem INTEGER NOT NULL,
  
  -- Critérios de Ativação
  dias_atraso INTEGER NOT NULL,
  valor_minimo DECIMAL(10,2),
  valor_maximo DECIMAL(10,2),
  
  -- Configuração da Etapa
  canais canal_cobranca_type[] NOT NULL,
  template_email UUID REFERENCES templates_campanhas(id),
  template_sms UUID REFERENCES templates_campanhas(id),
  template_whatsapp UUID REFERENCES templates_campanhas(id),
  delay_proxima_etapa INTEGER DEFAULT 3, -- dias
  
  -- Ofertas Especiais
  oferece_desconto BOOLEAN DEFAULT FALSE,
  percentual_desconto DECIMAL(5,2),
  oferece_parcelamento BOOLEAN DEFAULT FALSE,
  max_parcelas INTEGER,
  
  -- Condições de Parada
  condicoes_parada condicao_parada_type[],
  limite_tentativas INTEGER DEFAULT 3,
  
  -- Ativa
  ativa BOOLEAN DEFAULT TRUE,
  
  CONSTRAINT chk_regra_dias_atraso CHECK (dias_atraso > 0),
  CONSTRAINT chk_regra_ordem CHECK (ordem > 0),
  CONSTRAINT chk_desconto_valido CHECK (percentual_desconto IS NULL OR (percentual_desconto > 0 AND percentual_desconto <= 100)),
  CONSTRAINT uk_config_ordem UNIQUE (configuracao_id, ordem)
);

-- Processos de Cobrança
CREATE TABLE processos_cobranca (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  fatura_id UUID NOT NULL, -- Referência ao Epic 7 (Sistema Financeiro)
  paciente_id UUID NOT NULL REFERENCES pacientes(id),
  configuracao_id UUID NOT NULL REFERENCES configuracoes_cobranca(id),
  
  -- Dados da Dívida
  valor_original DECIMAL(15,2) NOT NULL,
  valor_atual DECIMAL(15,2) NOT NULL,
  dias_atraso INTEGER NOT NULL,
  data_vencimento DATE NOT NULL,
  
  -- Classificação
  categoria categoria_inadimplencia_type NOT NULL,
  prioridade_cobranca prioridade_cobranca_type DEFAULT 'media',
  propensao_pagamento propensao_pagamento_type,
  
  -- Status do Processo
  status status_processo_cobranca_type DEFAULT 'ativo',
  etapa_atual INTEGER DEFAULT 1,
  regras_aplicadas TEXT[],
  
  -- Histórico de Comunicação
  total_tentativas INTEGER DEFAULT 0,
  ultima_tentativa TIMESTAMPTZ,
  proxima_tentativa TIMESTAMPTZ,
  
  -- Resultados
  respondeu_comunicacao BOOLEAN DEFAULT FALSE,
  data_resposta TIMESTAMPTZ,
  tipo_resposta tipo_resposta_cobranca_type,
  valor_pago DECIMAL(15,2),
  data_pagamento TIMESTAMPTZ,
  
  -- Negociação
  negociacao_andamento BOOLEAN DEFAULT FALSE,
  proposta_negociacao_id UUID,
  
  -- Controle
  pausado BOOLEAN DEFAULT FALSE,
  pausado_por UUID REFERENCES auth.users(id),
  pausado_em TIMESTAMPTZ,
  motivo_pausa TEXT,
  
  -- Metadados
  iniciado_em TIMESTAMPTZ DEFAULT NOW(),
  finalizado_em TIMESTAMPTZ,
  motivo_finalizacao TEXT,
  responsavel_processo UUID REFERENCES auth.users(id),
  
  -- Multi-tenant  
  clinica_id UUID NOT NULL REFERENCES clinicas(id),
  
  CONSTRAINT chk_processo_valor_positivo CHECK (valor_original > 0 AND valor_atual >= 0),
  CONSTRAINT chk_processo_dias_atraso CHECK (dias_atraso >= 0),
  CONSTRAINT chk_processo_etapa CHECK (etapa_atual > 0)
);

-- Tentativas de Cobrança
CREATE TABLE tentativas_cobranca (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  processo_cobranca_id UUID NOT NULL REFERENCES processos_cobranca(id) ON DELETE CASCADE,
  regra_escalacao_id UUID NOT NULL REFERENCES regras_escalacao_cobranca(id),
  
  -- Configuração da Tentativa
  etapa INTEGER NOT NULL,
  canal canal_cobranca_type NOT NULL,
  template_id UUID REFERENCES templates_campanhas(id),
  
  -- Conteúdo Enviado
  conteudo_enviado JSONB NOT NULL,
  personalizado BOOLEAN DEFAULT TRUE,
  
  -- Resultado da Tentativa
  status status_tentativa_type DEFAULT 'enviando',
  data_envio TIMESTAMPTZ DEFAULT NOW(),
  data_entrega TIMESTAMPTZ,
  data_leitura TIMESTAMPTZ,
  data_resposta TIMESTAMPTZ,
  
  -- Resposta do Paciente
  tipo_resposta tipo_resposta_cobranca_type,
  conteudo_resposta TEXT,
  acao_tomada TEXT,
  
  -- Detalhes Técnicos
  id_externo VARCHAR(255), -- ID do sistema de envio (SendGrid, Twilio, etc.)
  detalhes_entrega JSONB,
  
  -- Métricas
  custo_tentativa DECIMAL(10,4) DEFAULT 0,
  efetiva_tentativa BOOLEAN DEFAULT FALSE,
  
  -- Erros
  erro_envio TEXT,
  codigo_erro VARCHAR(50),
  motivo_falha TEXT,
  
  CONSTRAINT chk_tentativa_etapa CHECK (etapa > 0),
  CONSTRAINT chk_custo_positivo CHECK (custo_tentativa >= 0)
);

-- Propostas de Negociação
CREATE TABLE propostas_negociacao (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  processo_cobranca_id UUID NOT NULL REFERENCES processos_cobranca(id),
  
  -- Dados da Proposta
  valor_original DECIMAL(15,2) NOT NULL,
  valor_proposto DECIMAL(15,2) NOT NULL,
  percentual_desconto DECIMAL(5,2) NOT NULL,
  
  -- Parcelamento
  numero_parcelas INTEGER DEFAULT 1,
  valor_parcela DECIMAL(10,2),
  data_vencimento_primeira DATE,
  dia_vencimento INTEGER DEFAULT 10,
  
  -- Condições
  condicoes TEXT[],
  validade_proposta TIMESTAMPTZ NOT NULL,
  
  -- Status
  status status_negociacao_type DEFAULT 'pendente',
  data_envio TIMESTAMPTZ DEFAULT NOW(),
  data_resposta TIMESTAMPTZ,
  resposta_paciente TEXT,
  
  -- Aprovação Interna
  requer_aprovacao BOOLEAN DEFAULT FALSE,
  aprovada_por UUID REFERENCES auth.users(id),
  data_aprovacao TIMESTAMPTZ,
  motivo_recusa TEXT,
  
  -- Execução
  executada BOOLEAN DEFAULT FALSE,
  data_execucao TIMESTAMPTZ,
  parcelas_geradas INTEGER DEFAULT 0,
  
  CONSTRAINT chk_proposta_valores CHECK (valor_proposto <= valor_original),
  CONSTRAINT chk_proposta_desconto CHECK (percentual_desconto >= 0 AND percentual_desconto <= 100),
  CONSTRAINT chk_proposta_parcelas CHECK (numero_parcelas > 0),
  CONSTRAINT chk_proposta_validade CHECK (validade_proposta > NOW())
);

-- Métricas de Cobrança Consolidadas
CREATE TABLE metricas_cobranca (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  configuracao_id UUID NOT NULL REFERENCES configuracoes_cobranca(id),
  
  -- Período
  data_inicio DATE NOT NULL,
  data_fim DATE NOT NULL,
  
  -- Volume
  total_processos_iniciados INTEGER DEFAULT 0,
  total_processos_finalizados INTEGER DEFAULT 0,
  total_tentativas_realizadas INTEGER DEFAULT 0,
  
  -- Efetividade
  taxa_recuperacao DECIMAL(5,2) DEFAULT 0,
  taxa_resposta DECIMAL(5,2) DEFAULT 0,
  taxa_negociacao DECIMAL(5,2) DEFAULT 0,
  taxa_sucesso_primeira_tentativa DECIMAL(5,2) DEFAULT 0,
  
  -- Financeiro
  valor_total_recuperado DECIMAL(15,2) DEFAULT 0,
  valor_ainda_em_aberto DECIMAL(15,2) DEFAULT 0,
  custo_total_cobranca DECIMAL(10,2) DEFAULT 0,
  roi_cobranca DECIMAL(10,4) DEFAULT 0,
  
  -- Performance por Canal
  performance_canais JSONB DEFAULT '{}',
  
  -- Performance por Etapa
  performance_etapas JSONB DEFAULT '{}',
  
  -- Tempo
  tempo_medio_recuperacao DECIMAL(8,2), -- dias
  tempo_medio_resposta DECIMAL(8,2), -- horas
  tempo_medio_primeira_resposta DECIMAL(8,2), -- horas
  
  -- Qualidade
  total_reclamacoes INTEGER DEFAULT 0,
  avaliacao_atendimento DECIMAL(3,2),
  satisfacao_pacientes DECIMAL(3,2),
  
  -- Compliance
  violacoes_compliance INTEGER DEFAULT 0,
  alertas_auditoria INTEGER DEFAULT 0,
  
  -- Última Atualização
  atualizada_em TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT uk_metricas_periodo UNIQUE (configuracao_id, data_inicio, data_fim),
  CONSTRAINT chk_periodo_metricas CHECK (data_fim >= data_inicio)
);

-- Log de Auditoria para Compliance
CREATE TABLE auditoria_cobranca (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  processo_cobranca_id UUID NOT NULL REFERENCES processos_cobranca(id),
  
  -- Tipo de Evento
  tipo_evento evento_auditoria_type NOT NULL,
  descricao_evento TEXT NOT NULL,
  
  -- Dados do Evento
  detalhes_evento JSONB,
  usuario_responsavel UUID REFERENCES auth.users(id),
  sistema_origem VARCHAR(100),
  
  -- Compliance
  conformidade_verificada BOOLEAN DEFAULT TRUE,
  violacao_detectada BOOLEAN DEFAULT FALSE,
  tipo_violacao TEXT,
  acao_corretiva_necessaria BOOLEAN DEFAULT FALSE,
  
  -- Timestamp
  ocorrido_em TIMESTAMPTZ DEFAULT NOW(),
  
  -- Contexto
  ip_origem INET,
  user_agent TEXT
);

-- Tipos Enum para Cobrança
CREATE TYPE categoria_inadimplencia_type AS ENUM ('consulta', 'procedimento', 'produto', 'parcelamento', 'taxa');
CREATE TYPE prioridade_cobranca_type AS ENUM ('baixa', 'media', 'alta', 'critica');
CREATE TYPE propensao_pagamento_type AS ENUM ('alta', 'media', 'baixa', 'critica');
CREATE TYPE status_processo_cobranca_type AS ENUM ('ativo', 'pausado', 'negociacao', 'pago', 'cancelado', 'juridico');
CREATE TYPE canal_cobranca_type AS ENUM ('email', 'sms', 'whatsapp', 'ligacao', 'carta', 'visita');
CREATE TYPE status_tentativa_type AS ENUM ('enviando', 'enviado', 'entregue', 'lido', 'respondido', 'falhado');
CREATE TYPE tipo_resposta_cobranca_type AS ENUM ('pagou', 'agendou', 'negociou', 'contestou', 'bloqueou', 'ignorou');
CREATE TYPE status_negociacao_type AS ENUM ('pendente', 'aceita', 'recusada', 'expirada', 'cancelada');
CREATE TYPE condicao_parada_type AS ENUM ('pagamento_completo', 'acordo_fechado', 'contestacao_juridica', 'limite_tentativas');
CREATE TYPE evento_auditoria_type AS ENUM ('inicio_processo', 'tentativa_envio', 'resposta_paciente', 'pagamento', 'negociacao', 'finalizacao');

-- Índices para Performance de Cobrança
CREATE INDEX idx_processos_status ON processos_cobranca(status);
CREATE INDEX idx_processos_proxima_tentativa ON processos_cobranca(proxima_tentativa) WHERE status = 'ativo';
CREATE INDEX idx_processos_paciente ON processos_cobranca(paciente_id);
CREATE INDEX idx_processos_clinica ON processos_cobranca(clinica_id);
CREATE INDEX idx_processos_prioridade ON processos_cobranca(prioridade_cobranca);

-- Índices para Tentativas
CREATE INDEX idx_tentativas_processo ON tentativas_cobranca(processo_cobranca_id);
CREATE INDEX idx_tentativas_canal ON tentativas_cobranca(canal);
CREATE INDEX idx_tentativas_status ON tentativas_cobranca(status);
CREATE INDEX idx_tentativas_data_envio ON tentativas_cobranca(data_envio);

-- Índices para Métricas
CREATE INDEX idx_metricas_periodo ON metricas_cobranca(data_inicio, data_fim);
CREATE INDEX idx_metricas_configuracao ON metricas_cobranca(configuracao_id);

-- Índices para Auditoria
CREATE INDEX idx_auditoria_processo ON auditoria_cobranca(processo_cobranca_id);
CREATE INDEX idx_auditoria_tipo ON auditoria_cobranca(tipo_evento);
CREATE INDEX idx_auditoria_data ON auditoria_cobranca(ocorrido_em);
CREATE INDEX idx_auditoria_violacao ON auditoria_cobranca(violacao_detectada) WHERE violacao_detectada = true;

-- Índices para Propostas
CREATE INDEX idx_propostas_processo ON propostas_negociacao(processo_cobranca_id);
CREATE INDEX idx_propostas_status ON propostas_negociacao(status);
CREATE INDEX idx_propostas_validade ON propostas_negociacao(validade_proposta);

-- Full-text search para auditoria
CREATE INDEX idx_auditoria_search ON auditoria_cobranca USING gin(
  to_tsvector('portuguese', coalesce(descricao_evento, '') || ' ' || coalesce(tipo_violacao, ''))
);
```

#### Billing Recovery API Endpoints

```typescript
// Billing Recovery Configuration API
export async function POST(request: NextRequest) {
  const {
    nome,
    configuracao,
    regrasEscalacao,
    configCompliance
  } = await request.json()
  
  const supabase = createServerClient()
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Validar configuração de compliance
    const validacaoCompliance = await validarConfigCompliance(configCompliance)
    if (!validacaoCompliance.valida) {
      return NextResponse.json({
        error: 'Configuração de compliance inválida',
        detalhes: validacaoCompliance.erros
      }, { status: 400 })
    }
    
    // Criar configuração de cobrança
    const { data: config, error } = await supabase
      .from('configuracoes_cobranca')
      .insert({
        nome,
        ...configuracao,
        config_compliance: configCompliance,
        criada_por: user.id,
        clinica_id: await getClinicaId(user.id)
      })
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    // Criar regras de escalação
    const regrasComConfigId = regrasEscalacao.map((regra: any, index: number) => ({
      ...regra,
      configuracao_id: config.id,
      ordem: index + 1
    }))
    
    const { error: regrasError } = await supabase
      .from('regras_escalacao_cobranca')
      .insert(regrasComConfigId)
    
    if (regrasError) {
      throw regrasError
    }
    
    // Agendar primeira execução
    await agendarVerificacaoCobranca(config.id)
    
    return NextResponse.json({
      configuracao: config,
      regrasEscalacao: regrasComConfigId,
      validacaoCompliance,
      message: 'Configuração de cobrança criada com sucesso'
    })
    
  } catch (error) {
    console.error('Error creating billing configuration:', error)
    return NextResponse.json({
      error: 'Erro ao criar configuração de cobrança'
    }, { status: 500 })
  }
}

// Process Billing Recovery API
export async function POST(
  request: NextRequest,
  { params }: { params: { action: string } }
) {
  const supabase = createServerClient()
  
  try {
    if (params.action === 'execute') {
      const { configuracaoId, forceExecution = false } = await request.json()
      
      // Buscar configuração
      const { data: configuracao } = await supabase
        .from('configuracoes_cobranca')
        .select(`
          *,
          regras_escalacao_cobranca(*)
        `)
        .eq('id', configuracaoId)
        .single()
      
      if (!configuracao?.ativo && !forceExecution) {
        return NextResponse.json({
          error: 'Configuração não está ativa'
        }, { status: 400 })
      }
      
      // Identificar faturas em atraso
      const faturasAtraso = await identificarFaturasEmAtraso(configuracao)
      
      // Processar cada fatura
      const resultados = await Promise.all(
        faturasAtraso.map(fatura => processarFaturaAtraso(fatura, configuracao))
      )
      
      // Consolidar métricas
      const metricas = await consolidarMetricasExecucao(resultados)
      
      return NextResponse.json({
        processados: resultados.length,
        novosProcessos: resultados.filter(r => r.novoProcesso).length,
        tentativasEnviadas: resultados.filter(r => r.tentativaEnviada).length,
        metricas,
        message: 'Execução de cobrança concluída'
      })
    }
    
    if (params.action === 'process-individual') {
      const { faturaId, configuracaoId } = await request.json()
      
      // Processar fatura específica
      const resultado = await processarFaturaIndividual(faturaId, configuracaoId)
      
      return NextResponse.json({
        resultado,
        message: 'Fatura processada individualmente'
      })
    }
    
  } catch (error) {
    console.error('Error processing billing recovery:', error)
    return NextResponse.json({
      error: 'Erro ao processar cobrança'
    }, { status: 500 })
  }
}

// Negotiation Management API
export async function POST(
  request: NextRequest,
  { params }: { params: { processoId: string, action: string } }
) {
  const supabase = createServerClient()
  
  try {
    if (params.action === 'create-proposal') {
      const {
        valorProposto,
        percentualDesconto,
        numeroParcelas,
        condicoes,
        validadeDias
      } = await request.json()
      
      // Buscar processo de cobrança
      const { data: processo } = await supabase
        .from('processos_cobranca')
        .select('*')
        .eq('id', params.processoId)
        .single()
      
      if (!processo) {
        return NextResponse.json({
          error: 'Processo de cobrança não encontrado'
        }, { status: 404 })
      }
      
      // Validar proposta
      const validacao = await validarPropostaNegociacao({
        valorOriginal: processo.valor_atual,
        valorProposto,
        percentualDesconto,
        numeroParcelas
      })
      
      if (!validacao.valida) {
        return NextResponse.json({
          error: 'Proposta inválida',
          detalhes: validacao.erros
        }, { status: 400 })
      }
      
      // Criar proposta
      const { data: proposta, error } = await supabase
        .from('propostas_negociacao')
        .insert({
          processo_cobranca_id: params.processoId,
          valor_original: processo.valor_atual,
          valor_proposto: valorProposto,
          percentual_desconto: percentualDesconto,
          numero_parcelas: numeroParcelas,
          valor_parcela: valorProposto / numeroParcelas,
          data_vencimento_primeira: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 dias
          condicoes,
          validade_proposta: new Date(Date.now() + validadeDias * 24 * 60 * 60 * 1000)
        })
        .select()
        .single()
      
      if (error) {
        throw error
      }
      
      // Enviar proposta para o paciente
      await enviarPropostaNegociacao(proposta.id)
      
      // Atualizar processo
      await supabase
        .from('processos_cobranca')
        .update({
          negociacao_andamento: true,
          proposta_negociacao_id: proposta.id
        })
        .eq('id', params.processoId)
      
      return NextResponse.json({
        proposta,
        message: 'Proposta de negociação criada e enviada'
      })
    }
    
    if (params.action === 'accept-proposal') {
      const { propostaId, formaPagamento } = await request.json()
      
      // Processar aceitação da proposta
      const resultado = await processarAceitacaoProposta(propostaId, formaPagamento)
      
      return NextResponse.json({
        resultado,
        message: 'Proposta aceita e processada'
      })
    }
    
  } catch (error) {
    console.error('Error managing negotiation:', error)
    return NextResponse.json({
      error: 'Erro ao gerenciar negociação'
    }, { status: 500 })
  }
}

// Billing Metrics API
export async function GET(
  request: NextRequest,
  { params }: { params: { configuracaoId: string } }
) {
  const { searchParams } = new URL(request.url)
  const periodo = searchParams.get('periodo') || '30' // dias
  const incluirDetalhes = searchParams.get('detalhes') === 'true'
  
  const supabase = createServerClient()
  
  try {
    const dataInicio = new Date()
    dataInicio.setDate(dataInicio.getDate() - parseInt(periodo))
    
    // Buscar métricas consolidadas
    const { data: metricas } = await supabase
      .from('metricas_cobranca')
      .select('*')
      .eq('configuracao_id', params.configuracaoId)
      .gte('data_inicio', dataInicio.toISOString().split('T')[0])
      .order('data_inicio', { ascending: false })
    
    // Calcular métricas em tempo real
    const metricasTempoReal = await calcularMetricasCobrancaTempoReal(
      params.configuracaoId,
      dataInicio
    )
    
    // Análise de performance por canal
    const performanceCanais = await analisarPerformanceCanaisCobranca(
      params.configuracaoId,
      dataInicio
    )
    
    // Análise de tendências
    const tendencias = await analisarTendenciasCobranca(metricas)
    
    // Buscar detalhes se solicitado
    let detalhes = null
    if (incluirDetalhes) {
      detalhes = await buscarDetalhesProcessosCobranca(
        params.configuracaoId,
        dataInicio
      )
    }
    
    return NextResponse.json({
      metricas,
      metricasTempoReal,
      performanceCanais,
      tendencias,
      detalhes,
      periodo: parseInt(periodo),
      message: 'Métricas de cobrança carregadas'
    })
    
  } catch (error) {
    console.error('Error loading billing metrics:', error)
    return NextResponse.json({
      error: 'Erro ao carregar métricas de cobrança'
    }, { status: 500 })
  }
}

// Compliance Audit API
export async function GET(
  request: NextRequest,
  { params }: { params: { processoId?: string } }
) {
  const { searchParams } = new URL(request.url)
  const dataInicio = searchParams.get('inicio')
  const dataFim = searchParams.get('fim')
  const tipoEvento = searchParams.get('evento')
  
  const supabase = createServerClient()
  
  try {
    let query = supabase
      .from('auditoria_cobranca')
      .select(`
        *,
        processo_cobranca:processo_cobranca_id(
          paciente:paciente_id(nome, cpf),
          valor_original,
          status
        )
      `)
    
    if (params.processoId) {
      query = query.eq('processo_cobranca_id', params.processoId)
    }
    
    if (dataInicio) {
      query = query.gte('ocorrido_em', dataInicio)
    }
    
    if (dataFim) {
      query = query.lte('ocorrido_em', dataFim)
    }
    
    if (tipoEvento) {
      query = query.eq('tipo_evento', tipoEvento)
    }
    
    const { data: auditorias } = await query
      .order('ocorrido_em', { ascending: false })
      .limit(1000)
    
    // Análise de compliance
    const analiseCompliance = await analisarComplianceCobranca(auditorias)
    
    // Identificar violações
    const violacoes = auditorias.filter(a => a.violacao_detectada)
    
    // Gerar relatório de auditoria
    const relatorioAuditoria = await gerarRelatorioAuditoria(auditorias)
    
    return NextResponse.json({
      auditorias,
      analiseCompliance,
      violacoes,
      relatorioAuditoria,
      total: auditorias.length,
      message: 'Auditoria de compliance carregada'
    })
    
  } catch (error) {
    console.error('Error loading compliance audit:', error)
    return NextResponse.json({
      error: 'Erro ao carregar auditoria de compliance'
    }, { status: 500 })
  }
}
```

### Integration Points

#### Epic 7 Integration (Financeiro Essencial)

- **Invoice Data**: Integração completa com sistema de faturas e recebíveis
- **Payment Processing**: Acompanhamento de pagamentos e baixas automáticas  
- **Financial Analytics**: Métricas de cobrança integradas aos dashboards financeiros
- **Credit Scoring**: Uso de análise de crédito para priorização de cobrança

#### Epic 5 Integration (Portal Paciente)

- **Self-Service Payment**: Portal para pagamento e negociação online
- **Communication Preferences**: Respeito às preferências de canal do paciente
- **Payment History**: Acesso ao histórico de pagamentos no portal
- **Negotiation Interface**: Interface para negociação direta

#### Epic 6 Integration (Agenda Inteligente)

- **Appointment Correlation**: Relacionar cobrança com consultas futuras
- **Schedule-Based Collection**: Lembretes baseados na proximidade de consultas
- **Professional Coordination**: Coordenação com agenda dos profissionais
- **Treatment Continuity**: Evitar interrupção de tratamentos por cobrança

#### Epic 8 Integration (BI & Dashboards)

- **Recovery Analytics**: Dashboards específicos para recuperação de cobrança
- **Predictive Models**: ML para identificar melhores estratégias de cobrança
- **Performance Tracking**: KPIs de efetividade da cobrança
- **ROI Analysis**: Análise de retorno do investimento em cobrança

#### Epic 9 Integration (Cadastro & Prontuário)

- **Patient Context**: Consideração do contexto médico na cobrança
- **Treatment Status**: Evitar cobrança agressiva durante tratamentos críticos
- **Medical History**: Uso do histórico para personalização da comunicação
- **Health Reminders**: Combinação de lembretes médicos e financeiros

### Testing Strategy

#### Billing Recovery Tests

```typescript
describe('Billing Recovery System', () => {
  test('identifies overdue invoices correctly', async () => {
    const config = await createBillingConfig({
      diasVencimentoMinimo: 5,
      valorMinimoCobranca: 50
    })
    
    const faturas = await identifyOverdueInvoices(config.id)
    
    expect(faturas.every(f => f.diasAtraso >= 5)).toBe(true)
    expect(faturas.every(f => f.valor >= 50)).toBe(true)
  })
  
  test('executes escalation rules correctly', async () => {
    const processo = await createBillingProcess({
      diasAtraso: 15,
      valor: 200
    })
    
    const tentativa = await executeEscalationRule(processo.id, 2)
    
    expect(tentativa.etapa).toBe(2)
    expect(tentativa.status).toBe('enviado')
  })
  
  test('creates negotiation proposal correctly', async () => {
    const proposta = await createNegotiationProposal({
      valorOriginal: 1000,
      percentualDesconto: 20,
      numeroParcelas: 3
    })
    
    expect(proposta.valorProposto).toBe(800)
    expect(proposta.valorParcela).toBeCloseTo(266.67, 2)
  })
  
  test('respects compliance rules', async () => {
    const tentativa = await attemptBillingContact({
      horario: '22:00',
      canal: 'ligacao'
    })
    
    // Não deve permitir ligação fora do horário comercial
    expect(tentativa.status).toBe('bloqueado')
    expect(tentativa.motivoFalha).toContain('horário comercial')
  })
  
  test('tracks metrics accurately', async () => {
    const config = await createBillingConfig()
    
    // Simular algumas tentativas
    await simulateBillingAttempts(config.id, 10)
    
    const metrics = await calculateBillingMetrics(config.id)
    
    expect(metrics.totalTentativasRealizadas).toBe(10)
    expect(metrics.taxaRecuperacao).toBeGreaterThanOrEqual(0)
  })
})
```

### Dev Notes

#### Advanced Recovery Features

- **AI-Powered Timing**: ML para otimizar horários de contato
- **Sentiment Analysis**: Análise de sentimento nas respostas dos pacientes
- **Dynamic Discounting**: Ofertas de desconto baseadas no perfil do paciente
- **Multi-Channel Orchestration**: Coordenação inteligente entre canais

#### Compliance & Legal Features

- **Regulatory Updates**: Atualizações automáticas de regulamentações
- **Legal Documentation**: Geração automática de documentos legais
- **Audit Trail**: Rastro completo para auditoria jurídica
- **Consumer Protection**: Proteção automática dos direitos do consumidor

#### Performance Optimization

- **Batch Processing**: Processamento em lotes para alta performance
- **Smart Queuing**: Filas inteligentes baseadas em prioridade
- **Rate Limiting**: Controle de velocidade por canal e operadora
- **Retry Logic**: Lógica inteligente de retentatiya com backoff

---

## Dev Agent Record

### Task Status

- [x] Analyzed billing recovery requirements for Epic 10
- [x] Created comprehensive story with 6 acceptance criteria
- [x] Designed advanced TypeScript interfaces for billing automation
- [x] Specified database schema with compliance and audit features
- [x] Developed complete API endpoints for recovery processes
- [x] Added negotiation management and proposal system
- [x] Integrated compliance monitoring and audit trails
- [x] Created comprehensive testing strategy for billing recovery
- [x] Established integration points with Epic 5-9

### File List

- `docs/stories/10.3.story.md` - Billing Recovery and Financial Reminders implementation story

### Change Log

- **Story 10.3 Creation**: Complete billing recovery system with intelligent escalation
- **Compliance Framework**: Full compliance monitoring with LGPD and CDC requirements
- **Negotiation System**: Advanced proposal and negotiation management
- **Escalation Rules**: Configurable escalation with multiple channels and timing
- **Audit Trail**: Complete audit system for legal compliance
- **Performance Metrics**: Comprehensive analytics for recovery effectiveness
- **Epic Integration**: Full integration with Epic 5-9 for context-aware collection

### Completion Notes

Story 10.3 delivers a sophisticated billing recovery system that balances effective collection with customer relationship preservation. The system ensures legal compliance while maximizing recovery rates through intelligent automation and personalized communication.

### Next Story

Ready to create **Story 10.4: Analytics e ROI de Campanhas** - the comprehensive campaign analytics and ROI tracking system.
