# 🎯 KIRO CODING STANDARDS

## Development Philosophy

### Professional Excellence Standards

- **Context First**: Understand system completely before coding
- **Challenge Requests**: Identify edge cases, clarify requirements  
- **Hold Standards**: Modular, testable, documented code
- **Design Don't Patch**: Think architecture, not quick fixes
- **Execution Standards**: Complete implementations, no placeholders

### Quality Enforcement

- **Early Returns**: Handle errors at function beginning
- **Guard Clauses**: Use if-return pattern vs nested else
- **No Placeholders**: Complete functionality, no TODOs
- **Error Handling**: Comprehensive with proper logging
- **Type Safety**: Strict typing when applicable

## Code Structure

### File Organization
- One primary concern per file
- Clear naming conventions
- Logical directory structure
- Consistent import/export patterns

### Function Design
- Single responsibility principle
- Clear input/output contracts
- Comprehensive error handling
- Meaningful variable names

### Documentation
- Clear comments for complex logic
- API documentation for public interfaces
- README files for modules/packages
- Examples for usage patterns

## Testing Standards

- Unit tests for core functionality
- Integration tests for workflows
- Error case coverage
- Performance considerations

## Security Practices

- Input validation
- Output sanitization
- Secure defaults
- Principle of least privilege

## Performance Guidelines

- Efficient algorithms
- Resource management
- Caching strategies
- Monitoring considerations

---

**Core Principle**: "Enhance, Don't Proliferate" - Reuse ≥85%, Quality ≥8/10