# AgendaTrintaE3 Backend Development Rules

## Calendar API Design

### Route Structure
```
/api/events/*        - Event CRUD operations
/api/calendars/*     - Calendar management
/api/schedules/*     - Scheduling and availability
/api/recurring/*     - Recurring event management
/api/invitations/*   - Event invitations and RSVPs
/api/notifications/* - Notification management
/api/integrations/*  - External calendar integrations
```

### Event Data Model
```typescript
interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startDateTime: Date;
  endDateTime: Date;
  timezone: string;
  isAllDay: boolean;
  recurrenceRule?: string;  // RRULE format
  attendees: Attendee[];
  location?: Location;
  reminders: Reminder[];
  status: 'confirmed' | 'tentative' | 'cancelled';
  visibility: 'public' | 'private' | 'confidential';
}
```

## Timezone Management

### Timezone Handling
```typescript
// Always store UTC in database, convert for display
const storeEvent = async (event: CalendarEvent) => {
  const utcEvent = {
    ...event,
    startDateTime: convertToUTC(event.startDateTime, event.timezone),
    endDateTime: convertToUTC(event.endDateTime, event.timezone),
    originalTimezone: event.timezone
  };
  
  return await db.events.create(utcEvent);
};
```

### DST Handling
- Account for daylight saving time transitions
- Validate timezone transitions for recurring events
- Handle timezone changes for moved events
- Implement proper timezone conversion APIs

## Recurring Events Engine

### RRULE Implementation
```typescript
// Support for RFC 5545 RRULE standard
interface RecurrenceRule {
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
  interval?: number;
  count?: number;
  until?: Date;
  byDay?: WeekDay[];
  byMonth?: number[];
  byMonthDay?: number[];
  exceptions?: Date[];  // EXDATE
}
```

### Performance Optimization
- Generate recurring instances on-demand
- Cache frequently accessed recurring events
- Implement efficient date range queries
- Use database partitioning for large datasets

## Conflict Detection & Resolution

### Scheduling Conflicts
```typescript
const detectConflicts = async (newEvent: CalendarEvent, userId: string) => {
  const conflicts = await db.events.findMany({
    where: {
      userId,
      AND: [
        { startDateTime: { lt: newEvent.endDateTime } },
        { endDateTime: { gt: newEvent.startDateTime } }
      ]
    }
  });
  
  return conflicts.filter(event => !isConflictAllowed(event, newEvent));
};
```

### Availability Management
- Track user availability windows
- Implement busy/free time calculations
- Support for multiple calendar overlays
- Handle partial availability and preferences

## Real-time Synchronization

### WebSocket Events
```typescript
// Real-time event updates
interface CalendarWebSocketEvent {
  type: 'EVENT_CREATED' | 'EVENT_UPDATED' | 'EVENT_DELETED' | 'INVITATION_SENT';
  eventId: string;
  calendarId: string;
  affectedUsers: string[];
  timestamp: Date;
  data: any;
}
```

### Conflict Resolution
- Implement operational transformation for concurrent edits
- Handle offline/online synchronization
- Resolve conflicts with user preferences
- Maintain event history for rollback

## External Calendar Integration

### Supported Providers
```typescript
interface CalendarProvider {
  name: 'google' | 'outlook' | 'apple' | 'caldav';
  authType: 'oauth2' | 'basic' | 'token';
  syncCapabilities: {
    read: boolean;
    write: boolean;
    realtime: boolean;
  };
}
```

### Sync Strategy
- Bidirectional synchronization
- Conflict resolution policies
- Rate limiting for API calls
- Error handling and retry logic

## Notification System

### Notification Types
```typescript
interface EventNotification {
  type: 'reminder' | 'invitation' | 'update' | 'cancellation';
  deliveryMethod: 'email' | 'sms' | 'push' | 'webhook';
  timing: {
    triggerTime: Date;
    advance: number;  // minutes before event
  };
  template: string;
  recipients: string[];
}
```

### Delivery Management
- Queue-based notification processing
- Retry logic for failed deliveries
- User preference management
- Delivery status tracking

## Performance & Scalability

### Database Optimization
```sql
-- Optimized indexes for calendar queries
CREATE INDEX idx_events_user_daterange ON events(user_id, start_date_time, end_date_time);
CREATE INDEX idx_events_recurrence ON events(recurrence_rule) WHERE recurrence_rule IS NOT NULL;
CREATE INDEX idx_events_timezone ON events(timezone, start_date_time);
```

### Caching Strategy
- Cache user calendars and preferences
- Cache recurring event expansions
- Cache availability calculations
- Implement cache invalidation on updates

## Data Validation & Integrity

### Event Validation
```typescript
const validateEvent = (event: CalendarEvent): ValidationResult => {
  const errors: string[] = [];
  
  // Date validation
  if (event.endDateTime <= event.startDateTime) {
    errors.push('End time must be after start time');
  }
  
  // Timezone validation
  if (!isValidTimezone(event.timezone)) {
    errors.push('Invalid timezone specified');
  }
  
  // Recurrence validation
  if (event.recurrenceRule && !isValidRRule(event.recurrenceRule)) {
    errors.push('Invalid recurrence rule');
  }
  
  return { isValid: errors.length === 0, errors };
};
```