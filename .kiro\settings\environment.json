{"kiro_environment": {"version": "1.0.0", "platform": "windows", "shell": "cmd", "created": "2025-01-16", "adapted_from": "VIBECODE V1.0"}, "system_paths": {"steering_rules": ".kiro/steering/", "settings": ".kiro/settings/", "specs": ".kiro/specs/", "hooks": ".kiro/hooks/"}, "quality_enforcement": {"minimum_threshold": 8, "auto_refinement": true, "max_refinement_cycles": 3, "mandatory_verification": true}, "workflow_settings": {"mandatory_steps": 7, "one_task_at_a_time": true, "user_approval_required": true, "research_protocol_automatic": true}, "mcp_configuration": {"auto_approve_safe_operations": true, "log_level": "ERROR", "timeout_seconds": 30, "retry_attempts": 3}, "performance_targets": {"response_time_simple": 10, "response_time_standard": 30, "response_time_complex": 120, "quality_achievement_cycles": 3}, "feature_flags": {"auto_research_detection": true, "quality_auto_refinement": true, "task_complexity_routing": true, "spec_workflow_enabled": true}}