import { defineConfig } from "drizzle-kit";
import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

export default defineConfig({
  schema: "./src/lib/db/schema.ts",
  out: "./src/lib/db/migrations",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
  verbose: true,
  strict: true,
  migrations: {
    prefix: "supabase",
  },
});

// Generated by VIBECODE SYSTEM V1.0 - Standardization Process
// Drizzle ORM configuration for Aegis Wallet
// Compatible with Supabase PostgreSQL