// Optimized Drizzle Configuration V2.0
// Enhanced with Phase 4 Validation Learnings
// Generated by VIBECODE SYSTEM V4.0

import { config } from "dotenv";
import { defineConfig } from "drizzle-kit";

// Load environment variables
config({ path: ".env.local" });

export default defineConfig({
  schema: "./src/lib/schema.ts",
  out: "./drizzle",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL || "postgresql://localhost:5432/template",
  },
  verbose: true,
  strict: true,
});
