---
description: Defines a process for Kiloc<PERSON> to reflect on interactions and suggest improvements to active .kilocode.
author: https://github.com/nickbaumann98
version: 1.0
tags: ["meta", "self-improvement", "kilocode", "reflection", "core-behavior"]
globs: ["*"]
---

# Self-Improving Kilocode Reflection

**Objective:** Offer opportunities to continuously improve `.kilocode` based on user interactions and feedback.

**Trigger:** Before using the `attempt_completion` tool for any task that involved user feedback provided at any point during the conversation, or involved multiple non-trivial steps (e.g., multiple file edits, complex logic generation).

**Process:**

1.  **Offer Reflection:** Ask the user: "Before I complete the task, would you like me to reflect on our interaction and suggest potential improvements to the active `.kilocode`?"
2.  **Await User Confirmation:** Proceed to `attempt_completion` immediately if the user declines or doesn't respond affirmatively.
3.  **If User Confirms:**
    a. **Review Interaction:** Synthesize all feedback provided by the user throughout the entire conversation history for the task. Analyze how this feedback relates to the active `.kilocode` and identify areas where modified instructions could have improved the outcome or better aligned with user preferences.
    b. **Identify Active Rules:** List the specific global and workspace `.kilocode` files active during the task.
    c. **Formulate & Propose Improvements:** Generate specific, actionable suggestions for improving the _content_ of the relevant active rule files. Prioritize suggestions directly addressing user feedback. Use `replace_in_file` diff blocks when practical, otherwise describe changes clearly.
    d. **Await User Action on Suggestions:** Ask the user if they agree with the proposed improvements and if they'd like me to apply them _now_ using the appropriate tool (`replace_in_file` or `write_to_file`). Apply changes if approved, then proceed to `attempt_completion`.

**Constraint:** Do not offer reflection if:

- No `.kilocode` were active.
- The task was very simple and involved no feedback.
- A `PolicyViolationError` ocorreu durante a tarefa ou algum Quality
  Gate da regra *Unified MCP & Core Feature Enforcement* não foi
  satisfeito.
