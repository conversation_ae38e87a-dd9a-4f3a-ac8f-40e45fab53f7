#!/usr/bin/env python3

"""
VIBECODE V2.0 Knowledge Graph Module
Modular architecture for enterprise-grade knowledge management

This module provides a clean, modular architecture while maintaining
100% backward compatibility with the original knowledge_graph_manager.py
"""

try:
    # Core components - now imported from consolidated core module
    from .core import (
        # From models
        VibeCodeDataPoint,
        VibeCodeTask,
        DataPointType,
        TaskStatus,
        PYDANTIC_AVAILABLE,
        # From constants
        AGENT_PERFORMANCE_DATA,
        DOMAIN_AGENT_MAPPING,
        SUCCESS_PATTERNS,
        ERROR_PATTERNS,
        EMOJIS,
        # From utils
        log_with_context,
        handle_with_fallback,
        measure_performance,
        generate_id,
        calculate_confidence_score,
        validate_data_quality,
        sanitize_content,
        format_agent_response
    )

    from .validation import (
        PydanticValidationLayer,
        ECLPipelineManager,
        TaskBasedProcessor
    )

    from .temporal import (
        BiTemporalTracker
    )

    from .ml_components import (
        SemanticSimilarityEngine,
        AdaptiveLearningSystem,
        IntelligentCache,
        PredictiveAnalytics
    )

    from .production import (
        Production<PERSON>pt<PERSON><PERSON>,
        <PERSON>rror<PERSON><PERSON><PERSON>,
        ObservabilityManager,
        ConfigurationManager
    )
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    
    from core import (
        # From models
        VibeCodeDataPoint,
        VibeCodeTask,
        DataPointType,
        TaskStatus,
        PYDANTIC_AVAILABLE,
        # From constants
        AGENT_PERFORMANCE_DATA,
        DOMAIN_AGENT_MAPPING,
        SUCCESS_PATTERNS,
        ERROR_PATTERNS,
        EMOJIS,
        # From utils
        log_with_context,
        handle_with_fallback,
        measure_performance,
        generate_id,
        calculate_confidence_score,
        validate_data_quality,
        sanitize_content,
        format_agent_response
    )

    from validation import (
        PydanticValidationLayer,
        ECLPipelineManager,
        TaskBasedProcessor
    )

    from temporal import (
        BiTemporalTracker
    )

    from ml_components import (
        SemanticSimilarityEngine,
        AdaptiveLearningSystem,
        IntelligentCache,
        PredictiveAnalytics
    )

    from production import (
        ProductionOptimizer,
        ErrorHandler,
        ObservabilityManager,
        ConfigurationManager
    )

# Version info
__version__ = "2.0.0"
__author__ = "GRUPO US - VIBECODE HYBRID SYSTEM"

# Public API
__all__ = [
    # Data models
    'VibeCodeDataPoint',
    'VibeCodeTask',
    'DataPointType',
    'TaskStatus',
    'PYDANTIC_AVAILABLE',

    # Constants
    'AGENT_PERFORMANCE_DATA',
    'DOMAIN_AGENT_MAPPING',
    'SUCCESS_PATTERNS',
    'ERROR_PATTERNS',
    'EMOJIS',

    # Utilities
    'log_with_context',
    'handle_with_fallback',
    'measure_performance',
    'generate_id',
    'calculate_confidence_score',

    # Core components
    'PydanticValidationLayer',
    'ECLPipelineManager',
    'TaskBasedProcessor',
    'BiTemporalTracker',

    # ML components
    'SemanticSimilarityEngine',
    'AdaptiveLearningSystem',
    'IntelligentCache',
    'PredictiveAnalytics',

    # Production components
    'ProductionOptimizer',
    'ErrorHandler',
    'ObservabilityManager',
    'ConfigurationManager'
]
