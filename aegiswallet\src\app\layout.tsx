// import { StagewiseProvider } from "@saas-projects/shared/stagewise";
import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Aegis Wallet - Crypto Financial Brasil",
  description:
    "Secure crypto wallet application for Brazilian financial market",
  keywords: [
    "crypto",
    "wallet",
    "financial",
    "brasil",
    "blockchain",
    "bitcoin",
  ],
  authors: [{ name: "GRUPO US" }],
  openGraph: {
    title: "Aegis Wallet - Crypto Financial Brasil",
    description:
      "Secure crypto wallet application for Brazilian financial market",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <body className="min-h-screen bg-slate-900 text-slate-50 antialiased">
        {children}
        {/* <StagewiseProvider
          projectName="aegiswallet"
          debug={process.env.NODE_ENV === "development"}
        /> */}
      </body>
    </html>
  );
}
