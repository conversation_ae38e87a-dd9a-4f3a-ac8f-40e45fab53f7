{"warpConfiguration": {"version": "1.0.0", "name": "VIBECODE Integration for Warp CLI", "description": "Complete integration of VIBECODE V1.0 standards with Warp CLI", "environment": "production", "lastUpdated": "2025-07-19T22:30:00.000Z"}, "paths": {"rules_directory": ".warp", "mcps_config": ".warp/mcps.json", "settings": ".warp/settings/warp-config.json", "project_root": "E:/VIBECODE", "backup_directory": "E:/CODE-BACKUP"}, "features": {"auto_load_rules": true, "enforce_workflow": true, "quality_gates": true, "task_management": true, "research_protocol": true, "batch_operations": true, "coding_standards": true, "performance_monitoring": true}, "behavior": {"auto_apply_rules": true, "enforce_quality_threshold": true, "auto_task_management_threshold": 3, "mandatory_research_protocol": true, "automatic_tool_selection": true, "batch_api_optimization": true}, "integration": {"vibecode_system": {"enabled": true, "version": "1.0.0", "principles": "Aprimore, Não Prolifere", "quality_minimum": 8, "confidence_minimum": 90}, "cursor_compatibility": true, "augment_compatibility": true, "native_task_management": true}, "workflow": {"enabled": true, "mandatory": true, "steps": 7, "auto_refinement": true, "max_refinement_iterations": 3, "quality_enforcement": true}, "mcps": {"enabled": true, "auto_select": true, "tier_system": true, "batch_operations": true, "api_optimization": true, "cost_reduction_target": 70}, "rules": {"master_rules": ".warp/master-rules.md", "coding_standards": ".warp/coding-standards.md", "project_config": ".warp/project-config.md", "auto_apply": true, "inheritance": "vibecode-v1.0"}, "quality": {"minimum_score": 8, "auto_refine": true, "completeness_required": true, "performance_targets": {"rule_lookup": "100ms", "lcp": "2.5s", "fid": "100ms", "bundle_size": "200kb"}}, "security": {"input_validation": true, "data_sanitization": true, "authentication_middleware": true, "security_headers": true, "environment_protection": true}, "monitoring": {"performance_tracking": true, "quality_metrics": true, "api_call_optimization": true, "error_tracking": true, "success_patterns": true}, "compatibility": {"windows": true, "powershell": true, "warp_cli": true, "typescript": true, "nextjs": true, "react": true, "supabase": true}}