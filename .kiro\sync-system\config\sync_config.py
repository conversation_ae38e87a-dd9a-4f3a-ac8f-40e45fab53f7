"""
Configuration management for the VIBECODE-Kiro sync system.
"""

import json
import os
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Any, Optional
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SyncConfig:
    """Main configuration for the sync system."""
    
    # Source and target paths
    source_path: str = r"E:\VIBECODE\.cursor"
    target_path: str = ".kiro"
    
    # Sync behavior
    auto_sync_enabled: bool = True
    sync_interval_minutes: int = 30
    backup_retention_days: int = 30
    conflict_resolution_strategy: str = "preserve_kiro_customizations"
    
    # Monitoring configuration
    file_types: List[str] = None
    excluded_paths: List[str] = None
    debounce_delay_ms: int = 1000
    
    # Adaptation settings
    preserve_kiro_optimizations: bool = True
    update_core_principles: bool = True
    merge_mcp_configurations: bool = True
    
    # Performance settings
    max_concurrent_operations: int = 5
    sync_timeout_seconds: int = 300
    enable_performance_monitoring: bool = True
    
    # Security settings
    validate_file_paths: bool = True
    encrypt_backups: bool = False
    log_security_events: bool = True
    
    def __post_init__(self):
        if self.file_types is None:
            self.file_types = [".mdc", ".json", ".md", ".py"]
        if self.excluded_paths is None:
            self.excluded_paths = ["temp", "cache", "logs", "__pycache__", ".git"]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncConfig':
        """Create configuration from dictionary."""
        return cls(**data)
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate paths
        if not os.path.exists(self.source_path):
            errors.append(f"Source path does not exist: {self.source_path}")
        
        if not os.path.exists(self.target_path):
            try:
                os.makedirs(self.target_path, exist_ok=True)
            except Exception as e:
                errors.append(f"Cannot create target path {self.target_path}: {e}")
        
        # Validate intervals and timeouts
        if self.sync_interval_minutes < 1:
            errors.append("Sync interval must be at least 1 minute")
        
        if self.backup_retention_days < 1:
            errors.append("Backup retention must be at least 1 day")
        
        if self.sync_timeout_seconds < 30:
            errors.append("Sync timeout must be at least 30 seconds")
        
        # Validate conflict resolution strategy
        valid_strategies = [
            "preserve_kiro_customizations",
            "prefer_vibecode_updates", 
            "manual_review_all",
            "merge_intelligent"
        ]
        if self.conflict_resolution_strategy not in valid_strategies:
            errors.append(f"Invalid conflict resolution strategy: {self.conflict_resolution_strategy}")
        
        return errors


class ConfigManager:
    """Manages loading, saving, and validation of sync configuration."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or ".kiro/sync-system/config/sync_config.json"
        self._config: Optional[SyncConfig] = None
        self._config_file_watcher = None
    
    def load_config(self) -> SyncConfig:
        """Load configuration from file or create default."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self._config = SyncConfig.from_dict(data)
                logger.info(f"Configuration loaded from {self.config_path}")
            else:
                self._config = SyncConfig()
                self.save_config()
                logger.info("Default configuration created")
            
            # Validate configuration
            errors = self._config.validate()
            if errors:
                logger.warning(f"Configuration validation errors: {errors}")
                
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self._config = SyncConfig()
        
        return self._config
    
    def save_config(self, config: Optional[SyncConfig] = None) -> bool:
        """Save configuration to file."""
        try:
            config_to_save = config or self._config
            if not config_to_save:
                logger.error("No configuration to save")
                return False
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_to_save.to_dict(), f, indent=2, default=str)
            
            logger.info(f"Configuration saved to {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False
    
    def get_config(self) -> SyncConfig:
        """Get current configuration, loading if necessary."""
        if self._config is None:
            self.load_config()
        return self._config
    
    def update_config(self, **kwargs) -> bool:
        """Update configuration with new values."""
        try:
            config = self.get_config()
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                else:
                    logger.warning(f"Unknown configuration key: {key}")
            
            return self.save_config(config)
            
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """Reset configuration to defaults."""
        try:
            self._config = SyncConfig()
            return self.save_config()
        except Exception as e:
            logger.error(f"Error resetting configuration: {e}")
            return False
    
    def validate_current_config(self) -> List[str]:
        """Validate current configuration."""
        config = self.get_config()
        return config.validate()