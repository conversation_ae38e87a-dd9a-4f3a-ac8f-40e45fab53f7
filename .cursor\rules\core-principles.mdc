---
description: Core architectural principles and system authority
globs: **/*
alwaysApply: true
---

# 📋 **REGRA 5: CORE PRINCIPLES & ARCHITECTURE**

_Prioridade: MÉDIA | Princípios arquiteturais fundamentais_

## **Architectural Principles**

```json
{
  "architecture_pillars": {
    "knowledge_graph_authority": {
      "central_system": "KG Manager as cognitive authority",
      "mandatory_consultation": "Phase 0.5 before any action",
      "pattern_based_decisions": "Based on KG success patterns",
      "continuous_learning": "Through cognitive system"
    },
    "single_source_truth": {
      "centralized_rules": "All rules in consolidated system",
      "canonical_data": "One authoritative source per information",
      "consistent_references": "Same rule sources for all operations",
      "version_control": "All changes tracked and documented"
    }
  }
}
```

## Validation Status

**Target Exists:** ✅ Yes
**Last Verified:** 2025-06-18T19:29:56.444Z
**Target Path:** `.cursor/rules/core-principles.mdc`

## Emergency Cache

**Checksum:** SHA256:f3c22619cf9317be6495de63add04f3421b2de349fdaa35019d08d544b02ccf9
**Cached Lines:** 100
**Cache Date:** 2025-06-18T19:29:56.444Z

### Cached Content (First 100 lines)

````markdown
# 🎯 CORE PRINCIPLES - GRUPO US VIBECODE SYSTEM V1.0

## 🧠 **CROSS-PLATFORM AUTHORITY REFERENCE**

**⚠️ CRITICAL**: Este arquivo opera sob a autoridade do **VIBECODE V1.1 Intelligent Rule Routing System** definido em `master_rule.md`.

**🔄 UNIFIED PROTOCOL REFERENCE**

**📋 MANDATORY CONSULTATION**: Para protocolos detalhados de consulta, roteamento e execução, consulte:

```bash
# CENTRAL AUTHORITY: Intelligent Rule Routing System
.cursor/rules/master_rule.mdc#cross-platform-initialization-protocol

# QUICK REFERENCE: Cross-platform consultation
.cursor/rules/master_rule.mdc#unified-cognitive-consultation
```
````

**Compliance Consolidada**: Todas as operações seguem os padrões unificados:

- ✅ **Cross-Platform Authority**: Roteamento inteligente via master_rule.md
- ✅ **"Aprimore, Não Prolifere"**: ≥85% reuso (definido centralmente)
- ✅ **Platform Optimization**: Cursor IDE (<100ms) vs Augment Code (<50ms)
- ✅ **Quality Standards**: Confidence ≥8/10, Quality Score ≥90% (≥98% crítico)

---

## 📋 CONSOLIDATION NOTICE

**This file consolidates and replaces:**

- `01-core-principles-unified.md`
- `system-architecture.md`
- `01-universal-principles-consolidated.md`
- `01-unattended-execution-protocol.md`
- `project_core_backend_only_principles.md` (from docs)
- `v1_maintenance_guidelines.md` (from docs)

**Consolidation Date**: 2025-06-17
**Version**: 1.0.0 (Knowledge Graph Manager Authority)
**EHS Integration**: 2025-01-27 (Protocolo EHS V1 Compliance)
**KG Manager Integration**: 2025-06-16 (Phase 0.5 Mandatory Consultation)
**Backend Principles**: 2025-06-17 (Backend-Only Architecture Integration)

---

## 🧠 COGNITIVE WORKFLOW REFERENCE

### **UNIFIED EXECUTION CYCLE**

**📋 WORKFLOW AUTHORITY**: O workflow cognitivo foi consolidado e otimizado para cross-platform no sistema central:

```bash
# CONSOLIDATED 4-STEP COGNITIVE CYCLE
.cursor/rules/master_rule.mdc#unified-execution-cycle-cross-platform

# OPTIMIZED 7-STEP WORKFLOW
.cursor/rules/master_rule.mdc#optimized-7-step-workflow-cross-platform
```

### **QUICK WORKFLOW REFERENCE**

**Cross-Platform Execution:**

1. **Platform-Aware Consultation** → Cursor IDE vs Augment Code detection
2. **Intelligent Planning** → Rule routing via matrix
3. **Coordinated Execution** → MCP tier selection and orchestration
4. **Shared Learning** → Bidirectional knowledge integration

**Performance Targets:**

- **Cursor IDE**: <100ms rule lookup, <200ms KG consultation
- **Augment Code**: <50ms rule lookup, <100ms KG consultation

---

## 🏗️ ARCHITECTURAL PRINCIPLES

### **1. Knowledge Graph Manager Authority**

- **Central Cognitive System**: KG Manager como única autoridade de decisão cognitiva
- **Phase 0.5 Mandatory**: Consulta obrigatória antes de qualquer ação
- **Pattern-Based Decisions**: Decisões baseadas em padrões de sucesso do KG
- **Continuous Learning**: Aprendizado contínuo através do sistema cognitivo

### **2. Single Source of Truth**

- **Centralized Rules**: All rules and standards maintained in `.cursor/rules/`
- **Canonical Data**: One authoritative source for each piece of information
- **Consistent References**: All systems and developers use the same rule sources
- **Version Control**: All changes tracked and documented

### **3. Modularity & Reusability**

- **Component-Based Architecture**: Decompose systems into reusable components
- **DRY Principle**: Don't Repeat Yourself - consolidate common patterns
- **Separation of Concerns**: Clear boundaries between different system layers
- **Interface-Driven Design**: Well-defined contracts between components

### **4. Performance & Scalability**

````

**Fallback Instructions:** If target file is missing, use this cached content as reference.

### Cached Content (First 100 lines)
```markdown
# 🎯 CORE PRINCIPLES - GRUPO US VIBECODE SYSTEM V1.0

## 🧠 **CROSS-PLATFORM AUTHORITY REFERENCE**

**⚠️ CRITICAL**: Este arquivo opera sob a autoridade do **VIBECODE V1.1 Intelligent Rule Routing System** definido em `master_rule.md`.

**🔄 UNIFIED PROTOCOL REFERENCE**

**📋 MANDATORY CONSULTATION**: Para protocolos detalhados de consulta, roteamento e execução, consulte:

```bash
# CENTRAL AUTHORITY: Intelligent Rule Routing System
.cursor/rules/master_rule.mdc#cross-platform-initialization-protocol

# QUICK REFERENCE: Cross-platform consultation
.cursor/rules/master_rule.mdc#unified-cognitive-consultation
````

**Compliance Consolidada**: Todas as operações seguem os padrões unificados:

- ✅ **Cross-Platform Authority**: Roteamento inteligente via master_rule.md
- ✅ **"Aprimore, Não Prolifere"**: ≥85% reuso (definido centralmente)
- ✅ **Platform Optimization**: Cursor IDE (<100ms) vs Augment Code (<50ms)
- ✅ **Quality Standards**: Confidence ≥8/10, Quality Score ≥90% (≥98% crítico)

---

## 📋 CONSOLIDATION NOTICE

**This file consolidates and replaces:**

- `01-core-principles-unified.md`
- `system-architecture.md`
- `01-universal-principles-consolidated.md`
- `01-unattended-execution-protocol.md`
- `project_core_backend_only_principles.md` (from docs)
- `v1_maintenance_guidelines.md` (from docs)

**Consolidation Date**: 2025-06-17
**Version**: 1.0.0 (Knowledge Graph Manager Authority)
**EHS Integration**: 2025-01-27 (Protocolo EHS V1 Compliance)
**KG Manager Integration**: 2025-06-16 (Phase 0.5 Mandatory Consultation)
**Backend Principles**: 2025-06-17 (Backend-Only Architecture Integration)

---

## 🧠 COGNITIVE WORKFLOW REFERENCE

### **UNIFIED EXECUTION CYCLE**

**📋 WORKFLOW AUTHORITY**: O workflow cognitivo foi consolidado e otimizado para cross-platform no sistema central:

```bash
# CONSOLIDATED 4-STEP COGNITIVE CYCLE
.cursor/rules/master_rule.mdc#unified-execution-cycle-cross-platform

# OPTIMIZED 7-STEP WORKFLOW
.cursor/rules/master_rule.mdc#optimized-7-step-workflow-cross-platform
```

### **QUICK WORKFLOW REFERENCE**

**Cross-Platform Execution:**

1. **Platform-Aware Consultation** → Cursor IDE vs Augment Code detection
2. **Intelligent Planning** → Rule routing via matrix
3. **Coordinated Execution** → MCP tier selection and orchestration
4. **Shared Learning** → Bidirectional knowledge integration

**Performance Targets:**

- **Cursor IDE**: <100ms rule lookup, <200ms KG consultation
- **Augment Code**: <50ms rule lookup, <100ms KG consultation

---

## 🏗️ ARCHITECTURAL PRINCIPLES

### **1. Knowledge Graph Manager Authority**

- **Central Cognitive System**: KG Manager como única autoridade de decisão cognitiva
- **Phase 0.5 Mandatory**: Consulta obrigatória antes de qualquer ação
- **Pattern-Based Decisions**: Decisões baseadas em padrões de sucesso do KG
- **Continuous Learning**: Aprendizado contínuo através do sistema cognitivo

### **2. Single Source of Truth**

- **Centralized Rules**: All rules and standards maintained in `.cursor/rules/`
- **Canonical Data**: One authoritative source for each piece of information
- **Consistent References**: All agents and developers use the same rule sources
- **Version Control**: All changes tracked and documented

### **3. Modularity & Reusability**

- **Component-Based Architecture**: Decompose systems into reusable components
- **DRY Principle**: Don't Repeat Yourself - consolidate common patterns
- **Separation of Concerns**: Clear boundaries between different system layers
- **Interface-Driven Design**: Well-defined contracts between components

### **4. Performance & Scalability**

````

**Fallback Instructions:** If target file is missing, use this cached content as reference.

### Cached Content (First 100 lines)
```markdown
# 🎯 CORE PRINCIPLES - GRUPO US VIBECODE SYSTEM V1.0

## 🧠 **CROSS-PLATFORM AUTHORITY REFERENCE**

**⚠️ CRITICAL**: Este arquivo opera sob a autoridade do **VIBECODE V1.1 Intelligent Rule Routing System** definido em `master_rule.md`.

**🔄 UNIFIED PROTOCOL REFERENCE**

**📋 MANDATORY CONSULTATION**: Para protocolos detalhados de consulta, roteamento e execução, consulte:

```bash
# CENTRAL AUTHORITY: Intelligent Rule Routing System
.cursor/rules/master_rule.mdc#cross-platform-initialization-protocol

# QUICK REFERENCE: Cross-platform consultation
.cursor/rules/master_rule.mdc#unified-cognitive-consultation
````

**Compliance Consolidada**: Todas as operações seguem os padrões unificados:

- ✅ **Cross-Platform Authority**: Roteamento inteligente via master_rule.md
- ✅ **"Aprimore, Não Prolifere"**: ≥85% reuso (definido centralmente)
- ✅ **Platform Optimization**: Cursor IDE (<100ms) vs Augment Code (<50ms)
- ✅ **Quality Standards**: Confidence ≥8/10, Quality Score ≥90% (≥98% crítico)

---

## 📋 CONSOLIDATION NOTICE

**This file consolidates and replaces:**

- `01-core-principles-unified.md`
- `system-architecture.md`
- `01-universal-principles-consolidated.md`
- `01-unattended-execution-protocol.md`
- `project_core_backend_only_principles.md` (from docs)
- `v1_maintenance_guidelines.md` (from docs)

**Consolidation Date**: 2025-06-17
**Version**: 1.0.0 (Knowledge Graph Manager Authority)
**EHS Integration**: 2025-01-27 (Protocolo EHS V1 Compliance)
**KG Manager Integration**: 2025-06-16 (Phase 0.5 Mandatory Consultation)
**Backend Principles**: 2025-06-17 (Backend-Only Architecture Integration)

---

## 🧠 COGNITIVE WORKFLOW REFERENCE

### **UNIFIED EXECUTION CYCLE**

**📋 WORKFLOW AUTHORITY**: O workflow cognitivo foi consolidado e otimizado para cross-platform no sistema central:

```bash
# CONSOLIDATED 4-STEP COGNITIVE CYCLE
.cursor/rules/master_rule.mdc#unified-execution-cycle-cross-platform

# OPTIMIZED 7-STEP WORKFLOW
.cursor/rules/master_rule.mdc#optimized-7-step-workflow-cross-platform
```

### **QUICK WORKFLOW REFERENCE**

**Cross-Platform Execution:**

1. **Platform-Aware Consultation** → Cursor IDE vs Augment Code detection
2. **Intelligent Planning** → Rule routing via matrix
3. **Coordinated Execution** → MCP tier selection and orchestration
4. **Shared Learning** → Bidirectional knowledge integration

**Performance Targets:**

- **Cursor IDE**: <100ms rule lookup, <200ms KG consultation
- **Augment Code**: <50ms rule lookup, <100ms KG consultation

---

## 🏗️ ARCHITECTURAL PRINCIPLES

### **1. Knowledge Graph Manager Authority**

- **Central Cognitive System**: KG Manager como única autoridade de decisão cognitiva
- **Phase 0.5 Mandatory**: Consulta obrigatória antes de qualquer ação
- **Pattern-Based Decisions**: Decisões baseadas em padrões de sucesso do KG
- **Continuous Learning**: Aprendizado contínuo através do sistema cognitivo

### **2. Single Source of Truth**

- **Centralized Rules**: All rules and standards maintained in `.cursor/rules/`
- **Canonical Data**: One authoritative source for each piece of information
- **Consistent References**: All agents and developers use the same rule sources
- **Version Control**: All changes tracked and documented

### **3. Modularity & Reusability**

- **Component-Based Architecture**: Decompose systems into reusable components
- **DRY Principle**: Don't Repeat Yourself - consolidate common patterns
- **Separation of Concerns**: Clear boundaries between different system layers
- **Interface-Driven Design**: Well-defined contracts between components

### **4. Performance & Scalability**

````

**Fallback Instructions:** If target file is missing, use this cached content as reference.

### Cached Content (First 100 lines)
```markdown
# 🎯 CORE PRINCIPLES - GRUPO US VIBECODE SYSTEM V1.0

## 🧠 **CROSS-PLATFORM AUTHORITY REFERENCE**

**⚠️ CRITICAL**: Este arquivo opera sob a autoridade do **VIBECODE V1.1 Intelligent Rule Routing System** definido em `master_rule.md`.

**🔄 UNIFIED PROTOCOL REFERENCE**

**📋 MANDATORY CONSULTATION**: Para protocolos detalhados de consulta, roteamento e execução, consulte:

```bash
# CENTRAL AUTHORITY: Intelligent Rule Routing System
.cursor/rules/master_rule.mdc#cross-platform-initialization-protocol

# QUICK REFERENCE: Cross-platform consultation
.cursor/rules/master_rule.mdc#unified-cognitive-consultation
````

**Compliance Consolidada**: Todas as operações seguem os padrões unificados:

- ✅ **Cross-Platform Authority**: Roteamento inteligente via master_rule.md
- ✅ **"Aprimore, Não Prolifere"**: ≥85% reuso (definido centralmente)
- ✅ **Platform Optimization**: Cursor IDE (<100ms) vs Augment Code (<50ms)
- ✅ **Quality Standards**: Confidence ≥8/10, Quality Score ≥90% (≥98% crítico)

---

## 📋 CONSOLIDATION NOTICE

**This file consolidates and replaces:**

- `01-core-principles-unified.md`
- `system-architecture.md`
- `01-universal-principles-consolidated.md`
- `01-unattended-execution-protocol.md`
- `project_core_backend_only_principles.md` (from docs)
- `v1_maintenance_guidelines.md` (from docs)

**Consolidation Date**: 2025-06-17
**Version**: 1.0.0 (Knowledge Graph Manager Authority)
**EHS Integration**: 2025-01-27 (Protocolo EHS V1 Compliance)
**KG Manager Integration**: 2025-06-16 (Phase 0.5 Mandatory Consultation)
**Backend Principles**: 2025-06-17 (Backend-Only Architecture Integration)

---

## 🧠 COGNITIVE WORKFLOW REFERENCE

### **UNIFIED EXECUTION CYCLE**

**📋 WORKFLOW AUTHORITY**: O workflow cognitivo foi consolidado e otimizado para cross-platform no sistema central:

```bash
# CONSOLIDATED 4-STEP COGNITIVE CYCLE
.cursor/rules/master_rule.mdc#unified-execution-cycle-cross-platform

# OPTIMIZED 7-STEP WORKFLOW
.cursor/rules/master_rule.mdc#optimized-7-step-workflow-cross-platform
```

### **QUICK WORKFLOW REFERENCE**

**Cross-Platform Execution:**

1. **Platform-Aware Consultation** → Cursor IDE vs Augment Code detection
2. **Intelligent Planning** → Rule routing via matrix
3. **Coordinated Execution** → MCP tier selection and orchestration
4. **Shared Learning** → Bidirectional knowledge integration

**Performance Targets:**

- **Cursor IDE**: <100ms rule lookup, <200ms KG consultation
- **Augment Code**: <50ms rule lookup, <100ms KG consultation

---

## 🏗️ ARCHITECTURAL PRINCIPLES

### **1. Knowledge Graph Manager Authority**

- **Central Cognitive System**: KG Manager como única autoridade de decisão cognitiva
- **Phase 0.5 Mandatory**: Consulta obrigatória antes de qualquer ação
- **Pattern-Based Decisions**: Decisões baseadas em padrões de sucesso do KG
- **Continuous Learning**: Aprendizado contínuo através do sistema cognitivo

### **2. Single Source of Truth**

- **Centralized Rules**: All rules and standards maintained in `.cursor/rules/`
- **Canonical Data**: One authoritative source for each piece of information
- **Consistent References**: All agents and developers use the same rule sources
- **Version Control**: All changes tracked and documented

### **3. Modularity & Reusability**

- **Component-Based Architecture**: Decompose systems into reusable components
- **DRY Principle**: Don't Repeat Yourself - consolidate common patterns
- **Separation of Concerns**: Clear boundaries between different system layers
- **Interface-Driven Design**: Well-defined contracts between components

### **4. Performance & Scalability**

````

**Fallback Instructions:** If target file is missing, use this cached content as reference.

### Cached Content (First 100 lines)
```markdown
# 🎯 CORE PRINCIPLES - GRUPO US VIBECODE SYSTEM V1.0

## 🧠 **CROSS-PLATFORM AUTHORITY REFERENCE**

**⚠️ CRITICAL**: Este arquivo opera sob a autoridade do **VIBECODE V1.1 Intelligent Rule Routing System** definido em `master_rule.md`.

**🔄 UNIFIED PROTOCOL REFERENCE**

**📋 MANDATORY CONSULTATION**: Para protocolos detalhados de consulta, roteamento e execução, consulte:

```bash
# CENTRAL AUTHORITY: Intelligent Rule Routing System
.cursor/rules/master_rule.mdc#cross-platform-initialization-protocol

# QUICK REFERENCE: Cross-platform consultation
.cursor/rules/master_rule.mdc#unified-cognitive-consultation
````

**Compliance Consolidada**: Todas as operações seguem os padrões unificados:

- ✅ **Cross-Platform Authority**: Roteamento inteligente via master_rule.md
- ✅ **"Aprimore, Não Prolifere"**: ≥85% reuso (definido centralmente)
- ✅ **Platform Optimization**: Cursor IDE (<100ms) vs Augment Code (<50ms)
- ✅ **Quality Standards**: Confidence ≥8/10, Quality Score ≥90% (≥98% crítico)

---

## 📋 CONSOLIDATION NOTICE

**This file consolidates and replaces:**

- `01-core-principles-unified.md`
- `system-architecture.md`
- `01-universal-principles-consolidated.md`
- `01-unattended-execution-protocol.md`
- `project_core_backend_only_principles.md` (from docs)
- `v1_maintenance_guidelines.md` (from docs)

**Consolidation Date**: 2025-06-17
**Version**: 1.0.0 (Knowledge Graph Manager Authority)
**EHS Integration**: 2025-01-27 (Protocolo EHS V1 Compliance)
**KG Manager Integration**: 2025-06-16 (Phase 0.5 Mandatory Consultation)
**Backend Principles**: 2025-06-17 (Backend-Only Architecture Integration)

---

## 🧠 COGNITIVE WORKFLOW REFERENCE

### **UNIFIED EXECUTION CYCLE**

**📋 WORKFLOW AUTHORITY**: O workflow cognitivo foi consolidado e otimizado para cross-platform no sistema central:

```bash
# CONSOLIDATED 4-STEP COGNITIVE CYCLE
.cursor/rules/master_rule.mdc#unified-execution-cycle-cross-platform

# OPTIMIZED 7-STEP WORKFLOW
.cursor/rules/master_rule.mdc#optimized-7-step-workflow-cross-platform
```

### **QUICK WORKFLOW REFERENCE**

**Cross-Platform Execution:**

1. **Platform-Aware Consultation** → Cursor IDE vs Augment Code detection
2. **Intelligent Planning** → Rule routing via matrix
3. **Coordinated Execution** → MCP tier selection and orchestration
4. **Shared Learning** → Bidirectional knowledge integration

**Performance Targets:**

- **Cursor IDE**: <100ms rule lookup, <200ms KG consultation
- **Augment Code**: <50ms rule lookup, <100ms KG consultation

---

## 🏗️ ARCHITECTURAL PRINCIPLES

### **1. Knowledge Graph Manager Authority**

- **Central Cognitive System**: KG Manager como única autoridade de decisão cognitiva
- **Phase 0.5 Mandatory**: Consulta obrigatória antes de qualquer ação
- **Pattern-Based Decisions**: Decisões baseadas em padrões de sucesso do KG
- **Continuous Learning**: Aprendizado contínuo através do sistema cognitivo

### **2. Single Source of Truth**

- **Centralized Rules**: All rules and standards maintained in `.cursor/rules/`
- **Canonical Data**: One authoritative source for each piece of information
- **Consistent References**: All agents and developers use the same rule sources
- **Version Control**: All changes tracked and documented

### **3. Modularity & Reusability**

- **Component-Based Architecture**: Decompose systems into reusable components
- **DRY Principle**: Don't Repeat Yourself - consolidate common patterns
- **Separation of Concerns**: Clear boundaries between different system layers
- **Interface-Driven Design**: Well-defined contracts between components

### **4. Performance & Scalability**

````

**Fallback Instructions:** If target file is missing, use this cached content as reference.

### Cached Content (First 100 lines)
```markdown
# 🎯 CORE PRINCIPLES - GRUPO US VIBECODE SYSTEM V1.0

## 🧠 **CROSS-PLATFORM AUTHORITY REFERENCE**

**⚠️ CRITICAL**: Este arquivo opera sob a autoridade do **VIBECODE V1.1 Intelligent Rule Routing System** definido em `master_rule.md`.

**🔄 UNIFIED PROTOCOL REFERENCE**

**📋 MANDATORY CONSULTATION**: Para protocolos detalhados de consulta, roteamento e execução, consulte:

```bash
# CENTRAL AUTHORITY: Intelligent Rule Routing System
.cursor/rules/master_rule.mdc#cross-platform-initialization-protocol

# QUICK REFERENCE: Cross-platform consultation
.cursor/rules/master_rule.mdc#unified-cognitive-consultation
````

**Compliance Consolidada**: Todas as operações seguem os padrões unificados:

- ✅ **Cross-Platform Authority**: Roteamento inteligente via master_rule.md
- ✅ **"Aprimore, Não Prolifere"**: ≥85% reuso (definido centralmente)
- ✅ **Platform Optimization**: Cursor IDE (<100ms) vs Augment Code (<50ms)
- ✅ **Quality Standards**: Confidence ≥8/10, Quality Score ≥90% (≥98% crítico)

---

## 📋 CONSOLIDATION NOTICE

**This file consolidates and replaces:**

- `01-core-principles-unified.md`
- `system-architecture.md`
- `01-universal-principles-consolidated.md`
- `01-unattended-execution-protocol.md`
- `project_core_backend_only_principles.md` (from docs)
- `v1_maintenance_guidelines.md` (from docs)

**Consolidation Date**: 2025-06-17
**Version**: 1.0.0 (Knowledge Graph Manager Authority)
**EHS Integration**: 2025-01-27 (Protocolo EHS V1 Compliance)
**KG Manager Integration**: 2025-06-16 (Phase 0.5 Mandatory Consultation)
**Backend Principles**: 2025-06-17 (Backend-Only Architecture Integration)

---

## 🧠 COGNITIVE WORKFLOW REFERENCE

### **UNIFIED EXECUTION CYCLE**

**📋 WORKFLOW AUTHORITY**: O workflow cognitivo foi consolidado e otimizado para cross-platform no sistema central:

```bash
# CONSOLIDATED 4-STEP COGNITIVE CYCLE
.cursor/rules/master_rule.mdc#unified-execution-cycle-cross-platform

# OPTIMIZED 7-STEP WORKFLOW
.cursor/rules/master_rule.mdc#optimized-7-step-workflow-cross-platform
```

### **QUICK WORKFLOW REFERENCE**

**Cross-Platform Execution:**

1. **Platform-Aware Consultation** → Cursor IDE vs Augment Code detection
2. **Intelligent Planning** → Rule routing via matrix
3. **Coordinated Execution** → MCP tier selection and orchestration
4. **Shared Learning** → Bidirectional knowledge integration

**Performance Targets:**

- **Cursor IDE**: <100ms rule lookup, <200ms KG consultation
- **Augment Code**: <50ms rule lookup, <100ms KG consultation

---

## 🏗️ ARCHITECTURAL PRINCIPLES

### **1. Knowledge Graph Manager Authority**

- **Central Cognitive System**: KG Manager como única autoridade de decisão cognitiva
- **Phase 0.5 Mandatory**: Consulta obrigatória antes de qualquer ação
- **Pattern-Based Decisions**: Decisões baseadas em padrões de sucesso do KG
- **Continuous Learning**: Aprendizado contínuo através do sistema cognitivo

### **2. Single Source of Truth**

- **Centralized Rules**: All rules and standards maintained in `.cursor/rules/`
- **Canonical Data**: One authoritative source for each piece of information
- **Consistent References**: All agents and developers use the same rule sources
- **Version Control**: All changes tracked and documented

### **3. Modularity & Reusability**

- **Component-Based Architecture**: Decompose systems into reusable components
- **DRY Principle**: Don't Repeat Yourself - consolidate common patterns
- **Separation of Concerns**: Clear boundaries between different system layers
- **Interface-Driven Design**: Well-defined contracts between components

### **4. Performance & Scalability**

```

**Fallback Instructions:** If target file is missing, use this cached content as reference.
```
