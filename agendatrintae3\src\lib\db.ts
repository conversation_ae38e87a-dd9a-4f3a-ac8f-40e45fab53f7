// AGENDATRINTAE3 Database Configuration
// Drizzle ORM + Supabase Integration for Medical Appointment System
// Generated by VIBECODE SYSTEM V4.0 - Phase 6 Migration

import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import * as schema from './schema'

// Supabase connection string
const connectionString = process.env.DATABASE_URL!

if (!connectionString) {
  throw new Error('DATABASE_URL environment variable is required')
}

// Create postgres client with optimized settings for Supabase
const client = postgres(connectionString, {
  prepare: false, // Required for Supabase connection pooler
  max: 10, // Connection pool size
  idle_timeout: 20,
  connect_timeout: 10,
})

// Create Drizzle database instance with schema
export const db = drizzle({ 
  client,
  schema,
  logger: process.env.NODE_ENV === 'development'
})

// Export types for type-safe operations
export type Database = typeof db
export * from './schema'
