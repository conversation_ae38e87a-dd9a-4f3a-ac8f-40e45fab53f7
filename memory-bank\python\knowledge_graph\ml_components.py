#!/usr/bin/env python3

"""
VIBECODE V2.0 - Machine Learning Components
Advanced ML integration with semantic similarity, adaptive learning, and predictive analytics

This module contains all ML-powered components including semantic similarity engine,
adaptive learning system, intelligent caching, and predictive analytics.
"""

import math
import re
import statistics
from collections import Counter, defaultdict
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple

try:
    from .models import VibeCodeDataPoint
    from .constants import DOMAIN_AGENT_MAPPING, AGENT_PERFORMANCE_DATA
    from .utils import log_with_context
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    from models import VibeC<PERSON>DataPoint
    from constants import DOMAIN_AGENT_MAPPING, AGENT_PERFORMANCE_DATA
    from utils import log_with_context

class SemanticSimilarityEngine:
    """Semantic similarity engine for enhanced pattern matching and contradiction detection"""

    def __init__(self):
        self.vocabulary = set()
        self.idf_scores = {}
        self.document_count = 0
        self.similarity_cache = {}

    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization with basic preprocessing"""
        # Convert to lowercase and extract words
        text = text.lower()
        words = re.findall(r'\b\w+\b', text)
        # Filter out very short words and common stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'}
        return [word for word in words if len(word) > 2 and word not in stop_words]

    def _compute_tf(self, tokens: List[str]) -> Dict[str, float]:
        """Compute term frequency"""
        token_count = len(tokens)
        if token_count == 0:
            return {}

        tf_scores = {}
        token_freq = Counter(tokens)

        for token, freq in token_freq.items():
            tf_scores[token] = freq / token_count

        return tf_scores

    def _update_idf(self, documents: List[str]):
        """Update IDF scores based on document collection"""
        self.document_count = len(documents)
        if self.document_count == 0:
            return

        # Count document frequency for each term
        doc_freq = defaultdict(int)

        for doc in documents:
            tokens = set(self._tokenize(doc))
            self.vocabulary.update(tokens)
            for token in tokens:
                doc_freq[token] += 1

        # Compute IDF scores
        for token in self.vocabulary:
            self.idf_scores[token] = math.log(self.document_count / (doc_freq[token] + 1))

    def compute_similarity(self, text1: str, text2: str, documents: Optional[List[str]] = None) -> float:
        """Compute semantic similarity between two texts using TF-IDF"""
        # Check cache first
        cache_key = f"{hash(text1)}_{hash(text2)}"
        if cache_key in self.similarity_cache:
            return self.similarity_cache[cache_key]

        # Update IDF if documents provided
        if documents:
            self._update_idf(documents)

        # Tokenize texts
        tokens1 = self._tokenize(text1)
        tokens2 = self._tokenize(text2)

        if not tokens1 or not tokens2:
            return 0.0

        # Compute TF scores
        tf1 = self._compute_tf(tokens1)
        tf2 = self._compute_tf(tokens2)

        # Compute TF-IDF vectors
        all_tokens = set(tokens1 + tokens2)

        vector1 = []
        vector2 = []

        for token in all_tokens:
            idf = self.idf_scores.get(token, 1.0)

            tfidf1 = tf1.get(token, 0.0) * idf
            tfidf2 = tf2.get(token, 0.0) * idf

            vector1.append(tfidf1)
            vector2.append(tfidf2)

        # Compute cosine similarity
        similarity = self._cosine_similarity(vector1, vector2)

        # Cache result
        self.similarity_cache[cache_key] = similarity

        return similarity

    def _cosine_similarity(self, vector1: List[float], vector2: List[float]) -> float:
        """Compute cosine similarity between two vectors"""
        if len(vector1) != len(vector2):
            return 0.0

        dot_product = sum(a * b for a, b in zip(vector1, vector2))
        magnitude1 = math.sqrt(sum(a * a for a in vector1))
        magnitude2 = math.sqrt(sum(b * b for b in vector2))

        if magnitude1 == 0.0 or magnitude2 == 0.0:
            return 0.0

        return dot_product / (magnitude1 * magnitude2)

    def find_similar_patterns(self, query: str, patterns: List[Dict[str, Any]], threshold: float = 0.3) -> List[Tuple[Dict[str, Any], float]]:
        """Find patterns similar to query text"""
        similar_patterns = []

        # Prepare documents for IDF computation
        documents = [pattern.get('description', '') for pattern in patterns] + [query]
        self._update_idf(documents)

        for pattern in patterns:
            pattern_text = pattern.get('description', '')
            similarity = self.compute_similarity(query, pattern_text)

            if similarity >= threshold:
                similar_patterns.append((pattern, similarity))

        # Sort by similarity score (descending)
        similar_patterns.sort(key=lambda x: x[1], reverse=True)

        return similar_patterns

    def detect_semantic_contradictions(self, fact1: str, fact2: str, threshold: float = 0.7) -> Dict[str, Any]:
        """Detect semantic contradictions between two facts"""
        # Check for high similarity with opposite sentiment
        similarity = self.compute_similarity(fact1, fact2)

        # Simple sentiment analysis based on keywords
        positive_words = {'success', 'complete', 'working', 'good', 'yes', 'true', 'correct', 'right'}
        negative_words = {'fail', 'error', 'broken', 'bad', 'no', 'false', 'wrong', 'incorrect'}

        tokens1 = set(self._tokenize(fact1))
        tokens2 = set(self._tokenize(fact2))

        sentiment1 = len(tokens1 & positive_words) - len(tokens1 & negative_words)
        sentiment2 = len(tokens2 & positive_words) - len(tokens2 & negative_words)

        # Contradiction if high similarity but opposite sentiments
        is_contradiction = (similarity >= threshold and
                          sentiment1 * sentiment2 < 0 and
                          abs(sentiment1) > 0 and abs(sentiment2) > 0)

        return {
            'is_contradiction': is_contradiction,
            'similarity_score': similarity,
            'sentiment_1': sentiment1,
            'sentiment_2': sentiment2,
            'confidence': similarity * 0.8 if is_contradiction else 0.0
        }

class AdaptiveLearningSystem:
    """Adaptive learning system that learns from user interactions and improves recommendations"""

    def __init__(self):
        self.agent_performance_history = defaultdict(list)
        self.domain_preferences = defaultdict(lambda: defaultdict(float))
        self.success_patterns = defaultdict(list)
        self.learning_stats = {'interactions_recorded': 0, 'adaptations_made': 0, 'performance_improvements': 0}

    def record_interaction(self, domain: str, complexity: int, agent: str, success: bool,
                          execution_time: float, user_satisfaction: Optional[float] = None):
        """Record user interaction for learning"""
        interaction = {
            'domain': domain,
            'complexity': complexity,
            'agent': agent,
            'success': success,
            'execution_time': execution_time,
            'user_satisfaction': user_satisfaction or (1.0 if success else 0.0),
            'timestamp': datetime.now(timezone.utc)
        }

        self.agent_performance_history[agent].append(interaction)
        self.learning_stats['interactions_recorded'] += 1

        # Update domain preferences
        satisfaction_score = interaction['user_satisfaction']
        self.domain_preferences[domain][agent] += satisfaction_score * 0.1  # Learning rate

        # Record success patterns
        if success:
            pattern = {
                'domain': domain,
                'complexity': complexity,
                'agent': agent,
                'execution_time': execution_time,
                'satisfaction': satisfaction_score
            }
            self.success_patterns[domain].append(pattern)

        log_with_context(
            'debug',
            f"Recorded interaction for {agent} in {domain}",
            component='adaptive_learning',
            success=success,
            satisfaction=satisfaction_score
        )

    def get_adaptive_agent_recommendation(self, domain: str, complexity: int) -> Tuple[str, float]:
        """Get agent recommendation based on learned patterns"""
        # Start with base preferences
        base_agents = DOMAIN_AGENT_MAPPING.get(domain, ['claude'])

        # Apply learned preferences
        agent_scores = {}
        for agent in base_agents:
            base_score = AGENT_PERFORMANCE_DATA.get(agent, AGENT_PERFORMANCE_DATA['default'])['success_rate']

            # Add learned preference bonus
            learned_bonus = self.domain_preferences[domain][agent]

            # Add complexity adjustment based on history
            complexity_bonus = self._get_complexity_bonus(agent, complexity)

            # Add recency bonus (recent successes weighted more)
            recency_bonus = self._get_recency_bonus(agent, domain)

            total_score = base_score + learned_bonus + complexity_bonus + recency_bonus
            agent_scores[agent] = total_score

        # Select best agent
        if agent_scores:
            best_agent = max(agent_scores.keys(), key=lambda k: agent_scores[k])
            confidence = min(agent_scores[best_agent], 1.0)
        else:
            best_agent = base_agents[0] if base_agents else 'claude'
            confidence = 0.5

        return best_agent, confidence

    def _get_complexity_bonus(self, agent: str, complexity: int) -> float:
        """Get bonus based on agent's performance with similar complexity tasks"""
        if agent not in self.agent_performance_history:
            return 0.0

        relevant_interactions = [
            interaction for interaction in self.agent_performance_history[agent]
            if abs(interaction['complexity'] - complexity) <= 2
        ]

        if not relevant_interactions:
            return 0.0

        success_rate = sum(1 for i in relevant_interactions if i['success']) / len(relevant_interactions)
        return (success_rate - 0.5) * 0.2  # Bonus/penalty based on success rate

    def _get_recency_bonus(self, agent: str, domain: str) -> float:
        """Get bonus based on recent performance in domain"""
        if agent not in self.agent_performance_history:
            return 0.0

        now = datetime.now(timezone.utc)
        recent_interactions = [
            interaction for interaction in self.agent_performance_history[agent]
            if interaction['domain'] == domain and
               (now - interaction['timestamp']).total_seconds() < 86400  # Last 24 hours
        ]

        if not recent_interactions:
            return 0.0

        recent_satisfaction = statistics.mean(i['user_satisfaction'] for i in recent_interactions)
        return (recent_satisfaction - 0.5) * 0.15  # Bonus based on recent satisfaction

    def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights from learning system"""
        insights = {
            'learning_stats': self.learning_stats,
            'top_performing_agents': {},
            'domain_preferences': dict(self.domain_preferences),
            'improvement_suggestions': []
        }

        # Analyze top performing agents per domain
        for domain, preferences in self.domain_preferences.items():
            if preferences:
                top_agent = max(preferences.keys(), key=lambda k: preferences[k])
                insights['top_performing_agents'][domain] = {
                    'agent': top_agent,
                    'score': preferences[top_agent]
                }

        # Generate improvement suggestions
        for agent, history in self.agent_performance_history.items():
            if len(history) >= 5:  # Enough data for analysis
                recent_performance = statistics.mean(
                    i['user_satisfaction'] for i in history[-5:]
                )
                if recent_performance < 0.6:
                    insights['improvement_suggestions'].append(
                        f"Consider reducing usage of {agent} - recent performance below threshold"
                    )

        return insights

class IntelligentCache:
    """ML-powered intelligent caching system"""

    def __init__(self, max_size: int = 1000):
        self.cache = {}
        self.access_history = defaultdict(list)
        self.prediction_scores = {}
        self.max_size = max_size
        self.cache_stats = {'hits': 0, 'misses': 0, 'evictions': 0, 'predictions': 0}

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache with learning"""
        if key in self.cache:
            self.cache_stats['hits'] += 1
            self._record_access(key)
            return self.cache[key]['data']
        else:
            self.cache_stats['misses'] += 1
            return None

    def put(self, key: str, value: Any, context: Optional[Dict[str, Any]] = None):
        """Put item in cache with intelligent scoring"""
        if len(self.cache) >= self.max_size:
            self._evict_least_valuable()

        # Compute prediction score based on context and history
        prediction_score = self._compute_prediction_score(key, context or {})

        self.cache[key] = {
            'data': value,
            'timestamp': datetime.now(timezone.utc),
            'access_count': 1,
            'prediction_score': prediction_score,
            'context': context or {}
        }

        self._record_access(key)

    def _record_access(self, key: str):
        """Record access for learning patterns"""
        now = datetime.now(timezone.utc)
        self.access_history[key].append(now)

        # Keep only recent history (last 100 accesses)
        if len(self.access_history[key]) > 100:
            self.access_history[key] = self.access_history[key][-100:]

        # Update access count
        if key in self.cache:
            self.cache[key]['access_count'] += 1

    def _compute_prediction_score(self, key: str, context: Dict[str, Any]) -> float:
        """Compute prediction score for cache value"""
        score = 0.5  # Base score

        # Historical access pattern bonus
        if key in self.access_history:
            access_count = len(self.access_history[key])
            recency_bonus = min(access_count / 10.0, 0.3)  # Max 0.3 bonus
            score += recency_bonus

        # Context-based scoring
        domain = context.get('domain', '')
        complexity = context.get('complexity', 5)

        # Higher score for common domains
        common_domains = ['coding', 'analysis', 'general']
        if domain in common_domains:
            score += 0.1

        # Higher score for medium complexity (most common)
        if 4 <= complexity <= 7:
            score += 0.1

        return min(score, 1.0)

    def _evict_least_valuable(self):
        """Evict least valuable item based on ML scoring"""
        if not self.cache:
            return

        # Compute value scores for all items
        item_scores = {}
        now = datetime.now(timezone.utc)

        for key, item in self.cache.items():
            # Recency factor (newer items score higher)
            age_hours = (now - item['timestamp']).total_seconds() / 3600
            recency_score = max(0, 1 - age_hours / 24)  # Decay over 24 hours

            # Frequency factor
            frequency_score = min(item['access_count'] / 10.0, 1.0)

            # Prediction score
            prediction_score = item['prediction_score']

            # Combined score
            total_score = (recency_score * 0.4 + frequency_score * 0.4 + prediction_score * 0.2)
            item_scores[key] = total_score

        # Evict item with lowest score
        least_valuable_key = min(item_scores.keys(), key=lambda k: item_scores[k])
        del self.cache[least_valuable_key]
        self.cache_stats['evictions'] += 1

    def predict_next_queries(self, current_context: Dict[str, Any], limit: int = 5) -> List[str]:
        """Predict likely next queries based on patterns"""
        predictions = []

        # Analyze access patterns
        domain = current_context.get('domain', '')

        # Find similar contexts in history
        similar_keys = []
        for key, item in self.cache.items():
            if item['context'].get('domain') == domain:
                similar_keys.append(key)

        # Score predictions based on access patterns
        prediction_scores = {}
        for key in similar_keys:
            if key in self.access_history:
                recent_accesses = len([
                    access for access in self.access_history[key]
                    if (datetime.now(timezone.utc) - access).total_seconds() < 3600  # Last hour
                ])
                prediction_scores[key] = recent_accesses

        # Sort by prediction score and return top predictions
        sorted_predictions = sorted(prediction_scores.items(), key=lambda x: x[1], reverse=True)
        predictions = [key for key, _ in sorted_predictions[:limit]]

        self.cache_stats['predictions'] += len(predictions)
        return predictions

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        hit_rate = 0.0
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        if total_requests > 0:
            hit_rate = self.cache_stats['hits'] / total_requests

        return {
            **self.cache_stats,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache),
            'max_size': self.max_size
        }

class PredictiveAnalytics:
    """Predictive analytics for proactive suggestions"""

    def __init__(self):
        self.usage_patterns = defaultdict(list)
        self.trend_analysis = {}
        self.prediction_stats = {'predictions_made': 0, 'accuracy_score': 0.0}

    def record_usage(self, action: str, context: Dict[str, Any]):
        """Record usage pattern for analysis"""
        usage_record = {
            'action': action,
            'context': context,
            'timestamp': datetime.now(timezone.utc)
        }
        self.usage_patterns[action].append(usage_record)

        # Keep only recent history (last 1000 records per action)
        if len(self.usage_patterns[action]) > 1000:
            self.usage_patterns[action] = self.usage_patterns[action][-1000:]

    def predict_next_actions(self, current_context: Dict[str, Any], limit: int = 3) -> List[Dict[str, Any]]:
        """Predict likely next actions based on patterns"""
        predictions = []

        # Analyze recent patterns
        now = datetime.now(timezone.utc)
        recent_threshold = now - timedelta(hours=24)

        action_scores = defaultdict(float)

        for action, records in self.usage_patterns.items():
            recent_records = [r for r in records if r['timestamp'] > recent_threshold]

            if recent_records:
                # Score based on frequency and recency
                frequency_score = len(recent_records) / 24.0  # Per hour

                # Context similarity bonus
                context_similarity = self._compute_context_similarity(current_context, recent_records)

                total_score = frequency_score + context_similarity
                action_scores[action] = total_score

        # Sort by score and create predictions
        sorted_actions = sorted(action_scores.items(), key=lambda x: x[1], reverse=True)

        for action, score in sorted_actions[:limit]:
            predictions.append({
                'action': action,
                'confidence': min(score / 2.0, 1.0),  # Normalize confidence
                'reasoning': f'Based on recent usage patterns (score: {score:.2f})'
            })

        self.prediction_stats['predictions_made'] += len(predictions)
        return predictions

    def _compute_context_similarity(self, current_context: Dict[str, Any], records: List[Dict[str, Any]]) -> float:
        """Compute similarity between current context and historical records"""
        if not records:
            return 0.0

        similarities = []
        for record in records:
            record_context = record['context']

            # Simple similarity based on matching keys
            matching_keys = set(current_context.keys()) & set(record_context.keys())
            if not matching_keys:
                similarities.append(0.0)
                continue

            matches = sum(1 for key in matching_keys
                         if current_context[key] == record_context[key])
            similarity = matches / len(matching_keys)
            similarities.append(similarity)

        return statistics.mean(similarities) if similarities else 0.0

    def get_trend_analysis(self) -> Dict[str, Any]:
        """Get trend analysis of usage patterns"""
        trends = {}

        for action, records in self.usage_patterns.items():
            if len(records) < 5:  # Need minimum data for trends
                continue

            # Analyze usage over time
            daily_counts = defaultdict(int)
            for record in records:
                day = record['timestamp'].date()
                daily_counts[day] += 1

            if len(daily_counts) >= 3:  # Need at least 3 days
                counts = list(daily_counts.values())
                trend_direction = 'increasing' if counts[-1] > counts[0] else 'decreasing'
                avg_daily_usage = statistics.mean(counts)

                trends[action] = {
                    'trend_direction': trend_direction,
                    'avg_daily_usage': avg_daily_usage,
                    'total_usage': len(records),
                    'days_tracked': len(daily_counts)
                }

        return trends

# Export all public symbols
__all__ = [
    'SemanticSimilarityEngine',
    'AdaptiveLearningSystem',
    'IntelligentCache',
    'PredictiveAnalytics'
]
