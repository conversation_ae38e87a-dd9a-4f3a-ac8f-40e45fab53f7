---
alwaysApply: true
---

# 📋 **REGRA 6: DEVELOPMENT ENVIRONMENT & INTEGRATION**

_Prioridade: MÉDIA | Ambiente de desenvolvimento e integração_

## **Cross-Platform Integration**

```json
{
  "platform_optimization": {
    "cursor_ide": {
      "performance_target": "<100ms rule lookup",
      "kg_consultation": "<200ms",
      "preferred_tools": ["desktop-commander", "cursor-editor"]
    },
    "augment_code": {
      "performance_target": "<50ms rule lookup",
      "kg_consultation": "<100ms",
      "preferred_tools": ["sequential-thinking", "advanced-reasoning"]
    }
  }
}
```

## **Testing Standards**

```typescript
// Component Test
import { render, screen, fireEvent } from "@testing-library/react";
import { Button } from "./Button";

describe("Button", () => {
  it("should render with text", () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText("Click me")).toBeInTheDocument();
  });

  it("should handle click", async () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click</Button>);

    await fireEvent.click(screen.getByText("Click"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### **API Test**

```typescript
// API Route Test
import { createMocks } from "node-mocks-http";
import handler from "./route";

describe("/api/users", () => {
  it("should return users", async () => {
    const { req, res } = createMocks({
      method: "GET",
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const json = JSON.parse(res._getData());
    expect(json.data).toBeInstanceOf(Array);
  });
});
```

### **Coverage Requirements**

- **Minimum**: 80% overall
- **Critical paths**: 95%
- **New code**: 90%
- **UI components**: 85%

## 🔐 SECURITY

### **Authentication Flow**

```typescript
// Middleware Protection
export async function middleware(req: NextRequest) {
  const session = await getSession(req);

  if (!session && req.nextUrl.pathname.startsWith("/dashboard")) {
    return NextResponse.redirect(new URL("/login", req.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/dashboard/:path*", "/api/protected/:path*"],
};
```

### **Data Validation**

```typescript
// Input Sanitization
import DOMPurify from "isomorphic-dompurify";

export function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ["b", "i", "em", "strong"],
    ALLOWED_ATTR: [],
  });
}

// SQL Injection Prevention
const user = await db.query(
  "SELECT * FROM users WHERE id = $1",
  [userId] // Parameterized query
);
```

### **Security Headers**

```typescript
// next.config.js
const securityHeaders = [
  { key: "X-Frame-Options", value: "DENY" },
  { key: "X-Content-Type-Options", value: "nosniff" },
  { key: "X-XSS-Protection", value: "1; mode=block" },
  { key: "Referrer-Policy", value: "strict-origin-when-cross-origin" },
];
```

## ⚡ PERFORMANCE

### **Component Optimization**

```typescript
// Memoization
import { memo, useMemo, useCallback } from "react";

const ExpensiveComponent = memo(({ data }) => {
  const processed = useMemo(
    () => data.map((item) => heavyComputation(item)),
    [data]
  );

  const handleClick = useCallback((id) => {
    // Handle click
  }, []);

  return <div>{/* Render */}</div>;
});
```

### **Image Optimization**

```typescript
import Image from "next/image";

// Use Next.js Image component
<Image
  src="/hero.jpg"
  alt="Hero"
  width={1200}
  height={600}
  priority
  placeholder="blur"
  blurDataURL={blurDataUrl}
/>;
```

### **Bundle Optimization**

```javascript
// Dynamic imports
const HeavyComponent = dynamic(() => import("./HeavyComponent"), {
  loading: () => <Skeleton />,
  ssr: false,
});

// Tree shaking
import { debounce } from "lodash-es"; // ES modules
```

### **Database Queries**

```typescript
// Optimize with indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_posts_user_created ON posts(user_id, created_at DESC);

// Use pagination
const posts = await db.posts.findMany({
  take: 20,
  skip: (page - 1) * 20,
  orderBy: { createdAt: 'desc' }
})

// Select only needed fields
const users = await db.users.findMany({
  select: {
    id: true,
    name: true,
    email: true
  }
})
```

## 📊 MONITORING

### **Error Tracking**

```typescript
// Sentry Integration
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1,
});

// Custom error boundary
class ErrorBoundary extends Component {
  componentDidCatch(error, errorInfo) {
    Sentry.captureException(error, {
      contexts: { react: errorInfo },
    });
  }
}
```

### **Performance Metrics**

```typescript
// Core Web Vitals
export function reportWebVitals(metric) {
  const { id, name, value } = metric;

  // Send to analytics
  analytics.track("Web Vitals", {
    metric: name,
    value: Math.round(name === "CLS" ? value * 1000 : value),
    id,
  });
}
```

## ✅ QUALITY CHECKLIST

### **Before Deploy**

- [ ] All tests passing (≥80% coverage)
- [ ] No console errors/warnings
- [ ] Security headers configured
- [ ] Performance budget met
- [ ] Accessibility audit passed
- [ ] SEO meta tags present

### **Performance Targets**

- **LCP**: <2.5s
- **FID**: <100ms
- **CLS**: <0.1
- **TTI**: <3.8s
- **Bundle size**: <200KB (gzipped)

## 🐍 PYTHON UV STANDARDS

### **Mandatory UV Environment**

```python
#!/usr/bin/env python3
"""
Script Name: [script_name].py
UV Required: YES (mandatory for all Python scripts)
"""

import sys
import os

def verify_uv_environment():
    """Verify script is running under UV management"""
    if not os.environ.get('UV_PROJECT_ROOT'):
        print("❌ ERROR: This script must be run with UV")
        print("💡 Install UV: curl -LsSf https://astral.sh/uv/install.sh | sh")
        print("   or on Windows: powershell -c \"irm https://astral.sh/uv/install.ps1 | iex\"")
        print("💡 Run script: uv run python path/to/script.py")
        sys.exit(1)

# Always verify UV at script start
verify_uv_environment()
```

### **Standard UV Commands**

```bash
# Environment management
uv venv .venv               # Create virtual environment
uv pip sync                 # Install all dependencies
uv pip install package      # Add new package

# Running scripts (MANDATORY)
uv run python script.py     # Run Python script
uv run pytest              # Run tests
uv run black .             # Run formatter

# Project management
uv pip compile pyproject.toml  # Create lock file
uv pip sync --dev             # Install dev dependencies
```

### **Configuration Requirements**

```toml
# pyproject.toml mandatory sections
[project]
dependencies = [
    "click>=8.1.7",
    "colorama>=0.4.6",
    "pyyaml>=6.0.1",
    "rich>=13.7.0",
]

[tool.uv]
python = "3.10"
exclude = ["node_modules", ".venv", ".next", "dist", "build"]
```

### **UV Troubleshooting**

```bash
# Common fixes
uv pip sync                    # Sync dependencies
uv venv --python 3.10         # Recreate environment
uv run python --version       # Verify Python version

# Error: "This script must be run with UV"
# Solution: Always use 'uv run python' instead of 'python'
```

**UV Compliance**: ALL Python scripts must include UV verification and be executed via `uv run python`.

---

**"Quality is not negotiable"**

# 📋 **REGRA 6: DEVELOPMENT ENVIRONMENT & INTEGRATION**

_Prioridade: MÉDIA | Ambiente de desenvolvimento e integração_

## **Cross-Platform Integration**

```json
{
  "platform_optimization": {
    "cursor_ide": {
      "performance_target": "<100ms rule lookup",
      "kg_consultation": "<200ms",
      "preferred_tools": ["desktop-commander", "cursor-editor"]
    },
    "augment_code": {
      "performance_target": "<50ms rule lookup",
      "kg_consultation": "<100ms",
      "preferred_tools": ["sequential-thinking", "advanced-reasoning"]
    }
  }
}
```

## **Testing Standards**

```typescript
// Component Test
import { render, screen, fireEvent } from "@testing-library/react";
import { Button } from "./Button";

describe("Button", () => {
  it("should render with text", () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText("Click me")).toBeInTheDocument();
  });

  it("should handle click", async () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click</Button>);

    await fireEvent.click(screen.getByText("Click"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### **API Test**

```typescript
// API Route Test
import { createMocks } from "node-mocks-http";
import handler from "./route";

describe("/api/users", () => {
  it("should return users", async () => {
    const { req, res } = createMocks({
      method: "GET",
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const json = JSON.parse(res._getData());
    expect(json.data).toBeInstanceOf(Array);
  });
});
```

### **Coverage Requirements**

- **Minimum**: 80% overall
- **Critical paths**: 95%
- **New code**: 90%
- **UI components**: 85%

## 🔐 SECURITY

### **Authentication Flow**

```typescript
// Middleware Protection
export async function middleware(req: NextRequest) {
  const session = await getSession(req);

  if (!session && req.nextUrl.pathname.startsWith("/dashboard")) {
    return NextResponse.redirect(new URL("/login", req.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/dashboard/:path*", "/api/protected/:path*"],
};
```

### **Data Validation**

```typescript
// Input Sanitization
import DOMPurify from "isomorphic-dompurify";

export function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ["b", "i", "em", "strong"],
    ALLOWED_ATTR: [],
  });
}

// SQL Injection Prevention
const user = await db.query(
  "SELECT * FROM users WHERE id = $1",
  [userId] // Parameterized query
);
```

### **Security Headers**

```typescript
// next.config.js
const securityHeaders = [
  { key: "X-Frame-Options", value: "DENY" },
  { key: "X-Content-Type-Options", value: "nosniff" },
  { key: "X-XSS-Protection", value: "1; mode=block" },
  { key: "Referrer-Policy", value: "strict-origin-when-cross-origin" },
];
```

## ⚡ PERFORMANCE

### **Component Optimization**

```typescript
// Memoization
import { memo, useMemo, useCallback } from "react";

const ExpensiveComponent = memo(({ data }) => {
  const processed = useMemo(
    () => data.map((item) => heavyComputation(item)),
    [data]
  );

  const handleClick = useCallback((id) => {
    // Handle click
  }, []);

  return <div>{/* Render */}</div>;
});
```

### **Image Optimization**

```typescript
import Image from "next/image";

// Use Next.js Image component
<Image
  src="/hero.jpg"
  alt="Hero"
  width={1200}
  height={600}
  priority
  placeholder="blur"
  blurDataURL={blurDataUrl}
/>;
```

### **Bundle Optimization**

```javascript
// Dynamic imports
const HeavyComponent = dynamic(() => import("./HeavyComponent"), {
  loading: () => <Skeleton />,
  ssr: false,
});

// Tree shaking
import { debounce } from "lodash-es"; // ES modules
```

### **Database Queries**

```typescript
// Optimize with indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_posts_user_created ON posts(user_id, created_at DESC);

// Use pagination
const posts = await db.posts.findMany({
  take: 20,
  skip: (page - 1) * 20,
  orderBy: { createdAt: 'desc' }
})

// Select only needed fields
const users = await db.users.findMany({
  select: {
    id: true,
    name: true,
    email: true
  }
})
```

## 📊 MONITORING

### **Error Tracking**

```typescript
// Sentry Integration
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1,
});

// Custom error boundary
class ErrorBoundary extends Component {
  componentDidCatch(error, errorInfo) {
    Sentry.captureException(error, {
      contexts: { react: errorInfo },
    });
  }
}
```

### **Performance Metrics**

```typescript
// Core Web Vitals
export function reportWebVitals(metric) {
  const { id, name, value } = metric;

  // Send to analytics
  analytics.track("Web Vitals", {
    metric: name,
    value: Math.round(name === "CLS" ? value * 1000 : value),
    id,
  });
}
```

## ✅ QUALITY CHECKLIST

### **Before Deploy**

- [ ] All tests passing (≥80% coverage)
- [ ] No console errors/warnings
- [ ] Security headers configured
- [ ] Performance budget met
- [ ] Accessibility audit passed
- [ ] SEO meta tags present

### **Performance Targets**

- **LCP**: <2.5s
- **FID**: <100ms
- **CLS**: <0.1
- **TTI**: <3.8s
- **Bundle size**: <200KB (gzipped)

## 🐍 PYTHON UV STANDARDS

### **Mandatory UV Environment**

```python
#!/usr/bin/env python3
"""
Script Name: [script_name].py
UV Required: YES (mandatory for all Python scripts)
"""

import sys
import os

def verify_uv_environment():
    """Verify script is running under UV management"""
    if not os.environ.get('UV_PROJECT_ROOT'):
        print("❌ ERROR: This script must be run with UV")
        print("💡 Install UV: curl -LsSf https://astral.sh/uv/install.sh | sh")
        print("   or on Windows: powershell -c \"irm https://astral.sh/uv/install.ps1 | iex\"")
        print("💡 Run script: uv run python path/to/script.py")
        sys.exit(1)

# Always verify UV at script start
verify_uv_environment()
```

### **Standard UV Commands**

```bash
# Environment management
uv venv .venv               # Create virtual environment
uv pip sync                 # Install all dependencies
uv pip install package      # Add new package

# Running scripts (MANDATORY)
uv run python script.py     # Run Python script
uv run pytest              # Run tests
uv run black .             # Run formatter

# Project management
uv pip compile pyproject.toml  # Create lock file
uv pip sync --dev             # Install dev dependencies
```

### **Configuration Requirements**

```toml
# pyproject.toml mandatory sections
[project]
dependencies = [
    "click>=8.1.7",
    "colorama>=0.4.6",
    "pyyaml>=6.0.1",
    "rich>=13.7.0",
]

[tool.uv]
python = "3.10"
exclude = ["node_modules", ".venv", ".next", "dist", "build"]
```

### **UV Troubleshooting**

```bash
# Common fixes
uv pip sync                    # Sync dependencies
uv venv --python 3.10         # Recreate environment
uv run python --version       # Verify Python version

# Error: "This script must be run with UV"
# Solution: Always use 'uv run python' instead of 'python'
```

**UV Compliance**: ALL Python scripts must include UV verification and be executed via `uv run python`.

---

**"Quality is not negotiable"**
