"""
Core data models for the VIBECODE-Kiro sync system.
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any, Optional
from pathlib import Path


class ActionType(Enum):
    """Types of sync actions that can be performed."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    MERGE = "merge"


class Priority(Enum):
    """Priority levels for sync actions."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


class ChangeType(Enum):
    """Types of file system changes."""
    CREATED = "created"
    MODIFIED = "modified"
    DELETED = "deleted"
    MOVED = "moved"


class ConflictType(Enum):
    """Types of conflicts that can occur during sync."""
    CONTENT_CONFLICT = "content_conflict"
    PATH_CONFLICT = "path_conflict"
    FORMAT_CONFLICT = "format_conflict"
    PERMISSION_CONFLICT = "permission_conflict"
    CUSTOMIZATION_CONFLICT = "customization_conflict"


@dataclass
class FileChange:
    """Represents a detected file system change."""
    file_path: str
    change_type: ChangeType
    timestamp: datetime
    old_path: Optional[str] = None
    file_size: Optional[int] = None
    checksum: Optional[str] = None


@dataclass
class SyncAction:
    """Represents an action to be performed during sync."""
    action_type: ActionType
    source_file: str
    target_file: str
    priority: Priority
    requires_adaptation: bool = False
    backup_required: bool = True
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class Conflict:
    """Represents a conflict between VIBECODE and Kiro configurations."""
    conflict_type: ConflictType
    source_file: str
    target_file: str
    source_content: str
    target_content: str
    resolution_strategy: str
    manual_review_required: bool = False
    conflict_details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.conflict_details is None:
            self.conflict_details = {}


@dataclass
class Resolution:
    """Represents the resolution of a conflict."""
    conflict_id: str
    resolution_type: str
    resolved_content: str
    applied: bool = False
    applied_timestamp: Optional[datetime] = None
    rollback_info: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.rollback_info is None:
            self.rollback_info = {}


@dataclass
class MergeProposal:
    """Represents a proposed merge for manual review."""
    conflict: Conflict
    proposed_resolution: str
    merge_strategy: str
    confidence_score: float
    requires_user_approval: bool = True
    review_notes: List[str] = None
    
    def __post_init__(self):
        if self.review_notes is None:
            self.review_notes = []


@dataclass
class BackupInfo:
    """Information about a backup operation."""
    backup_id: str
    backup_path: str
    created_timestamp: datetime
    files_backed_up: List[str]
    backup_size: int
    checksum: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class SyncStatus:
    """Current status of the sync system."""
    last_sync_time: Optional[datetime]
    files_synced: int
    conflicts_resolved: int
    errors_encountered: List[str]
    next_scheduled_sync: Optional[datetime]
    is_monitoring: bool = False
    sync_in_progress: bool = False
    last_error: Optional[str] = None
    performance_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.performance_metrics is None:
            self.performance_metrics = {}


@dataclass
class SyncResult:
    """Result of a sync operation."""
    success: bool
    actions_performed: List[SyncAction]
    conflicts_encountered: List[Conflict]
    errors: List[str]
    start_time: datetime
    end_time: datetime
    files_processed: int
    backup_created: Optional[BackupInfo] = None
    
    @property
    def duration(self) -> float:
        """Duration of sync operation in seconds."""
        return (self.end_time - self.start_time).total_seconds()


@dataclass
class MonitoringConfig:
    """Configuration for file system monitoring."""
    source_path: str
    file_types: List[str]
    excluded_paths: List[str]
    debounce_delay_ms: int = 1000
    recursive: bool = True
    follow_symlinks: bool = False


@dataclass
class AdaptationRule:
    """Rule for adapting VIBECODE content to Kiro format."""
    rule_name: str
    pattern: str
    replacement: str
    applies_to_files: List[str]
    priority: int = 1
    enabled: bool = True