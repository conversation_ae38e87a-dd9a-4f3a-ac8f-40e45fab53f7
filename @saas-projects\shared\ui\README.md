# 🎨 Shared UI Components

Esta pasta contém componentes de UI compartilhados entre todos os projetos SaaS.

## 📁 Estrutura

```
@saas-projects/shared/ui/
├── components/          # Componentes reutilizáveis
├── hooks/              # Hooks customizados compartilhados
├── utils/              # Utilitários de UI
├── styles/             # Estilos globais compartilhados
└── types/              # Tipos TypeScript para UI
```

## 🚀 Como usar

```typescript
// Importar componentes compartilhados
import { SharedButton, SharedModal } from '@saas-projects/shared/ui/components';

// Importar hooks compartilhados
import { useSharedTheme, useSharedAuth } from '@saas-projects/shared/ui/hooks';
```

## 📋 Componentes Disponíveis

- **SharedButton**: Botão padronizado com variantes
- **SharedModal**: Modal reutilizável
- **SharedForm**: Formulários com validação
- **SharedTable**: Tabela com paginação e filtros

## 🎯 Próximos Passos

1. Migrar componentes comuns dos projetos existentes
2. Criar design system unificado
3. Implementar temas compartilhados
4. Documentar padrões de uso