# AgendaTrintaE3 - Sistema de Agendamento Médico

## 🚀 Sobre o Projeto

AgendaTrintaE3 é um sistema completo de agendamento médico SaaS, otimizado para clínicas e consultórios.

## 📦 Estrutura do Projeto

Este projeto contém todos os arquivos necessários para funcionar independentemente:

```
agendatrintae3/
├── app/              # Next.js App Router
├── components/       # Componentes React
│   └── ui/          # Componentes UI
├── lib/             # Utilitários e configurações
├── public/          # Arquivos públicos
├── styles/          # Estilos globais
├── drizzle/         # Schema do banco de dados
├── .env.example     # Template de variáveis de ambiente
├── drizzle.config.ts # Configuração Drizzle
└── package.json     # Dependências e scripts
```

## 🛠️ Tecnologias

- **Framework**: Next.js 15.4.0-canary
- **UI**: TailwindCSS + componentes customizados
- **Database**: Supabase + Drizzle ORM
- **Calendar**: Integração com calendários
- **Payments**: Stripe para pagamentos
- **Language**: TypeScript

## 🏥 Funcionalidades

- Agendamento online de consultas
- Gestão de pacientes
- Calendário de profissionais
- Lembretes automáticos
- Dashboard administrativo
- Relatórios e analytics
- Sistema de pagamentos integrado

## 🚀 Deploy

### 1. Preparação

```bash
# Clone apenas este projeto
git clone https://github.com/seu-usuario/agendatrintae3.git
cd agendatrintae3

# Instale as dependências
npm install

# Configure as variáveis de ambiente
cp .env.example .env.local
```

### 2. Banco de Dados

```bash
# Push do schema
npm run db:push

# Visualizar banco (desenvolvimento)
npm run db:studio
```

### 3. Deploy em Produção

```bash
# Build
npm run build

# Deploy no Vercel (recomendado)
vercel

# Configurar variáveis de ambiente no painel Vercel
```

## 📝 Desenvolvimento

```bash
# Modo desenvolvimento
npm run dev

# Verificar tipos
npm run type-check

# Lint
npm run lint
```

## 🔧 Configuração Específica

### Horários de Funcionamento
Configure em `lib/config/clinic-settings.ts`

### Tipos de Consulta
Ajuste em `lib/config/appointment-types.ts`

### Notificações
Configure SMS/Email em `lib/notifications/`

## 📱 Responsivo

Totalmente otimizado para:
- Desktop
- Tablet
- Mobile

---

**Sistema completo e independente para gestão de agendamentos médicos.**