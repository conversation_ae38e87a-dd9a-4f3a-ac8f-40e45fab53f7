// Teste de validação do Stagewise - VIBECODE V1.0
const fs = require('fs');
const path = require('path');

console.log('🎯 VALIDAÇÃO STAGEWISE - VIBECODE V1.0');
console.log('=====================================\n');

const baseDir = 'C:\\Users\\<USER>\\OneDrive\\GRUPOUS\\VSCODE';
const projects = ['aegiswallet', 'agendatrintae3', 'neonpro'];

console.log('📦 VERIFICANDO DEPENDÊNCIAS NOS PROJETOS:');
console.log('------------------------------------------');

projects.forEach(project => {
  const packagePath = path.join(baseDir, '@project-core', 'projects', project, 'package.json');
  console.log(`\n🔍 ${project.toUpperCase()}:`);
  
  if (fs.existsSync(packagePath)) {
    try {
      const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      const deps = { ...pkg.dependencies, ...pkg.devDependencies };
      
      console.log('  ✅ package.json encontrado');
      
      const toolbarNext = deps['@stagewise/toolbar-next'];
      const pluginsReact = deps['@stagewise-plugins/react'];
      
      console.log(`  📋 @stagewise/toolbar-next: ${toolbarNext ? '✅ ' + toolbarNext : '❌ Não instalado'}`);
      console.log(`  📋 @stagewise-plugins/react: ${pluginsReact ? '✅ ' + pluginsReact : '❌ Não instalado'}`);
      
      // Verificar layout.tsx
      const layoutPath = path.join(baseDir, '@project-core', 'projects', project, 'src', 'app', 'layout.tsx');
      if (fs.existsSync(layoutPath)) {
        const layoutContent = fs.readFileSync(layoutPath, 'utf8');
        const hasImport = layoutContent.includes('StagewiseProvider');
        const hasComponent = layoutContent.includes('<StagewiseProvider');
        
        console.log(`  📄 Layout integrado: ${hasImport && hasComponent ? '✅ Sim' : '❌ Não'}`);
      } else {
        console.log('  📄 Layout: ❌ Não encontrado');
      }
      
    } catch (error) {
      console.log('  ❌ Erro ao ler package.json:', error.message);
    }
  } else {
    console.log('  ❌ package.json não encontrado');
  }
});

console.log('\n\n🏗️ VERIFICANDO ESTRUTURA CENTRALIZADA:');
console.log('---------------------------------------');

const sharedPath = path.join(baseDir, '@project-core', 'shared', 'stagewise');
if (fs.existsSync(sharedPath)) {
  console.log('✅ Estrutura centralizada criada em:', sharedPath);
  
  const requiredFiles = [
    'index.ts',
    'README.md',
    'config/stagewise.config.ts',
    'config/environment.config.ts',
    'components/StagewiseProvider.tsx',
    'components/index.ts',
    'utils/validation.ts'
  ];
  
  console.log('\n📁 Arquivos obrigatórios:');
  requiredFiles.forEach(file => {
    const filePath = path.join(sharedPath, file);
    const exists = fs.existsSync(filePath);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  });
  
} else {
  console.log('❌ Estrutura centralizada não encontrada em:', sharedPath);
}

console.log('\n\n🎯 RESUMO DA VALIDAÇÃO:');
console.log('=======================');

// Contar sucessos
let projectsWithDeps = 0;
let projectsWithIntegration = 0;

projects.forEach(project => {
  const packagePath = path.join(baseDir, '@project-core', 'projects', project, 'package.json');
  const layoutPath = path.join(baseDir, '@project-core', 'projects', project, 'src', 'app', 'layout.tsx');
  
  if (fs.existsSync(packagePath)) {
    try {
      const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      const deps = { ...pkg.dependencies, ...pkg.devDependencies };
      
      if (deps['@stagewise/toolbar-next'] && deps['@stagewise-plugins/react']) {
        projectsWithDeps++;
      }
      
      if (fs.existsSync(layoutPath)) {
        const layoutContent = fs.readFileSync(layoutPath, 'utf8');
        if (layoutContent.includes('StagewiseProvider') && layoutContent.includes('<StagewiseProvider')) {
          projectsWithIntegration++;
        }
      }
    } catch (error) {
      // Ignorar erros para o resumo
    }
  }
});

const structureExists = fs.existsSync(sharedPath);

console.log(`📦 Dependências instaladas: ${projectsWithDeps}/3 projetos`);
console.log(`🔗 Integração completa: ${projectsWithIntegration}/3 projetos`);
console.log(`🏗️ Estrutura centralizada: ${structureExists ? '✅ Criada' : '❌ Faltando'}`);

const overallSuccess = projectsWithDeps === 3 && projectsWithIntegration === 3 && structureExists;
console.log(`\n🎯 STATUS GERAL: ${overallSuccess ? '✅ SUCESSO COMPLETO' : '⚠️ NECESSITA AJUSTES'}`);

if (overallSuccess) {
  console.log('\n🚀 STAGEWISE INTEGRADO COM SUCESSO!');
  console.log('   - Solução centralizada implementada');
  console.log('   - Todos os projetos configurados');
  console.log('   - Seguindo padrões VIBECODE V1.0');
}