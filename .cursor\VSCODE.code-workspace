{"folders": [{"path": ".."}], "settings": {"task.autoDetect": "on", "task.showDecorations": true, "task.problemMatchers.neverPrompt": false, "task.saveBeforeRun": "always", "task.quickOpen.history": 10, "task.quickOpen.detail": true, "task.quickOpen.skip": false, "files.associations": {"task-storage.md": "markdown", "task-config.json": "jsonc"}, "markdown.preview.breaks": true, "markdown.preview.linkify": true, "json.schemas": [{"fileMatch": ["**/memory-bank/task-config.json"], "url": "./memory-bank/task-config.json"}], "vibecode.taskManagement": {"enabled": true, "storageLocation": "memory-bank/task-storage.md", "configLocation": "memory-bank/task-config.json", "syncWithAugment": true, "autoSave": true}}, "tasks": {"version": "2.0.0", "tasks": [{"label": "VIBECODE: Sync Task Management", "type": "shell", "command": "uv", "args": ["run", "python", ".cursor/scripts/vibecode_task_system.py", "--sync"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "VIBECODE: Validate Task System", "type": "shell", "command": "uv", "args": ["run", "python", ".cursor/scripts/vibecode_task_system.py", "--status"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}, "extensions": {"recommendations": ["ms-vscode.vscode-json", "yzhang.markdown-all-in-one", "ms-python.python", "charliermarsh.ruff"]}}