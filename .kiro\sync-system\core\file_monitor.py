"""
File system monitoring implementation for VIBECODE-Kiro sync system.
"""

import os
import time
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable, Set
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEvent<PERSON>andler, FileSystemEvent

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .interfaces import IFileSystemMonitor
from models.sync_models import FileChange, ChangeType, MonitoringConfig
from utils.logger import get_logger, SyncLogger
from utils.path_utils import PathUtils

logger = SyncLogger(__name__)


class SyncFileSystemEventHandler(FileSystemEventHandler):
    """Custom event handler for file system changes."""
    
    def __init__(self, callback: Callable[[List[FileChange]], None], config: MonitoringConfig):
        super().__init__()
        self.callback = callback
        self.config = config
        self.debounce_buffer: Dict[str, FileChange] = {}
        self.debounce_timer: Optional[threading.Timer] = None
        self.lock = threading.Lock()
    
    def on_any_event(self, event: FileSystemEvent):
        """Handle any file system event."""
        if event.is_directory:
            return
        
        # Check if file type should be monitored
        if not self._should_monitor_file(event.src_path):
            return
        
        # Check if path should be excluded
        if PathUtils.is_excluded_path(event.src_path, self.config.excluded_paths):
            logger.debug(f"Ignoring excluded path: {event.src_path}")
            return
        
        # Convert watchdog event to our FileChange model
        change = self._convert_event_to_change(event)
        if change:
            self._add_to_debounce_buffer(change)
    
    def _should_monitor_file(self, file_path: str) -> bool:
        """Check if file should be monitored based on file types."""
        if not self.config.file_types:
            return True
        
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in [ft.lower() for ft in self.config.file_types]
    
    def _convert_event_to_change(self, event: FileSystemEvent) -> Optional[FileChange]:
        """Convert watchdog event to FileChange."""
        try:
            change_type_map = {
                'created': ChangeType.CREATED,
                'modified': ChangeType.MODIFIED,
                'deleted': ChangeType.DELETED,
                'moved': ChangeType.MOVED
            }
            
            change_type = change_type_map.get(event.event_type)
            if not change_type:
                return None
            
            # Get file size if file exists
            file_size = None
            if os.path.exists(event.src_path) and os.path.isfile(event.src_path):
                try:
                    file_size = os.path.getsize(event.src_path)
                except Exception:
                    pass
            
            # Handle moved events
            old_path = None
            if hasattr(event, 'dest_path'):
                old_path = event.src_path
                file_path = event.dest_path
            else:
                file_path = event.src_path
            
            return FileChange(
                file_path=file_path,
                change_type=change_type,
                timestamp=datetime.now(),
                old_path=old_path,
                file_size=file_size
            )
            
        except Exception as e:
            logger.error(f"Error converting event to change: {e}")
            return None
    
    def _add_to_debounce_buffer(self, change: FileChange):
        """Add change to debounce buffer."""
        with self.lock:
            # Use file path as key for debouncing
            self.debounce_buffer[change.file_path] = change
            
            # Reset debounce timer
            if self.debounce_timer:
                self.debounce_timer.cancel()
            
            self.debounce_timer = threading.Timer(
                self.config.debounce_delay_ms / 1000.0,
                self._flush_debounce_buffer
            )
            self.debounce_timer.start()
    
    def _flush_debounce_buffer(self):
        """Flush debounced changes to callback."""
        with self.lock:
            if self.debounce_buffer:
                changes = list(self.debounce_buffer.values())
                self.debounce_buffer.clear()
                
                logger.debug(f"Flushing {len(changes)} debounced changes")
                
                try:
                    self.callback(changes)
                except Exception as e:
                    logger.error(f"Error in change callback: {e}")


class FileSystemMonitor(IFileSystemMonitor):
    """File system monitor implementation using watchdog."""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.observer: Optional[Observer] = None
        self.event_handler: Optional[SyncFileSystemEventHandler] = None
        self.change_callback: Optional[Callable[[List[FileChange]], None]] = None
        self.is_running = False
        self.monitored_paths: Set[str] = set()
        
        logger.info(f"FileSystemMonitor initialized for path: {config.source_path}")
    
    def set_change_callback(self, callback: Callable[[List[FileChange]], None]) -> None:
        """Set callback function for file changes."""
        self.change_callback = callback
        logger.debug("Change callback set")
    
    def start_monitoring(self) -> None:
        """Start monitoring the file system for changes."""
        if self.is_running:
            logger.warning("Monitoring is already running")
            return
        
        if not self.change_callback:
            raise ValueError("Change callback must be set before starting monitoring")
        
        try:
            # Validate source path
            if not os.path.exists(self.config.source_path):
                raise FileNotFoundError(f"Source path does not exist: {self.config.source_path}")
            
            # Create observer and event handler
            self.observer = Observer()
            self.event_handler = SyncFileSystemEventHandler(
                self.change_callback, 
                self.config
            )
            
            # Schedule monitoring
            self.observer.schedule(
                self.event_handler,
                self.config.source_path,
                recursive=self.config.recursive
            )
            
            # Start observer
            self.observer.start()
            self.is_running = True
            self.monitored_paths.add(self.config.source_path)
            
            logger.info(f"File system monitoring started for: {self.config.source_path}")
            
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
            self.is_running = False
            raise
    
    def stop_monitoring(self) -> None:
        """Stop monitoring the file system."""
        if not self.is_running:
            logger.warning("Monitoring is not running")
            return
        
        try:
            if self.observer:
                self.observer.stop()
                self.observer.join(timeout=5.0)  # Wait up to 5 seconds
                
                if self.observer.is_alive():
                    logger.warning("Observer did not stop gracefully")
                
                self.observer = None
            
            self.event_handler = None
            self.is_running = False
            self.monitored_paths.clear()
            
            logger.info("File system monitoring stopped")
            
        except Exception as e:
            logger.error(f"Error stopping monitoring: {e}")
    
    def is_monitoring(self) -> bool:
        """Check if monitoring is active."""
        return self.is_running and self.observer is not None and self.observer.is_alive()
    
    def get_monitored_paths(self) -> List[str]:
        """Get list of currently monitored paths."""
        return list(self.monitored_paths)
    
    def add_monitored_path(self, path: str) -> bool:
        """Add additional path to monitor."""
        try:
            if not os.path.exists(path):
                logger.error(f"Path does not exist: {path}")
                return False
            
            if not self.observer or not self.event_handler:
                logger.error("Monitoring not started")
                return False
            
            self.observer.schedule(
                self.event_handler,
                path,
                recursive=self.config.recursive
            )
            
            self.monitored_paths.add(path)
            logger.info(f"Added monitored path: {path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add monitored path {path}: {e}")
            return False
    
    def remove_monitored_path(self, path: str) -> bool:
        """Remove path from monitoring."""
        try:
            if path not in self.monitored_paths:
                logger.warning(f"Path not being monitored: {path}")
                return False
            
            # Note: watchdog doesn't provide easy way to remove specific paths
            # This would require recreating the observer
            logger.warning("Removing specific paths requires restarting monitoring")
            return False
            
        except Exception as e:
            logger.error(f"Failed to remove monitored path {path}: {e}")
            return False
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get monitoring statistics."""
        return {
            'is_running': self.is_running,
            'monitored_paths': list(self.monitored_paths),
            'file_types': self.config.file_types,
            'excluded_paths': self.config.excluded_paths,
            'debounce_delay_ms': self.config.debounce_delay_ms,
            'recursive': self.config.recursive,
            'observer_alive': self.observer.is_alive() if self.observer else False
        }
    
    def test_monitoring(self) -> bool:
        """Test monitoring functionality by creating a temporary file."""
        try:
            if not self.is_running:
                logger.error("Monitoring not running")
                return False
            
            # Create a temporary test file
            test_file = os.path.join(self.config.source_path, "sync_test_file.tmp")
            
            # Set up a test callback to capture changes
            test_changes = []
            original_callback = self.change_callback
            
            def test_callback(changes: List[FileChange]):
                test_changes.extend(changes)
                if original_callback:
                    original_callback(changes)
            
            self.event_handler.callback = test_callback
            
            # Create and delete test file
            with open(test_file, 'w') as f:
                f.write("test")
            
            time.sleep(0.1)  # Brief pause
            
            if os.path.exists(test_file):
                os.remove(test_file)
            
            # Wait for debounce
            time.sleep(self.config.debounce_delay_ms / 1000.0 + 0.1)
            
            # Restore original callback
            self.event_handler.callback = original_callback
            
            # Check if changes were detected
            success = len(test_changes) > 0
            logger.info(f"Monitoring test {'passed' if success else 'failed'}: {len(test_changes)} changes detected")
            
            return success
            
        except Exception as e:
            logger.error(f"Monitoring test failed: {e}")
            return False
    
    def __enter__(self):
        """Context manager entry."""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_monitoring()