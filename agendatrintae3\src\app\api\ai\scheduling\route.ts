// AGENDATRINTAE3 AI Smart Scheduling API Route
// Phase 7 AI Integration - Streaming API for Smart Scheduling
// Generated by VIBECODE SYSTEM V4.0 - AI Integration

import { NextRequest, NextResponse } from 'next/server';
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { createServerSupabaseClient } from '@/lib/supabase';
import { db } from '@/lib/db';
import { 
  ai_scheduling_optimization, 
  ai_scheduling_history, 
  appointments, 
  doctors, 
  services,
  users,
  availability
} from '@/lib/schema';
import { eq, desc, and, gte, lte, ne } from 'drizzle-orm';

// Validation Schema
const requestSchema = z.object({
  user_id: z.string().uuid(),
  service_id: z.string().uuid(),
  urgency_level: z.enum(['emergency', 'urgent', 'routine', 'follow_up']),
  medical_notes: z.string().optional(),
  patient_preferences: z.string().optional(),
  doctor_preferences: z.object({
    preferred_doctors: z.array(z.string().uuid()).optional(),
    specialty_requirements: z.array(z.string()).optional(),
    exclude_doctors: z.array(z.string().uuid()).optional(),
  }).optional(),
  scheduling_constraints: z.object({
    preferred_dates: z.array(z.string()).optional(),
    preferred_times: z.array(z.string()).optional(),
    avoid_dates: z.array(z.string()).optional(),
    max_wait_days: z.number().min(1).max(90).optional(),
  }).optional(),
});

// Helper Functions
async function getPatientContext(userId: string) {
  // Get patient's appointment history
  const appointmentHistory = await db
    .select({
      appointment: appointments,
      doctor: doctors,
      doctor_user: users,
      service: services,
    })
    .from(appointments)
    .leftJoin(doctors, eq(appointments.doctor_id, doctors.id))
    .leftJoin(users, eq(doctors.user_id, users.id))
    .leftJoin(services, eq(appointments.service_id, services.id))
    .where(eq(appointments.patient_id, userId))
    .orderBy(desc(appointments.scheduled_date))
    .limit(10);

  return { appointmentHistory };
}

async function getAvailableDoctors(serviceId: string, specialtyRequirements?: string[]) {
  // Get doctors who can provide the requested service
  const availableDoctors = await db
    .select({
      doctor: doctors,
      user: users,
    })
    .from(doctors)
    .leftJoin(users, eq(doctors.user_id, users.id))
    .where(and(
      eq(doctors.is_available, true),
      eq(users.is_active, true)
    ))
    .orderBy(desc(doctors.rating));

  return availableDoctors;
}

async function generateOptimalSlots(doctorId: string, serviceDuration: number) {
  // Get doctor's availability
  const doctorAvailability = await db
    .select()
    .from(availability)
    .where(and(
      eq(availability.doctor_id, doctorId),
      eq(availability.is_active, true)
    ));

  // Get existing appointments for next 30 days
  const startDate = new Date().toISOString().split('T')[0];
  const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

  const existingAppointments = await db
    .select()
    .from(appointments)
    .where(and(
      eq(appointments.doctor_id, doctorId),
      gte(appointments.scheduled_date, startDate),
      lte(appointments.scheduled_date, endDate),
      ne(appointments.status, 'cancelled'),
      ne(appointments.status, 'no_show')
    ));

  // Generate available time slots
  const slots = [];
  const now = new Date();
  
  for (let day = 0; day < 30; day++) {
    const date = new Date(now);
    date.setDate(date.getDate() + day);
    const dayOfWeek = date.getDay();
    
    // Find availability for this day
    const dayAvailability = doctorAvailability.filter(av => av.day_of_week === dayOfWeek);
    
    for (const avail of dayAvailability) {
      const startTime = new Date(`${date.toISOString().split('T')[0]}T${avail.start_time}`);
      const endTime = new Date(`${date.toISOString().split('T')[0]}T${avail.end_time}`);
      
      // Generate slots every 30 minutes
      let currentTime = new Date(startTime);
      
      while (currentTime < endTime) {
        const slotEnd = new Date(currentTime.getTime() + serviceDuration * 60000);
        
        // Check if slot conflicts with existing appointments
        const hasConflict = existingAppointments.some(apt => {
          const aptStart = new Date(`${apt.scheduled_date}T${apt.scheduled_time}`);
          const aptEnd = new Date(aptStart.getTime() + (apt.duration_minutes || 30) * 60000);
          
          return (currentTime < aptEnd && slotEnd > aptStart);
        });
        
        if (!hasConflict && currentTime > now) {
          slots.push({
            date: date.toISOString().split('T')[0],
            time: currentTime.toTimeString().split(' ')[0].substring(0, 5),
            datetime: currentTime.toISOString(),
            available: true,
          });
        }
        
        currentTime = new Date(currentTime.getTime() + 30 * 60000);
      }
    }
  }
  
  return slots.slice(0, 10); // Return top 10 slots
}

function buildSchedulingPrompt(
  patientContext: any,
  availableDoctors: any[],
  serviceDetails: any,
  input: any
): string {
  const urgencyPriority = {
    'emergency': 10,
    'urgent': 8,
    'routine': 5,
    'follow_up': 3,
  }[input.urgency_level] || 5;

  return `
You are an intelligent medical scheduling assistant for AGENDATRINTAE3. Optimize appointment scheduling based on medical urgency, doctor availability, patient preferences, and operational efficiency.

PATIENT CONTEXT:
- Patient ID: ${input.user_id}
- Urgency Level: ${input.urgency_level} (Priority: ${urgencyPriority}/10)
- Medical Notes: ${input.medical_notes || 'None provided'}
- Patient Preferences: ${input.patient_preferences || 'None specified'}

REQUESTED SERVICE:
- Service: ${serviceDetails.name}
- Duration: ${serviceDetails.duration_minutes} minutes
- Category: ${serviceDetails.category}
- Price: $${serviceDetails.price}
- Preparation Required: ${serviceDetails.requires_preparation ? 'Yes' : 'No'}
${serviceDetails.preparation_instructions ? `- Instructions: ${serviceDetails.preparation_instructions}` : ''}

PATIENT MEDICAL HISTORY:
${patientContext.appointmentHistory.length > 0 
  ? patientContext.appointmentHistory.map((h: any) => 
      `- ${h.service?.name || 'Unknown Service'} with Dr. ${h.doctor_user?.name || 'Unknown'} (${h.appointment.scheduled_date}): ${h.appointment.status}`
    ).join('\n')
  : 'No previous appointments recorded'
}

AVAILABLE DOCTORS:
${availableDoctors.slice(0, 5).map((d: any) => 
  `- Dr. ${d.user?.name || 'Unknown'} (${d.doctor.specialty}) - Rating: ${d.doctor.rating}/5, Experience: ${d.doctor.experience_years} years`
).join('\n')}

SCHEDULING CONSTRAINTS:
- Max Wait Time: ${input.scheduling_constraints?.max_wait_days || 30} days
${input.scheduling_constraints?.preferred_dates?.length 
  ? `- Preferred Dates: ${input.scheduling_constraints.preferred_dates.join(', ')}`
  : '- No date preferences'
}
${input.scheduling_constraints?.preferred_times?.length 
  ? `- Preferred Times: ${input.scheduling_constraints.preferred_times.join(', ')}`
  : '- No time preferences'
}

Please provide comprehensive scheduling optimization including:

## OPTIMAL SCHEDULING RECOMMENDATIONS

### PRIMARY RECOMMENDATION
- **Recommended Doctor**: [Doctor name and rationale]
- **Optimal Date & Time**: [Specific date and time with reasoning]
- **Scheduling Efficiency**: [Why this slot optimizes patient care and clinic operations]
- **Medical Urgency Alignment**: [How recommendation addresses urgency level]

### ALTERNATIVE OPTIONS (2-3 alternatives)
- **Option 2**: [Alternative doctor/time with rationale]
- **Option 3**: [Another alternative with different benefits]

## CONTINUITY OF CARE ANALYSIS
- **Previous Doctor Relationships**: [Analysis of patient-doctor history]
- **Specialty Matching**: [How doctor expertise aligns with patient needs]
- **Treatment Timeline Optimization**: [Optimal timing for medical care]

## CONFLICT RESOLUTION STRATEGIES
- **Scheduling Conflicts Identified**: [Any conflicts found and solutions]
- **Wait Time Optimization**: [Strategies to minimize patient wait time]
- **Operational Efficiency**: [How to optimize clinic resource utilization]

## PATIENT EXPERIENCE OPTIMIZATION
- **Preference Alignment**: [How recommendations match patient preferences]
- **Convenience Factors**: [Travel time, parking, accessibility considerations]
- **Preparation Time**: [Adequate time for any required preparation]

## MEDICAL PRIORITY CONSIDERATIONS
- **Urgency Assessment**: [Medical urgency evaluation and scheduling priority]
- **Risk Mitigation**: [Ensuring appropriate medical care timing]
- **Follow-up Planning**: [Integration with ongoing treatment plans]

## IMPLEMENTATION RECOMMENDATIONS
- **Immediate Actions**: [Steps to secure optimal appointment]
- **Backup Plans**: [Alternative strategies if primary option unavailable]
- **Patient Communication**: [How to communicate options to patient]

**IMPORTANT MEDICAL DISCLAIMERS:**
- Scheduling recommendations based on available data and operational efficiency
- Medical urgency should always be verified by healthcare professionals
- Emergency cases should bypass normal scheduling and seek immediate care
- Patient safety and medical best practices take priority over operational efficiency

Always prioritize patient safety and medical best practices in scheduling decisions.
`.trim();
}

export async function POST(request: NextRequest) {
  try {
    // Validate authentication
    const supabase = await createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedInput = requestSchema.parse(body);

    // Get service details
    const [serviceDetails] = await db
      .select()
      .from(services)
      .where(eq(services.id, validatedInput.service_id))
      .limit(1);

    if (!serviceDetails) {
      return NextResponse.json(
        { error: 'Service not found' },
        { status: 404 }
      );
    }

    // Get patient context and available doctors
    const patientContext = await getPatientContext(validatedInput.user_id);
    const availableDoctors = await getAvailableDoctors(
      validatedInput.service_id,
      validatedInput.doctor_preferences?.specialty_requirements
    );

    // Build AI prompt
    const prompt = buildSchedulingPrompt(
      patientContext,
      availableDoctors,
      serviceDetails,
      validatedInput
    );

    // Create optimization record
    const [newOptimization] = await db
      .insert(ai_scheduling_optimization)
      .values({
        user_id: validatedInput.user_id,
        request_data: {
          service_id: validatedInput.service_id,
          urgency_level: validatedInput.urgency_level,
          medical_notes: validatedInput.medical_notes,
          patient_preferences: validatedInput.patient_preferences,
        },
        doctor_preferences: validatedInput.doctor_preferences || {},
        scheduling_constraints: validatedInput.scheduling_constraints || {},
        urgency_level: validatedInput.urgency_level,
        optimization_results: {}, // Will be updated after streaming
        recommended_slots: [], // Will be populated with actual slots
        alternative_options: [],
        efficiency_score: '8.5',
        medical_priority: validatedInput.urgency_level === 'emergency' ? 'high' : 
                         validatedInput.urgency_level === 'urgent' ? 'medium' : 'low',
        ai_model: 'gpt-4o',
        ai_version: '2024-11-20',
        confidence_score: '0.85',
      })
      .returning();

    // Log optimization generation
    if (newOptimization) {
      await db.insert(ai_scheduling_history).values({
        optimization_id: newOptimization.id,
        action: 'generated',
        performed_by: user.id,
        notes: 'AI scheduling optimization generation started',
        metadata: {
          prompt_length: prompt.length,
          available_doctors_count: availableDoctors.length,
          urgency_level: validatedInput.urgency_level,
          service_name: serviceDetails.name,
        },
      });
    }

    // Generate streaming AI response
    const result = await streamText({
      model: openai('gpt-4o'),
      prompt,
      maxTokens: 2500,
      temperature: 0.5,
    });

    // Return streaming response with optimization ID
    return new Response(result.textStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'X-Optimization-ID': newOptimization?.id || '',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('AI Scheduling API Error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
