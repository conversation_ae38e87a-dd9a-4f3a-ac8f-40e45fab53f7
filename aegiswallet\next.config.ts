import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // === EXPERIMENTAL FEATURES ===
  experimental: {
    // Partial Prerendering (PPR) - Next.js 15 Performance Boost
    ppr: "incremental",

    // Performance optimizations
    optimizePackageImports: [
      "@heroicons/react",
      "@headlessui/react",
      "framer-motion",
      "lucide-react",
      "@supabase/supabase-js",
      "drizzle-orm",
    ],
  },

  // === TURBOPACK CONFIGURATION ===
  turbopack: {
    rules: {
      "*.svg": {
        loaders: ["@svgr/webpack"],
        as: "*.js",
      },
    },
  },

  // === SERVER EXTERNAL PACKAGES ===
  serverExternalPackages: ["@prisma/client"],

  // === TYPESCRIPT CONFIGURATION ===
  typescript: {
    // Type checking during build (temporarily disabled for validation)
    ignoreBuildErrors: true,
  },

  // === ESLint CONFIGURATION ===
  eslint: {
    // Lint during builds
    ignoreDuringBuilds: false,
    dirs: ["src", "components", "lib", "app"],
  },

  // === IMAGE OPTIMIZATION ===
  images: {
    // Image domains for external images
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "via.placeholder.com",
      },
      {
        protocol: "https",
        hostname: "picsum.photos",
      },
    ],
    // Image formats
    formats: ["image/webp", "image/avif"],
    // Image sizes for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Enable image optimization
    unoptimized: false,
  },

  // === WEBPACK CONFIGURATION ===
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // SVG handling
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    // Bundle analyzer (development only)
    if (dev && !isServer) {
      config.plugins.push(
        new webpack.DefinePlugin({
          __DEV__: JSON.stringify(true),
          __THEME_NAME__: JSON.stringify("Aegis Wallet"),
          __THEME_VERSION__: JSON.stringify("1.0.0"),
          __THEME_VARIANT__: JSON.stringify("crypto"),
        })
      );
    }

    // Production optimizations
    if (!dev) {
      config.plugins.push(
        new webpack.DefinePlugin({
          __DEV__: JSON.stringify(false),
          __THEME_NAME__: JSON.stringify("Aegis Wallet"),
          __THEME_VERSION__: JSON.stringify("1.0.0"),
          __THEME_VARIANT__: JSON.stringify("crypto"),
        })
      );
    }

    return config;
  },

  // === ENVIRONMENT VARIABLES ===
  env: {
    THEME_NAME: "Aegis Wallet",
    THEME_VERSION: "1.0.0",
    THEME_VARIANT: "crypto",
    THEME_PRIMARY_COLOR: "#F59E0B",
    THEME_SECONDARY_COLOR: "#8B5CF6",
    THEME_BACKGROUND_COLOR: "#0F172A",
  },

  // === REDIRECTS ===
  async redirects() {
    return [
      {
        source: "/theme",
        destination: "/dashboard",
        permanent: false,
      },
    ];
  },

  // === HEADERS ===
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          // Security headers
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
          // Theme metadata headers
          {
            key: "X-Theme-Name",
            value: "Aegis Wallet",
          },
          {
            key: "X-Theme-Version",
            value: "1.0.0",
          },
          {
            key: "X-Theme-Variant",
            value: "crypto",
          },
        ],
      },
      {
        source: "/api/(.*)",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "Content-Type, Authorization",
          },
        ],
      },
    ];
  },

  // === PERFORMANCE OPTIMIZATIONS ===
  compress: true,
  poweredByHeader: false,
  generateEtags: true,

  // === OUTPUT CONFIGURATION ===
  output: "standalone",

  // === SASS CONFIGURATION ===
  sassOptions: {
    includePaths: ["./src/styles"],
    prependData: `
      $primary-color: #F59E0B;
      $secondary-color: #8B5CF6;
      $background-color: #0F172A;
      $text-color: #F8FAFC;
      $border-color: #334155;
    `,
  },

  // === COMPILER OPTIONS ===
  compiler: {
    // Remove console logs in production
    removeConsole: process.env.NODE_ENV === "production",
    // Enable SWC minification
    styledComponents: true,
  },

  // === REWRITES ===
  async rewrites() {
    return [
      // API rewrites for theme endpoints
      {
        source: "/api/theme/:path*",
        destination: "/api/theme/:path*",
      },
    ];
  },

  // === TRAILING SLASH ===
  trailingSlash: false,

  // === REACT STRICT MODE ===
  reactStrictMode: true,
};

export default nextConfig;

// Generated by VIBECODE SYSTEM V1.0 - Standardization Process
// Enhanced with neonpro advanced configuration
// Optimized for Next.js 15 + PPR + Drizzle ORM + Supabase
// Crypto Wallet Theme Configuration