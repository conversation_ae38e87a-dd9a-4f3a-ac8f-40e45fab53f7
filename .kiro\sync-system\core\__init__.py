"""
Core components for VIBECODE-Kiro Sync System
"""

from .file_monitor import FileSystemMonitor
from .change_detection import ChangeDetectionEngine

# Import other components if they exist
try:
    from .sync_engine import SyncEngine
except ImportError:
    SyncEngine = None

try:
    from .rule_adapter import RuleAdaptationEngine
except ImportError:
    RuleAdaptationEngine = None

try:
    from .conflict_resolver import ConflictResolver
except ImportError:
    ConflictResolver = None

try:
    from .backup_manager import BackupManager
except ImportError:
    BackupManager = None

__all__ = [
    'FileSystemMonitor',
    'ChangeDetectionEngine'
]

# Add available components to __all__
if SyncEngine:
    __all__.append('SyncEngine')
if RuleAdaptationEngine:
    __all__.append('RuleAdaptationEngine')
if ConflictResolver:
    __all__.append('ConflictResolver')
if BackupManager:
    __all__.append('BackupManager')