# 🧠 MEMORY BANK INTEGRATION - VIBECODE KILOCODE

## **PURPOSE**

Ensure AI model utilizes persistent memory effectively by integrating with VIBECODE's `@project-core/memory` system through orchestrator-based coordination.

---

## 🎯 **MEMORY BANK STRATEGY**

### **1. Context Persistence**

- **ALWAYS** load project context from `@project-core/memory/` before task execution
- **MAINTAIN** session continuity through memory bank files
- **UPDATE** memory bank after significant discoveries or pattern changes
- **REFERENCE** existing knowledge to avoid repetition

### **2. Orchestrator Coordination**

- **ORCHESTRATOR AGENT** coordinates all memory operations
- **NO Plan/Act Mode workflows** - orchestrator handles all coordination
- **CENTRALIZED** memory management through single coordination point
- **INTELLIGENT** memory routing based on task complexity and context

### **3. Knowledge Integration**

- **LEVERAGE** existing `@project-core/memory/knowledge_graph_manager.py`
- **SYNC** with current memory structures and patterns
- **ENHANCE** existing self-correction and learning systems
- **PRESERVE** VIBECODE principles ("<PERSON><PERSON><PERSON>, Não Prolifere")

### **4. Pattern Recognition**

- **APPLY** successful patterns from memory bank
- **AVOID** repeating documented mistakes from self_correction_log.md
- **UTILIZE** established coding standards and preferences
- **MAINTAIN** consistency with existing rule system

---

## 📁 **MEMORY BANK STRUCTURE**

### **Core Memory Files** (Mapped to VIBECODE)

```
@project-core/memory/
├── brief.md          # → master_rule.md (project overview)
├── product.md        # → system_context.md (product details)
├── context.md         # → current_session_context.md (active work)
├── architecture.md       # → technical_architecture.md (architecture details)
├── system_patterns.md        # → self_correction_log.md (patterns)
├── tasks.md              # → task_tracking.md (task details and progress)
├── tech.md           # → global_standards.md (tech stack)
└── progress_tracking.md      # → progress_log.md (advancement)
```

### **Integration Points**

- **Knowledge Graph**: Central authority for memory decisions
- **Self-Correction Log**: Mistake prevention and learning
- **Global Standards**: Technical consistency enforcement
- **Session Context**: Real-time working memory

---

## 🤖 **AI BEHAVIOR RULES**

### **Memory Consultation (MANDATORY)**

```bash
# BEFORE any task execution
1. READ @project-core/memory/master_rule.md
2. CHECK @project-core/memory/self_correction_log.md
3. LOAD @project-core/memory/current_session_context.md
4. QUERY knowledge_graph_manager.py for relevant patterns
```

### **Orchestrator Coordination**

- **SINGLE POINT**: Orchestrator manages all memory access
- **INTELLIGENT ROUTING**: Route requests based on complexity and domain
- **CONTEXT AWARENESS**: Maintain full context across agent handoffs
- **MEMORY PERSISTENCE**: Ensure continuity between sessions

### **Quality Gates**

- **Confidence**: ≥8/10 for all memory-based decisions
- **Consistency**: Align with existing VIBECODE standards
- **Completeness**: Address all aspects of memory integration
- **Performance**: <50ms memory lookup overhead

---

## 🚨 **RESTRICTIONS**

### **NEVER DO**

- ❌ Ignore existing memory when starting tasks
- ❌ Create duplicate memory structures
- ❌ Bypass orchestrator for memory operations
- ❌ Use Plan/Act Mode workflows (orchestrator coordinates)
- ❌ Override established patterns without justification

### **ALWAYS DO**

- ✅ Consult memory bank before major decisions
- ✅ Update memory bank after learning new patterns
- ✅ Use orchestrator for all memory coordination
- ✅ Maintain consistency with VIBECODE principles
- ✅ Document significant changes in appropriate memory files

---

## 🔧 **IMPLEMENTATION**

### **Orchestrator Memory Protocol**

```python
# Orchestrator coordinates memory operations
def coordinate_memory_access(task_context):
    # 1. Assess task complexity and domain
    complexity = assess_complexity(task_context)
    domain = identify_domain(task_context)

    # 2. Route to appropriate memory sections
    relevant_memory = knowledge_graph_manager.query_patterns(
        complexity=complexity,
        domain=domain
    )

    # 3. Load context and patterns
    context = load_session_context()
    patterns = load_successful_patterns()

    # 4. Coordinate agent selection and memory handoff
    selected_agent = select_agent_by_complexity(complexity)
    return coordinate_handoff(selected_agent, relevant_memory, context)
```

### **Memory Update Protocol**

```python
# Update memory bank after significant discoveries
def update_memory_bank(new_knowledge, session_results):
    # 1. Orchestrator validates updates
    if orchestrator.validate_update(new_knowledge):
        # 2. Update appropriate memory files
        update_self_correction_log(session_results.errors_and_fixes)
        update_session_context(session_results.active_work)
        update_progress_tracking(session_results.advancement)

        # 3. Sync with Knowledge Graph
        knowledge_graph_manager.integrate_learning(new_knowledge)
```

---

## 📊 **SUCCESS METRICS**

### **Performance Indicators**

- **Memory Utilization**: >85% of decisions informed by memory
- **Pattern Reuse**: >85% alignment with "Aprimore, Não Prolifere"
- **Error Reduction**: <15% repetition of documented mistakes
- **Context Continuity**: >90% session-to-session consistency

### **Quality Indicators**

- **Decision Confidence**: ≥8/10 average
- **Pattern Recognition**: ≥90% successful pattern application
- **Memory Accuracy**: ≥95% correct memory retrieval
- **Integration Efficiency**: <50ms memory access overhead

---

## 🎯 **INTEGRATION CHECKLIST**

- [ ] Orchestrator agent configured for memory coordination
- [ ] Memory bank files mapped to existing VIBECODE structure
- [ ] Knowledge Graph Manager integration active
- [ ] Self-correction patterns accessible to orchestrator
- [ ] Session context persistence implemented
- [ ] Quality gates and metrics tracking enabled
- [ ] Documentation updated with memory integration protocols

---

**AUTHORITY**: Integrates with @project-core/rules/master_rule.md
**VERSION**: 1.0.0
**COMPLIANCE**: VIBECODE V1.0 Standards
**COORDINATION**: Orchestrator Agent Authority
