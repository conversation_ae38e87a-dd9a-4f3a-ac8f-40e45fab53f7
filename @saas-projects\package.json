{"name": "@vibecode/saas-shared-resources", "version": "1.0.0", "private": true, "description": "VIBECODE Shared Resources Library - Templates, Components, and Utilities", "scripts": {"build:shared": "cd shared && npm run build", "dev:shared": "cd shared && npm run dev", "install:shared": "cd shared && npm install", "format": "biome format --write .", "format:check": "biome format .", "clean": "rm -rf shared/dist shared/node_modules", "setup": "npm run install:shared && npm run build:shared"}, "devDependencies": {"@biomejs/biome": "^1.9.1"}, "engines": {"node": ">=20.0.0"}, "workspaces": ["shared"]}