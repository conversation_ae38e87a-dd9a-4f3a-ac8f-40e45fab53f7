# Story 7.2: Caixa Diário e Fluxo de Caixa

## Status

Approved

## Story

**As a** clinic administrator and financial manager,  
**I want** a comprehensive daily cash management and cash flow system with real-time entry/exit tracking, automated daily closing, cash flow projections, and multi-payment method support,  
**so that** I can maintain accurate cash control, ensure daily closing in under 2 hours, and have clear visibility of cash flow trends and projections.

## Acceptance Criteria

1. **Real-time Cash Control:**
   - Real-time cash entry and exit tracking with instant balance updates
   - Multi-payment method support (cash, card, PIX, boleto, bank transfer)
   - Integration with Story 7.1 receivables and payables
   - Automatic transaction categorization and validation
   - Cash position dashboard with real-time KPIs

2. **Automated Daily Closing:**
   - Automated daily cash closing process with validation checks
   - Reconciliation with Story 7.1 accounts and expected transactions
   - Variance detection and resolution workflow
   - Daily closing reports with detailed breakdown
   - Historical daily closing tracking and analysis

3. **Cash Flow Management:**
   - Cash flow projections based on receivables and payables
   - Weekly and monthly cash flow forecasting
   - Cash flow variance analysis and alerts
   - Integration with Epic 6 scheduling for future revenue projections
   - Multi-scenario cash flow modeling

4. **Financial Dashboard and Reporting:**
   - Real-time financial dashboard with key cash metrics
   - Daily, weekly, and monthly cash flow reports
   - Payment method breakdown and analysis
   - Cash trend analysis with historical comparisons
   - Alert system for cash position thresholds

## Tasks / Subtasks

- [ ] Task 1: Build Real-time Cash Control System (AC: 1)
  - [ ] Create real-time cash transaction tracking with instant updates
  - [ ] Implement multi-payment method support and categorization
  - [ ] Build integration with Story 7.1 accounts receivable/payable
  - [ ] Create automatic transaction validation and error detection
  - [ ] Implement real-time cash position dashboard with KPIs

- [ ] Task 2: Develop Automated Daily Closing (AC: 2)
  - [ ] Create automated daily closing process with validation workflows
  - [ ] Build reconciliation system with Story 7.1 expected transactions
  - [ ] Implement variance detection and resolution interface
  - [ ] Create comprehensive daily closing reports and summaries
  - [ ] Build historical daily closing tracking and trend analysis

- [ ] Task 3: Implement Cash Flow Management (AC: 3)
  - [ ] Build cash flow projection engine using receivables/payables data
  - [ ] Create weekly and monthly cash flow forecasting system
  - [ ] Implement cash flow variance analysis with alert notifications
  - [ ] Build Epic 6 integration for future revenue projections
  - [ ] Create multi-scenario cash flow modeling and what-if analysis

- [ ] Task 4: Create Financial Dashboard and Reporting (AC: 4)
  - [ ] Build real-time financial dashboard with cash metrics and KPIs
  - [ ] Create comprehensive cash flow reporting system (daily/weekly/monthly)
  - [ ] Implement payment method analysis and breakdown reports
  - [ ] Build cash trend analysis with historical comparison tools
  - [ ] Create intelligent alert system for cash position thresholds

- [ ] Task 5: Develop Cash Management Interface (All ACs)
  - [ ] Create intuitive cash entry and transaction management interface
  - [ ] Build daily closing workflow with guided validation steps
  - [ ] Implement cash flow visualization with interactive charts
  - [ ] Create financial dashboard with real-time updates
  - [ ] Build comprehensive reporting interface with export capabilities

- [ ] Task 6: Build Integration and Synchronization (All ACs)
  - [ ] Create real-time integration with Story 7.1 accounts system
  - [ ] Build Epic 6 scheduling integration for revenue projections
  - [ ] Implement payment gateway synchronization for automatic entries
  - [ ] Create bank integration for cash position verification
  - [ ] Build audit trail and transaction logging system

- [ ] Task 7: Implement Analytics and Intelligence (All ACs)
  - [ ] Create cash flow trend analysis and pattern recognition
  - [ ] Build predictive analytics for cash position forecasting
  - [ ] Implement anomaly detection for unusual cash movements
  - [ ] Create benchmark analysis and performance metrics
  - [ ] Build automated insights and recommendation system

## Dev Notes

### Architecture Context

**Source:** docs/architecture/01-system-overview-context.md, 02-logical-components-data-flow.md

The cash management system integrates with the complete NeonPro financial architecture:

- Server Components for cash calculations and report generation
- Client Components for real-time cash entry and dashboard updates
- Edge Functions for automated closing processes and cash flow calculations
- Real-time subscriptions for instant cash position updates
- Background jobs for daily closing automation and report generation

### Database Integration

**Source:** docs/architecture/03-data-model-rls-policies.md, Epic 7 financial schema extension

Enhanced database schema for cash management:

- `cash_transactions` table for all cash movements with real-time tracking
- `daily_cash_closing` table for daily closing records and validation
- `cash_flow_projections` table for forecasting and scenario modeling
- `payment_methods` table for payment method configuration and tracking
- `cash_positions` table for real-time cash position snapshots
- `cash_alerts` table for threshold-based alert configuration

### Cash Management Engine

**Source:** Financial cash management best practices and real-time processing

Cash Engine Components:

- **Transaction Processor**: Real-time cash transaction processing and validation
- **Closing Engine**: Automated daily closing with reconciliation and validation
- **Projection Engine**: Cash flow forecasting using ML and historical data
- **Alert System**: Intelligent threshold-based alerting and notifications
- **Analytics Engine**: Cash trend analysis and predictive insights

### API Endpoints

**Source:** docs/architecture/05-api-surface-edge-functions.md

Required Edge Functions for cash management:

- `/api/cash/transactions` - Real-time cash transaction management
- `/api/cash/closing` - Daily closing process and validation
- `/api/cash/flow` - Cash flow projections and forecasting
- `/api/cash/dashboard` - Real-time dashboard data and KPIs
- `/api/cash/reports` - Cash reporting and analytics

### Component Architecture

**Source:** NeonPro financial UI patterns and real-time dashboard design

Location: `components/cash/` (new directory)

- `CashDashboard` - Real-time cash position and KPI dashboard
- `TransactionEntry` - Cash transaction entry and management
- `DailyClosing` - Daily closing process and validation interface
- `CashFlowProjections` - Cash flow forecasting and scenario modeling
- `PaymentMethodManager` - Payment method configuration and tracking
- `CashReports` - Comprehensive cash reporting and analytics

Pages: Cash management interfaces

- `app/dashboard/cash/page.tsx` - Main cash dashboard
- `app/dashboard/cash/transactions/page.tsx` - Transaction management
- `app/dashboard/cash/closing/page.tsx` - Daily closing interface
- `app/dashboard/cash/flow/page.tsx` - Cash flow analysis

### Integration with Story 7.1 (Contas a Pagar/Receber)

**Source:** Story 7.1 accounts management and financial transaction flow

Story 7.1 Integration Points:

- **Transaction Synchronization**: Real-time sync with receivables/payables
- **Payment Recognition**: Automatic cash entry from account payments
- **Reconciliation**: Daily reconciliation with account movements
- **Projection Data**: Use receivables/payables for cash flow forecasting
- **Validation**: Cross-validation between accounts and cash transactions

### Integration with Epic 6 (Agenda Inteligente)

**Source:** Epic 6 scheduling and revenue integration

Epic 6 Integration Points:

- **Revenue Projections**: Future cash flow based on scheduled appointments
- **Service Revenue**: Automatic cash recognition from completed services
- **Package Payments**: Integration with treatment package cash flows
- **Cancellation Impact**: Cash flow adjustment for cancelled appointments
- **Professional Revenue**: Cash flow breakdown by professional and service

### Real-time Cash Processing

**Source:** Real-time financial processing and instant updates

Real-time Features:

- **WebSocket Updates**: Instant cash position updates across all clients
- **Transaction Streaming**: Real-time transaction feed with instant validation
- **Balance Calculations**: Immediate balance updates with every transaction
- **Alert Triggers**: Real-time threshold monitoring and instant notifications
- **Dashboard Updates**: Live dashboard updates with sub-second latency

### Multi-Payment Method Support

**Source:** Brazilian payment ecosystem and financial processing

Payment Method Integration:

- **PIX Integration**: Real-time PIX transaction processing and recognition
- **Card Processing**: Credit/debit card transaction integration
- **Bank Transfer**: Electronic transfer processing and validation
- **Cash Handling**: Physical cash transaction tracking and validation
- **Boleto Integration**: Boleto payment processing and reconciliation

### Daily Closing Automation

**Source:** Financial closing best practices and automation workflows

Closing Process Features:

- **Automated Validation**: Multi-level validation checks and error detection
- **Reconciliation Engine**: Automatic reconciliation with expected transactions
- **Variance Analysis**: Intelligent variance detection and explanation
- **Approval Workflow**: Multi-step approval process for closing validation
- **Historical Tracking**: Complete audit trail of all closing processes

### Cash Flow Forecasting

**Source:** Financial forecasting methodologies and predictive analytics

Forecasting Engine:

- **ML Predictions**: Machine learning-based cash flow predictions
- **Scenario Modeling**: Multiple scenario analysis and what-if modeling
- **Historical Analysis**: Pattern recognition from historical cash data
- **External Factors**: Integration of external factors and seasonality
- **Confidence Intervals**: Statistical confidence ranges for predictions

### Security and Compliance

**Source:** docs/architecture/06-security-compliance.md, financial security standards

Cash Security:

- **Transaction Encryption**: End-to-end encryption for all cash transactions
- **Access Control**: Role-based access with RLS policies for cash data
- **Audit Logging**: Immutable audit trail for all cash movements
- **Fraud Detection**: Anomaly detection for suspicious cash patterns
- **Compliance**: Full compliance with financial regulations and LGPD

### Performance Requirements

**Source:** docs/prd/06-requirements.md, PRD 4 cash management specifications

- **Real-time Updates**: < 500ms for cash position updates
- **Daily Closing**: Complete closing process < 2 hours (PRD 4)
- **Dashboard Load**: < 2 seconds for cash dashboard (PRD 4)
- **Transaction Processing**: < 1 second for cash transaction entry
- **Report Generation**: < 5 seconds for monthly cash reports

### Error Handling and Recovery

**Source:** Financial system reliability and transaction integrity

Error Management:

- **Transaction Rollback**: Automatic rollback for failed transactions
- **Data Consistency**: ACID compliance for all cash operations
- **Recovery Procedures**: Automated recovery from system failures
- **Backup Integration**: Real-time backup of critical cash data
- **Disaster Recovery**: Complete disaster recovery procedures

### Analytics and Intelligence

**Source:** Financial analytics and business intelligence

Analytics Features:

- **Trend Analysis**: Cash trend identification and pattern analysis
- **Anomaly Detection**: Automatic detection of unusual cash patterns
- **Performance Metrics**: KPI tracking and benchmark comparisons
- **Predictive Insights**: AI-powered insights and recommendations
- **Comparative Analysis**: Period-over-period and year-over-year analysis

### Testing Strategy

**Testing Standards from Architecture:**

- Test file location: `__tests__/cash/` and `components/cash/__tests__/`
- Unit tests for cash calculations and transaction processing
- Integration tests with Story 7.1 and Epic 6 systems
- End-to-end tests for complete cash management workflows
- Performance testing for real-time updates and high transaction volumes
- Security testing for cash data protection and access control

**Required Test Coverage:**

- **Cash Calculations**: Accuracy of all cash balance calculations
- **Real-time Processing**: Performance and reliability of real-time updates
- **Daily Closing**: Complete daily closing workflow validation
- **Integration Testing**: Story 7.1 and Epic 6 integration validation
- **Security Testing**: Cash data access control and encryption

### File Structure

```text
components/cash/
├── CashDashboard.tsx          # Real-time cash dashboard
├── TransactionEntry.tsx       # Cash transaction entry
├── DailyClosing.tsx          # Daily closing interface
├── CashFlowProjections.tsx   # Cash flow forecasting
├── PaymentMethodManager.tsx  # Payment method management
├── CashReports.tsx           # Cash reporting and analytics
├── CashAlerts.tsx            # Alert and notification system
├── TransactionHistory.tsx    # Transaction history view
└── __tests__/
    ├── CashDashboard.test.tsx
    ├── TransactionEntry.test.tsx
    └── DailyClosing.test.tsx

app/dashboard/cash/
├── page.tsx                  # Main cash dashboard
├── transactions/
│   ├── page.tsx             # Transaction management
│   ├── entry/page.tsx       # Transaction entry
│   └── history/page.tsx     # Transaction history
├── closing/
│   ├── page.tsx             # Daily closing overview
│   ├── process/page.tsx     # Closing process
│   └── history/page.tsx     # Closing history
├── flow/
│   ├── page.tsx             # Cash flow analysis
│   ├── projections/page.tsx # Cash flow projections
│   └── scenarios/page.tsx   # Scenario modeling
└── reports/
    ├── page.tsx             # Cash reports
    └── analytics/page.tsx   # Cash analytics

app/api/cash/
├── transactions/route.ts     # Transaction API
├── closing/route.ts         # Daily closing API
├── flow/route.ts            # Cash flow API
├── dashboard/route.ts       # Dashboard data API
└── reports/route.ts         # Reporting API

lib/cash/
├── transaction-processor.ts  # Core transaction processing
├── closing-engine.ts        # Daily closing automation
├── flow-projector.ts        # Cash flow forecasting
├── payment-processor.ts     # Payment method processing
├── analytics-engine.ts      # Cash analytics
└── alert-system.ts          # Alert and notification system
```

### Dependencies

**External Dependencies:**

- decimal.js for precise cash calculations
- date-fns for date handling in cash flow projections
- recharts for cash flow visualization
- ioredis for real-time cash position caching
- bull for background job processing (daily closing)

**Internal Dependencies:**

- Story 7.1: Accounts receivable/payable integration
- Epic 6: Scheduling revenue integration
- Supabase: Real-time subscriptions and database
- Authentication: User and role management
- Notification system: Alert and notification delivery

### Payment Method Configuration

**Source:** Brazilian payment ecosystem and multi-method processing

Payment Method Features:

- **PIX Configuration**: Automatic PIX transaction recognition and processing
- **Card Integration**: Credit/debit card processing with gateway integration
- **Bank Account Management**: Multiple bank account configuration and tracking
- **Cash Handling**: Physical cash transaction validation and control
- **Fee Management**: Automatic fee calculation and deduction

### Cash Flow Modeling

**Source:** Financial modeling and forecasting methodologies

Modeling Features:

- **Historical Patterns**: Pattern recognition from historical cash data
- **Seasonal Adjustments**: Seasonal trend analysis and adjustment
- **Growth Projections**: Business growth impact on cash flow
- **Risk Analysis**: Cash flow risk assessment and mitigation
- **Sensitivity Analysis**: Impact analysis of key variables

### Compliance and Auditing

**Source:** Financial compliance and audit requirements

Compliance Features:

- **Audit Trail**: Complete immutable audit trail for all cash transactions
- **Regulatory Reporting**: Automated compliance reporting generation
- **Data Retention**: Long-term data retention with secure archiving
- **Access Logging**: Complete access log for all cash data interactions
- **Compliance Monitoring**: Automated compliance check and validation

### Performance Optimization

**Source:** Real-time system performance and scalability

Optimization Features:

- **Caching Strategy**: Intelligent caching for frequently accessed cash data
- **Database Indexing**: Optimized indexing for cash transaction queries
- **Real-time Processing**: Efficient real-time processing architecture
- **Batch Processing**: Optimized batch processing for reports and analytics
- **Load Balancing**: Distributed processing for high-volume cash operations

## Testing

### Testing Requirements

**Unit Testing:**

- Real-time cash transaction processing accuracy
- Daily closing calculation validation
- Cash flow projection algorithm testing
- Payment method processing logic validation

**Integration Testing:**

- Story 7.1 accounts integration validation
- Epic 6 scheduling revenue integration
- Payment gateway integration testing
- Real-time update system validation

**End-to-End Testing:**

- Complete cash management workflow testing
- Daily closing process end-to-end validation
- Multi-payment method transaction processing
- Cash flow forecasting accuracy testing

**Performance Testing:**

- Real-time update performance under high load
- Daily closing process performance optimization
- Dashboard loading performance with large datasets
- Concurrent user access performance testing

**Security Testing:**

- Cash data encryption and access control validation
- Transaction integrity and fraud detection testing
- Audit trail completeness and immutability verification
- Role-based access control validation

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-18 | 1.0 | Initial story creation from Epic 7 | Scrum Master Bob |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

### Agent Model Used

*To be filled by dev agent*

### Implementation Status

*To be filled by dev agent*

### Task Completion Tracking

*To be filled by dev agent*

### Debug Log References

*To be filled by dev agent*

### Completion Notes

*To be filled by dev agent*

### File List

*To be filled by dev agent*

### Change Log

*To be filled by dev agent*
