# Story 5.2: Portal de Gestão de Consultas do Paciente

## Status

Approved

## Story

**As a** registered patient,  
**I want** to manage my existing appointments, view my appointment history, and access my medical information,  
**so that** I can have full control over my healthcare appointments and access important information.

## Acceptance Criteria

1. **Patient Authentication and Dashboard:**
   - Secure patient login with email/phone verification
   - Personalized dashboard with upcoming appointments
   - Appointment history with status and notes
   - Quick access to frequently used actions
   - Mobile-responsive design for smartphone access

2. **Appointment Management:**
   - View detailed appointment information (date, time, professional, service)
   - Cancel appointments with cancellation policy enforcement
   - Reschedule appointments with available slot selection
   - Add notes or special requests to existing appointments
   - Download appointment confirmation and receipts

3. **Medical Information Access:**
   - Access to appointment summaries and notes (LGPD compliant)
   - View treatment history and progress photos (with consent)
   - Download medical reports and certificates
   - Update personal and emergency contact information
   - Manage consent preferences and privacy settings

4. **Communication Features:**
   - Secure messaging with healthcare professionals
   - Appointment reminders via email/SMS preferences
   - Notification settings management
   - Feedback and rating system for completed appointments
   - Emergency contact information and clinic hours

## Tasks / Subtasks

- [ ] Task 1: Implement Patient Authentication System (AC: 1)
  - [ ] Create patient login/registration flow with Supabase Auth
  - [ ] Implement email/phone verification using existing auth patterns
  - [ ] Set up patient-specific RLS policies for data access
  - [ ] Create secure password reset and account recovery
  - [ ] Add two-factor authentication option

- [ ] Task 2: Build Patient Dashboard Interface (AC: 1)
  - [ ] Create personalized dashboard layout using shadcn/ui components
  - [ ] Implement upcoming appointments display with real-time updates
  - [ ] Build appointment history view with filtering and search
  - [ ] Add quick action buttons for common tasks
  - [ ] Ensure mobile-responsive design with touch-friendly navigation

- [ ] Task 3: Develop Appointment Management Features (AC: 2)
  - [ ] Create detailed appointment view with all relevant information
  - [ ] Implement cancellation flow with policy validation
  - [ ] Build rescheduling interface with availability checking
  - [ ] Add appointment notes and special requests functionality
  - [ ] Create appointment confirmation and receipt download feature

- [ ] Task 4: Implement Medical Information Access (AC: 3)
  - [ ] Create LGPD-compliant medical information display
  - [ ] Build treatment history view with progress tracking
  - [ ] Implement secure document download functionality
  - [ ] Add personal information update forms with validation
  - [ ] Create consent management interface for privacy settings

- [ ] Task 5: Build Communication Features (AC: 4)
  - [ ] Implement secure messaging system with healthcare professionals
  - [ ] Create notification preferences management interface
  - [ ] Build feedback and rating system for completed appointments
  - [ ] Add emergency contact and clinic information display
  - [ ] Integrate with existing messaging system from Epic 1

- [ ] Task 6: Implement Security and Compliance (All ACs)
  - [ ] Add comprehensive audit logging for patient actions
  - [ ] Implement session management and timeout handling
  - [ ] Create LGPD-compliant data access controls
  - [ ] Add encryption for sensitive patient communications
  - [ ] Implement rate limiting for patient portal actions

- [ ] Task 7: Create Integration Points (All ACs)
  - [ ] Integrate with Epic 1 authentication system
  - [ ] Connect to Epic 2 financial system for receipts and billing
  - [ ] Link to Epic 3 BI system for analytics
  - [ ] Prepare integration points for Epic 4 AI suggestions

## Dev Notes

### Architecture Context

**Source:** docs/architecture/01-system-overview-context.md, 02-logical-components-data-flow.md

The patient portal follows the established Next.js 15 architecture with enhanced security for patient data:

- Server Components for secure data rendering with patient-specific RLS
- Client Components for interactive dashboard and appointment management
- Edge Functions for secure patient actions and real-time updates
- Enhanced Supabase RLS policies for patient data isolation

### Database Integration

**Source:** docs/architecture/03-data-model-rls-policies.md

Patient portal requires enhanced RLS policies for:

- `patients` table with patient-specific access controls
- `appointments` table with patient ownership validation
- `medical_records` table with LGPD-compliant access patterns
- `patient_communications` table for secure messaging
- `patient_preferences` table for notification and privacy settings

### Authentication System

**Source:** Epic 1 integration patterns

Patient authentication extends existing system:

- Separate patient role in Supabase Auth
- Enhanced security with optional 2FA
- Patient-specific session management
- Integration with existing clinic staff authentication
- Secure patient data access patterns

### API Endpoints

**Source:** docs/architecture/05-api-surface-edge-functions.md

Required Edge Functions for patient portal:

- `/api/patient/dashboard` - Get patient dashboard data with appointments
- `/api/patient/appointments` - Manage patient appointments (view, cancel, reschedule)
- `/api/patient/medical-records` - LGPD-compliant medical information access
- `/api/patient/communications` - Secure messaging with healthcare professionals
- `/api/patient/preferences` - Manage notification and privacy preferences

### Component Architecture

**Source:** Existing component patterns in components/

Location: `components/patient/` (new directory)

- `PatientDashboard` - Main dashboard with appointments overview
- `AppointmentManager` - Appointment management interface
- `MedicalRecords` - LGPD-compliant medical information display
- `SecureMessaging` - Communication with healthcare professionals
- `PreferencesManager` - Privacy and notification settings
- `PatientAuth` - Patient-specific authentication components

Pages: `app/patient/` (new directory)

- `login/page.tsx` - Patient login page
- `dashboard/page.tsx` - Main patient dashboard
- `appointments/page.tsx` - Appointment management
- `medical/page.tsx` - Medical information access
- `messages/page.tsx` - Secure messaging
- `settings/page.tsx` - Preferences and privacy settings

### Security and Compliance

**Source:** docs/architecture/06-security-compliance.md

Enhanced security for patient portal:

- LGPD compliance for patient data access and retention
- End-to-end encryption for sensitive communications
- Audit logging for all patient data access
- Session timeout and security monitoring
- Two-factor authentication option
- Secure document download with watermarking

### Integration Points

**Source:** Epic dependencies and existing systems

- Epic 1: Extended authentication system with patient roles
- Epic 2: Financial integration for appointment receipts and billing
- Epic 3: Analytics integration for patient engagement metrics
- Epic 4: AI integration for personalized recommendations
- Story 5.1: Shared components and authentication flow

### Technical Constraints

**Source:** docs/prd/06-requirements.md

- LGPD compliance requirements for patient data handling
- Session security with automatic timeout (30 minutes inactivity)
- Mobile-first responsive design requirements
- Performance: Dashboard load < 2 seconds
- Accessibility: WCAG 2.1 AA compliance
- Data retention policies for patient communications

### Testing

**Testing Standards from Architecture:**

- Test file location: `__tests__/patient/` and `components/patient/__tests__/`
- Unit tests for patient authentication flow
- Integration tests for appointment management functionality
- End-to-end tests for complete patient journey
- Security testing for data access controls
- LGPD compliance testing for data handling

**Required Test Coverage:**

- Patient authentication and authorization scenarios
- Appointment management operations (view, cancel, reschedule)
- Medical information access with consent validation
- Secure messaging functionality
- Privacy settings and preferences management
- Mobile responsiveness and accessibility

### Performance Requirements

**Source:** docs/prd/06-requirements.md

- Dashboard load times under 2 seconds (RNF-01)
- Mobile optimization for patient smartphone usage
- Offline capabilities for appointment viewing
- Image optimization for medical records and photos
- Caching strategy for patient data
- Real-time updates for appointment changes

### File Structure

```text
app/patient/
├── login/page.tsx              # Patient login
├── dashboard/page.tsx          # Main dashboard
├── appointments/
│   ├── page.tsx               # Appointment list
│   ├── [id]/page.tsx          # Appointment details
│   └── reschedule/page.tsx    # Rescheduling interface
├── medical/
│   ├── page.tsx               # Medical records
│   └── documents/page.tsx     # Document downloads
├── messages/
│   ├── page.tsx               # Message list
│   └── [threadId]/page.tsx    # Message thread
├── settings/page.tsx           # Preferences
└── layout.tsx                 # Patient portal layout

components/patient/
├── PatientDashboard.tsx
├── AppointmentManager.tsx
├── MedicalRecords.tsx
├── SecureMessaging.tsx
├── PreferencesManager.tsx
├── PatientAuth.tsx
└── __tests__/
    ├── PatientDashboard.test.tsx
    ├── AppointmentManager.test.tsx
    └── SecureMessaging.test.tsx

app/api/patient/
├── dashboard/route.ts
├── appointments/route.ts
├── medical-records/route.ts
├── communications/route.ts
└── preferences/route.ts
```

### Dependencies

**External Dependencies:**

- @supabase/auth-helpers-nextjs for patient authentication
- react-hook-form + zod for form validation
- date-fns for appointment date handling
- react-pdf for document viewing
- socket.io-client for real-time messaging

**Internal Dependencies:**

- Story 5.1: Shared authentication and booking components
- Epic 1: Enhanced authentication system
- Epic 2: Financial system integration
- Epic 3: Analytics for patient engagement
- Existing messaging infrastructure

### Data Privacy and LGPD Compliance

**Source:** Brazilian data protection regulations

- Patient consent management for data processing
- Right to data portability implementation
- Right to be forgotten functionality
- Data minimization for appointment and medical records
- Audit trail for all patient data access
- Anonymization options for analytics
- Secure data export functionality

## Testing

### Testing Requirements

**Unit Testing:**

- Patient authentication flow with edge cases
- Appointment management operations validation
- Medical record access with consent checking
- Privacy settings and preferences management

**Integration Testing:**

- Complete patient journey from login to appointment management
- Integration with existing Epic systems
- Real-time appointment updates and notifications
- Secure messaging with healthcare professionals

**End-to-End Testing:**

- Full patient portal functionality across devices
- LGPD compliance scenarios (consent, data access, deletion)
- Security testing for patient data protection
- Performance testing under concurrent patient load

**Security Testing:**

- Authentication bypass attempts
- Session hijacking prevention
- Data access authorization validation
- Secure communication encryption verification

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-18 | 1.0 | Initial story creation from Epic 5 | Scrum Master Bob |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

### Agent Model Used

*To be filled by dev agent*

### Implementation Status

*To be filled by dev agent*

### Task Completion Tracking

*To be filled by dev agent*

### Debug Log References

*To be filled by dev agent*

### Completion Notes

*To be filled by dev agent*

### File List

*To be filled by dev agent*

### Change Log

*To be filled by dev agent*
