"use client";

import { createClient } from "@/app/utils/supabase/client";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useReducer,
} from "react";
import { toast } from "sonner";

// Types
export interface Customer {
  id: string;
  profile_id: string;
  profile?: {
    name: string;
    email: string;
    phone: string;
  };
  customer_since: string;
  lifetime_value: number;
  last_treatment?: string;
  last_visit?: string;
  total_visits: number;
  email_opt_in: boolean;
  sms_opt_in: boolean;
  whatsapp_opt_in: boolean;
  marketing_opt_in: boolean;
  status: "active" | "inactive" | "blocked";
  created_at: string;
  updated_at: string;
}

export interface CustomerSegment {
  id: string;
  name: string;
  description?: string;
  criteria: Record<string, any>;
  auto_update: boolean;
  customer_count: number;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface MarketingCampaign {
  id: string;
  name: string;
  description?: string;
  type: "email" | "sms" | "whatsapp" | "push";
  status: "draft" | "scheduled" | "sending" | "sent" | "cancelled";
  target_segment_id?: string;
  target_all_customers: boolean;
  subject?: string;
  content: Record<string, any>;
  schedule_date?: string;
  send_immediately: boolean;
  sent_at?: string;
  total_recipients: number;
  metrics: Record<string, any>;
  created_by: string;
  created_at: string;
  updated_at: string;
}

// State interface
interface CRMState {
  customers: Customer[];
  segments: CustomerSegment[];
  campaigns: MarketingCampaign[];
  loading: {
    customers: boolean;
    segments: boolean;
    campaigns: boolean;
  };
  errors: {
    customers?: string;
    segments?: string;
    campaigns?: string;
  };
  filters: {
    customer_search: string;
    customer_status: string;
    customer_segment: string;
  };
}

// Actions
type CRMAction =
  | {
      type: "SET_LOADING";
      payload: { key: keyof CRMState["loading"]; value: boolean };
    }
  | {
      type: "SET_ERROR";
      payload: { key: keyof CRMState["errors"]; value?: string };
    }
  | { type: "SET_CUSTOMERS"; payload: Customer[] }
  | { type: "ADD_CUSTOMER"; payload: Customer }
  | { type: "UPDATE_CUSTOMER"; payload: Customer }
  | { type: "DELETE_CUSTOMER"; payload: string }
  | { type: "SET_SEGMENTS"; payload: CustomerSegment[] }
  | { type: "ADD_SEGMENT"; payload: CustomerSegment }
  | { type: "UPDATE_SEGMENT"; payload: CustomerSegment }
  | { type: "DELETE_SEGMENT"; payload: string }
  | { type: "SET_CAMPAIGNS"; payload: MarketingCampaign[] }
  | { type: "ADD_CAMPAIGN"; payload: MarketingCampaign }
  | { type: "UPDATE_CAMPAIGN"; payload: MarketingCampaign }
  | { type: "DELETE_CAMPAIGN"; payload: string }
  | {
      type: "SET_FILTER";
      payload: { key: keyof CRMState["filters"]; value: string };
    };

// Reducer
const crmReducer = (state: CRMState, action: CRMAction): CRMState => {
  switch (action.type) {
    case "SET_LOADING":
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.key]: action.payload.value,
        },
      };
    case "SET_ERROR":
      return {
        ...state,
        errors: { ...state.errors, [action.payload.key]: action.payload.value },
      };
    case "SET_CUSTOMERS":
      return { ...state, customers: action.payload };
    case "ADD_CUSTOMER":
      return { ...state, customers: [...state.customers, action.payload] };
    case "UPDATE_CUSTOMER":
      return {
        ...state,
        customers: state.customers.map((customer) =>
          customer.id === action.payload.id ? action.payload : customer
        ),
      };
    case "DELETE_CUSTOMER":
      return {
        ...state,
        customers: state.customers.filter(
          (customer) => customer.id !== action.payload
        ),
      };
    case "SET_SEGMENTS":
      return { ...state, segments: action.payload };
    case "ADD_SEGMENT":
      return { ...state, segments: [...state.segments, action.payload] };
    case "UPDATE_SEGMENT":
      return {
        ...state,
        segments: state.segments.map((segment) =>
          segment.id === action.payload.id ? action.payload : segment
        ),
      };
    case "DELETE_SEGMENT":
      return {
        ...state,
        segments: state.segments.filter(
          (segment) => segment.id !== action.payload
        ),
      };
    case "SET_CAMPAIGNS":
      return { ...state, campaigns: action.payload };
    case "ADD_CAMPAIGN":
      return { ...state, campaigns: [...state.campaigns, action.payload] };
    case "UPDATE_CAMPAIGN":
      return {
        ...state,
        campaigns: state.campaigns.map((campaign) =>
          campaign.id === action.payload.id ? action.payload : campaign
        ),
      };
    case "DELETE_CAMPAIGN":
      return {
        ...state,
        campaigns: state.campaigns.filter(
          (campaign) => campaign.id !== action.payload
        ),
      };
    case "SET_FILTER":
      return {
        ...state,
        filters: {
          ...state.filters,
          [action.payload.key]: action.payload.value,
        },
      };
    default:
      return state;
  }
};

// Initial state
const initialState: CRMState = {
  customers: [],
  segments: [],
  campaigns: [],
  loading: {
    customers: false,
    segments: false,
    campaigns: false,
  },
  errors: {},
  filters: {
    customer_search: "",
    customer_status: "",
    customer_segment: "",
  },
};

// Context interface
interface CRMContextType {
  state: CRMState;

  // Customer actions
  loadCustomers: () => Promise<void>;
  createCustomer: (
    customer: Omit<Customer, "id" | "created_at" | "updated_at">
  ) => Promise<void>;
  updateCustomer: (customer: Customer) => Promise<void>;
  deleteCustomer: (customerId: string) => Promise<void>;

  // Segment actions
  loadSegments: () => Promise<void>;
  createSegment: (
    segment: Omit<CustomerSegment, "id" | "created_at" | "updated_at">
  ) => Promise<void>;
  updateSegment: (segment: CustomerSegment) => Promise<void>;
  deleteSegment: (segmentId: string) => Promise<void>;

  // Campaign actions
  loadCampaigns: () => Promise<void>;
  createCampaign: (
    campaign: Omit<MarketingCampaign, "id" | "created_at" | "updated_at">
  ) => Promise<void>;
  updateCampaign: (campaign: MarketingCampaign) => Promise<void>;
  deleteCampaign: (campaignId: string) => Promise<void>;

  // Filter actions
  setFilter: (key: keyof CRMState["filters"], value: string) => void;
  clearFilters: () => void;

  // Computed properties
  filteredCustomers: Customer[];
}

// Create context
const CRMContext = createContext<CRMContextType | undefined>(undefined);

// Provider component
export function CRMProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(crmReducer, initialState);
  const supabase = createClient();

  // Customer functions
  const loadCustomers = async () => {
    dispatch({
      type: "SET_LOADING",
      payload: { key: "customers", value: true },
    });
    dispatch({
      type: "SET_ERROR",
      payload: { key: "customers", value: undefined },
    });

    try {
      const { data, error } = await supabase
        .from("customers")
        .select(
          `
          *,
          profile:profiles(
            name,
            email,
            phone
          )
        `
        )
        .order("created_at", { ascending: false });

      if (error) throw error;

      dispatch({ type: "SET_CUSTOMERS", payload: data || [] });
    } catch (error) {
      console.error("Error loading customers:", error);
      dispatch({
        type: "SET_ERROR",
        payload: { key: "customers", value: "Erro ao carregar clientes" },
      });
      toast.error("Erro ao carregar clientes");
    } finally {
      dispatch({
        type: "SET_LOADING",
        payload: { key: "customers", value: false },
      });
    }
  };

  const createCustomer = async (
    customerData: Omit<Customer, "id" | "created_at" | "updated_at">
  ) => {
    try {
      const { data, error } = await supabase
        .from("customers")
        .insert([customerData])
        .select()
        .single();

      if (error) throw error;

      dispatch({ type: "ADD_CUSTOMER", payload: data });
      toast.success("Cliente criado com sucesso");
    } catch (error) {
      console.error("Error creating customer:", error);
      toast.error("Erro ao criar cliente");
      throw error;
    }
  };

  const updateCustomer = async (customer: Customer) => {
    try {
      const { data, error } = await supabase
        .from("customers")
        .update(customer)
        .eq("id", customer.id)
        .select()
        .single();

      if (error) throw error;

      dispatch({ type: "UPDATE_CUSTOMER", payload: data });
      toast.success("Cliente atualizado com sucesso");
    } catch (error) {
      console.error("Error updating customer:", error);
      toast.error("Erro ao atualizar cliente");
      throw error;
    }
  };

  const deleteCustomer = async (customerId: string) => {
    try {
      const { error } = await supabase
        .from("customers")
        .delete()
        .eq("id", customerId);

      if (error) throw error;

      dispatch({ type: "DELETE_CUSTOMER", payload: customerId });
      toast.success("Cliente excluído com sucesso");
    } catch (error) {
      console.error("Error deleting customer:", error);
      toast.error("Erro ao excluir cliente");
      throw error;
    }
  };

  // Segment functions
  const loadSegments = async () => {
    dispatch({
      type: "SET_LOADING",
      payload: { key: "segments", value: true },
    });
    dispatch({
      type: "SET_ERROR",
      payload: { key: "segments", value: undefined },
    });

    try {
      const { data, error } = await supabase
        .from("customer_segments")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;

      dispatch({ type: "SET_SEGMENTS", payload: data || [] });
    } catch (error) {
      console.error("Error loading segments:", error);
      dispatch({
        type: "SET_ERROR",
        payload: { key: "segments", value: "Erro ao carregar segmentos" },
      });
      toast.error("Erro ao carregar segmentos");
    } finally {
      dispatch({
        type: "SET_LOADING",
        payload: { key: "segments", value: false },
      });
    }
  };

  const createSegment = async (
    segmentData: Omit<CustomerSegment, "id" | "created_at" | "updated_at">
  ) => {
    try {
      const { data, error } = await supabase
        .from("customer_segments")
        .insert([segmentData])
        .select()
        .single();

      if (error) throw error;

      dispatch({ type: "ADD_SEGMENT", payload: data });
      toast.success("Segmento criado com sucesso");
    } catch (error) {
      console.error("Error creating segment:", error);
      toast.error("Erro ao criar segmento");
      throw error;
    }
  };

  const updateSegment = async (segment: CustomerSegment) => {
    try {
      const { data, error } = await supabase
        .from("customer_segments")
        .update(segment)
        .eq("id", segment.id)
        .select()
        .single();

      if (error) throw error;

      dispatch({ type: "UPDATE_SEGMENT", payload: data });
      toast.success("Segmento atualizado com sucesso");
    } catch (error) {
      console.error("Error updating segment:", error);
      toast.error("Erro ao atualizar segmento");
      throw error;
    }
  };

  const deleteSegment = async (segmentId: string) => {
    try {
      const { error } = await supabase
        .from("customer_segments")
        .delete()
        .eq("id", segmentId);

      if (error) throw error;

      dispatch({ type: "DELETE_SEGMENT", payload: segmentId });
      toast.success("Segmento excluído com sucesso");
    } catch (error) {
      console.error("Error deleting segment:", error);
      toast.error("Erro ao excluir segmento");
      throw error;
    }
  };

  // Campaign functions
  const loadCampaigns = async () => {
    dispatch({
      type: "SET_LOADING",
      payload: { key: "campaigns", value: true },
    });
    dispatch({
      type: "SET_ERROR",
      payload: { key: "campaigns", value: undefined },
    });

    try {
      const { data, error } = await supabase
        .from("marketing_campaigns")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;

      dispatch({ type: "SET_CAMPAIGNS", payload: data || [] });
    } catch (error) {
      console.error("Error loading campaigns:", error);
      dispatch({
        type: "SET_ERROR",
        payload: { key: "campaigns", value: "Erro ao carregar campanhas" },
      });
      toast.error("Erro ao carregar campanhas");
    } finally {
      dispatch({
        type: "SET_LOADING",
        payload: { key: "campaigns", value: false },
      });
    }
  };

  const createCampaign = async (
    campaignData: Omit<MarketingCampaign, "id" | "created_at" | "updated_at">
  ) => {
    try {
      const { data, error } = await supabase
        .from("marketing_campaigns")
        .insert([campaignData])
        .select()
        .single();

      if (error) throw error;

      dispatch({ type: "ADD_CAMPAIGN", payload: data });
      toast.success("Campanha criada com sucesso");
    } catch (error) {
      console.error("Error creating campaign:", error);
      toast.error("Erro ao criar campanha");
      throw error;
    }
  };

  const updateCampaign = async (campaign: MarketingCampaign) => {
    try {
      const { data, error } = await supabase
        .from("marketing_campaigns")
        .update(campaign)
        .eq("id", campaign.id)
        .select()
        .single();

      if (error) throw error;

      dispatch({ type: "UPDATE_CAMPAIGN", payload: data });
      toast.success("Campanha atualizada com sucesso");
    } catch (error) {
      console.error("Error updating campaign:", error);
      toast.error("Erro ao atualizar campanha");
      throw error;
    }
  };

  const deleteCampaign = async (campaignId: string) => {
    try {
      const { error } = await supabase
        .from("marketing_campaigns")
        .delete()
        .eq("id", campaignId);

      if (error) throw error;

      dispatch({ type: "DELETE_CAMPAIGN", payload: campaignId });
      toast.success("Campanha excluída com sucesso");
    } catch (error) {
      console.error("Error deleting campaign:", error);
      toast.error("Erro ao excluir campanha");
      throw error;
    }
  };

  // Filter functions
  const setFilter = (key: keyof CRMState["filters"], value: string) => {
    dispatch({ type: "SET_FILTER", payload: { key, value } });
  };

  const clearFilters = () => {
    dispatch({
      type: "SET_FILTER",
      payload: { key: "customer_search", value: "" },
    });
    dispatch({
      type: "SET_FILTER",
      payload: { key: "customer_status", value: "" },
    });
    dispatch({
      type: "SET_FILTER",
      payload: { key: "customer_segment", value: "" },
    });
  };

  // Filtered customers (computed property)
  const filteredCustomers = state.customers.filter((customer) => {
    // Search filter
    if (state.filters.customer_search) {
      const search = state.filters.customer_search.toLowerCase();
      const matchesSearch =
        customer.profile?.name?.toLowerCase().includes(search) ||
        customer.profile?.email?.toLowerCase().includes(search) ||
        customer.profile?.phone?.includes(search);

      if (!matchesSearch) return false;
    }

    // Status filter
    if (
      state.filters.customer_status &&
      customer.status !== state.filters.customer_status
    ) {
      return false;
    }

    return true;
  });

  // Load initial data
  useEffect(() => {
    loadCustomers();
    loadSegments();
    loadCampaigns();
  }, []);

  const contextValue: CRMContextType = {
    state,

    // Customer actions
    loadCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,

    // Segment actions
    loadSegments,
    createSegment,
    updateSegment,
    deleteSegment,

    // Campaign actions
    loadCampaigns,
    createCampaign,
    updateCampaign,
    deleteCampaign,

    // Filter actions
    setFilter,
    clearFilters,

    // Computed properties
    filteredCustomers,
  };

  return (
    <CRMContext.Provider value={contextValue}>{children}</CRMContext.Provider>
  );
}

// Custom hook
export function useCRM() {
  const context = useContext(CRMContext);
  if (context === undefined) {
    throw new Error("useCRM must be used within a CRMProvider");
  }
  return context;
}
