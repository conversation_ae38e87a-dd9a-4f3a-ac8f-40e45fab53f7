---
source_id: self_correction
data_file: @project-core/memory/self_correction_log.md
priority: 2
enabled: true
tags_func: error_correction_tags
description: Sistema de auto-correção e aprendizado de erros
---

# Self Correction Manifest

Este manifesto configura o sistema de auto-correção que aprende com erros anteriores.

## Configuração

- **Source ID**: `self_correction`
- **Data File**: Caminho para o log de auto-correção
- **Priority**: 2 (alta prioridade)
- **Tags Function**: `error_correction_tags` para categorização de erros

## Funcionalidade

Carrega o histórico de erros e correções para evitar repetição de problemas já resolvidos, seguindo o princípio "Aprimore, Não Prolifere".
