{"name": "@vibecode/shared", "version": "1.0.0", "description": "Shared components and utilities for VIBECODE SaaS projects", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./ui": {"import": "./dist/ui/index.mjs", "require": "./dist/ui/index.js", "types": "./dist/ui/index.d.ts"}, "./lib/*": {"import": "./dist/lib/*.mjs", "require": "./dist/lib/*.js", "types": "./dist/lib/*.d.ts"}, "./config/*": {"import": "./dist/config/*.mjs", "require": "./dist/config/*.js", "types": "./dist/config/*.d.ts"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "type-check": "tsc --noEmit", "clean": "rm -rf dist .turbo"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.2", "@stripe/stripe-js": "^5.5.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.50.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "react-hook-form": "^7.54.2", "stripe": "^18.1.0", "tailwind-merge": "^3.3.0", "zod": "^3.25.64"}, "peerDependencies": {"next": ">=15.0.0", "react": ">=19.0.0", "react-dom": ">=19.0.0"}, "devDependencies": {"@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tsup": "^8.3.0", "typescript": "^5.8.3"}}