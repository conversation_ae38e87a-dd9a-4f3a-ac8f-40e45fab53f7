"""
Logging utilities for the sync system
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Optional


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    log_format: Optional[str] = None
) -> logging.Logger:
    """
    Set up logging for the sync system
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        log_format: Optional custom log format
    
    Returns:
        Configured logger instance
    """
    
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Create logger
    logger = logging.getLogger("vibecode_sync")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_formatter = logging.Formatter(log_format)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        # Ensure log directory exists
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, log_level.upper()))
        file_formatter = logging.Formatter(log_format)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str = "vibecode_sync") -> logging.Logger:
    """
    Get a logger instance
    
    Args:
        name: Logger name
    
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


class SyncLogger:
    """Enhanced logger for sync operations"""
    
    def __init__(self, name: str = "vibecode_sync"):
        self.logger = get_logger(name)
        self.operation_id = None
    
    def start_operation(self, operation_name: str) -> str:
        """Start a new operation and return operation ID"""
        self.operation_id = f"{operation_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"Starting operation: {self.operation_id}")
        return self.operation_id
    
    def end_operation(self, success: bool = True, message: str = ""):
        """End the current operation"""
        if self.operation_id:
            status = "SUCCESS" if success else "FAILED"
            self.logger.info(f"Operation {self.operation_id} completed: {status} - {message}")
            self.operation_id = None
    
    def log_file_operation(self, operation: str, file_path: str, success: bool = True):
        """Log file operations"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"File {operation}: {file_path} - {status}")
    
    def log_conflict(self, conflict_type: str, source: str, target: str):
        """Log conflict detection"""
        self.logger.warning(f"Conflict detected ({conflict_type}): {source} <-> {target}")
    
    def log_sync_stats(self, files_synced: int, conflicts: int, errors: int):
        """Log sync statistics"""
        self.logger.info(f"Sync completed - Files: {files_synced}, Conflicts: {conflicts}, Errors: {errors}")
    
    def debug(self, message: str):
        """Log debug message"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Log error message"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """Log critical message"""
        self.logger.critical(message)