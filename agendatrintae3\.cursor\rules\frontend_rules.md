# AgendaTrintaE3 Frontend Development Rules

## Calendar UI Architecture

### Component Structure
```
src/
├── components/
│   ├── calendar/
│   │   ├── CalendarGrid.tsx      # Main calendar display
│   │   ├── EventCard.tsx         # Individual event display
│   │   ├── TimeSlot.tsx          # Time slot components
│   │   └── RecurrenceEditor.tsx  # Recurring event editor
│   ├── scheduling/
│   │   ├── AvailabilityPicker.tsx
│   │   ├── ConflictResolver.tsx
│   │   └── TimezonePicker.tsx
│   └── forms/
│       ├── EventForm.tsx
│       ├── InvitationForm.tsx
│       └── ReminderForm.tsx
```

### Calendar View Components
```typescript
// Multi-view calendar component
const CalendarView = () => {
  const [viewType, setViewType] = useState<'day' | 'week' | 'month' | 'year'>('month');
  const [currentDate, setCurrentDate] = useState(new Date());
  
  const ViewComponent = {
    day: DayView,
    week: WeekView,
    month: MonthView,
    year: YearView
  }[viewType];
  
  return (
    <div className="calendar-container">
      <CalendarHeader 
        viewType={viewType}
        onViewChange={setViewType}
        currentDate={currentDate}
        onDateChange={setCurrentDate}
      />
      <ViewComponent 
        date={currentDate}
        events={events}
        onEventClick={handleEventClick}
        onTimeSlotClick={handleTimeSlotClick}
      />
    </div>
  );
};
```

## Date/Time Handling

### Timezone-Aware Components
```typescript
// Timezone-aware date picker
const TimezoneAwareDatePicker = ({ value, onChange, timezone }: Props) => {
  const localDate = convertFromTimezone(value, timezone);
  
  const handleDateChange = (newDate: Date) => {
    const utcDate = convertToTimezone(newDate, timezone);
    onChange(utcDate);
  };
  
  return (
    <div className="timezone-date-picker">
      <DatePicker 
        value={localDate}
        onChange={handleDateChange}
        showTimeSelect
        timeFormat="HH:mm"
        dateFormat="MMMM d, yyyy h:mm aa"
      />
      <TimezoneDisplay timezone={timezone} />
    </div>
  );
};
```

### Date Formatting
```typescript
// Consistent date formatting across the app
const formatEventDate = (date: Date, timezone: string, format: 'short' | 'long' | 'time') => {
  const localDate = convertFromTimezone(date, timezone);
  
  const formats = {
    short: 'MMM d',
    long: 'MMMM d, yyyy',
    time: 'h:mm aa'
  };
  
  return format(localDate, formats[format]);
};
```

## Event Management UI

### Event Creation Flow
```typescript
const EventCreationWizard = () => {
  const [step, setStep] = useState(1);
  const [eventData, setEventData] = useState<Partial<CalendarEvent>>({});
  
  const steps = [
    { component: BasicInfoStep, title: 'Event Details' },
    { component: DateTimeStep, title: 'Date & Time' },
    { component: RecurrenceStep, title: 'Repeat Settings' },
    { component: AttendeesStep, title: 'Attendees' },
    { component: RemindersStep, title: 'Reminders' }
  ];
  
  return (
    <WizardContainer>
      <StepIndicator currentStep={step} totalSteps={steps.length} />
      <StepContent 
        step={steps[step - 1]}
        data={eventData}
        onDataChange={setEventData}
        onNext={() => setStep(step + 1)}
        onPrevious={() => setStep(step - 1)}
      />
    </WizardContainer>
  );
};
```

### Recurring Event Editor
```typescript
const RecurrenceEditor = ({ rule, onChange }: Props) => {
  const [frequency, setFrequency] = useState(rule?.frequency || 'WEEKLY');
  const [interval, setInterval] = useState(rule?.interval || 1);
  const [endType, setEndType] = useState<'never' | 'count' | 'until'>('never');
  
  return (
    <div className="recurrence-editor">
      <FrequencySelector value={frequency} onChange={setFrequency} />
      <IntervalInput value={interval} onChange={setInterval} />
      <EndConditionSelector 
        type={endType}
        onTypeChange={setEndType}
        rule={rule}
        onChange={onChange}
      />
      <RecurrencePreview rule={buildRule()} />
    </div>
  );
};
```

## Real-time Updates

### WebSocket Integration
```typescript
const useCalendarRealtime = (calendarId: string) => {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  
  useEffect(() => {
    const ws = new WebSocket(`/api/calendar/${calendarId}/realtime`);
    
    ws.onmessage = (message) => {
      const update = JSON.parse(message.data);
      
      switch (update.type) {
        case 'EVENT_CREATED':
          setEvents(prev => [...prev, update.event]);
          break;
        case 'EVENT_UPDATED':
          setEvents(prev => prev.map(e => 
            e.id === update.event.id ? update.event : e
          ));
          break;
        case 'EVENT_DELETED':
          setEvents(prev => prev.filter(e => e.id !== update.eventId));
          break;
      }
    };
    
    return () => ws.close();
  }, [calendarId]);
  
  return events;
};
```

## Conflict Resolution UI

### Conflict Detection Display
```typescript
const ConflictResolver = ({ conflicts, newEvent }: Props) => {
  const [resolution, setResolution] = useState<'reschedule' | 'overlap' | 'cancel'>('reschedule');
  
  return (
    <div className="conflict-resolver">
      <ConflictList conflicts={conflicts} />
      <ResolutionOptions 
        value={resolution}
        onChange={setResolution}
        options={[
          { value: 'reschedule', label: 'Find alternative time' },
          { value: 'overlap', label: 'Allow overlap' },
          { value: 'cancel', label: 'Cancel new event' }
        ]}
      />
      {resolution === 'reschedule' && (
        <AlternativeTimesSuggestion 
          originalEvent={newEvent}
          conflicts={conflicts}
        />
      )}
    </div>
  );
};
```

## Performance Optimization

### Virtual Scrolling for Large Calendars
```typescript
const VirtualizedCalendar = ({ events, dateRange }: Props) => {
  const rowHeight = 60;
  const containerHeight = 600;
  
  const visibleEvents = useMemo(() => {
    return events.filter(event => 
      isEventInDateRange(event, dateRange)
    );
  }, [events, dateRange]);
  
  return (
    <FixedSizeList
      height={containerHeight}
      itemCount={visibleEvents.length}
      itemSize={rowHeight}
      itemData={visibleEvents}
    >
      {EventRow}
    </FixedSizeList>
  );
};
```

### Optimistic Updates
```typescript
const useOptimisticEvents = () => {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [optimisticEvents, setOptimisticEvents] = useState<CalendarEvent[]>([]);
  
  const addEventOptimistically = async (event: CalendarEvent) => {
    // Add to optimistic state immediately
    setOptimisticEvents(prev => [...prev, event]);
    
    try {
      const savedEvent = await api.createEvent(event);
      // Replace optimistic event with real event
      setEvents(prev => [...prev, savedEvent]);
      setOptimisticEvents(prev => prev.filter(e => e.id !== event.id));
    } catch (error) {
      // Remove optimistic event on failure
      setOptimisticEvents(prev => prev.filter(e => e.id !== event.id));
      throw error;
    }
  };
  
  return {
    allEvents: [...events, ...optimisticEvents],
    addEventOptimistically
  };
};
```

## Accessibility Features

### Keyboard Navigation
```typescript
const CalendarKeyboardHandler = ({ onDateChange, onEventSelect }: Props) => {
  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowLeft':
        onDateChange(addDays(currentDate, -1));
        break;
      case 'ArrowRight':
        onDateChange(addDays(currentDate, 1));
        break;
      case 'ArrowUp':
        onDateChange(addWeeks(currentDate, -1));
        break;
      case 'ArrowDown':
        onDateChange(addWeeks(currentDate, 1));
        break;
      case 'Enter':
      case ' ':
        onEventSelect(selectedEvent);
        break;
    }
  };
  
  return <div onKeyDown={handleKeyDown} tabIndex={0} />;
};
```