{"timestamp": "2025-07-16T12:30:15.688692", "workspace_root": "E:\\VIBECODE", "task_counts": {"total_tasks": 0, "active_file": "task-storage.md", "last_modified": "2025-07-16T02:16:59.248522"}, "system_health": {"system_available": true, "kg_manager_available": true, "native_integration_score": 100.0, "components_status": {"memory_bank": "available", "python_dir": "available", "python_files": {"found": ["knowledge_graph_manager.py", "operational_context_manager.py", "automatic_memory_updater.py", "cursor_memory_bridge.py"], "total": 4}, "tasks_file": "available"}}, "performance_metrics": {"recent_insights": 2, "recent_metrics": 3, "task_metrics": {"augment_config_valid": true, "cursor_config_valid": true, "primary_storage": true}}, "errors": []}