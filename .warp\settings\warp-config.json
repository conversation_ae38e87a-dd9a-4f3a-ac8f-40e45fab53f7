{"warp": {"version": "1.0.0", "description": "Warp CLI Configuration - VIBECODE Integration", "rules_path": ".warp", "auto_load_rules": true, "enforce_quality": true, "quality_threshold": 8}, "rules": {"enabled": true, "auto_apply": true, "sources": [".warp/master-rules.md", ".warp/coding-standards.md", ".warp/project-config.md"], "workflow": {"steps": 7, "mandatory": true, "auto_task_management": true, "complexity_threshold": 3}}, "mcps": {"enabled": true, "config_path": ".warp/mcps.json", "auto_select": true, "batch_operations": true, "api_optimization": true}, "quality": {"minimum_score": 8, "auto_refine": true, "max_iterations": 3, "enforce_completeness": true}, "research": {"auto_detect": true, "mandatory_protocol": true, "sources_required": 3, "synthesis_mandatory": true}, "file_operations": {"auto_tool_selection": true, "size_threshold": 200, "always_verify": true}, "coding": {"standards": "typescript-nextjs14", "enforce_interfaces": true, "guard_clauses": true, "early_returns": true, "strict_mode": true}, "performance": {"targets": {"rule_lookup": "100ms", "lcp": "2.5s", "fid": "100ms", "bundle_size": "200kb"}}, "security": {"input_validation": true, "sanitization": true, "authentication": true, "headers": true}}