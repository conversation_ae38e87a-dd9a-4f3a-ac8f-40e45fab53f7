# 📋 KIRO TASK MANAGEMENT - INTELLIGENT COORDINATION

## Task Complexity Routing

### Complexity Assessment (1-10 Scale)
- **1-3**: Simple, single-step operations
- **4-6**: Multi-step workflows with dependencies
- **7-8**: Complex coordination with multiple systems
- **9-10**: Advanced architecture requiring deep planning

### Tool Selection by Complexity

#### Simple Tasks (1-4)
- **Tools**: Native Kiro tools, built-in functions
- **Approach**: Direct execution
- **Coordination**: Minimal
- **Examples**: File operations, simple edits, basic queries

#### Medium Tasks (5-7)
- **Tools**: Native Kiro + Sequential-thinking MCP
- **Approach**: Structured workflow
- **Coordination**: Step-by-step planning
- **Examples**: Feature implementation, multi-file changes, research synthesis

#### Complex Tasks (8-10)
- **Tools**: Full MCP orchestration + manual coordination
- **Approach**: Comprehensive planning and execution
- **Coordination**: Multi-phase with checkpoints
- **Examples**: System architecture, complex integrations, full feature development

## Task Execution Principles

### One Task at a Time
- **Focus**: Single task completion before moving to next
- **Quality**: Ensure ≥8/10 quality before proceeding
- **User Review**: Stop after each task for user feedback
- **No Assumptions**: Don't automatically continue to next task

### Task Breakdown Strategy
- **Decomposition**: Break complex tasks into manageable subtasks
- **Dependencies**: Identify and sequence dependent tasks
- **Milestones**: Set clear completion criteria
- **Validation**: Define success metrics for each task

## Spec Task Management

### Task Creation from Specs
- **Requirements-based**: Each task references specific requirements
- **Incremental**: Build on previous tasks
- **Testable**: Include validation criteria
- **Complete**: No placeholder or TODO items

### Task Status Tracking
- **Not Started**: Initial state
- **In Progress**: Currently being worked on
- **Completed**: Finished and validated
- **Blocked**: Waiting for dependencies or user input

### Task Documentation
```markdown
- [ ] Task ID: Task Description
  - Implementation details
  - Success criteria
  - Dependencies
  - _Requirements: X.Y, Z.A_
```

## Coordination Strategies

### Native Kiro Integration
- **Built-in task lists**: Use Kiro's native task management
- **Workspace integration**: Sync with workspace tasks
- **Command palette**: Quick task access and creation
- **Auto-detection**: Identify task management needs automatically

### MCP Integration
- **Sequential-thinking**: For complex reasoning and planning
- **Context management**: Maintain task context across sessions
- **Progress tracking**: Monitor task completion status
- **Quality validation**: Ensure task output meets standards

## Task Automation

### Auto-Detection Triggers
- **Multi-step requests**: Automatically create task breakdown
- **Planning keywords**: "plan", "organize", "coordinate", "manage"
- **Complexity indicators**: Multiple files, systems, or dependencies
- **User workflow patterns**: Recognize recurring task patterns

### Batch Operations
- **Similar tasks**: Group and execute efficiently
- **File operations**: Batch file changes when possible
- **Validation cycles**: Batch quality checks
- **User reviews**: Present related tasks together

## Progress Monitoring

### Task Metrics
- **Completion rate**: Percentage of tasks completed successfully
- **Quality scores**: Average quality of task outputs
- **Time tracking**: Duration for different task types
- **User satisfaction**: Feedback on task completion

### Reporting
- **Progress summaries**: Regular status updates
- **Bottleneck identification**: Identify common blocking points
- **Performance trends**: Track improvement over time
- **Recommendation engine**: Suggest process improvements

## Integration with Main Workflow

### Workflow Integration Points
1. **Analyze** → Assess task complexity and management needs
2. **Select** → Choose appropriate task management tools
3. **Execute** → Apply task coordination strategies
4. **Reflect** → Evaluate task completion quality
5. **Refine** → Improve task management approach
6. **Validate** → Confirm task objectives met
7. **Learn** → Update task management patterns

---

**Key Principle**: One task at a time, quality ≥8/10, user review between tasks.