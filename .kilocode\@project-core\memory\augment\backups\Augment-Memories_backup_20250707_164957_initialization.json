# MCP Server Configuration

- Environment variables and API keys for MCP servers are stored in E:\VIBECODE\@project-core\configs\environment-complete.env
- Sentry API key for the project is *********************************************************************** and should be configured in environment-complete.env
- MCP servers should use Node.js installation at E:\NODEJS and require specific packages (@wonderwhy-er/desktop-commander, @modelcontextprotocol/server-sequential-thinking, mcp-shrimp-task-manager, @upstash/context7-mcp@latest, tavily-mcp@0.2.4, exa-mcp-server, mcp-remote@latest) with API keys (TAVILY_API_KEY, EXA_API_KEY, UPSTASH_CONTEXT7_API_KEY, SENTRY_ACCESS_TOKEN) configured in environment-complete.env.
- The project uses a master rule workflow system with MCP servers, agent routing mechanisms, and a 'boomerang agent' that requires specific integration with other agents and compliance validation steps.
- Desktop Commander MCP tool configuration fixed: uses npx @wonderwhy-er/desktop-commander@0.2.3, integrated into EXECUTOR agent tools, and properly configured for files ≤200 lines according to VIBECODE V1.0 master rule with fallback to cursor_editor for larger files.
- Use Desktop Commander MCP tool for Python script verification in @project-core: list_directory for mapping, read_file for examination, execute_command for testing, get_file_info for metadata, with focus on UV compatibility, syntax/import errors, dependencies, and environment variables from environment-complete.env.
- MCP shrimp task manager should store tasks in E:\VIBECODE\@project-core\tasks directory and system rules should be updated accordingly.
- User prefers to utilize all available MCP tools for plan execution, specifically including Context7 and Tavily for deep research tasks.

# Task Management

- User prefers to use Augment Agent's built-in task management functionality (add_tasks, update_tasks, view_tasklist, reorganize_tasklist) for organizing work in AI assistant conversations, not the external mcp-shrimp-task-manager MCP server.

# File Storage and Handling

- All temporary files, backups, tests, reports, cache, logs, and VIBECODE project analysis outputs in VIBECODE V1.0 should be stored in E:\CODE-BACKUP with appropriate subdirectories, never in the normal working directory.
- Test outputs, logs, and temporary files for VIBECODE project verification tasks should be stored in E:\CODE-BACKUP directory as per project standards.
- User prefers using temporary chunks pattern: create temporary chunks, read each chunk, then delete temporary files after processing.

# VIBECODE Project Structure, Dependencies, and Execution

- VIBECODE V1.0 requires primary activation script at @project-core/vibecode_main.py, Phase 0.5 mandatory scripts in config/automation/memory directories, master_rule.md synchronization with 4-agent architecture, automatic chat activation triggers, and UV compliance standards for all Python execution.
- VIBECODE project uses UV package manager instead of pip for Python dependencies and has a @project-core directory structure with configs/, learning/, monitoring/ subdirectories containing system_config.py, learning_engine.py, monitoring_system.py scripts.
- !finaltest is an alias for executing the validation framework at @project-core/automation/validation_framework.py for comprehensive system testing.
- User approved proceeding with the complete VIBECODE V1.0 automation directory optimization implementation plan (48% file reduction, consolidation strategy, and quality improvements).
- The project-core system is backend-focused and does not require dashboard functionality.
- VIBECODE V1.0 requires mandatory File Duplication Prevention Protocol: scan existing structure, enhance existing files instead of creating new ones, enforce limits (max 10 rules, 7 agents), use @project-core/scripts/ for all script implementations, and verify with codebase-retrieval before any file creation.
- User approved implementation of VIBECODE V1.0 agent architecture migration: 9→4 agents consolidation (TECHNICAL_ARCHITECT, OPERATIONS_COORDINATOR, RESEARCH_STRATEGIST, QUALITY_GUARDIAN) with 4-phase plan, preserving 100% functionality including PRD_Specialist, LangGraph, Pydantic V2 structures, targeting 55% complexity reduction and 30% performance improvement.
- VIBECODE V1.0 agent architecture should be optimized to maximum 4-5 agents with no functional loss, quality gate minimum 9/10, and must follow 3-phase approach: mapping/analysis, consolidation proposal, and validation with migration plan.
- VIBECODE V1.0 activation testing requires comprehensive validation including primary activation script status check, Phase 0.5 mandatory scripts execution (8 scripts across config/automation/memory/learning/monitoring directories), complete system activation validation, !finaltest framework execution, and automatic activation implementation in master_rule.md with environment detection, auto-execution sequence, and failsafe mechanisms.
- VIBECODE V1.0 project follows 'Enhance, Don't Proliferate' principle with ≥85% code reuse target, prefers @project-core/scripts/ directory, requires comprehensive deduplication analysis, and mandates scanning existing codebase before creating new files to enhance existing functionality instead.
- VIBECODE V1.0 migration workflow requires scanning existing codebase with codebase-retrieval before moving files, merging content into existing files rather than creating new ones, maintaining ≥85% code reuse target, and following 5-step process: analyze structure, identify destinations, check for enhancement opportunities, perform consolidation, update references, and verify integrity.
- VIBECODE V1.0 should integrate Cursor's official memory bank system from https://gist.github.com/ipenywis/1bdb541c3a612dbac4a14e1e3f4341ab while maintaining master_rule.md as central governance, following 'Enhance, Don't Proliferate' principle (≥85% code reuse), preserving 4-agent architecture, and ensuring quality gates ≥8/10.
- VIBECODE V1.0 uses @project-core/scripts/ as the standard and universal directory for all scripts, and this should be recognized by @project-core/agents/ and .cursor/rules/ directories instead of automation/ references.

# Repository Management

- User prefers using Augment's native GitHub API integration tools rather than local git commands for repository synchronization and GitHub operations.

# Prompt Engineering Guidelines

- User wants to create comprehensive prompt creation guidelines that integrate VIBECODE V1.0 master_rule.md principles with template models, covering compliance checkpoints, agent routing, MCP orchestration, quality gates (≥8/10), file operation protocols, and error handling for consistent prompt generation.
- User wants detailed instructions created for a Prompt Agent that serves as a creator prompt and quality gatekeeper with responsibilities including universal request handling, complexity analysis, agent routing, quality gatekeeping (≥8/10), memory integration, and MCP orchestration within VIBECODE V1.0 system.
- User refined the Prompt Creator Agent definition to focus specifically on prompt generation (not execution or routing) with mandatory VIBECODE V1.0 compliance elements, 7-step creation workflow, and specific output requirements including proper headers, compliance sections, and quality validation checkpoints.

# NeonPro Deployment Workflow

- NeonPro deployment workflow: run tests with coverage (70% requirement), test PWA installation and offline functionality, configure WhatsApp Business API integration with test functionality, deploy to Vercel with environment variables, and customize branding through settings and PWA manifest.

# Supabase Integration

- User prefers using Augment's direct Supabase connection rather than installing separate @supabase/ssr or other Supabase packages.
- Google OAuth Console requires top-level private domains for redirect URIs, preventing use of Supabase subdomain URLs like gfkskrkbnawkuppazkpt.supabase.co/auth/v1/callback.