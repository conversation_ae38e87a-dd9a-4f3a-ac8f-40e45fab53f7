---
applyTo: "*all*"
---

# 🚀 VIBECODE V1.0 - MASTER RULE

**VERSION**: 4.1.0 - MEMORY INTEGRATED
**LOCATION**: E:\VIBECODE\.cursor\rules\
**AUTHORITY**: Single Source of Truth

## 🧠 CORE SYSTEM

### **Hybrid Memory Architecture**

- **VIBECODE Core**: Central authority and quality gates
- **Cursor Memory Bank**: Persistent memory and context optimization
- **Integration Bridge**: `@project-core/memory/cursor_memory_bridge.py`
- **Knowledge Graph**: `@project-core/memory/knowledge_graph_manager.py`
- **Principle**: "Aprimore, Não Prolifere" (≥85% reuse)
- **Quality**: ≥8/10 mandatory, ≥90% target

### **Memory Bank Files**

```
memory-bank/
├── projectbrief.md          # Foundation document
├── activeContext.md         # Current focus & state
├── progress.md              # Implementation tracking
├── systemPatterns.md        # Architecture patterns
├── techContext.md           # Technical specifications
└── tasks.md                 # Source of truth for tasks
```

### **Phase 0.5: Mandatory Init**

```bash
# Execute before ANY task
npm run sync:ai
uv run python @project-core/memory/cursor_memory_bridge.py
uv run python @project-core/memory/knowledge_graph_manager.py --validate_connections
uv run python @project-core/scripts/vibecode_main.py --status
```

## 📋 DEVELOPMENT STANDARDS

### **Code Quality**

- **TypeScript**: Strict mode, full type coverage
- **Python**: Type hints, Pydantic validation, UV package manager
- **Testing**: ≥80% coverage
- **Docs**: Inline + external + memory bank updates

### **Best Practices**

- Early returns & guard clauses
- No TODOs or placeholders
- Comprehensive error handling
- Component-based architecture
- Memory persistence after each significant change## 🔧 TOOLING MATRIX

### **File Operations**

- **≤200 lines**: Desktop Commander MCP
- **>200 lines**: Cursor Editor
- **Verification**: Always read after write
- **Memory Sync**: Update memory-bank after changes

### **MCP Tiers**

- **TIER 0**: Sentry monitoring (all operations)
- **TIER 1**: Sequential-thinking (complexity ≥7)
- **TIER 2**: Shrimp-task-manager (complexity ≥3)
- **TIER 3**: Research (Context7→Tavily→Exa priority)
- **TIER 4**: Specialized tools (desktop-commander, etc.)

### **Universal Scripts Directory**

- **Standard Location**: `.cursor/scripts/` (UNIVERSAL DEFAULT)
- **All Scripts**: Python execution scripts MUST be in .cursor/scripts/
- **No Exceptions**: automation/ directory deprecated and removed
- **Path References**: Always use `.cursor/scripts/` in all configurations
- **Import Pattern**: `from scripts.module_name import function`

### **MCP Conformity Framework**

- **Validator**: `@project-core/scripts/validate_file_location.py --mcp`
- **Checklist**: `@project-core/memory/MCP_CHECKLIST_OPERATIONS.md`
- **Enforcement**: Automatic validation on system startup
- **Violations**: Tracked and reported in mcp_violations_report.json
- **Mandatory**: ALL file operations ≤200 lines MUST use Desktop Commander

### **Visual Process Maps**

```
.cursor/rules/visual-maps/
├── van-mode-map.mdc         # Initialization workflow
├── plan-mode-map.mdc        # Planning workflow
├── creative-mode-map.mdc    # Design workflow
├── implement-mode-map.mdc   # Implementation workflow
└── qa-mode-map.mdc          # Validation workflow
```

## 📁 STRUCTURE

### **Project Layout**

```
E:\VIBECODE\
├── .cursor/rules/           # All AI rules HERE
│   ├── master_rule.mdc      # This file (central authority)
│   ├── visual-maps/         # Process visualization
│   ├── phases/              # Phase-specific rules
│   └── levels/              # Complexity-based rules
├── memory-bank/             # Persistent memory system
├── @project-core/           # System core
│   ├── agents/              # Agent system & task classification
│   │   ├── task_classifier.py  # Task classification
│   │   └── validation/      # Agent validation reports
│   ├── memory/              # Memory management
│   └── scripts/             # Utility scripts & system activation (NEW SCRIPTS HERE)
├── @saas-projects/          # SaaS projects
├── src/                     # Source code
└── app/                     # Next.js app
```

### **Root Directory Rules**

- **Only essentials in root**: Config files only
- **Allowed files**: package.json, pyproject.toml, .gitignore, etc
- **All NEW scripts**: → .cursor/scripts/ (DEFAULT for new scripts)
- **Agent-specific**: → @project-core/agents/ (task classification, agent logic)
- **All docs**: → appropriate docs/ folders
- **Memory files**: → memory-bank/
- **Validation**: `uv run python @project-core/scripts/validate_root_directory.py`### **Rules Location**
- **ALL rules**: `.cursor/rules/*.mdc`
- **NO rules elsewhere**
- **Augment config**: Points here
- **Memory integration**: Automatic sync with memory-bank/

## 🎯 ENHANCED WORKFLOW

### **7-Step Process with Memory Integration**

1. **ANALYZE**: Complexity (1-10) + Update activeContext.md
2. **SELECT**: Choose agent + Load phase-specific rules
3. **EXECUTE**: Use MCP tools + Visual process maps
4. **REFLECT**: Quality check + Update progress.md
5. **REFINE**: If <8/10 + Document improvements
6. **VALIDATE**: Final check + Update tasks.md
7. **LEARN**: Update Knowledge Graph + Memory persistence

### **Consolidated Agent Matrix (4 Agents)**

- **TECHNICAL_ARCHITECT** (7-10): Architecture, design, coding
- **OPERATIONS_COORDINATOR** (1-6): Operations, management, execution
- **RESEARCH_STRATEGIST** (3-8): Research, documentation, analysis
- **QUALITY_GUARDIAN** (1-10): Quality control, reflection, improvement

### **Mode Integration**

- **VAN Mode**: Initialization & context setup (Complexity 1-4)
- **PLAN Mode**: Task planning & breakdown (Complexity 2-5)
- **CREATIVE Mode**: Design decisions & architecture (Complexity 3-8)
- **IMPLEMENT Mode**: Code implementation & testing (Complexity 4-9)
- **QA Mode**: Validation & quality assurance (Complexity 1-10)

## 🧠 MEMORY INTEGRATION PROTOCOLS

### **Context Optimization**

- **JIT Rule Loading**: Load only relevant rules per phase
- **60% Context Reduction**: Target through smart loading
- **Visual Maps**: Reduce cognitive load with process diagrams
- **Memory Persistence**: Maintain state across sessions

### **Cross-Session Continuity**

````python
# Memory bridge usage
from @project-core.memory.cursor_memory_bridge import CursorMemoryBridge

bridge = CursorMemoryBridge()
context = bridge.sync_context()
bridge.update_memory({"progress": new_progress_data})
rules = bridge.load_phase_rules("implement", complexity=7)
```## ⚡ QUICK REFERENCE

### **Commands**
```bash
# Memory integration
uv run python @project-core/memory/cursor_memory_bridge.py

# Sync rules (enhanced)
uv run python @project-core/scripts/sync_ai_rules.py

# Start system
uv run python @project-core/scripts/vibecode_main.py --status

# Validate structure
uv run python @project-core/scripts/validate_file_location.py

# Validate root directory
uv run python @project-core/scripts/validate_root_directory.py

# Test integration
uv run python @project-core/memory/cursor_memory_bridge.py
````

### **Quality Gates**

- **Confidence**: ≥8/10 (mandatory)
- **Performance**: <30s execution
- **Documentation**: 100% coverage
- **Compliance**: 100% VIBECODE standards
- **Memory Sync**: Automatic after each task
- **Context Optimization**: 60% reduction target

### **Integration Health Check**

```python
# Quick validation
bridge = CursorMemoryBridge()
status = bridge.validate_integration()
print(f"Status: {status['integration_status']}")
```

## 🎯 SUCCESS METRICS

### **Performance Targets**

- **Context Reduction**: 60% through JIT loading
- **Execution Speed**: 30% improvement
- **Memory Efficiency**: Cross-session persistence
- **Quality Maintenance**: ≥8/10 throughout
- **Code Reuse**: ≥85% (Enhance, Don't Proliferate)

### **Integration Validation**

- **Memory Bank**: Complete file structure
- **Visual Maps**: Process clarity
- **Bridge Functionality**: Sync and optimization
- **Rule Loading**: Phase-specific efficiency
- **Cross-Session**: State continuity

---

**"One Rule, Zero Redundancy, Perfect Memory"** - VIBECODE V1.0 Enhanced
Coding standards, domain knowledge, and preferences that AI should follow.
