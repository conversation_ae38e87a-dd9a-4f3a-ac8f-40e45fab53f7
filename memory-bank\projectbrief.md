# VIBECODE V1.0 - Project Brief

## 🎯 PROJECT OVERVIEW

**Project Name**: VIBECODE V1.0 - Advanced AI Development Framework
**Version**: 4.0.0 Consolidated
**Location**: E:\VIBECODE
**Authority**: Single Source of Truth System

## 🚀 CORE MISSION

VIBECODE V1.0 é um framework avançado de desenvolvimento orientado por IA que integra:
- Sistema de agentes consolidados (4 agentes especializados)
- Orquestração MCP multi-tier (Tiers 0-4)
- Knowledge Graph Manager para aprendizado contínuo
- Quality gates obrigatórios (≥8/10)
- Integração com Cursor Memory Bank para persistência

## 🎯 OBJECTIVES

### **Primary Goals**
1. **Consolidação de Agentes**: Redução de 9→4 agentes (55% reduction)
2. **Quality Assurance**: Manter padrão ≥8/10 em todas as saídas
3. **Memory Integration**: Sistema híbrido VIBECODE + Cursor Memory Bank
4. **Performance**: 30% melhoria na velocidade de execução
5. **Context Optimization**: 60% redução no uso de contexto

### **Secondary Goals**
- Implementar visual process maps
- Criar JIT rule loading system
- Estabelecer phase-specific workflows
- Manter 100% compliance com UV package manager
- Preservar princípio "Aprimore, Não Prolifere" (≥85% reuso)

## 🏗️ ARCHITECTURE PRINCIPLES

### **Core Values**
- **SIMPLE**: Soluções priorizem simplicidade sobre complexidade
- **DIGITAL**: Workflows totalmente automatizados onde possível
- **AGILE**: Arquitetura suporte desenvolvimento incremental

### **Technical Constraints**
- Python: UV package manager obrigatório
- TypeScript: Strict mode, full type coverage
- Quality: ≥8/10 mandatory, ≥90% target
- Files: ≤200 lines → Desktop Commander, >200 → Cursor Editor
- Location: Todas as regras em `.cursor/rules/*.mdc`

## 🤖 AGENT ARCHITECTURE

### **Consolidated 4-Agent System**
1. **TECHNICAL_ARCHITECT** (Complexity 7-10)
   - Consolidates: architect, coder, ruler
   - Capabilities: architecture, design, coding, implementation

2. **OPERATIONS_COORDINATOR** (Complexity 1-6)
   - Consolidates: manager, executor, prd_specialist
   - Capabilities: operations, coordination, management

3. **RESEARCH_STRATEGIST** (Complexity 3-8)
   - Enhanced from: search_strategist
   - Capabilities: research, documentation, analysis

4. **QUALITY_GUARDIAN** (Complexity 1-10)
   - Consolidates: boomerang, self_reflection, prompt_creator
   - Capabilities: quality_control, system_reflection

## 🔧 MCP ORCHESTRATION

### **Tier System**
- **TIER 0**: Sentry (monitoring - all operations)
- **TIER 1**: Sequential-thinking (complexity ≥7)
- **TIER 2**: Native-task-management (complexity ≥3)
- **TIER 3**: Research (Context7→Tavily→Exa priority)
- **TIER 4**: Specialized (desktop-commander, figma, playwright)

## 📋 WORKFLOW STANDARDS

### **7-Step Mandatory Process**
1. **ANALYZE**: Complexity assessment (1-10)
2. **SELECT**: Agent routing by complexity
3. **EXECUTE**: MCP tools by tier system
4. **REFLECT**: Quality evaluation
5. **REFINE**: Improvement if <8/10
6. **VALIDATE**: Final quality confirmation
7. **LEARN**: Knowledge Graph update

### **Phase 0.5: Mandatory Init**
```bash
uv run python @project-core/scripts/sync_ai_rules.py
uv run python @project-core/scripts/validate_knowledge_graph.py
uv run python @project-core/scripts/vibecode_main.py --status
```

## 🎯 SUCCESS CRITERIA

### **Quality Gates**
- **Output Quality**: ≥8/10 (mandatory)
- **Code Reuse**: ≥85% (Enhance, Don't Proliferate)
- **Performance**: <30s execution time
- **Compliance**: 100% VIBECODE standards
- **Documentation**: 100% coverage

### **Integration Targets**
- **Context Reduction**: 60% through JIT loading
- **Memory Persistence**: 100% between sessions
- **Visual Workflows**: Complete process maps
- **Phase Specialization**: Targeted rule sets
- **Cross-Session Learning**: Knowledge accumulation

## 🚧 CURRENT STATUS

**Phase**: Integration Implementation
**Completion**: 60% (Analysis and Strategy Complete)
**Next Steps**: Memory Bank infrastructure creation
**Quality Score**: 9/10
**Compliance**: 100% VIBECODE V1.0

---
**"One Rule, Zero Redundancy"** - VIBECODE V1.0 Master Principle