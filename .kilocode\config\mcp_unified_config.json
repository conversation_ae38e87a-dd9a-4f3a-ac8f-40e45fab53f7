{
  "mcp_integration": {
    "version": "1.0.0",
    "last_updated": "2025-01-07",
    "hierarchy": {
      "tier_1_advanced_reasoning": {
        "description": "Advanced reasoning and complex problem-solving tools",
        "priority": 1,
        "tools": [
          {
            "server_name": "sequential-thinking",
            "tool_name": "sequentialthinking",
            "complexity_threshold": 7,
            "agent_affinity": ["TechnicalArchitect", "QualityGuardian"],
            "keywords": ["complex", "analysis", "architecture", "design", "planning"],
            "usage_patterns": {
              "max_concurrent": 2,
              "timeout_seconds": 120,
              "retry_attempts": 3
            }
          },
          {
            "server_name": "mcp-shrimp-task-manager",
            "tool_name": "analyze_task",
            "complexity_threshold": 6,
            "agent_affinity": ["TechnicalArchitect", "OperationsCoordinator"],
            "keywords": ["task", "analyze", "breakdown", "requirements"],
            "usage_patterns": {
              "max_concurrent": 3,
              "timeout_seconds": 90,
              "retry_attempts": 2
            }
          }
        ]
      },
      "tier_2_coordination": {
        "description": "Coordination and orchestration tools",
        "priority": 2,
        "tools": [
          {
            "server_name": "mcp-shrimp-task-manager",
            "tool_name": "plan_task",
            "complexity_threshold": 5,
            "agent_affinity": ["OperationsCoordinator", "TechnicalArchitect"],
            "keywords": ["plan", "coordinate", "organize", "schedule"],
            "usage_patterns": {
              "max_concurrent": 4,
              "timeout_seconds": 60,
              "retry_attempts": 2
            }
          },
          {
            "server_name": "mcp-shrimp-task-manager",
            "tool_name": "execute_task",
            "complexity_threshold": 4,
            "agent_affinity": ["OperationsCoordinator", "QualityGuardian"],
            "keywords": ["execute", "implement", "deploy", "run"],
            "usage_patterns": {
              "max_concurrent": 5,
              "timeout_seconds": 180,
              "retry_attempts": 3
            }
          },
          {
            "server_name": "desktop-commander",
            "tool_name": "execute_command",
            "complexity_threshold": 3,
            "agent_affinity": ["OperationsCoordinator"],
            "keywords": ["command", "terminal", "system", "execution"],
            "usage_patterns": {
              "max_concurrent": 3,
              "timeout_seconds": 300,
              "retry_attempts": 2
            }
          }
        ]
      },
      "tier_3_research": {
        "description": "Research and information gathering tools",
        "priority": 3,
        "tools": [
          {
            "server_name": "tavily",
            "tool_name": "tavily-search",
            "complexity_threshold": 2,
