import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@/app/utils/supabase/server';
import { sendInvoiceEmail } from '@/lib/payments/email';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
});

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

/**
 * Webhook handler para eventos do Stripe
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe-signature header' }, 
        { status: 400 }
      );
    }

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' }, 
        { status: 400 }
      );
    }

    // Processar eventos do Stripe
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' }, 
      { status: 500 }
    );
  }
}

/**
 * Processa pagamento bem-sucedido
 */
async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    const supabase = createClient();
    
    // Buscar fatura relacionada ao payment_intent
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('stripe_payment_intent_id', paymentIntent.id)
      .single();

    if (invoiceError || !invoice) {
      console.error('Invoice not found for payment intent:', paymentIntent.id);
      return;
    }

    // Atualizar status da fatura
    const { error: updateError } = await supabase
      .from('invoices')
      .update({
        status: 'paid',
        paid_at: new Date().toISOString(),
        stripe_payment_status: 'succeeded',
        updated_at: new Date().toISOString()
      })
      .eq('id', invoice.id);

    if (updateError) {
      throw updateError;
    }

    // Buscar dados do usuário
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', invoice.user_id)
      .single();

    if (!profileError && profile) {
      // Enviar email de confirmação
      await sendInvoiceEmail({
        to: profile.email,
        userName: profile.full_name || profile.email,
        invoiceNumber: invoice.invoice_number,
        amount: invoice.total,
        status: 'paid',
        dueDate: invoice.due_date,
        pdfBuffer: null // PDF será gerado se necessário
      });
    }

    console.log(`Payment succeeded for invoice ${invoice.id}`);
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

/**
 * Processa falha de pagamento
 */
async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    const supabase = createClient();
    
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('stripe_payment_intent_id', paymentIntent.id)
      .single();

    if (invoiceError || !invoice) {
      console.error('Invoice not found for payment intent:', paymentIntent.id);
      return;
    }

    // Atualizar status da fatura
    const { error: updateError } = await supabase
      .from('invoices')
      .update({
        status: 'overdue',
        stripe_payment_status: 'failed',
        updated_at: new Date().toISOString()
      })
      .eq('id', invoice.id);

    if (updateError) {
      throw updateError;
    }

    console.log(`Payment failed for invoice ${invoice.id}`);
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

/**
 * Processa pagamento de fatura bem-sucedido
 */
async function handleInvoicePaymentSucceeded(stripeInvoice: Stripe.Invoice) {
  try {
    const supabase = createClient();
    
    // Lógica para faturas recorrentes/assinaturas
    console.log(`Invoice payment succeeded: ${stripeInvoice.id}`);
    
    // Implementar lógica específica para assinaturas aqui
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

/**
 * Processa atualização de assinatura
 */
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    const supabase = createClient();
    
    // Implementar lógica de atualização de assinatura
    console.log(`Subscription updated: ${subscription.id}`);
    
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

/**
 * Processa cancelamento de assinatura
 */
async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    const supabase = createClient();
    
    // Implementar lógica de cancelamento
    console.log(`Subscription deleted: ${subscription.id}`);
    
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}