{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"]}, "dev": {"persistent": true, "cache": false, "dependsOn": ["^build"]}, "lint": {"dependsOn": ["^build"], "inputs": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"]}, "type-check": {"dependsOn": ["^build"], "inputs": ["**/*.ts", "**/*.tsx"], "outputs": ["*.tsbuildinfo"]}, "test": {"dependsOn": ["build"], "inputs": ["src/**", "tests/**", "**/*.test.*", "**/*.spec.*"]}, "test:e2e": {"dependsOn": ["build"], "cache": false}, "clean": {"cache": false}}, "globalEnv": ["NODE_ENV", "VERCEL", "VERCEL_ENV", "VERCEL_URL", "ANALYZE"]}