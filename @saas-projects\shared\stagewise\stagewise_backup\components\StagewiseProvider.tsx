/**
 * 🎯 Stagewise Provider Component
 * VIBECODE V1.0 - Centralized Stagewise Integration for Next.js Projects
 * 
 * Reusable React component that provides Stagewise toolbar functionality
 * across all Next.js projects with automatic environment detection.
 */

'use client';

import React, { useEffect, useState } from 'react';
import { StagewiseToolbar } from '@stagewise/toolbar-next';
import { getStagewiseConfig, validateStagewiseConfig } from '../config/stagewise.config';
import { isStagewiseEnabled } from '../config/environment.config';
import { runFullValidation, logValidationResults } from '../utils/validation';

export interface StagewiseProviderProps {
  /**
   * Project name for project-specific configuration
   */
  projectName?: string;
  
  /**
   * Override default configuration
   */
  configOverride?: Partial<any>;
  
  /**
   * Enable debug logging
   */
  debug?: boolean;
  
  /**
   * Custom error boundary
   */
  onError?: (error: Error) => void;
}

/**
 * Centralized Stagewise Provider Component
 * 
 * Features:
 * - Automatic environment detection
 * - Project-specific configuration
 * - Error boundary protection
 * - Performance optimization
 * - SSR compatibility
 */
export function StagewiseProvider({
  projectName,
  configOverride,
  debug = false,
  onError
}: StagewiseProviderProps) {
  const [isEnabled, setIsEnabled] = useState(false);
  const [config, setConfig] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Check if Stagewise should be enabled
      const enabled = isStagewiseEnabled();
      setIsEnabled(enabled);

      if (!enabled) {
        if (debug) {
          console.log('🎯 Stagewise disabled (not in development mode)');
        }
        return;
      }

      // Get configuration
      const stagewiseConfig = getStagewiseConfig(projectName);
      
      // Apply overrides
      const finalConfig = configOverride 
        ? { ...stagewiseConfig, ...configOverride }
        : stagewiseConfig;

      // Validate configuration
      const isValidConfig = validateStagewiseConfig(finalConfig);
      if (!isValidConfig) {
        throw new Error('Invalid Stagewise configuration');
      }

      // Run full validation if debug mode
      if (debug) {
        const validation = runFullValidation(finalConfig);
        logValidationResults(validation, projectName);
      }

      setConfig(finalConfig);
      setError(null);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
      
      if (debug) {
        console.error('❌ Stagewise initialization failed:', errorMessage);
      }
    }
  }, [projectName, configOverride, debug, onError]);

  // Don't render anything if disabled or error
  if (!isEnabled || error || !config) {
    return null;
  }

  // Error boundary wrapper
  return (
    <StagewiseErrorBoundary onError={onError} debug={debug}>
      <StagewiseToolbar config={config} />
    </StagewiseErrorBoundary>
  );
}

/**
 * Error Boundary for Stagewise Component
 */
interface StagewiseErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error) => void;
  debug?: boolean;
}

class StagewiseErrorBoundary extends React.Component<
  StagewiseErrorBoundaryProps,
  { hasError: boolean; error?: Error }
> {
  constructor(props: StagewiseErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (this.props.debug) {
      console.error('❌ Stagewise Error Boundary caught error:', error, errorInfo);
    }
    
    if (this.props.onError) {
      this.props.onError(error);
    }
  }

  render() {
    if (this.state.hasError) {
      // Fail silently in production, log in development
      if (this.props.debug) {
        console.warn('⚠️ Stagewise failed to render, continuing without toolbar');
      }
      return null;
    }

    return this.props.children;
  }
}

/**
 * Hook for accessing Stagewise status
 */
export function useStagewise() {
  const [isEnabled, setIsEnabled] = useState(false);
  
  useEffect(() => {
    setIsEnabled(isStagewiseEnabled());
  }, []);
  
  return {
    isEnabled,
    isProduction: process.env.NODE_ENV === 'production',
    isDevelopment: process.env.NODE_ENV === 'development'
  };
}

export default StagewiseProvider;
