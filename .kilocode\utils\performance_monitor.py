import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class PerformanceMetric:
    """
    Represents a performance metric measurement.
    """
    name: str
    value: float
    timestamp: datetime
    target: Optional[float] = None
    unit: str = "ms"

class KiloQualityGates:
    """
    Implements quality gates and performance monitoring for the Kilo Code system.
    """

    def __init__(self):
        """
        Initializes the quality gates system with default thresholds.
        """
        self.performance_targets = {
            "startup_time_ms": 200,
            "config_access_time_ms": 10,
            "agent_routing_time_ms": 50,
            "mcp_activation_time_ms": 100,
            "quality_threshold": 8.0
        }
        self.metrics_history: List[PerformanceMetric] = []
        self.quality_scores: List[Dict[str, Any]] = []

    def enforce_quality_threshold(self, response: Dict[str, Any], threshold: float = 8.0) -> Dict[str, Any]:
        """
        Enforces quality gates on agent responses.

        Args:
            response (Dict[str, Any]): The agent response to evaluate
            threshold (float): Quality threshold (default 8.0)

        Returns:
            Dict[str, Any]: Enhanced response with quality enforcement
        """
        quality_score = response.get("quality_score", 0.0)

        if quality_score < threshold:
            # Trigger quality enhancement
            enhanced_response = self._enhance_response(response, quality_score, threshold)
            return enhanced_response

        # Record successful quality gate pass
        self.quality_scores.append({
            "score": quality_score,
            "threshold": threshold,
            "passed": True,
            "timestamp": datetime.now()
        })

        return response

    def _enhance_response(self, response: Dict[str, Any], current_score: float, target_score: float) -> Dict[str, Any]:
        """
        Enhances a response that failed quality gates.

        Args:
            response (Dict[str, Any]): Original response
            current_score (float): Current quality score
            target_score (float): Target quality score

        Returns:
            Dict[str, Any]: Enhanced response
        """
        enhanced_response = response.copy()

        # Simple enhancement strategies
        enhancements = []

        if current_score < 5.0:
            enhancements.append("detailed_explanation")
            enhancements.append("example_usage")
        elif current_score < 7.0:
            enhancements.append("clarification")
            enhancements.append("additional_context")
        else:
            enhancements.append("minor_improvements")

        enhanced_response.update({
            "original_quality_score": current_score,
            "enhancement_applied": True,
            "enhancements": enhancements,
            "enhanced_quality_score": min(current_score + 1.5, 10.0),  # Simulated improvement
            "quality_gate_triggered": True
        })

        # Record quality gate trigger
        self.quality_scores.append({
            "original_score": current_score,
            "enhanced_score": enhanced_response["enhanced_quality_score"],
            "threshold": target_score,
            "passed": False,
            "enhanced": True,
            "timestamp": datetime.now()
        })

        return enhanced_response

    def trigger_fallback(self, failed_response: Dict[str, Any], fallback_agent: str = None) -> Dict[str, Any]:
        """
        Triggers fallback mechanisms when quality gates fail severely.

        Args:
            failed_response (Dict[str, Any]): The failed response
            fallback_agent (str): Optional fallback agent name

        Returns:
            Dict[str, Any]: Fallback response
        """
        fallback_response = {
            "fallback_triggered": True,
            "original_failure": failed_response,
            "fallback_agent": fallback_agent or "QualityGuardian",
            "fallback_reason": "Quality threshold severely missed",
            "estimated_quality_score": 7.5,  # Conservative fallback score
            "timestamp": datetime.now()
        }

        return fallback_response

    def measure_performance(self, operation_name: str, start_time: float) -> PerformanceMetric:
        """
        Measures and records performance of an operation.

        Args:
            operation_name (str): Name of the operation
            start_time (float): Start time from time.time()

        Returns:
            PerformanceMetric: The recorded metric
        """
        end_time = time.time()
        duration_ms = (end_time - start_time) * 1000

        target = self.performance_targets.get(f"{operation_name}_time_ms")

        metric = PerformanceMetric(
            name=operation_name,
            value=duration_ms,
            timestamp=datetime.now(),
            target=target,
            unit="ms"
        )

        self.metrics_history.append(metric)

        # Keep only last 1000 metrics to prevent memory bloat
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]

        return metric

    def optimize_performance(self, metrics: List[PerformanceMetric]) -> Dict[str, Any]:
        """
        Analyzes performance metrics and suggests optimizations.

        Args:
            metrics (List[PerformanceMetric]): List of performance metrics

        Returns:
            Dict[str, Any]: Optimization recommendations
        """
        optimizations = {
            "recommendations": [],
            "performance_summary": {},
            "concerning_metrics": []
        }

        # Group metrics by operation
        operation_metrics = {}
        for metric in metrics:
            if metric.name not in operation_metrics:
                operation_metrics[metric.name] = []
            operation_metrics[metric.name].append(metric)

        # Analyze each operation
        for operation, op_metrics in operation_metrics.items():
            if len(op_metrics) < 2:
                continue

            avg_time = sum(m.value for m in op_metrics) / len(op_metrics)
            max_time = max(m.value for m in op_metrics)
            target_time = op_metrics[0].target

            optimizations["performance_summary"][operation] = {
                "average_ms": round(avg_time, 2),
                "max_ms": round(max_time, 2),
                "target_ms": target_time,
                "sample_count": len(op_metrics)
            }

            if target_time and avg_time > target_time:
                optimizations["concerning_metrics"].append({
                    "operation": operation,
                    "average_ms": round(avg_time, 2),
                    "target_ms": target_time,
                    "performance_ratio": round(avg_time / target_time, 2)
                })

                if avg_time > target_time * 2:
                    optimizations["recommendations"].append(f"URGENT: {operation} is {avg_time/target_time:.1f}x slower than target")
                elif avg_time > target_time * 1.5:
                    optimizations["recommendations"].append(f"WARNING: {operation} is {avg_time/target_time:.1f}x slower than target")

        return optimizations

    def get_system_health(self) -> Dict[str, Any]:
        """
        Provides an overall system health report.

        Returns:
            Dict[str, Any]: System health report
        """
        recent_metrics = [m for m in self.metrics_history if m.timestamp > datetime.now() - timedelta(minutes=30)]
        recent_quality = [q for q in self.quality_scores if q["timestamp"] > datetime.now() - timedelta(minutes=30)]

        health_report = {
            "timestamp": datetime.now(),
            "metrics_count": len(recent_metrics),
            "quality_checks_count": len(recent_quality),
            "quality_pass_rate": 0.0,
            "performance_status": "unknown",
            "system_status": "healthy"
        }

        # Calculate quality pass rate
        if recent_quality:
            passed_count = sum(1 for q in recent_quality if q.get("passed", False))
            health_report["quality_pass_rate"] = passed_count / len(recent_quality)

        # Determine performance status
        if recent_metrics:
            over_target_count = sum(1 for m in recent_metrics if m.target and m.value > m.target)
            if over_target_count / len(recent_metrics) > 0.5:
                health_report["performance_status"] = "degraded"
                health_report["system_status"] = "warning"
            else:
                health_report["performance_status"] = "good"

        # Overall system status
        if health_report["quality_pass_rate"] < 0.8 or health_report["performance_status"] == "degraded":
            health_report["system_status"] = "needs_attention"

        return health_report

if __name__ == '__main__':
    # Example Usage
    quality_gates = KiloQualityGates()

    # Test quality enforcement
    low_quality_response = {"content": "Basic response", "quality_score": 6.5}
    enhanced = quality_gates.enforce_quality_threshold(low_quality_response)
    print("Enhanced response:", enhanced)

    # Test performance measurement
    start_time = time.time()
    time.sleep(0.05)  # Simulate 50ms operation
    metric = quality_gates.measure_performance("agent_routing", start_time)
    print(f"\nPerformance metric: {metric.name} = {metric.value:.2f}ms (target: {metric.target}ms)")

    # Get system health
    health = quality_gates.get_system_health()
    print(f"\nSystem health: {health}")
