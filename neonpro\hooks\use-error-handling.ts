import { useCallback, useRef, useState } from "react";

// =============================================
// NeonPro Error Messaging System
// Story 1.2: Comprehensive error handling
// =============================================

interface ErrorMessage {
  id: string;
  type: "error" | "warning" | "info" | "success";
  title: string;
  message: string;
  details?: string;
  actionable?: boolean;
  actions?: ErrorAction[];
  timestamp: Date;
  context?: ErrorContext;
  dismissible?: boolean;
  autoHide?: boolean;
  duration?: number;
}

interface ErrorAction {
  label: string;
  action: () => void | Promise<void>;
  variant?: "primary" | "secondary" | "destructive";
}

interface ErrorMetadata {
  [key: string]: string | number | boolean | Date | null | undefined;
}

interface ErrorContext {
  component?: string;
  operation?: string;
  userId?: string;
  clinicId?: string;
  appointmentId?: string;
  metadata?: ErrorMetadata;
}

interface ErrorHandlingConfig {
  showTechnicalDetails: boolean;
  enableProgressiveDisclosure: boolean;
  maxRetryAttempts: number;
  retryDelay: number;
  enableContextualHelp: boolean;
  lgpdCompliant: boolean;
  locale: "pt-BR" | "en-US";
}

const DEFAULT_CONFIG: ErrorHandlingConfig = {
  showTechnicalDetails: false,
  enableProgressiveDisclosure: true,
  maxRetryAttempts: 3,
  retryDelay: 1000,
  enableContextualHelp: true,
  lgpdCompliant: true,
  locale: "pt-BR",
};

// Portuguese error messages for common scenarios
const ERROR_MESSAGES_PT = {
  network: {
    title: "Problema de Conexão",
    message:
      "Não foi possível conectar ao servidor. Verifique sua conexão com a internet.",
    details: "Erro de rede detectado. Tente novamente em alguns instantes.",
  },
  unauthorized: {
    title: "Acesso Negado",
    message: "Você não tem permissão para realizar esta ação.",
    details:
      "Entre em contato com o administrador da clínica para solicitar as permissões necessárias.",
  },
  validation: {
    title: "Dados Inválidos",
    message: "Alguns campos contêm informações inválidas.",
    details:
      "Revise os campos destacados e corrija as informações antes de continuar.",
  },
  conflict: {
    title: "Conflito de Agendamento",
    message: "Este horário possui conflitos que impedem o agendamento.",
    details: "Escolha outro horário ou solicite autorização de um gestor.",
  },
  capacity: {
    title: "Capacidade Esgotada",
    message: "Não há vagas disponíveis neste horário.",
    details: "Selecione outro horário ou aguarde a liberação de uma vaga.",
  },
  server: {
    title: "Erro do Servidor",
    message: "Ocorreu um problema interno no sistema.",
    details: "Nossa equipe foi notificada. Tente novamente em alguns minutos.",
  },
  timeout: {
    title: "Tempo Esgotado",
    message: "A operação demorou mais que o esperado.",
    details: "O sistema pode estar temporariamente lento. Tente novamente.",
  },
  notFound: {
    title: "Não Encontrado",
    message: "O recurso solicitado não foi encontrado.",
    details: "Verifique se os dados estão corretos ou se o item ainda existe.",
  },
  lgpd: {
    title: "Privacidade de Dados",
    message: "Esta ação envolve dados pessoais sensíveis.",
    details:
      "O processamento seguirá as diretrizes da LGPD para proteção de dados.",
  },
};

export const useErrorHandling = (config: Partial<ErrorHandlingConfig> = {}) => {
  const [messages, setMessages] = useState<ErrorMessage[]>([]);
  const [isRetrying, setIsRetrying] = useState(false);
  const retryCountRef = useRef<Record<string, number>>({});

  const mergedConfig = { ...DEFAULT_CONFIG, ...config };

  const generateId = useCallback(() => {
    return `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const createMessage = useCallback(
    (
      type: ErrorMessage["type"],
      titleKey: keyof typeof ERROR_MESSAGES_PT | string,
      customMessage?: string,
      context?: ErrorContext,
      options: {
        dismissible?: boolean;
        autoHide?: boolean;
        duration?: number;
        actions?: ErrorAction[];
        details?: string;
      } = {}
    ): ErrorMessage => {
      const id = generateId();
      const timestamp = new Date();

      // Get predefined message or use custom
      const predefined =
        typeof titleKey === "string" && titleKey in ERROR_MESSAGES_PT
          ? ERROR_MESSAGES_PT[titleKey as keyof typeof ERROR_MESSAGES_PT]
          : null;

      return {
        id,
        type,
        title: predefined?.title || titleKey,
        message: customMessage || predefined?.message || titleKey,
        details: options.details || predefined?.details,
        timestamp,
        context,
        dismissible: options.dismissible ?? true,
        autoHide: options.autoHide ?? (type === "success" || type === "info"),
        duration:
          options.duration ??
          (type === "success" ? 3000 : type === "info" ? 5000 : undefined),
        actionable: Boolean(options.actions?.length),
        actions: options.actions,
      };
    },
    [generateId]
  );

  const addMessage = useCallback((message: ErrorMessage) => {
    setMessages((prev) => [...prev, message]);

    // Auto-hide if configured
    if (message.autoHide && message.duration) {
      setTimeout(() => {
        removeMessage(message.id);
      }, message.duration);
    }
  }, []);

  const removeMessage = useCallback((id: string) => {
    setMessages((prev) => prev.filter((msg) => msg.id !== id));
  }, []);

  const clearAllMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const showError = useCallback(
    (
      error: Error | string,
      context?: ErrorContext,
      customActions?: ErrorAction[]
    ) => {
      const errorMessage = typeof error === "string" ? error : error.message;
      const errorType = getErrorType(errorMessage);

      const actions: ErrorAction[] = [...(customActions || [])];

      // Add retry action for retryable errors
      if (isRetryableError(errorType) && context?.operation) {
        const retryKey = `${context.operation}-${
          context.appointmentId || "default"
        }`;
        const retryCount = retryCountRef.current[retryKey] || 0;

        if (retryCount < mergedConfig.maxRetryAttempts) {
          actions.push({
            label: "Tentar Novamente",
            action: async () => {
              await handleRetry(retryKey, context.operation!);
            },
            variant: "primary",
          });
        }
      }

      // Add contextual help action
      if (mergedConfig.enableContextualHelp && context?.component) {
        actions.push({
          label: "Ajuda",
          action: () => showContextualHelp(context.component!),
          variant: "secondary",
        });
      }

      const message = createMessage("error", errorType, undefined, context, {
        actions,
        details: mergedConfig.showTechnicalDetails ? errorMessage : undefined,
      });

      addMessage(message);
    },
    [createMessage, addMessage, mergedConfig]
  );

  const showWarning = useCallback(
    (
      title: string,
      message: string,
      context?: ErrorContext,
      actions?: ErrorAction[]
    ) => {
      const warningMessage = createMessage("warning", title, message, context, {
        actions,
      });
      addMessage(warningMessage);
    },
    [createMessage, addMessage]
  );

  const showInfo = useCallback(
    (title: string, message: string, context?: ErrorContext) => {
      const infoMessage = createMessage("info", title, message, context, {
        autoHide: true,
        duration: 5000,
      });
      addMessage(infoMessage);
    },
    [createMessage, addMessage]
  );

  const showSuccess = useCallback(
    (title: string, message: string, context?: ErrorContext) => {
      const successMessage = createMessage("success", title, message, context, {
        autoHide: true,
        duration: 3000,
      });
      addMessage(successMessage);
    },
    [createMessage, addMessage]
  );

  const handleRetry = useCallback(
    async (retryKey: string, operation: string) => {
      const currentCount = retryCountRef.current[retryKey] || 0;
      retryCountRef.current[retryKey] = currentCount + 1;

      setIsRetrying(true);

      try {
        // Add delay before retry
        await new Promise((resolve) =>
          setTimeout(resolve, mergedConfig.retryDelay)
        );

        // The actual retry logic should be implemented by the consuming component
        // This is just the framework for handling retries
      } finally {
        setIsRetrying(false);
      }
    },
    [mergedConfig.retryDelay]
  );

  const showContextualHelp = useCallback(
    (component: string) => {
      const helpContent = getContextualHelp(component);
      if (helpContent) {
        showInfo("Ajuda", helpContent);
      }
    },
    [showInfo]
  );

  const handleLGPDCompliantError = useCallback(
    (
      operation: string,
      dataType: "personal" | "sensitive" | "medical",
      context?: ErrorContext
    ) => {
      if (!mergedConfig.lgpdCompliant) {
        return;
      }

      const lgpdMessage = createMessage("info", "lgpd", undefined, context, {
        dismissible: true,
        actions: [
          {
            label: "Entendi",
            action: () => {},
            variant: "primary",
          },
          {
            label: "Política de Privacidade",
            action: () => {
              window.open("/privacy-policy", "_blank");
            },
            variant: "secondary",
          },
        ],
      });

      addMessage(lgpdMessage);
    },
    [createMessage, addMessage, mergedConfig.lgpdCompliant]
  );

  return {
    messages,
    showError,
    showWarning,
    showInfo,
    showSuccess,
    removeMessage,
    clearAllMessages,
    isRetrying,
    handleLGPDCompliantError,
    config: mergedConfig,
  };
};

// Helper functions
function getErrorType(errorMessage: string): keyof typeof ERROR_MESSAGES_PT {
  const message = errorMessage.toLowerCase();

  if (message.includes("network") || message.includes("fetch"))
    return "network";
  if (message.includes("unauthorized") || message.includes("401"))
    return "unauthorized";
  if (message.includes("validation") || message.includes("invalid"))
    return "validation";
  if (message.includes("conflict")) return "conflict";
  if (message.includes("capacity") || message.includes("full"))
    return "capacity";
  if (message.includes("timeout")) return "timeout";
  if (message.includes("not found") || message.includes("404"))
    return "notFound";
  if (message.includes("500") || message.includes("server")) return "server";

  return "server"; // Default fallback
}

function isRetryableError(errorType: string): boolean {
  return ["network", "timeout", "server"].includes(errorType);
}

function getContextualHelp(component: string): string | null {
  const helpMap: Record<string, string> = {
    "appointment-form":
      "Para agendar, selecione primeiro o profissional, depois o serviço e por fim o horário disponível.",
    "calendar-view":
      "Clique em uma data para ver os horários disponíveis. Cores diferentes indicam níveis de ocupação.",
    "conflict-validation":
      "Conflitos são detectados automaticamente. Gestores podem autorizar exceções quando necessário.",
    "patient-search":
      "Digite pelo menos 3 caracteres para buscar pacientes. Use CPF para busca exata.",
    "schedule-management":
      "Agendas são configuradas por profissional. Ajuste horários de trabalho e intervalos conforme necessário.",
  };

  return helpMap[component] || null;
}

export type { ErrorAction, ErrorContext, ErrorHandlingConfig, ErrorMessage };
