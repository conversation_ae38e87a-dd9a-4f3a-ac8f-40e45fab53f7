{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "moduleResolution": "bundler", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "dist", "rootDir": "src", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}