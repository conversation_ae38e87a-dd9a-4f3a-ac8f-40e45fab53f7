# Story 9.1: Sistema de Cadastro de Pacientes

## Story Overview

**Como** recepcionista/administrativo da clínica  
**Eu quero** sistema completo de cadastro de pacientes com validação avançada  
**Para que** eu possa registrar pacientes rapidamente com dados completos e conformidade LGPD

### Story Details

- **Epic**: Epic 9 - Cadastro Pacientes & Prontuário
- **Story Points**: 8
- **Priority**: P0 (Critical)
- **Theme**: Patient Registration & Data Management
- **Dependencies**: Epic 1-4 (Foundation)

### Acceptance Criteria

#### AC1: Cadastro Básico de Paciente
- [ ] **GIVEN** usuário com permissão de cadastro
- [ ] **WHEN** inicia novo cadastro de paciente
- [ ] **THEN** apresenta formulário com campos obrigatórios e opcionais
- [ ] **AND** valida CPF/documento em tempo real
- [ ] **AND** verifica duplicação automática por CPF/nome/telefone
- [ ] **AND** permite completar cadastro em ≤ 5 minutos

#### AC2: Dados Demográficos Completos
- [ ] **GIVEN** formulário de cadastro aberto
- [ ] **WHEN** preenche dados demográficos
- [ ] **THEN** inclui nome completo, documento, telefones, email
- [ ] **AND** endereço completo com busca por CEP
- [ ] **AND** dados pessoais: nascimento, gênero, estado civil
- [ ] **AND** contato de emergência obrigatório

#### AC3: Informações Médicas Básicas
- [ ] **GIVEN** dados básicos preenchidos
- [ ] **WHEN** acessa seção médica básica
- [ ] **THEN** registra alergias conhecidas e medicamentos
- [ ] **AND** histórico médico familiar relevante
- [ ] **AND** plano de saúde e dados do convênio
- [ ] **AND** profissional de referência (se houver)

#### AC4: Controles LGPD Integrados
- [ ] **GIVEN** cadastro sendo finalizado
- [ ] **WHEN** solicita consentimentos LGPD
- [ ] **THEN** apresenta termos de privacidade claros
- [ ] **AND** coleta consentimento específico por finalidade
- [ ] **AND** permite opt-in/opt-out para comunicações
- [ ] **AND** registra histórico de consentimentos com timestamp

#### AC5: Sistema de Busca Inteligente
- [ ] **GIVEN** base de pacientes cadastrados
- [ ] **WHEN** realiza busca de paciente
- [ ] **THEN** busca por nome, CPF, telefone, email
- [ ] **AND** sugestão automática durante digitação
- [ ] **AND** filtros avançados por período, status, profissional
- [ ] **AND** resultados em < 2 segundos

#### AC6: Prevenção de Duplicação e Merge
- [ ] **GIVEN** tentativa de cadastro duplicado
- [ ] **WHEN** sistema detecta possível duplicação
- [ ] **THEN** alerta sobre registros similares existentes
- [ ] **AND** permite comparação lado a lado
- [ ] **AND** oferece opção de merge de registros
- [ ] **AND** mantém audit trail de operações de merge

### Technical Requirements

#### Frontend Components
```typescript
// Patient Registration Form
interface PatientRegistrationProps {
  onSubmit: (data: PatientData) => Promise<void>
  existingPatient?: Patient
  isEditing?: boolean
  lgpdConsents: LGPDConsent[]
}

// Patient Data Structure
interface PatientData {
  // Dados Básicos
  nomeCompleto: string
  documento: string
  tipoDocumento: 'cpf' | 'rg' | 'passaporte'
  dataNascimento: Date
  genero: 'masculino' | 'feminino' | 'outro' | 'nao_informar'
  estadoCivil: string
  
  // Contato
  telefones: TelefoneContato[]
  email: string
  endereco: EnderecoCompleto
  contatoEmergencia: ContatoEmergencia
  
  // Dados Médicos Básicos
  alergias: Alergia[]
  medicamentos: MedicamentoUso[]
  historicoFamiliar: string
  planoSaude?: PlanoSaude
  profissionalReferencia?: string
  
  // LGPD
  consentimentos: ConsentimentoLGPD[]
  preferenciasPrivacidade: PreferenciasPrivacidade
}

// LGPD Consent Management
interface ConsentimentoLGPD {
  id: string
  finalidade: string
  descricao: string
  obrigatorio: boolean
  consentido: boolean
  dataConsentimento?: Date
  versaoTermo: string
  ipOrigemConsentimento: string
}

// Smart Search Component
interface PatientSearchProps {
  onPatientSelect: (patient: Patient) => void
  placeholder?: string
  filters?: SearchFilters
  showAdvancedFilters?: boolean
}
```

#### Backend Architecture
```sql
-- Tabela Principal de Pacientes
CREATE TABLE pacientes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome_completo VARCHAR(255) NOT NULL,
  nome_social VARCHAR(255),
  documento VARCHAR(20) UNIQUE NOT NULL,
  tipo_documento paciente_documento_type NOT NULL,
  data_nascimento DATE NOT NULL,
  genero paciente_genero_type,
  estado_civil VARCHAR(50),
  
  -- Índices para busca otimizada
  nome_busca TEXT GENERATED ALWAYS AS (
    unaccent(lower(nome_completo))
  ) STORED,
  
  -- Auditoria
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- LGPD
  lgpd_consentimento_data TIMESTAMPTZ,
  lgpd_consentimento_versao VARCHAR(10),
  lgpd_data_exclusao TIMESTAMPTZ,
  
  CONSTRAINT chk_documento_valido CHECK (
    (tipo_documento = 'cpf' AND length(documento) = 11) OR
    (tipo_documento = 'rg' AND length(documento) >= 7) OR
    (tipo_documento = 'passaporte' AND length(documento) >= 6)
  )
);

-- Contatos do Paciente
CREATE TABLE paciente_contatos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  paciente_id UUID NOT NULL REFERENCES pacientes(id) ON DELETE CASCADE,
  tipo contato_tipo_type NOT NULL, -- 'telefone', 'email', 'endereco'
  valor TEXT NOT NULL,
  principal BOOLEAN DEFAULT FALSE,
  ativo BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Dados Médicos Básicos
CREATE TABLE paciente_dados_medicos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  paciente_id UUID NOT NULL REFERENCES pacientes(id) ON DELETE CASCADE,
  
  -- Alergias e Medicamentos
  alergias JSONB DEFAULT '[]',
  medicamentos_uso JSONB DEFAULT '[]',
  historico_familiar TEXT,
  
  -- Plano de Saúde
  plano_saude_nome VARCHAR(255),
  plano_saude_numero VARCHAR(50),
  plano_saude_validade DATE,
  
  -- Referências
  profissional_referencia_id UUID REFERENCES profissionais(id),
  
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- LGPD Consent Log
CREATE TABLE paciente_lgpd_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  paciente_id UUID NOT NULL REFERENCES pacientes(id) ON DELETE CASCADE,
  finalidade VARCHAR(100) NOT NULL,
  consentimento_dado BOOLEAN NOT NULL,
  data_consentimento TIMESTAMPTZ DEFAULT NOW(),
  versao_termo VARCHAR(10) NOT NULL,
  ip_origem INET,
  user_agent TEXT,
  
  -- Para auditoria de mudanças
  consentimento_anterior BOOLEAN,
  data_revogacao TIMESTAMPTZ,
  motivo_revogacao TEXT
);

-- Indexes para Performance
CREATE INDEX idx_pacientes_nome_busca ON pacientes USING gin(nome_busca gin_trgm_ops);
CREATE INDEX idx_pacientes_documento ON pacientes(documento);
CREATE INDEX idx_pacientes_data_nascimento ON pacientes(data_nascimento);
CREATE INDEX idx_paciente_contatos_valor ON paciente_contatos USING gin(valor gin_trgm_ops);
CREATE INDEX idx_lgpd_log_paciente_finalidade ON paciente_lgpd_log(paciente_id, finalidade);
```

#### Patient Registration API
```typescript
// Patient Registration API
export async function POST(request: NextRequest) {
  const patientData: PatientData = await request.json()
  
  const supabase = createServerClient()
  
  try {
    // 1. Validate and check for duplicates
    const duplicateCheck = await checkForDuplicates(patientData)
    if (duplicateCheck.hasDuplicates) {
      return NextResponse.json({
        error: 'Possível duplicação detectada',
        suggestions: duplicateCheck.suggestions
      }, { status: 409 })
    }
    
    // 2. Validate document (CPF, RG, etc.)
    const documentValidation = await validateDocument(
      patientData.documento, 
      patientData.tipoDocumento
    )
    if (!documentValidation.valid) {
      return NextResponse.json({
        error: 'Documento inválido',
        details: documentValidation.errors
      }, { status: 400 })
    }
    
    // 3. Create patient record with transaction
    const { data: patient, error } = await supabase.rpc('create_patient_complete', {
      patient_data: patientData,
      lgpd_consents: patientData.consentimentos,
      user_ip: getClientIP(request),
      user_agent: request.headers.get('user-agent')
    })
    
    if (error) {
      console.error('Patient creation error:', error)
      return NextResponse.json({
        error: 'Erro ao criar cadastro do paciente'
      }, { status: 500 })
    }
    
    // 4. Log LGPD consents
    await logLGPDConsents(patient.id, patientData.consentimentos, {
      ip: getClientIP(request),
      userAgent: request.headers.get('user-agent')
    })
    
    // 5. Trigger integrations
    await Promise.all([
      // Sync with Epic 5 (Portal Paciente)
      syncWithPatientPortal(patient.id),
      // Update Epic 6 (Agenda) suggestions
      updateSchedulingSuggestions(patient.id),
      // Notify Epic 7 (Financeiro) for plan verification
      verifyInsurancePlan(patient.planoSaude)
    ])
    
    return NextResponse.json({
      patient,
      message: 'Paciente cadastrado com sucesso',
      integrations: {
        portal: 'synced',
        agenda: 'updated',
        financeiro: 'notified'
      }
    })
    
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}

// Duplicate Detection Function
export async function checkForDuplicates(patientData: PatientData) {
  const supabase = createServerClient()
  
  // Check by document
  const { data: byDocument } = await supabase
    .from('pacientes')
    .select('id, nome_completo, documento, telefones:paciente_contatos!inner(*)')
    .eq('documento', patientData.documento)
    .limit(1)
  
  // Check by name similarity (fuzzy matching)
  const { data: byName } = await supabase
    .from('pacientes')
    .select('id, nome_completo, documento')
    .textSearch('nome_busca', patientData.nomeCompleto, {
      type: 'websearch',
      config: 'portuguese'
    })
    .limit(5)
  
  // Check by phone
  const phones = patientData.telefones.map(t => t.numero)
  const { data: byPhone } = await supabase
    .from('paciente_contatos')
    .select('paciente_id, pacientes!inner(nome_completo, documento)')
    .in('valor', phones)
    .eq('tipo', 'telefone')
    .limit(5)
  
  const hasDuplicates = byDocument.length > 0 || 
                       byName.length > 0 || 
                       byPhone.length > 0
  
  return {
    hasDuplicates,
    suggestions: {
      byDocument,
      byName,
      byPhone,
      confidence: calculateSimilarityConfidence(patientData, [
        ...byDocument, 
        ...byName, 
        ...byPhone.map(p => p.pacientes)
      ])
    }
  }
}

// Smart Search API
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q')
  const filters = JSON.parse(searchParams.get('filters') || '{}')
  
  if (!query || query.length < 2) {
    return NextResponse.json({ patients: [] })
  }
  
  const supabase = createServerClient()
  
  let baseQuery = supabase
    .from('pacientes')
    .select(`
      id,
      nome_completo,
      documento,
      data_nascimento,
      telefones:paciente_contatos!inner(valor),
      email:paciente_contatos!inner(valor)
    `)
  
  // Search by multiple criteria
  if (isValidDocument(query)) {
    // Search by document
    baseQuery = baseQuery.eq('documento', query)
  } else if (isValidEmail(query)) {
    // Search by email
    baseQuery = baseQuery
      .eq('paciente_contatos.tipo', 'email')
      .ilike('paciente_contatos.valor', `%${query}%`)
  } else if (isValidPhone(query)) {
    // Search by phone
    baseQuery = baseQuery
      .eq('paciente_contatos.tipo', 'telefone')
      .ilike('paciente_contatos.valor', `%${query}%`)
  } else {
    // Search by name (fuzzy)
    baseQuery = baseQuery
      .textSearch('nome_busca', query, {
        type: 'websearch',
        config: 'portuguese'
      })
  }
  
  // Apply filters
  if (filters.ageRange) {
    const { min, max } = filters.ageRange
    const minDate = subYears(new Date(), max)
    const maxDate = subYears(new Date(), min)
    baseQuery = baseQuery
      .gte('data_nascimento', minDate.toISOString())
      .lte('data_nascimento', maxDate.toISOString())
  }
  
  if (filters.hasInsurance !== undefined) {
    if (filters.hasInsurance) {
      baseQuery = baseQuery.not('plano_saude_nome', 'is', null)
    } else {
      baseQuery = baseQuery.is('plano_saude_nome', null)
    }
  }
  
  const { data: patients, error } = await baseQuery
    .limit(20)
    .order('nome_completo')
  
  if (error) {
    return NextResponse.json({ error: 'Erro na busca' }, { status: 500 })
  }
  
  return NextResponse.json({
    patients: patients.map(patient => ({
      ...patient,
      matchType: determineMatchType(query, patient),
      relevanceScore: calculateRelevanceScore(query, patient)
    })).sort((a, b) => b.relevanceScore - a.relevanceScore)
  })
}
```

### Integration Points

#### Epic 5 Integration (Portal Paciente)
- **Auto-Sync**: Dados básicos sincronizados automaticamente
- **Self-Update**: Pacientes podem atualizar dados via portal
- **Consent Management**: Consentimentos LGPD gerenciados no portal
- **Profile Completion**: Portal sugere completar dados faltantes

#### Epic 6 Integration (Agenda Inteligente)
- **Patient Context**: Dados do paciente disponíveis na agenda
- **Smart Suggestions**: Sugestão de profissionais baseado no histórico
- **Medical Alerts**: Alertas de alergias na interface de agendamento
- **Insurance Validation**: Verificação de plano na criação de consultas

#### Epic 7 Integration (Financeiro Essencial)
- **Insurance Data**: Dados de plano para faturamento automático
- **Payment History**: Histórico financeiro disponível no cadastro
- **Credit Analysis**: Análise de crédito baseada no histórico
- **Billing Integration**: Códigos de procedimento no cadastro

### Testing Strategy

#### Unit Tests
```typescript
describe('Patient Registration', () => {
  test('validates CPF correctly', () => {
    expect(validateCPF('12345678901')).toBe(true)
    expect(validateCPF('11111111111')).toBe(false)
    expect(validateCPF('123')).toBe(false)
  })
  
  test('detects duplicate patients', async () => {
    const existingPatient = await createTestPatient()
    
    const duplicateData = {
      ...existingPatient,
      id: undefined // New patient with same data
    }
    
    const duplicateCheck = await checkForDuplicates(duplicateData)
    expect(duplicateCheck.hasDuplicates).toBe(true)
  })
  
  test('LGPD consent recording', async () => {
    const patientData = createTestPatientData()
    const response = await createPatient(patientData)
    
    expect(response.patient.id).toBeDefined()
    
    const lgpdLog = await getLGPDLog(response.patient.id)
    expect(lgpdLog.length).toBeGreaterThan(0)
    expect(lgpdLog[0].finalidade).toBe('cadastro_paciente')
  })
})
```

#### Integration Tests
```typescript
describe('Patient Search Performance', () => {
  test('search responds within 2 seconds', async () => {
    const startTime = performance.now()
    
    const response = await fetch('/api/patients/search?q=João Silva')
    const data = await response.json()
    
    const endTime = performance.now()
    const searchTime = endTime - startTime
    
    expect(searchTime).toBeLessThan(2000)
    expect(data.patients).toBeDefined()
  })
  
  test('handles concurrent patient registrations', async () => {
    const patientPromises = Array.from({ length: 10 }, (_, i) => 
      createPatient(generateTestPatientData(i))
    )
    
    const results = await Promise.allSettled(patientPromises)
    const successful = results.filter(r => r.status === 'fulfilled')
    
    expect(successful.length).toBe(10)
  })
})
```

### Dev Notes

#### Performance Optimization
- **Search Indexes**: GIN indexes para busca full-text em português
- **Materialized Views**: Para queries complexas de relatórios
- **Connection Pooling**: Pool de conexões otimizado para alta concorrência
- **Caching Strategy**: Redis cache para dados de pacientes frequentemente acessados

#### Security & LGPD
- **Data Encryption**: Campos sensíveis criptografados com chaves rotativas
- **Access Logging**: Log completo de acesso aos dados de pacientes
- **Right to be Forgotten**: Implementação do direito ao esquecimento LGPD
- **Consent Versioning**: Versionamento de termos com migração automática

#### Medical Compliance
- **CFM Standards**: Conformidade com resoluções do CFM
- **Data Retention**: Retenção de dados conforme regulamentação médica
- **Audit Trail**: Rastro de auditoria para todas as modificações
- **Professional Validation**: Validação de profissionais via APIs CRM/CFM

---

## Story 9.1 Status

**Status**: ✅ READY - Sistema completo de cadastro com LGPD compliance, busca inteligente e integração com Epic 5-7.

**Próxima**: Story 9.2 - Prontuário Eletrônico e Histórico Médico
