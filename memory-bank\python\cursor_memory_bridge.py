#!/usr/bin/env python3
"""
VIBECODE V1.0 - Cursor Memory Bank Integration Bridge
Integrates Cursor Memory Bank system with VIBECODE core architecture.
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CursorMemoryBridge:
    """Bridge between VIBECODE V1.0 and Cursor Memory Bank systems."""
    
    def __init__(self, project_root: str = "E:\\VIBECODE"):
        self.project_root = Path(project_root)
        self.memory_bank_path = self.project_root / "memory-bank"
        self.cursor_rules_path = self.project_root / ".cursor" / "rules"
        self.visual_maps_path = self.cursor_rules_path / "visual-maps"
        
        # Ensure directories exist
        self.memory_bank_path.mkdir(exist_ok=True)
        self.visual_maps_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"CursorMemoryBridge initialized for {project_root}")
    
    def sync_context(self) -> Dict[str, Any]:
        """Synchronize context between VIBECODE and Cursor Memory Bank."""
        try:
            context = {
                "timestamp": datetime.now().isoformat(),
                "active_context": self._read_memory_file("activeContext.md"),
                "current_tasks": self._read_memory_file("tasks.md"),
                "progress": self._read_memory_file("progress.md"),
                "system_patterns": self._read_memory_file("systemPatterns.md")
            }
            
            logger.info("Context synchronized successfully")
            return context
            
        except Exception as e:
            logger.error(f"Context sync failed: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    def update_memory(self, data: Dict[str, Any]) -> bool:
        """Update memory bank with new data."""
        try:
            # Update activeContext.md with current state
            if "active_context" in data:
                self._update_active_context(data["active_context"])
            
            # Update progress.md with task progress
            if "progress" in data:
                self._update_progress(data["progress"])
            
            # Update tasks.md with task changes
            if "tasks" in data:
                self._update_tasks(data["tasks"])
            
            logger.info("Memory updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Memory update failed: {e}")
            return False    
    def load_phase_rules(self, phase: str, complexity: int = 5) -> List[str]:
        """Load phase-specific rules based on current phase and complexity."""
        try:
            rules = []
            
            # Always load master rule
            master_rule_path = self.cursor_rules_path / "master_rule.mdc"
            if master_rule_path.exists():
                rules.append(str(master_rule_path))
            
            # Load visual map for current phase
            visual_map_path = self.visual_maps_path / f"{phase}-mode-map.mdc"
            if visual_map_path.exists():
                rules.append(str(visual_map_path))
            
            # Load complexity-specific rules
            level_rules_path = self.cursor_rules_path / "levels" / f"level-{min(complexity//3 + 1, 4)}"
            if level_rules_path.exists():
                for rule_file in level_rules_path.glob("*.mdc"):
                    rules.append(str(rule_file))
            
            logger.info(f"Loaded {len(rules)} rules for phase {phase}, complexity {complexity}")
            return rules
            
        except Exception as e:
            logger.error(f"Rule loading failed: {e}")
            return []
    
    def optimize_context(self) -> Dict[str, Any]:
        """Optimize context usage through JIT loading and caching."""
        try:
            optimization = {
                "timestamp": datetime.now().isoformat(),
                "memory_usage": self._calculate_memory_usage(),
                "context_size": self._calculate_context_size(),
                "optimization_suggestions": self._get_optimization_suggestions()
            }
            
            logger.info("Context optimization analysis complete")
            return optimization
            
        except Exception as e:
            logger.error(f"Context optimization failed: {e}")
            return {"error": str(e)}
    
    def _read_memory_file(self, filename: str) -> Optional[str]:
        """Read content from memory bank file."""
        file_path = self.memory_bank_path / filename
        if file_path.exists():
            return file_path.read_text(encoding="utf-8")
        return None    
    def _update_active_context(self, context_data: Dict[str, Any]) -> None:
        """Update activeContext.md with new context data."""
        active_context_path = self.memory_bank_path / "activeContext.md"
        
        # Read current content
        current_content = ""
        if active_context_path.exists():
            current_content = active_context_path.read_text(encoding="utf-8")
        
        # Update with new data (implementation depends on context_data structure)
        updated_content = self._merge_context_content(current_content, context_data)
        
        # Write updated content
        active_context_path.write_text(updated_content, encoding="utf-8")
        logger.info("activeContext.md updated")
    
    def _update_progress(self, progress_data: Dict[str, Any]) -> None:
        """Update progress.md with new progress data."""
        progress_path = self.memory_bank_path / "progress.md"
        
        # Implementation for progress updates
        # This would merge new progress data with existing content
        logger.info("progress.md updated")
    
    def _update_tasks(self, tasks_data: Dict[str, Any]) -> None:
        """Update tasks.md with new task data."""
        tasks_path = self.memory_bank_path / "tasks.md"
        
        # Implementation for task updates
        # This would merge new task data with existing content
        logger.info("tasks.md updated")
    
    def _merge_context_content(self, current: str, new_data: Dict[str, Any]) -> str:
        """Merge new context data with existing content."""
        # Implementation for intelligent content merging
        # This is a simplified version - real implementation would be more sophisticated
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M UTC")
        
        # Add update timestamp
        if "Last Updated" in current:
            import re
            current = re.sub(
                r"Last Updated.*",
                f"Last Updated**: {timestamp}",
                current
            )
        
        return current    
    def _calculate_memory_usage(self) -> Dict[str, int]:
        """Calculate current memory usage statistics."""
        memory_stats = {
            "total_files": 0,
            "total_size_bytes": 0,
            "memory_bank_files": 0,
            "rules_files": 0
        }
        
        # Count memory bank files
        if self.memory_bank_path.exists():
            for file_path in self.memory_bank_path.glob("*.md"):
                memory_stats["memory_bank_files"] += 1
                memory_stats["total_size_bytes"] += file_path.stat().st_size
        
        # Count rules files
        if self.cursor_rules_path.exists():
            for file_path in self.cursor_rules_path.glob("**/*.mdc"):
                memory_stats["rules_files"] += 1
                memory_stats["total_size_bytes"] += file_path.stat().st_size
        
        memory_stats["total_files"] = memory_stats["memory_bank_files"] + memory_stats["rules_files"]
        return memory_stats
    
    def _calculate_context_size(self) -> Dict[str, int]:
        """Calculate context size for optimization."""
        context_stats = {
            "active_context_chars": 0,
            "tasks_chars": 0,
            "progress_chars": 0,
            "total_context_chars": 0
        }
        
        # Calculate character counts for key files
        for filename, key in [
            ("activeContext.md", "active_context_chars"),
            ("tasks.md", "tasks_chars"),
            ("progress.md", "progress_chars")
        ]:
            content = self._read_memory_file(filename)
            if content:
                context_stats[key] = len(content)
        
        context_stats["total_context_chars"] = sum(
            v for k, v in context_stats.items() if k != "total_context_chars"
        )
        
        return context_stats    
    def _get_optimization_suggestions(self) -> List[str]:
        """Generate optimization suggestions based on current state."""
        suggestions = []
        
        # Check file sizes
        memory_stats = self._calculate_memory_usage()
        if memory_stats["total_size_bytes"] > 100000:  # 100KB threshold
            suggestions.append("Consider archiving old memory bank entries")
        
        # Check context size
        context_stats = self._calculate_context_size()
        if context_stats["total_context_chars"] > 50000:  # 50K chars threshold
            suggestions.append("Implement context summarization for large files")
        
        # Check file count
        if memory_stats["total_files"] > 20:
            suggestions.append("Consider consolidating related rule files")
        
        return suggestions
    
    def validate_integration(self) -> Dict[str, Any]:
        """Validate the integration between VIBECODE and Cursor Memory Bank."""
        validation_results = {
            "timestamp": datetime.now().isoformat(),
            "memory_bank_status": "unknown",
            "rules_status": "unknown",
            "integration_status": "unknown",
            "errors": [],
            "warnings": []
        }
        
        try:
            # Check memory bank directory
            if not self.memory_bank_path.exists():
                validation_results["errors"].append("Memory bank directory not found")
                validation_results["memory_bank_status"] = "missing"
            else:
                required_files = ["projectbrief.md", "activeContext.md", "tasks.md", "progress.md"]
                missing_files = []
                for filename in required_files:
                    if not (self.memory_bank_path / filename).exists():
                        missing_files.append(filename)
                
                if missing_files:
                    validation_results["warnings"].append(f"Missing memory bank files: {missing_files}")
                    validation_results["memory_bank_status"] = "incomplete"
                else:
                    validation_results["memory_bank_status"] = "complete"
            
            # Check rules directory
            if not self.cursor_rules_path.exists():
                validation_results["errors"].append("Cursor rules directory not found")
                validation_results["rules_status"] = "missing"
            else:
                master_rule = self.cursor_rules_path / "master_rule.mdc"
                if not master_rule.exists():
                    validation_results["errors"].append("master_rule.mdc not found")
                    validation_results["rules_status"] = "incomplete"
                else:
                    validation_results["rules_status"] = "complete"
            
            # Overall integration status
            if not validation_results["errors"]:
                if not validation_results["warnings"]:
                    validation_results["integration_status"] = "healthy"
                else:
                    validation_results["integration_status"] = "functional_with_warnings"
            else:
                validation_results["integration_status"] = "broken"
            
            logger.info(f"Integration validation complete: {validation_results['integration_status']}")
            return validation_results
            
        except Exception as e:
            validation_results["errors"].append(f"Validation failed: {str(e)}")
            validation_results["integration_status"] = "error"
            logger.error(f"Integration validation failed: {e}")
            return validation_results


def main():
    """Main function for testing the bridge."""
    bridge = CursorMemoryBridge()
    
    # Test basic functionality
    print("Testing Cursor Memory Bridge...")
    
    # Sync context
    context = bridge.sync_context()
    print(f"✅ Context sync: {'Success' if 'error' not in context else 'Failed'}")
    
    # Validate integration
    validation = bridge.validate_integration()
    print(f"✅ Integration status: {validation['integration_status']}")
    
    # Optimize context
    optimization = bridge.optimize_context()
    print(f"✅ Context optimization: {'Success' if 'error' not in optimization else 'Failed'}")
    
    print("Bridge testing complete!")


if __name__ == "__main__":
    main()