# 🎯 WARP CLI - CODING STANDARDS V2.0

_Expert TypeScript, Next.js 14 App Router, React, Supabase Standards_

**Core Principles**: "Aprimore, Não Prolifere" (≥85% reuse), Confidence ≥8/10, Quality ≥90%
**Stack**: TypeScript, Node.js, Next.js 14, React, Supabase, Tailwind CSS, Shadcn UI

## 🏗️ CORE DEVELOPMENT PHILOSOPHY

### **Professional Excellence Standards**

```json
{
  "core_principles": {
    "context_first": "Understand system completely before coding",
    "challenge_requests": "Identify edge cases, clarify requirements", 
    "hold_standards": "Modular, testable, documented code",
    "design_not_patch": "Think architecture, not quick fixes"
  },
  "programming_paradigm": {
    "style": "functional, declarative programming",
    "avoid": "classes",
    "prefer": "iteration and modularization over duplication",
    "pattern": "RORO (Receive an Object, Return an Object)"
  }
}
```

### **Quality Enforcement**
- **Early Returns**: Handle errors at function beginning
- **Guard Clauses**: Use if-return pattern vs nested else
- **No Placeholders**: Complete functionality, no TODOs
- **Error Handling**: Comprehensive with proper logging
- **Type Safety**: TypeScript strict mode, interfaces over types

## 📝 TYPESCRIPT & JAVASCRIPT STANDARDS

### **Language Conventions**

```typescript
// ✅ CORRECT: Function keyword, omit semicolons
function calculateTotal(items: Item[]): number {
  if (!items.length) return 0
  
  return items.reduce((sum, item) => sum + item.price, 0)
}

// ✅ CORRECT: Interfaces over types
interface UserProfile {
  id: string
  name: string
  email: string
  isActive: boolean
}

// ✅ CORRECT: Descriptive variable names with auxiliary verbs
const isLoading = false
const hasError = true
const shouldRender = true
```

### **File Structure Standards**

```typescript
// File order: Exported component, subcomponents, helpers, types
// 1. Exported component
export function UserDashboard({ userId }: UserDashboardProps) {
  // Implementation
}

// 2. Subcomponents
function UserStats({ stats }: UserStatsProps) {
  // Implementation
}

// 3. Helper functions
function formatUserData(user: User): FormattedUser {
  // Implementation
}

// 4. Types and interfaces
interface UserDashboardProps {
  userId: string
}
```

### **Conditional Statements**

```typescript
// ✅ CORRECT: Guard clauses for early returns
function processUser(user: User | null): ProcessedUser {
  if (!user) throw new Error('User is required')
  if (!user.isActive) throw new Error('User is not active')
  if (!user.profile) throw new Error('User profile is incomplete')
  
  // Happy path last
  return {
    id: user.id,
    displayName: user.profile.name,
    permissions: user.permissions
  }
}
```

## ⚠️ ERROR HANDLING & VALIDATION

### **Error Handling Patterns**

```typescript
// ✅ CORRECT: Early error handling, guard clauses
function validateUser(user: unknown): User {
  // Handle errors at the beginning
  if (!user) throw new Error('User data is required')
  if (typeof user !== 'object') throw new Error('Invalid user data format')
  
  const userData = user as Record<string, unknown>
  
  // Guard clauses for preconditions
  if (!userData.id) throw new Error('User ID is required')
  if (!userData.email) throw new Error('User email is required')
  
  // Happy path last
  return {
    id: userData.id as string,
    email: userData.email as string,
    name: userData.name as string || ''
  }
}

// ✅ CORRECT: Custom error types
class ValidationError extends Error {
  constructor(field: string, value: unknown) {
    super(`Validation failed for field '${field}' with value: ${value}`)
    this.name = 'ValidationError'
  }
}
```

## ⚛️ REACT & NEXT.JS 14 STANDARDS

### **Component Patterns**

```typescript
// ✅ CORRECT: Functional components with function keyword
function UserProfile({ user, onUpdate }: UserProfileProps) {
  // Minimize use client, favor Server Components
  return (
    <div className="space-y-4">
      <h1>{user.name}</h1>
      <UserActions onUpdate={onUpdate} />
    </div>
  )
}

// ✅ CORRECT: Named exports
export { UserProfile }

// ✅ CORRECT: Wrap client components in Suspense
import { Suspense } from 'react'

function UserDashboard() {
  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <UserProfileClient />
    </Suspense>
  )
}
```

### **Form Handling with Validation**

```typescript
// ✅ CORRECT: Zod + react-hook-form + useActionState
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useActionState } from 'react'

const userSchema = z.object({
  email: z.string().email(),
  name: z.string().min(2)
})

function UserForm() {
  const [state, formAction] = useActionState(createUserAction, null)
  
  const form = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema)
  })
  
  return (
    <form action={formAction} className="space-y-4">
      <input
        {...form.register('email')}
        type="email"
        className="w-full px-3 py-2 border rounded"
      />
      {form.formState.errors.email && (
        <p className="text-red-500">{form.formState.errors.email.message}</p>
      )}
      
      <button type="submit" disabled={form.formState.isSubmitting}>
        Submit
      </button>
    </form>
  )
}
```

## 🗄️ SUPABASE STANDARDS

### **Type-safe Client**

```typescript
// ✅ CORRECT: Type-safe Supabase client
import { createClient } from '@supabase/supabase-js'
import type { Database } from './database.types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// ✅ CORRECT: Type-safe queries with error handling
async function getUser(id: string): Promise<User | null> {
  const { data, error } = await supabase
    .from('users')
    .select('id, email, profile(name, avatar_url)')
    .eq('id', id)
    .single()
  
  if (error) {
    if (error.code === 'PGRST116') return null // Not found
    throw new Error(`Failed to fetch user: ${error.message}`)
  }
  
  return data
}
```

## 🎨 STYLING & UI STANDARDS

### **Tailwind CSS Conventions**

```typescript
// ✅ CORRECT: Utility-first approach, mobile-first responsive
function ResponsiveCard({ title, children }: CardProps) {
  return (
    <div className="
      w-full
      p-4 md:p-6 lg:p-8
      bg-white dark:bg-gray-900
      border border-gray-200 dark:border-gray-800
      rounded-lg shadow-sm
      hover:shadow-md transition-shadow
    ">
      <h2 className="text-lg md:text-xl font-semibold mb-4">{title}</h2>
      {children}
    </div>
  )
}

// ✅ CORRECT: Class Variance Authority (CVA)
import { cva } from 'class-variance-authority'

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
)
```

## 📁 NAMING CONVENTIONS & FILE STRUCTURE

### **Naming Standards**

```typescript
// ✅ CORRECT: File naming conventions
// components/auth-wizard.tsx
// hooks/use-auth.hook.ts
// types/user.type.ts
// config/database.config.ts

// ✅ CORRECT: Component structure  
/*
components/
├── user-dashboard/
│   ├── user-dashboard.tsx          # Main component
│   ├── user-stats.tsx             # Subcomponent
│   ├── user-dashboard.types.ts    # Types
│   └── index.ts                   # Export
*/

// ✅ CORRECT: Break down components with minimal props
function UserDashboard({ userId }: { userId: string }) {
  return (
    <div className="space-y-6">
      <UserHeader userId={userId} />
      <UserStats userId={userId} />
      <UserActions userId={userId} />
    </div>
  )
}
```

## ♿ ACCESSIBILITY STANDARDS

```typescript
// ✅ CORRECT: Keyboard navigation and ARIA labels
function Modal({ isOpen, onClose, children }: ModalProps) {
  return (
    <dialog
      className="backdrop:bg-black/50 p-6 rounded-lg"
      onClose={onClose}
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <div className="flex justify-between items-center mb-4">
        <h2 id="modal-title" className="text-xl font-semibold">
          Modal Title
        </h2>
        <button
          onClick={onClose}
          aria-label="Close modal"
          className="p-2 hover:bg-gray-100 rounded"
        >
          ×
        </button>
      </div>
      <div id="modal-description">
        {children}
      </div>
    </dialog>
  )
}
```

## ✅ QUALITY CHECKLIST

### **Pre-commit Checklist**
- [ ] TypeScript strict mode passes
- [ ] All interfaces properly defined
- [ ] Error handling implemented with guard clauses
- [ ] Components use proper naming conventions
- [ ] Accessibility attributes included
- [ ] Performance optimizations applied
- [ ] Tests cover critical paths

### **Performance Targets**
- **LCP**: <2.5s
- **FID**: <100ms
- **CLS**: <0.1
- **Bundle size**: <200KB gzipped

### **Key Conventions Compliance**
1. ✅ Next.js App Router for routing
2. ✅ Minimize 'use client' - prefer Server Components
3. ✅ TypeScript interfaces over types
4. ✅ Function keyword for components
5. ✅ RORO pattern implementation
6. ✅ Early returns and guard clauses
7. ✅ Mobile-first responsive design

---

**Remember**: "Context First, Quality Always, Performance Matters"
**Status**: ✅ ATIVO - Optimizado para Warp CLI
