# 🔍 KIRO RESEARCH PROTOCOL - MANDATORY IMPLEMENTATION

## Auto-Detection System

### Research Keywords (Auto-Activation)
```
search, find, documentation, tutorial, how to, example, guide, library, 
framework, API, best practices, implementation, configuration, integration,
pesquisar, buscar, encontrar, documentação, como fazer, guia, biblioteca
```

### Activation Rules
- **ANY** research keyword detected → Activate ALL 3 MCPs
- **MANDATORY** sequence execution
- **NO** skipping tools even if individual tools fail
- **CONTINUE** sequence regardless of individual failures

## Mandatory 3-Step Research Sequence

### Step 1: Context7-MCP (Technical Documentation)
- **Purpose**: Technical documentation and library references
- **Priority**: ALWAYS first
- **Focus**: Official docs, API references, technical specifications
- **Failure handling**: Continue to Step 2

### Step 2: Tavily-MCP (General Web Search)
- **Purpose**: General web search and current information
- **Priority**: ALWAYS second
- **Focus**: Tutorials, guides, community discussions, recent updates
- **Failure handling**: Continue to Step 3

### Step 3: Exa-MCP (Alternative Search)
- **Purpose**: Alternative search perspectives and sources
- **Priority**: ALWAYS third
- **Focus**: Different viewpoints, alternative solutions, edge cases
- **Failure handling**: Document and proceed with available information

## Quality Requirements

### Synthesis Standards
- **Minimum sources**: 3 different sources documented
- **Quality threshold**: ≥8/10 for final synthesis
- **Completeness check**: MANDATORY verification
- **Source documentation**: REQUIRED for all findings

### Output Format
```markdown
## Research Summary

### Sources Consulted
1. **Context7**: [findings summary]
2. **Tavily**: [findings summary]  
3. **Exa**: [findings summary]

### Synthesis (Quality: X/10)
[Comprehensive synthesis of all sources]

### Recommendations
[Actionable recommendations based on research]
```

## Enforcement Rules

### Mandatory Practices
- **Before any implementation**: Research if knowledge gaps exist
- **Document all sources**: Never skip source attribution
- **Quality validation**: Ensure ≥8/10 synthesis quality
- **Completeness verification**: Check all aspects covered

### Prohibited Practices
- **Single-source decisions**: Never rely on one source only
- **Skipping tools**: All 3 MCPs must be attempted
- **Incomplete synthesis**: Must synthesize findings from all sources
- **Undocumented research**: All research must be documented

## Integration with Main Workflow

### Research Phase Integration
1. **Analyze** → Include research needs assessment
2. **Select** → Auto-activate research protocol if keywords detected
3. **Execute** → Run mandatory 3-step research sequence
4. **Reflect** → Validate research quality ≥8/10
5. **Refine** → Improve research if quality insufficient
6. **Validate** → Confirm comprehensive research completion
7. **Learn** → Update knowledge base with findings

---

**Critical**: Research protocol is MANDATORY and AUTOMATIC. Quality ≥8/10 required.