import { useCallback, useState } from 'react';
import { StageConfig } from '../config/stagewise.config';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export function useStageValidation() {
  const [validationState, setValidationState] = useState<Record<string, ValidationResult>>({});

  const validateStage = useCallback((stageId: string, data: any, config: StageConfig): ValidationResult => {
    const errors: string[] = [];
    
    // Validate required fields
    if (config.validation?.requiredFields) {
      config.validation.requiredFields.forEach(field => {
        if (!data[field]) {
          errors.push(`${field} is required`);
        }
      });
    }

    // Run custom validation
    if (config.validation?.customValidator) {
      const customErrors = config.validation.customValidator(data);
      if (customErrors && customErrors.length > 0) {
        errors.push(...customErrors);
      }
    }

    const result = {
      isValid: errors.length === 0,
      errors
    };

    setValidationState(prev => ({
      ...prev,
      [stageId]: result
    }));

    return result;
  }, []);

  const clearValidation = useCallback((stageId?: string) => {
    if (stageId) {
      setValidationState(prev => {
        const newState = { ...prev };
        delete newState[stageId];
        return newState;
      });
    } else {
      setValidationState({});
    }
  }, []);

  return {
    validationState,
    validateStage,
    clearValidation
  };
}