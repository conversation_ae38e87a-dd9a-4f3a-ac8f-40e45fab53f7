# AegisWallet Backend Development Rules

## Security-First API Design

### Route Structure
```
/api/auth/*          - Multi-factor authentication
/api/wallet/*        - Wallet management (encrypted)
/api/transactions/*  - Transaction processing
/api/keys/*          - Key management (highly secured)
/api/security/*      - Security settings and logs
/api/compliance/*    - Regulatory compliance
```

### Security Headers
```typescript
// Required security headers for all responses
{
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Content-Security-Policy': 'default-src \'self\'',
  'Referrer-Policy': 'strict-origin-when-cross-origin'
}
```

## Cryptographic Standards

### Encryption Requirements
- AES-256-GCM for symmetric encryption
- RSA-4096 or ECDSA P-384 for asymmetric encryption
- PBKDF2 with 100,000+ iterations for key derivation
- Secure random number generation (crypto.randomBytes)

### Key Management
```typescript
// Never store private keys in plain text
interface SecureKeyStorage {
  encryptedPrivateKey: string;  // Encrypted with user password
  publicKey: string;
  keyDerivationSalt: string;
  encryptionIV: string;
}
```

### Sensitive Data Handling
- Encrypt all PII and financial data at rest
- Use secure memory for temporary key storage
- Implement proper key rotation policies
- Zero out sensitive variables after use

## Database Security

### Encryption at Rest
```sql
-- Encrypt sensitive columns
CREATE TABLE wallets (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  encrypted_private_key BYTEA NOT NULL,  -- Encrypted
  public_key TEXT NOT NULL,
  balance_encrypted BYTEA,               -- Encrypted balance
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Access Control
- Database-level encryption for sensitive tables
- Row-level security policies
- Separate read/write database users
- Audit logging for all database operations

## Transaction Security

### Validation Pipeline
```typescript
// Multi-layer transaction validation
const validateTransaction = async (tx: Transaction) => {
  // 1. Signature verification
  await verifyTransactionSignature(tx);
  
  // 2. Balance verification
  await verifySufficientBalance(tx);
  
  // 3. Rate limiting check
  await checkTransactionRateLimit(tx.fromAddress);
  
  // 4. Compliance screening
  await screenForCompliance(tx);
  
  // 5. Final security checks
  await performSecurityChecks(tx);
};
```

### Audit Trail
- Log all transaction attempts (success/failure)
- Track all wallet access and modifications
- Monitor for suspicious activity patterns
- Implement real-time fraud detection

## Authentication & Authorization

### Multi-Factor Authentication
```typescript
interface AuthenticationFlow {
  step1: 'password_verification';
  step2: 'totp_verification' | 'sms_verification';
  step3?: 'biometric_verification';
  step4?: 'hardware_key_verification';
}
```

### Session Management
- Short-lived JWT tokens (15 minutes)
- Secure refresh token rotation
- Device fingerprinting
- Automatic logout on suspicious activity

## Compliance & Regulatory

### KYC/AML Requirements
- Identity verification workflows
- Transaction monitoring and reporting
- Sanctions list screening
- Suspicious activity reporting

### Data Retention
- Secure data archival policies
- Right to be forgotten compliance
- Audit trail preservation
- Regulatory reporting capabilities

## Error Handling & Logging

### Security-Aware Logging
```typescript
// Never log sensitive information
logger.error('Transaction failed', {
  transactionId: tx.id,
  errorCode: 'INSUFFICIENT_BALANCE',
  userId: hashUserId(tx.userId),  // Hash PII
  timestamp: new Date().toISOString()
  // Never log: private keys, passwords, full addresses
});
```

### Incident Response
- Automated security incident detection
- Immediate notification systems
- Forensic logging capabilities
- Recovery and rollback procedures