"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  AlertCircle,
  AlertTriangle,
  Calendar,
  CheckCircle2,
  Clock,
  Shield,
  Users,
  XCircle,
} from "lucide-react";
import { useState } from "react";

// =============================================
// NeonPro Conflict Override Dialog
// Story 1.2: Manager conflict override system
// =============================================

interface ConflictItem {
  type: string;
  message: string;
  severity: "error" | "warning" | "info";
}

interface ConflictOverrideDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  conflicts: ConflictItem[];
  appointmentData: {
    appointment_id?: string;
    professional_id: string;
    clinic_id: string;
    patient_id: string;
    service_type_id: string;
    start_time: string;
    end_time: string;
    professional_name: string;
    patient_name: string;
    service_name: string;
  };
  onConfirm: (overrideReason: string) => Promise<void>;
  isLoading?: boolean;
}

const overrideSchema = {
  validate: (reason: string): string | null => {
    if (!reason || reason.trim().length < 10) {
      return "O motivo deve ter pelo menos 10 caracteres";
    }
    if (reason.length > 500) {
      return "O motivo não pode exceder 500 caracteres";
    }
    return null;
  },
};

const getSeverityIcon = (severity: string) => {
  switch (severity) {
    case "error":
      return <XCircle className="h-4 w-4 text-red-500" />;
    case "warning":
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case "info":
      return <AlertCircle className="h-4 w-4 text-blue-500" />;
    default:
      return <AlertCircle className="h-4 w-4 text-gray-500" />;
  }
};

export default function ConflictOverrideDialog({
  isOpen,
  onOpenChange,
  conflicts,
  appointmentData,
  onConfirm,
  isLoading = false,
}: ConflictOverrideDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [overrideReason, setOverrideReason] = useState("");
  const [validationError, setValidationError] = useState<string | null>(null);

  const errorConflicts = conflicts.filter((c) => c.severity === "error");
  const warningConflicts = conflicts.filter((c) => c.severity === "warning");
  const infoConflicts = conflicts.filter((c) => c.severity === "info");

  const validateAndSubmit = async () => {
    const error = overrideSchema.validate(overrideReason);
    setValidationError(error);

    if (error) return;

    if (isSubmitting) return;

    try {
      setIsSubmitting(true);
      await onConfirm(overrideReason);

      // Show success notification (simple alert for now)
      alert(
        "Override autorizado com sucesso. O agendamento foi confirmado mesmo com conflitos."
      );

      setOverrideReason("");
      setValidationError(null);
      onOpenChange(false);
    } catch (error) {
      console.error("Error submitting override:", error);
      alert(
        "Erro ao processar override. Não foi possível autorizar o agendamento. Tente novamente."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setOverrideReason("");
    setValidationError(null);
    onOpenChange(false);
  };

  const handleReasonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setOverrideReason(value);

    // Clear validation error on change
    if (validationError) {
      const error = overrideSchema.validate(value);
      setValidationError(error);
    }
  };

  const isValid = !overrideSchema.validate(overrideReason);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-amber-500" />
            Override de Conflitos - Autorização de Gestor
          </DialogTitle>
          <DialogDescription>
            Este agendamento possui conflitos que requerem autorização de
            gestor. Revise os conflitos abaixo e forneça um motivo para o
            override.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh] pr-4">
          <div className="space-y-6">
            {/* Appointment Summary */}
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-500" />
                    <span className="font-medium">Paciente:</span>
                    <span>{appointmentData.patient_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-green-500" />
                    <span className="font-medium">Profissional:</span>
                    <span>{appointmentData.professional_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-purple-500" />
                    <span className="font-medium">Serviço:</span>
                    <span>{appointmentData.service_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-orange-500" />
                    <span className="font-medium">Horário:</span>
                    <span>
                      {format(new Date(appointmentData.start_time), "PPp", {
                        locale: ptBR,
                      })}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Conflicts Summary */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Conflitos Identificados</h4>
                <div className="flex gap-2">
                  {errorConflicts.length > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {errorConflicts.length} crítico(s)
                    </Badge>
                  )}
                  {warningConflicts.length > 0 && (
                    <Badge variant="default" className="text-xs">
                      {warningConflicts.length} aviso(s)
                    </Badge>
                  )}
                  {infoConflicts.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {infoConflicts.length} informativo(s)
                    </Badge>
                  )}
                </div>
              </div>

              {/* Critical Conflicts */}
              {errorConflicts.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium text-red-700">
                      Conflitos Críticos
                    </span>
                  </div>
                  <div className="space-y-2 pl-6">
                    {errorConflicts.map((conflict, index) => (
                      <Alert key={index} className="border-red-200 bg-red-50">
                        <AlertDescription className="flex items-center gap-2">
                          {getSeverityIcon(conflict.severity)}
                          <span className="text-sm">{conflict.message}</span>
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}

              {/* Warning Conflicts */}
              {warningConflicts.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium text-yellow-700">
                      Avisos
                    </span>
                  </div>
                  <div className="space-y-2 pl-6">
                    {warningConflicts.map((conflict, index) => (
                      <Alert
                        key={index}
                        className="border-yellow-200 bg-yellow-50"
                      >
                        <AlertDescription className="flex items-center gap-2">
                          {getSeverityIcon(conflict.severity)}
                          <span className="text-sm">{conflict.message}</span>
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}

              {/* Info Conflicts */}
              {infoConflicts.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium text-blue-700">
                      Informações
                    </span>
                  </div>
                  <div className="space-y-2 pl-6">
                    {infoConflicts.map((conflict, index) => (
                      <Alert key={index} className="border-blue-200 bg-blue-50">
                        <AlertDescription className="flex items-center gap-2">
                          {getSeverityIcon(conflict.severity)}
                          <span className="text-sm">{conflict.message}</span>
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Override Reason Form */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Motivo do Override <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  placeholder="Explique detalhadamente o motivo para autorizar este agendamento mesmo com conflitos..."
                  className="min-h-[100px] resize-none"
                  maxLength={500}
                  value={overrideReason}
                  onChange={handleReasonChange}
                />
                <div className="flex justify-between text-xs text-gray-500">
                  {validationError && (
                    <span className="text-red-500">{validationError}</span>
                  )}
                  {!validationError && <span />}
                  <span>{overrideReason.length}/500</span>
                </div>
              </div>

              {/* Warning Notice */}
              <Alert className="border-amber-200 bg-amber-50">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  <strong>Atenção:</strong> Este override será registrado no
                  sistema de auditoria e todas as partes relevantes serão
                  notificadas sobre esta decisão.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting || isLoading}
          >
            Cancelar
          </Button>
          <Button
            type="button"
            onClick={validateAndSubmit}
            disabled={!isValid || isSubmitting || isLoading}
            className="bg-amber-600 hover:bg-amber-700 text-white"
          >
            {isSubmitting || isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                Processando...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4" />
                Autorizar Override
              </div>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
