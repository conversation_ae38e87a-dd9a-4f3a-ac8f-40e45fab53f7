"use client";

// AGENDATRINTAE3 AI Smart Scheduling Page
// Phase 7 AI Integration - AI Smart Scheduling Dashboard
// Generated by VI<PERSON><PERSON>DE SYSTEM V4.0 - AI Integration

// import SmartScheduling from "@/components/ai/SmartScheduling";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  CheckCircle,
  Clock,
  Sparkles,
  TrendingUp,
  Users,
} from "lucide-react";
import React from "react";

// export const metadata: Metadata = {
//   title: "AI Smart Scheduling | AGENDATRINTAE3",
//   description: "AI-powered medical appointment scheduling optimization",
// };

// Simple UI Components for layout
const Card = ({ children, className }: any) => (
  <div className={`border rounded-lg shadow-sm bg-white ${className}`}>
    {children}
  </div>
);

const CardHeader = ({ children }: any) => (
  <div className="p-6 pb-4">{children}</div>
);
const CardTitle = ({ children, className }: any) => (
  <h3 className={`text-lg font-semibold ${className}`}>{children}</h3>
);
const CardDescription = ({ children }: any) => (
  <p className="text-sm text-gray-600 mt-1">{children}</p>
);
const CardContent = ({ children, className }: any) => (
  <div className={`p-6 pt-0 ${className}`}>{children}</div>
);

const Button = ({ children, onClick, disabled, className }: any) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`px-4 py-2 bg-blue-600 text-white rounded disabled:opacity-50 hover:bg-blue-700 transition-colors ${className}`}
  >
    {children}
  </button>
);

const Tabs = ({ children, defaultValue, className }: any) => (
  <div className={className}>{children}</div>
);

const TabsList = ({ children }: any) => (
  <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-4">
    {children}
  </div>
);

const TabsTrigger = ({ children, value, isActive, onClick }: any) => (
  <button
    onClick={() => onClick(value)}
    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
      isActive
        ? "bg-white text-blue-600 shadow-sm"
        : "text-gray-600 hover:text-gray-900"
    }`}
  >
    {children}
  </button>
);

const TabsContent = ({ children, value, activeTab }: any) =>
  activeTab === value ? <div>{children}</div> : null;

const Badge = ({ children, className }: any) => (
  <span className={`px-2 py-1 text-xs rounded-full font-medium ${className}`}>
    {children}
  </span>
);

// Mock data for demonstration
const mockStats = {
  totalOptimizations: 156,
  implementedOptimizations: 142,
  averageWaitReduction: 35,
  patientSatisfaction: 94,
};

const mockRecentOptimizations = [
  {
    id: "1",
    patient: "Maria Santos",
    service: "Cardiology Consultation",
    urgency: "urgent",
    status: "implemented",
    efficiency: 9.2,
    createdAt: "2025-06-15T10:30:00Z",
  },
  {
    id: "2",
    patient: "João Silva",
    service: "General Consultation",
    urgency: "routine",
    status: "reviewed",
    efficiency: 8.7,
    createdAt: "2025-06-15T09:15:00Z",
  },
  {
    id: "3",
    patient: "Ana Costa",
    service: "Dermatology Consultation",
    urgency: "follow_up",
    status: "implemented",
    efficiency: 9.0,
    createdAt: "2025-06-15T08:45:00Z",
  },
];

function StatCard({
  title,
  value,
  unit,
  icon: Icon,
  description,
  trend,
}: {
  title: string;
  value: number;
  unit?: string;
  icon: React.ElementType;
  description: string;
  trend?: string;
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {value}
          {unit}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {trend && (
          <div className="flex items-center mt-2">
            <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
            <span className="text-xs text-green-600">{trend}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function OptimizationCard({ optimization }: { optimization: any }) {
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "emergency":
        return "bg-red-100 text-red-800";
      case "urgent":
        return "bg-orange-100 text-orange-800";
      case "routine":
        return "bg-blue-100 text-blue-800";
      case "follow_up":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "implemented":
        return "bg-green-100 text-green-800";
      case "reviewed":
        return "bg-blue-100 text-blue-800";
      case "generated":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-semibold">{optimization.patient}</h4>
          <div className="flex gap-2">
            <Badge className={getUrgencyColor(optimization.urgency)}>
              {optimization.urgency}
            </Badge>
            <Badge className={getStatusColor(optimization.status)}>
              {optimization.status}
            </Badge>
          </div>
        </div>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex justify-between">
            <span>Service:</span>
            <span className="font-medium">{optimization.service}</span>
          </div>
          <div className="flex justify-between">
            <span>Efficiency Score:</span>
            <span className="font-medium text-green-600">
              {optimization.efficiency}/10
            </span>
          </div>
          <div className="flex justify-between">
            <span>Generated:</span>
            <span>{new Date(optimization.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function AISmartSchedulingPage() {
  const [activeTab, setActiveTab] = React.useState("optimize");

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Brain className="h-8 w-8 text-blue-600" />
            AI Smart Scheduling
          </h1>
          <p className="text-muted-foreground">
            Intelligent medical appointment scheduling optimization with
            AI-powered conflict resolution
          </p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Sparkles className="mr-2 h-4 w-4" />
          New Optimization
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Optimizations"
          value={mockStats.totalOptimizations}
          icon={Brain}
          description="AI scheduling optimizations"
          trend="+12% this month"
        />
        <StatCard
          title="Implementation Rate"
          value={Math.round(
            (mockStats.implementedOptimizations /
              mockStats.totalOptimizations) *
              100
          )}
          unit="%"
          icon={CheckCircle}
          description="Successfully implemented"
          trend="+5% improvement"
        />
        <StatCard
          title="Wait Time Reduction"
          value={mockStats.averageWaitReduction}
          unit="%"
          icon={Clock}
          description="Average wait time reduction"
          trend="Consistent improvement"
        />
        <StatCard
          title="Patient Satisfaction"
          value={mockStats.patientSatisfaction}
          unit="%"
          icon={Users}
          description="Patient satisfaction rate"
          trend="+3% this quarter"
        />
      </div>

      {/* Main Content */}
      <Tabs defaultValue="optimize" className="space-y-4">
        <TabsList>
          <TabsTrigger
            value="optimize"
            isActive={activeTab === "optimize"}
            onClick={setActiveTab}
          >
            Generate Optimization
          </TabsTrigger>
          <TabsTrigger
            value="history"
            isActive={activeTab === "history"}
            onClick={setActiveTab}
          >
            Optimization History
          </TabsTrigger>
          <TabsTrigger
            value="analytics"
            isActive={activeTab === "analytics"}
            onClick={setActiveTab}
          >
            Scheduling Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="optimize" activeTab={activeTab}>
          <Card>
            <CardHeader>
              <CardTitle>Smart Scheduling Optimization</CardTitle>
              <CardDescription>
                Generate AI-powered scheduling optimization for medical
                appointments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg bg-blue-50">
                <p className="text-sm text-blue-800 mb-4">
                  <strong>Demo Mode:</strong> Using sample medical scheduling
                  data for demonstration.
                </p>
                {/* <SmartScheduling
                  userId="demo-user-1"
                  onOptimizationGenerated={(optimization) => {
                    console.log("Optimization generated:", optimization);
                  }}
                /> */}
                <div className="p-4 border rounded-lg bg-gray-50">
                  <p className="text-center text-gray-600">
                    SmartScheduling component temporarily disabled during
                    migration
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" activeTab={activeTab}>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Optimization History
              </CardTitle>
              <CardDescription>
                View and manage previous AI scheduling optimizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {mockRecentOptimizations.map((optimization) => (
                  <OptimizationCard
                    key={optimization.id}
                    optimization={optimization}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" activeTab={activeTab}>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Scheduling Efficiency Trends
                </CardTitle>
                <CardDescription>
                  AI optimization performance and efficiency metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">
                        Average Efficiency Score
                      </span>
                      <Badge className="bg-green-100 text-green-800">
                        8.9/10
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">
                      Consistently high efficiency in appointment optimization
                      with minimal conflicts.
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">
                        Conflict Resolution Rate
                      </span>
                      <Badge className="bg-blue-100 text-blue-800">96%</Badge>
                    </div>
                    <p className="text-sm text-gray-600">
                      AI successfully resolves scheduling conflicts with
                      alternative optimal solutions.
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">
                        Urgency Prioritization
                      </span>
                      <Badge className="bg-orange-100 text-orange-800">
                        Excellent
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">
                      Emergency and urgent cases consistently prioritized with
                      appropriate scheduling.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Medical Priority Insights
                </CardTitle>
                <CardDescription>
                  Analysis of medical urgency and scheduling patterns
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg bg-red-50">
                    <h4 className="font-medium text-red-800 mb-2">
                      🚨 Emergency Cases
                    </h4>
                    <p className="text-sm text-red-700">
                      100% same-day scheduling success rate for emergency
                      medical cases
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg bg-orange-50">
                    <h4 className="font-medium text-orange-800 mb-2">
                      ⚡ Urgent Cases
                    </h4>
                    <p className="text-sm text-orange-700">
                      Average 1.8 days wait time for urgent cases (target: &lt;2
                      days)
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg bg-blue-50">
                    <h4 className="font-medium text-blue-800 mb-2">
                      📅 Routine Cases
                    </h4>
                    <p className="text-sm text-blue-700">
                      Optimal scheduling with 35% reduction in average wait
                      times
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg bg-green-50">
                    <h4 className="font-medium text-green-800 mb-2">
                      🔄 Follow-up Cases
                    </h4>
                    <p className="text-sm text-green-700">
                      98% continuity of care maintained with preferred doctors
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
