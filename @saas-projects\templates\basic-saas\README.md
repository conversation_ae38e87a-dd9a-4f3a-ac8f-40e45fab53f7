# 🚀 Basic SaaS Template

A simple, production-ready SaaS starter template for VIBECODE projects.

## Features

- ✅ **Authentication**: <PERSON>pa<PERSON> Auth with email/password
- ✅ **Payments**: Stripe subscription integration
- ✅ **Dashboard**: User dashboard with basic metrics
- ✅ **Settings**: User profile and account settings
- ✅ **Email**: Transactional emails with React Email
- ✅ **Responsive**: Mobile-first design
- ✅ **Dark Mode**: Theme switching support
- ✅ **Type Safe**: Full TypeScript coverage

## Quick Start

```bash
# Copy template
cp -r ../basic-saas my-app
cd my-app

# Install dependencies
bun install

# Set up environment
cp .env.example .env.local
# Edit .env.local with your credentials

# Run development server
bun dev
```

## Stack

- Next.js 15 with App Router
- Supabase (Auth + Database)
- Stripe for payments
- TailwindCSS + shadcn/ui
- TypeScript
- Zustand for state management

## Perfect For

- MVPs and prototypes
- Simple B2C SaaS applications
- Solo founders and small teams
- Learning the VIBECODE stack

## Customization

1. Update branding in `src/config/site.ts`
2. Modify pricing plans in `src/config/pricing.ts`
3. Customize UI components in `src/components/ui/`
4. Add your features in `src/app/dashboard/`