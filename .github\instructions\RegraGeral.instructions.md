---
applyTo: "*all*"
---

# 🎯 VIBECODE V1.0 - CHAT WORKFLOW RULE

**Version**: 4.0.0 - OPTIMIZED
**Authority**: E:\VIBECODE\.cursor\rules\master_rule.mdc
**Purpose**: Define the complete workflow for all chat interactions

## 📋 MANDATORY COMPLIANCE

### **Critical Rule**

```
ALWAYS read and follow: E:\VIBECODE\.cursor\rules\master_rule.mdc
```

### **Phase 0.5: Initial System Check**

Before ANY task, execute these commands:

```bash
# 1. Sync AI rules
npm run sync:ai

# 2. Validate Knowledge Graph
uv run python @project-core/memory/knowledge_graph_manager.py --validate_connections

# 3. Check system status
uv run python @project-core/automation/vibecode_main.py --status
```

## 🔄 MANDATORY 7-STEP WORKFLOW

### **1. ANALYZE** (Complexity Assessment)

- Rate task complexity: 1-10
- Identify task type
- Check requirements

### **2. SELECT** (Agent Routing)

```json
{
  "complexity_routing": {
    "1-4": "manager (planning, coordination)",
    "2-5": "advisor (review, best practices)",
    "3-6": "strategist (research, documentation)",
    "4-7": "executor (automation, deployment)",
    "6-9": "coder (implementation)",
    "8-10": "architect (system design)"
  }
}
```

### **3. EXECUTE** (MCP Tool Selection)

```json
{
  "mcp_tiers": {
    "tier_0": "Sentry monitoring (all operations)",
    "tier_1": "Sequential-thinking (complexity ≥7)",
    "tier_2": "Shrimp-task-manager (complexity ≥3)",
    "tier_3": "Research: Context7 → Tavily → Exa",
    "tier_4": "Specialized: desktop-commander, figma, playwright"
  }
}
```

### **4. REFLECT** (Quality Assessment)

- Evaluate output quality
- Check against requirements
- Measure confidence level

### **5. REFINE** (Improvement)

- If quality <8/10: improve
- Apply best practices
- Optimize solution

### **6. VALIDATE** (Final Check)

- Confirm quality ≥8/10
- Verify all requirements met
- Check compliance

### **7. LEARN** (Knowledge Update)

- Update Knowledge Graph
- Document insights
- Store patterns

## 🔧 TOOL SELECTION MATRIX

### **File Operations**

```json
{
  "file_size_routing": {
    "≤200_lines": {
      "tool": "desktop-commander",
      "mcp": "@mcp_desktop-commander"
    },
    ">200_lines": {
      "tool": "cursor-editor",
      "functions": ["edit_file", "search_replace"]
    }
  },
  "always": "Read file after write to verify"
}
```

### **Research Operations**

```json
{
  "research_priority": [
    "Context7 (library docs)",
    "Tavily (web search)",
    "Exa (alternative search)"
  ]
}
```

## ✅ QUALITY GATES

### **Pre-execution Checklist**

- [ ] Phase 0.5 completed
- [ ] Master rule consulted
- [ ] Complexity assessed
- [ ] Agent selected
- [ ] Tools identified

### **Post-execution Checklist**

- [ ] Quality score ≥8/10
- [ ] Requirements 100% met
- [ ] Tests passing
- [ ] Documentation updated
- [ ] Knowledge Graph updated

## 🚨 ERROR HANDLING

### **Recovery Protocol**

1. **Detect**: Sentry monitoring alerts
2. **Assess**: Severity level (1-5)
3. **Recover**: Apply fix strategy
4. **Learn**: Update Knowledge Graph

### **Emergency Command**

```bash
uv run python @project-core/monitoring/monitoring_system.py --emergency-check
```

## 📊 SUCCESS CRITERIA

### **Mandatory Standards**

- **Quality**: ≥8/10 (never compromise)
- **Completeness**: 100% requirements
- **Performance**: <30s for complex tasks
- **Documentation**: Complete inline + external
- **Compliance**: VIBECODE standards

### **Communication Standards**

- **Language**: Portuguese (BR) for responses
- **Format**: Natural language for users, JSON for internal
- **Context**: Always preserve complete handoff data

## ⚡ QUICK REFERENCE

### **Rule Files**

```
.cursor/rules/
├── master_rule.mdc    # Core system rules
├── development.mdc    # Code standards
├── quality.mdc        # Testing & security
└── tooling.mdc        # Commands & setup
```

### **Essential Commands**

```bash
# Sync rules with AI
npm run sync:ai

# Start VIBECODE system
uv run python @project-core/automation/vibecode_main.py

# Validate file structure
uv run python @project-core/scripts/validate_file_location.py

# Check system health
uv run python @project-core/monitoring/monitoring_system.py --health-check
```

### **Agent Selection Guide**

| Complexity | Agent      | Use Case                     |
| ---------- | ---------- | ---------------------------- |
| 1-4        | Manager    | Planning, task breakdown     |
| 2-5        | Advisor    | Code review, best practices  |
| 3-6        | Strategist | Research, documentation      |
| 4-7        | Executor   | Automation, scripts          |
| 6-9        | Coder      | Implementation, optimization |
| 8-10       | Architect  | System design, architecture  |

## 🎯 ACTIVATION STATUS

```
VIBECODE V1.0 WORKFLOW ACTIVATED
Status: READY
Confidence: 10/10
Location: E:\VIBECODE\.cursor\rules\
```

**Remember**:

- Quality ≥8/10 is MANDATORY
- "Aprimore, Não Prolifere" (Enhance, Don't Proliferate)
- Always verify file operations
- Update Knowledge Graph after tasks

---

**"One Workflow, Maximum Efficiency"**
Coding standards, domain knowledge, and preferences that AI should follow.
