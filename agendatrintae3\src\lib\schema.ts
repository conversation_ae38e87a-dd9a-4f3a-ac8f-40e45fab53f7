// AGENDATRINTAE3 Database Schema
// Sistema de Agendamento Médico Schema
// Generated by VIBECODE SYSTEM V4.0 - Phase 6 Migration

import { relations } from "drizzle-orm";
import {
  boolean,
  date,
  decimal,
  integer,
  jsonb,
  pgTable,
  text,
  time,
  timestamp,
  uuid,
} from "drizzle-orm/pg-core";

// ===== USERS & AUTHENTICATION =====

export const users = pgTable("users", {
  id: uuid("id").defaultRandom().primaryKey(),
  email: text("email").notNull().unique(),
  name: text("name").notNull(),
  avatar_url: text("avatar_url"),
  role: text("role").notNull().default("patient"), // patient, doctor, admin, receptionist
  phone: text("phone"),
  cpf: text("cpf").unique(),
  birth_date: date("birth_date"),
  address: jsonb("address").default({}), // {street, city, state, zip, country}
  emergency_contact: jsonb("emergency_contact").default({}),
  medical_history: text("medical_history"),
  allergies: text("allergies"),
  medications: text("medications"),
  is_active: boolean("is_active").default(true),
  created_at: timestamp("created_at").defaultNow().notNull(),
  updated_at: timestamp("updated_at").defaultNow().notNull(),
});

// ===== DOCTORS =====

export const doctors = pgTable("doctors", {
  id: uuid("id").defaultRandom().primaryKey(),
  user_id: uuid("user_id").references(() => users.id, { onDelete: "cascade" }),
  crm: text("crm").notNull().unique(),
  specialty: text("specialty").notNull(),
  sub_specialties: jsonb("sub_specialties").default([]),
  bio: text("bio"),
  experience_years: integer("experience_years"),
  consultation_fee: decimal("consultation_fee", { precision: 10, scale: 2 }),
  is_available: boolean("is_available").default(true),
  rating: decimal("rating", { precision: 3, scale: 2 }).default("0"),
  total_reviews: integer("total_reviews").default(0),
  created_at: timestamp("created_at").defaultNow().notNull(),
  updated_at: timestamp("updated_at").defaultNow().notNull(),
});

// ===== SERVICES =====

export const services = pgTable("services", {
  id: uuid("id").defaultRandom().primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  duration_minutes: integer("duration_minutes").notNull().default(30),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  category: text("category").notNull(), // consultation, exam, procedure, surgery
  requires_preparation: boolean("requires_preparation").default(false),
  preparation_instructions: text("preparation_instructions"),
  is_active: boolean("is_active").default(true),
  created_at: timestamp("created_at").defaultNow().notNull(),
  updated_at: timestamp("updated_at").defaultNow().notNull(),
});

// ===== DOCTOR SERVICES (Many-to-Many) =====

export const doctor_services = pgTable("doctor_services", {
  id: uuid("id").defaultRandom().primaryKey(),
  doctor_id: uuid("doctor_id").references(() => doctors.id, {
    onDelete: "cascade",
  }),
  service_id: uuid("service_id").references(() => services.id, {
    onDelete: "cascade",
  }),
  custom_price: decimal("custom_price", { precision: 10, scale: 2 }), // Override service price
  is_available: boolean("is_available").default(true),
  created_at: timestamp("created_at").defaultNow().notNull(),
});

// ===== AVAILABILITY =====

export const availability = pgTable("availability", {
  id: uuid("id").defaultRandom().primaryKey(),
  doctor_id: uuid("doctor_id").references(() => doctors.id, {
    onDelete: "cascade",
  }),
  day_of_week: integer("day_of_week").notNull(), // 0=Sunday, 1=Monday, ..., 6=Saturday
  start_time: time("start_time").notNull(),
  end_time: time("end_time").notNull(),
  is_active: boolean("is_active").default(true),
  created_at: timestamp("created_at").defaultNow().notNull(),
});

// ===== APPOINTMENTS =====

export const appointments = pgTable("appointments", {
  id: uuid("id").defaultRandom().primaryKey(),
  patient_id: uuid("patient_id").references(() => users.id, {
    onDelete: "cascade",
  }),
  doctor_id: uuid("doctor_id").references(() => doctors.id, {
    onDelete: "cascade",
  }),
  service_id: uuid("service_id").references(() => services.id),
  scheduled_date: date("scheduled_date").notNull(),
  scheduled_time: time("scheduled_time").notNull(),
  duration_minutes: integer("duration_minutes").notNull().default(30),
  status: text("status").notNull().default("scheduled"), // scheduled, confirmed, in_progress, completed, cancelled, no_show
  type: text("type").notNull().default("in_person"), // in_person, telemedicine
  notes: text("notes"),
  patient_notes: text("patient_notes"), // Notes from patient
  doctor_notes: text("doctor_notes"), // Notes from doctor
  prescription: text("prescription"),
  diagnosis: text("diagnosis"),
  follow_up_date: date("follow_up_date"),
  total_amount: decimal("total_amount", { precision: 10, scale: 2 }),
  payment_status: text("payment_status").default("pending"), // pending, paid, cancelled
  payment_method: text("payment_method"), // cash, card, insurance, pix
  reminder_sent: boolean("reminder_sent").default(false),
  created_at: timestamp("created_at").defaultNow().notNull(),
  updated_at: timestamp("updated_at").defaultNow().notNull(),
});

// ===== APPOINTMENT HISTORY =====

export const appointment_history = pgTable("appointment_history", {
  id: uuid("id").defaultRandom().primaryKey(),
  appointment_id: uuid("appointment_id").references(() => appointments.id, {
    onDelete: "cascade",
  }),
  action: text("action").notNull(), // created, updated, cancelled, completed, rescheduled
  old_values: jsonb("old_values").default({}),
  new_values: jsonb("new_values").default({}),
  performed_by: uuid("performed_by").references(() => users.id),
  notes: text("notes"),
  created_at: timestamp("created_at").defaultNow().notNull(),
});

// ===== REVIEWS =====

export const reviews = pgTable("reviews", {
  id: uuid("id").defaultRandom().primaryKey(),
  appointment_id: uuid("appointment_id").references(() => appointments.id, {
    onDelete: "cascade",
  }),
  patient_id: uuid("patient_id").references(() => users.id, {
    onDelete: "cascade",
  }),
  doctor_id: uuid("doctor_id").references(() => doctors.id, {
    onDelete: "cascade",
  }),
  rating: integer("rating").notNull(), // 1-5 stars
  comment: text("comment"),
  is_anonymous: boolean("is_anonymous").default(false),
  is_approved: boolean("is_approved").default(false),
  created_at: timestamp("created_at").defaultNow().notNull(),
});

// ===== RELATIONS =====

export const usersRelations = relations(users, ({ many, one }) => ({
  doctor_profile: one(doctors, {
    fields: [users.id],
    references: [doctors.user_id],
  }),
  patient_appointments: many(appointments, {
    relationName: "patient_appointments",
  }),
  reviews: many(reviews),
  appointment_history: many(appointment_history),
  ai_scheduling_optimizations: many(ai_scheduling_optimization),
  ai_scheduling_history: many(ai_scheduling_history),
}));

export const doctorsRelations = relations(doctors, ({ one, many }) => ({
  user: one(users, {
    fields: [doctors.user_id],
    references: [users.id],
  }),
  doctor_services: many(doctor_services),
  availability: many(availability),
  appointments: many(appointments),
  reviews: many(reviews),
}));

export const servicesRelations = relations(services, ({ many }) => ({
  doctor_services: many(doctor_services),
  appointments: many(appointments),
}));

export const doctor_servicesRelations = relations(
  doctor_services,
  ({ one }) => ({
    doctor: one(doctors, {
      fields: [doctor_services.doctor_id],
      references: [doctors.id],
    }),
    service: one(services, {
      fields: [doctor_services.service_id],
      references: [services.id],
    }),
  })
);

export const availabilityRelations = relations(availability, ({ one }) => ({
  doctor: one(doctors, {
    fields: [availability.doctor_id],
    references: [doctors.id],
  }),
}));

export const appointmentsRelations = relations(
  appointments,
  ({ one, many }) => ({
    patient: one(users, {
      fields: [appointments.patient_id],
      references: [users.id],
      relationName: "patient_appointments",
    }),
    doctor: one(doctors, {
      fields: [appointments.doctor_id],
      references: [doctors.id],
    }),
    service: one(services, {
      fields: [appointments.service_id],
      references: [services.id],
    }),
    history: many(appointment_history),
    review: one(reviews),
  })
);

export const appointment_historyRelations = relations(
  appointment_history,
  ({ one }) => ({
    appointment: one(appointments, {
      fields: [appointment_history.appointment_id],
      references: [appointments.id],
    }),
    performed_by_user: one(users, {
      fields: [appointment_history.performed_by],
      references: [users.id],
    }),
  })
);

export const reviewsRelations = relations(reviews, ({ one }) => ({
  appointment: one(appointments, {
    fields: [reviews.appointment_id],
    references: [appointments.id],
  }),
  patient: one(users, {
    fields: [reviews.patient_id],
    references: [users.id],
  }),
  doctor: one(doctors, {
    fields: [reviews.doctor_id],
    references: [doctors.id],
  }),
}));

// ===== AI SMART SCHEDULING - PHASE 7 =====

export const ai_scheduling_optimization = pgTable(
  "ai_scheduling_optimization",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    user_id: uuid("user_id").references(() => users.id, {
      onDelete: "cascade",
    }),

    // Scheduling Request Data
    request_data: jsonb("request_data").notNull(), // Patient preferences, urgency, constraints
    doctor_preferences: jsonb("doctor_preferences").default({}), // Preferred doctors, specialties
    scheduling_constraints: jsonb("scheduling_constraints").default({}), // Time constraints, availability
    urgency_level: text("urgency_level").notNull().default("routine"), // emergency, urgent, routine, follow_up

    // AI Generated Optimization
    optimization_results: jsonb("optimization_results").notNull(), // AI-generated scheduling suggestions
    recommended_slots: jsonb("recommended_slots").notNull(), // Optimal appointment slots
    alternative_options: jsonb("alternative_options").default([]), // Alternative scheduling options
    conflict_resolution: jsonb("conflict_resolution").default({}), // Conflict resolution strategies
    efficiency_score: decimal("efficiency_score", { precision: 3, scale: 2 }), // Scheduling efficiency (0-10)

    // Medical Context Analysis
    medical_priority: text("medical_priority"), // high, medium, low based on medical urgency
    specialty_matching: jsonb("specialty_matching").default({}), // Doctor-patient specialty matching
    continuity_of_care: jsonb("continuity_of_care").default({}), // Previous doctor relationships
    treatment_timeline: jsonb("treatment_timeline").default({}), // Treatment sequence optimization

    // AI Model Information
    ai_model: text("ai_model").default("gpt-4o"), // AI model used
    ai_version: text("ai_version"), // Model version
    confidence_score: decimal("confidence_score", { precision: 3, scale: 2 }), // AI confidence (0-1)

    // Implementation and Feedback
    status: text("status").notNull().default("generated"), // generated, reviewed, implemented, archived
    implemented_slot: uuid("implemented_slot").references(
      () => appointments.id
    ), // Implemented appointment
    feedback: text("feedback"), // User feedback on optimization
    accuracy_rating: integer("accuracy_rating"), // 1-5 rating of optimization accuracy

    // Metadata
    created_at: timestamp("created_at").defaultNow().notNull(),
    updated_at: timestamp("updated_at").defaultNow().notNull(),
  }
);

export const ai_scheduling_history = pgTable("ai_scheduling_history", {
  id: uuid("id").defaultRandom().primaryKey(),
  optimization_id: uuid("optimization_id").references(
    () => ai_scheduling_optimization.id,
    { onDelete: "cascade" }
  ),
  action: text("action").notNull(), // generated, reviewed, implemented, rescheduled, cancelled
  performed_by: uuid("performed_by").references(() => users.id),
  notes: text("notes"),
  metadata: jsonb("metadata").default({}),
  created_at: timestamp("created_at").defaultNow().notNull(),
});

export const scheduling_analytics = pgTable("scheduling_analytics", {
  id: uuid("id").defaultRandom().primaryKey(),
  date: date("date").notNull(),
  doctor_id: uuid("doctor_id").references(() => doctors.id),

  // Daily Analytics
  total_appointments: integer("total_appointments").default(0),
  ai_optimized_appointments: integer("ai_optimized_appointments").default(0),
  efficiency_improvement: decimal("efficiency_improvement", {
    precision: 5,
    scale: 2,
  }), // Percentage improvement
  conflict_resolutions: integer("conflict_resolutions").default(0),
  patient_satisfaction_score: decimal("patient_satisfaction_score", {
    precision: 3,
    scale: 2,
  }),

  // Performance Metrics
  average_wait_time: integer("average_wait_time"), // Minutes
  schedule_utilization: decimal("schedule_utilization", {
    precision: 3,
    scale: 2,
  }), // Percentage
  no_show_rate: decimal("no_show_rate", { precision: 3, scale: 2 }), // Percentage

  created_at: timestamp("created_at").defaultNow().notNull(),
});

// ===== EXTENDED RELATIONS =====

export const aiSchedulingOptimizationRelations = relations(
  ai_scheduling_optimization,
  ({ one, many }) => ({
    user: one(users, {
      fields: [ai_scheduling_optimization.user_id],
      references: [users.id],
    }),
    implemented_appointment: one(appointments, {
      fields: [ai_scheduling_optimization.implemented_slot],
      references: [appointments.id],
    }),
    history: many(ai_scheduling_history),
  })
);

export const aiSchedulingHistoryRelations = relations(
  ai_scheduling_history,
  ({ one }) => ({
    optimization: one(ai_scheduling_optimization, {
      fields: [ai_scheduling_history.optimization_id],
      references: [ai_scheduling_optimization.id],
    }),
    performed_by_user: one(users, {
      fields: [ai_scheduling_history.performed_by],
      references: [users.id],
    }),
  })
);

export const schedulingAnalyticsRelations = relations(
  scheduling_analytics,
  ({ one }) => ({
    doctor: one(doctors, {
      fields: [scheduling_analytics.doctor_id],
      references: [doctors.id],
    }),
  })
);

// ===== TYPE EXPORTS =====

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Doctor = typeof doctors.$inferSelect;
export type NewDoctor = typeof doctors.$inferInsert;

export type Service = typeof services.$inferSelect;
export type NewService = typeof services.$inferInsert;

export type DoctorService = typeof doctor_services.$inferSelect;
export type NewDoctorService = typeof doctor_services.$inferInsert;

export type Availability = typeof availability.$inferSelect;
export type NewAvailability = typeof availability.$inferInsert;

export type Appointment = typeof appointments.$inferSelect;
export type NewAppointment = typeof appointments.$inferInsert;

export type AppointmentHistory = typeof appointment_history.$inferSelect;
export type NewAppointmentHistory = typeof appointment_history.$inferInsert;

export type Review = typeof reviews.$inferSelect;
export type NewReview = typeof reviews.$inferInsert;

export type AISchedulingOptimization =
  typeof ai_scheduling_optimization.$inferSelect;
export type NewAISchedulingOptimization =
  typeof ai_scheduling_optimization.$inferInsert;
export type AISchedulingHistory = typeof ai_scheduling_history.$inferSelect;
export type NewAISchedulingHistory = typeof ai_scheduling_history.$inferInsert;
export type SchedulingAnalytics = typeof scheduling_analytics.$inferSelect;
export type NewSchedulingAnalytics = typeof scheduling_analytics.$inferInsert;
