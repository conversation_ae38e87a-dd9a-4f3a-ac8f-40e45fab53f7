{"kiro": {"version": "1.0.0", "description": "KIRO V1.0 - Master Configuration (Adapted from VIBECODE)", "created": "2025-01-16", "status": "PRODUCTION_READY", "principle": "Enhance, Don't Proliferate (≥85% reuse)"}, "mcp_servers": {"desktop-commander": "@mcp/desktop-commander", "sequential-thinking": "@mcp/sequential-thinking", "context7": "@mcp/context7-mcp", "tavily": "@mcp/tavily-mcp", "exa": "@mcp/exa-mcp"}, "workflows": {"mandatory_steps": 7, "quality_threshold": 8, "principle": "<PERSON><PERSON><PERSON>, Don't Proliferate"}, "paths": {"steering": ".kiro/steering/", "settings": ".kiro/settings/", "specs": ".kiro/specs/", "hooks": ".kiro/hooks/"}, "complexity_routing": {"1-4": "basic_operations", "5-7": "standard_workflow", "8-10": "advanced_workflow"}, "environment": {"platform": "windows", "shell": "cmd"}, "file_operations": {"small_files_limit": 200, "verification": "always_read_after_write"}, "quality_standards": {"minimum_quality_threshold": 8, "mandatory_verification": true, "auto_refinement": true, "max_refinement_cycles": 3}, "research_protocol": {"mandatory_sequence": ["context7", "tavily", "exa"], "minimum_sources": 3, "synthesis_quality_min": 8}}