@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Glass Morphism Variables */
@layer base {
  :root {
    /* Glass Morphism Effects */
    --glass-blur: 16px;
    --glass-saturation: 180%;
    --glass-opacity: 0.1;
    --glass-border-opacity: 0.2;

    /* Enhanced Shadows */
    --shadow-glass: 0 8px 32px rgba(17, 32, 49, 0.08);
    --shadow-glass-hover: 0 8px 40px rgba(172, 148, 105, 0.12);
    --shadow-glow: 0 0 60px rgba(172, 148, 105, 0.15);

    /* Animation Timing */
    --transition-fast: 150ms;
    --transition-base: 300ms;
    --transition-slow: 500ms;
    --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply antialiased;
    background-image: radial-gradient(
        circle at 20% 50%,
        rgba(172, 148, 105, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(17, 32, 49, 0.03) 0%,
        transparent 50%
      );
  }

  * {
    @apply border-border;
  }
}

/* Glass Morphism Components */
@layer components {
  /* Container Styles */
  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  /* Glass Effects Base */
  .glass {
    @apply backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/20;
    backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation));
  }

  .glass-subtle {
    @apply backdrop-blur-sm bg-white/5 dark:bg-gray-900/5 border border-white/10 dark:border-gray-700/10;
  }

  .glass-strong {
    @apply backdrop-blur-xl bg-white/15 dark:bg-gray-900/15 border border-white/25 dark:border-gray-700/25;
  }

  /* Button Styles with Glass Morphism */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-300 ease-smooth;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#AC9469] focus-visible:ring-offset-2;
    @apply disabled:pointer-events-none disabled:opacity-50;
    @apply relative overflow-hidden;
  }

  .btn-primary {
    @apply btn bg-gradient-to-br from-[#AC9469] to-[#8a7854] text-white;
    @apply shadow-[0_4px_20px_rgba(172,148,105,0.3)];
    @apply hover:shadow-[0_6px_30px_rgba(172,148,105,0.4)] hover:scale-[1.02];
  }

  .btn-secondary {
    @apply btn glass text-gray-900 dark:text-gray-100;
    @apply hover:bg-white/20 dark:hover:bg-gray-900/20;
  }

  .btn-ghost {
    @apply btn bg-transparent hover:glass;
  }

  /* Modern Card Styles */
  .card-glass {
    @apply glass rounded-xl p-6 shadow-glass transition-all duration-300;
    @apply hover:shadow-glass-hover hover:bg-white/15 dark:hover:bg-gray-900/15;
  }

  /* Input Styles with Glass Effect */
  .input-glass {
    @apply flex h-11 w-full rounded-lg px-4 py-2 text-sm;
    @apply glass-subtle placeholder:text-gray-500 dark:placeholder:text-gray-400;
    @apply transition-all duration-200;
    @apply focus:bg-white/10 dark:focus:bg-gray-900/10;
    @apply focus:border-[#AC9469] focus:ring-2 focus:ring-[#AC9469]/20;
  }

  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-br from-[#112031] to-[#AC9469] bg-clip-text text-transparent;
  }

  /* Glow Effects */
  .glow-gold {
    filter: drop-shadow(0 0 8px rgba(172, 148, 105, 0.7));
  }

  .glow-soft {
    filter: drop-shadow(0 0 20px rgba(172, 148, 105, 0.3));
  }
}

/* Animations */
@layer utilities {
  /* Text Balance */
  .text-balance {
    text-wrap: balance;
  }

  /* Smooth Animations */
  .animate-in {
    animation: animateIn 0.5s var(--ease-smooth) forwards;
  }

  .animate-fade {
    animation: fadeIn 0.5s var(--ease-smooth) forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.5s var(--ease-smooth) forwards;
  }

  .animate-scale {
    animation: scaleIn 0.5s var(--ease-smooth) forwards;
  }

  /* Hover Lift Effect */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-1;
  }

  /* Pulse Glow */
  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }
}

/* Keyframe Animations */
@keyframes animateIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseGlow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}