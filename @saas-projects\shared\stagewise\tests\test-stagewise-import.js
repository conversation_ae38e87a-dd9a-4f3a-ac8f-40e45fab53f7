// Teste de importação do Stagewise
try {
  console.log('Testando importação do Stagewise...');
  
  // Verificar se os pacotes estão instalados
  const fs = require('fs');
  const path = require('path');
  
  const projects = ['aegiswallet', 'agendatrintae3', 'neonpro'];
  
  projects.forEach(project => {
    const packagePath = path.join('@project-core', 'projects', project, 'package.json');
    console.log(`\n📦 Verificando ${project}:`);
    
    if (fs.existsSync(packagePath)) {
      const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      const deps = { ...pkg.dependencies, ...pkg.devDependencies };
      
      console.log('  ✅ package.json encontrado');
      console.log('  📋 Stagewise packages:');
      console.log('    @stagewise/toolbar-next:', deps['@stagewise/toolbar-next'] || '❌ Não encontrado');
      console.log('    @stagewise-plugins/react:', deps['@stagewise-plugins/react'] || '❌ Não encontrado');
    } else {
      console.log('  ❌ package.json não encontrado');
    }
  });
  
  console.log('\n🎯 Verificando estrutura centralizada:');
  const sharedPath = path.join('@project-core', 'shared', 'stagewise');
  if (fs.existsSync(sharedPath)) {
    console.log('  ✅ Estrutura centralizada criada');
    const files = fs.readdirSync(sharedPath, { recursive: true });
    files.forEach(file => console.log(`    📄 ${file}`));
  } else {
    console.log('  ❌ Estrutura centralizada não encontrada');
  }
  
} catch (error) {
  console.error('❌ Erro no teste:', error.message);
}