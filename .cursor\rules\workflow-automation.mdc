---
alwaysApply: true
---

# 📋 **REGRA: WORKFLOW AUTOMATION - 7 ETAPAS**

_Prioridade: ALTA | Workflow de 7 etapas como regras automatizadas_

## **Workflow Obrigatório VIBECODE V1.0**

```json
{
  "workflow_phases": {
    "phase_0_5": "System Validation (8 scripts)",
    "phase_1": "Analyze (complexity + task management)",
    "phase_2": "Select (tools based on complexity)",
    "phase_3": "Execute (with task tracking)",
    "phase_4": "Reflect (quality assessment)",
    "phase_5": "Refine (if quality <8/10)",
    "phase_6": "Validate (final + KG update)"
  }
}
```

## **Phase 0.5: System Validation**

### **Comandos de Validação Essenciais**

```bash
uv run python memory-bank/python/knowledge_graph_manager.py --validate_connections
uv run python .cursor/scripts/finaltest.py
uv run python .cursor/scripts/vibecode_core_validator.py
uv run python .cursor/scripts/vibecode_task_system.py --status
```

### **Validação de Sucesso**

- Todos os scripts essenciais executados sem erro
- Conexões KG validadas
- Sistema operacional confirmado
- Testes de integração passaram

## **Phase 1: Analyze**

### **Análise de Complexidade**

```json
{
  "complexity_analysis": {
    "input": "user_request + context",
    "output": "complexity_score (1-10)",
    "task_management_decision": "based on ≥3 threshold",
    "triggers_identified": "list of activation triggers"
  }
}
```

### **Critérios de Análise**

- Usar regras de `task-automation.mdc`
- Calcular complexity_score
- Determinar necessidade de task management
- Identificar triggers específicos

## **Phase 2: Select**

### **Seleção de Ferramentas por Complexidade**

```json
{
  "tool_selection": {
    "complexity_1_3": {
      "task_management": false,
      "tools": ["basic_operations"]
    },
    "complexity_4_6": {
      "task_management": true,
      "tools": ["add_tasks", "update_tasks"]
    },
    "complexity_7_10": {
      "task_management": true,
      "sequential_thinking": true,
      "tools": [
        "view_tasklist",
        "add_tasks",
        "update_tasks",
        "reorganize_tasklist"
      ]
    }
  }
}
```

### **MCP Tool Routing**

```
≥7 complexity: Sequential Thinking MCP
≥3 complexity: Native Task Management
Research needs: Context7 → Tavily → Exa
File ops: ≤200 lines = Desktop Commander, >200 = Cursor Editor
```

## **Phase 3: Execute**

### **Execução com Tracking**

```json
{
  "execution_tracking": {
    "task_management_activated": "if complexity ≥ 3",
    "batch_updates_enabled": true,
    "workflow_transitions": "automatic",
    "progress_monitoring": "real-time"
  }
}
```

### **Task Creation Automática**

- Se task management ativado: criar breakdown automático
- Usar padrões de `task-automation.mdc`
- Implementar batch updates eficientes
- Monitorar progresso em tempo real

## **Phase 4: Reflect**

### **Avaliação de Qualidade**

```json
{
  "quality_assessment": {
    "base_score": 8.0,
    "bonuses": {
      "request_processed": "+1.0",
      "task_management_appropriate": "+0.5",
      "all_requirements_met": "+0.5"
    },
    "minimum_threshold": 8.0
  }
}
```

### **Critérios de Qualidade**

- Completude: 100% dos requisitos atendidos
- Funcionalidade: Sistema operacional
- Task management: Apropriadamente usado
- Performance: Dentro dos limites

## **Phase 5: Refine (Condicional)**

### **Ativação de Refinamento**

```
IF quality_score < 8.0 THEN
  - Identificar pontos de melhoria
  - Aplicar correções automáticas
  - Recalcular quality_score
  - Repetir até ≥8.0 OU máximo 3 iterações
```

### **Estratégias de Refinamento**

- Task organization: Reorganizar estrutura
- Batch operations: Otimizar transições
- Quality gates: Aplicar validações adicionais
- Error recovery: Implementar correções

## **Phase 6: Validate**

### **Validação Final**

```json
{
  "final_validation": {
    "quality_threshold": "≥8.0 (mandatory)",
    "completeness_check": "100% requirements",
    "task_management_validation": "appropriate usage",
    "knowledge_graph_update": "if quality ≥8.0"
  }
}
```

### **Knowledge Graph Update**

- Registrar padrões de uso
- Atualizar métricas de efetividade
- Aprender com decisões tomadas
- Melhorar detecção futura

## **Batch Update Patterns**

### **Workflow Transitions**

```json
{
  "transition_patterns": {
    "complete_current_start_next": {
      "tasks": [
        { "task_id": "current", "state": "COMPLETE" },
        { "task_id": "next", "state": "IN_PROGRESS" }
      ]
    },
    "bulk_completion": {
      "tasks": [
        { "task_id": "task1", "state": "COMPLETE" },
        { "task_id": "task2", "state": "COMPLETE" },
        { "task_id": "task3", "state": "COMPLETE" }
      ]
    }
  }
}
```

## **Error Handling**

### **Recovery Protocols**

```
Phase failure → Log error + Continue with degraded functionality
Quality <8.0 → Auto-refinement (max 3 attempts)
Task management error → Fallback to manual mode
KG unavailable → Use cached patterns
```

## **Performance Targets**

- **Phase 0.5**: <30s (script execution)
- **Phase 1-2**: <5s (analysis + selection)
- **Phase 3**: Variable (execution dependent)
- **Phase 4-6**: <10s (assessment + validation)
- **Total workflow**: <60s for typical requests

---

**Princípio**: "Aprimore, Não Prolifere" - Workflow como regras ao invés de código
**Status**: ✅ ATIVO - Substitui vibecode_workflow_orchestrator.py (258 linhas)# 📋 **REGRA: WORKFLOW AUTOMATION - 7 ETAPAS**

_Prioridade: ALTA | Workflow de 7 etapas como regras automatizadas_

## **Workflow Obrigatório VIBECODE V1.0**

```json
{
  "workflow_phases": {
    "phase_0_5": "System Validation (8 scripts)",
    "phase_1": "Analyze (complexity + task management)",
    "phase_2": "Select (tools based on complexity)",
    "phase_3": "Execute (with task tracking)",
    "phase_4": "Reflect (quality assessment)",
    "phase_5": "Refine (if quality <8/10)",
    "phase_6": "Validate (final + KG update)"
  }
}
```

## **Phase 0.5: System Validation**

### **Comandos de Validação Essenciais**

```bash
uv run python memory-bank/python/knowledge_graph_manager.py --validate_connections
uv run python .cursor/scripts/finaltest.py
uv run python .cursor/scripts/vibecode_core_validator.py
uv run python .cursor/scripts/vibecode_task_system.py --status
```

### **Validação de Sucesso**

- Todos os scripts essenciais executados sem erro
- Conexões KG validadas
- Sistema operacional confirmado
- Testes de integração passaram

## **Phase 1: Analyze**

### **Análise de Complexidade**

```json
{
  "complexity_analysis": {
    "input": "user_request + context",
    "output": "complexity_score (1-10)",
    "task_management_decision": "based on ≥3 threshold",
    "triggers_identified": "list of activation triggers"
  }
}
```

### **Critérios de Análise**

- Usar regras de `task-automation.mdc`
- Calcular complexity_score
- Determinar necessidade de task management
- Identificar triggers específicos

## **Phase 2: Select**

### **Seleção de Ferramentas por Complexidade**

```json
{
  "tool_selection": {
    "complexity_1_3": {
      "task_management": false,
      "tools": ["basic_operations"]
    },
    "complexity_4_6": {
      "task_management": true,
      "tools": ["add_tasks", "update_tasks"]
    },
    "complexity_7_10": {
      "task_management": true,
      "sequential_thinking": true,
      "tools": [
        "view_tasklist",
        "add_tasks",
        "update_tasks",
        "reorganize_tasklist"
      ]
    }
  }
}
```

### **MCP Tool Routing**

```
≥7 complexity: Sequential Thinking MCP
≥3 complexity: Native Task Management
Research needs: Context7 → Tavily → Exa
File ops: ≤200 lines = Desktop Commander, >200 = Cursor Editor
```

## **Phase 3: Execute**

### **Execução com Tracking**

```json
{
  "execution_tracking": {
    "task_management_activated": "if complexity ≥ 3",
    "batch_updates_enabled": true,
    "workflow_transitions": "automatic",
    "progress_monitoring": "real-time"
  }
}
```

### **Task Creation Automática**

- Se task management ativado: criar breakdown automático
- Usar padrões de `task-automation.mdc`
- Implementar batch updates eficientes
- Monitorar progresso em tempo real

## **Phase 4: Reflect**

### **Avaliação de Qualidade**

```json
{
  "quality_assessment": {
    "base_score": 8.0,
    "bonuses": {
      "request_processed": "+1.0",
      "task_management_appropriate": "+0.5",
      "all_requirements_met": "+0.5"
    },
    "minimum_threshold": 8.0
  }
}
```

### **Critérios de Qualidade**

- Completude: 100% dos requisitos atendidos
- Funcionalidade: Sistema operacional
- Task management: Apropriadamente usado
- Performance: Dentro dos limites

## **Phase 5: Refine (Condicional)**

### **Ativação de Refinamento**

```
IF quality_score < 8.0 THEN
  - Identificar pontos de melhoria
  - Aplicar correções automáticas
  - Recalcular quality_score
  - Repetir até ≥8.0 OU máximo 3 iterações
```

### **Estratégias de Refinamento**

- Task organization: Reorganizar estrutura
- Batch operations: Otimizar transições
- Quality gates: Aplicar validações adicionais
- Error recovery: Implementar correções

## **Phase 6: Validate**

### **Validação Final**

```json
{
  "final_validation": {
    "quality_threshold": "≥8.0 (mandatory)",
    "completeness_check": "100% requirements",
    "task_management_validation": "appropriate usage",
    "knowledge_graph_update": "if quality ≥8.0"
  }
}
```

### **Knowledge Graph Update**

- Registrar padrões de uso
- Atualizar métricas de efetividade
- Aprender com decisões tomadas
- Melhorar detecção futura

## **Batch Update Patterns**

### **Workflow Transitions**

```json
{
  "transition_patterns": {
    "complete_current_start_next": {
      "tasks": [
        { "task_id": "current", "state": "COMPLETE" },
        { "task_id": "next", "state": "IN_PROGRESS" }
      ]
    },
    "bulk_completion": {
      "tasks": [
        { "task_id": "task1", "state": "COMPLETE" },
        { "task_id": "task2", "state": "COMPLETE" },
        { "task_id": "task3", "state": "COMPLETE" }
      ]
    }
  }
}
```

## **Error Handling**

### **Recovery Protocols**

```
Phase failure → Log error + Continue with degraded functionality
Quality <8.0 → Auto-refinement (max 3 attempts)
Task management error → Fallback to manual mode
KG unavailable → Use cached patterns
```

## **Performance Targets**

- **Phase 0.5**: <30s (script execution)
- **Phase 1-2**: <5s (analysis + selection)
- **Phase 3**: Variable (execution dependent)
- **Phase 4-6**: <10s (assessment + validation)
- **Total workflow**: <60s for typical requests

---

**Princípio**: "Aprimore, Não Prolifere" - Workflow como regras ao invés de código
**Status**: ✅ ATIVO - Substitui vibecode_workflow_orchestrator.py (258 linhas)
