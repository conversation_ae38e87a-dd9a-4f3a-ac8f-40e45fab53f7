[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "vibecode-core"
version = "1.0.0"
description = "VIBECODE V1.0 Core System"
readme = "README.md"
requires-python = ">=3.10"
license = { text = "Proprietary" }
authors = [
    { name = "GRUPO US", email = "<EMAIL>" }
]
dependencies = [
    "click>=8.1.7",
    "colorama>=0.4.6",
    "pyyaml>=6.0.1",
    "rich>=13.7.0",
    "gitpython>=3.1.40",
    "pydantic>=2.0.0",
    "langchain>=0.1.0",
    "langgraph>=0.0.20",
    "loguru>=0.7.0",
    "httpx>=0.25.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "psutil>=5.9.0",
    "networkx>=3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "mypy>=1.5.1",
]

[tool.uv]
python = "3.10"
exclude = ["node_modules", ".venv", ".next", "dist", "build"]

[tool.black]
line-length = 88
target-version = ["py310"]
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | node_modules
)/
'''

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest]
testpaths = ["tests"]
python_files = "test_*.py"
